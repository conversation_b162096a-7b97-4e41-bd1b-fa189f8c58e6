/**
 * Test de la Correction DIRECTE pour ERR_CONTENT_LENGTH_MISMATCH
 * Teste la correction qui s'applique au tout début de main.js
 */

const { app, BrowserWindow } = require('electron');
const log = require('electron-log');

async function testDirectFix() {
  log.info('🧪 [TestDirect] ========================================');
  log.info('🧪 [TestDirect] TEST DE LA CORRECTION DIRECTE');
  log.info('🧪 [TestDirect] ========================================');

  try {
    // Étape 1: Vérifier que la correction directe est chargée
    log.info('🔍 [TestDirect] Vérification de la correction directe...');
    const { getDirectFix } = require('./src/main/direct-content-length-fix');
    const directFix = getDirectFix();
    const stats = directFix.getStats();
    
    log.info(`📊 [TestDirect] État de la correction directe: ${JSON.stringify(stats)}`);
    
    if (!stats.isApplied) {
      log.error('❌ [TestDirect] LA CORRECTION DIRECTE N\'EST PAS APPLIQUÉE !');
      log.info('🔧 [TestDirect] Tentative d\'application manuelle...');
      directFix.applyImmediateFix();
      
      const newStats = directFix.getStats();
      log.info(`📊 [TestDirect] Nouvel état: ${JSON.stringify(newStats)}`);
    } else {
      log.info('✅ [TestDirect] Correction directe déjà appliquée');
    }

    // Étape 2: Créer une fenêtre de test
    log.info('🪟 [TestDirect] Création de la fenêtre de test...');
    const testWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      show: true,
      title: 'Test Correction Directe - Edara ERP',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true
      }
    });

    let errorCount = 0;
    let contentLengthErrors = 0;
    let progressEventErrors = 0;
    let loadSuccess = false;
    const startTime = Date.now();

    // Étape 3: Surveiller TOUTES les erreurs
    testWindow.webContents.on('console-message', (event, level, message) => {
      if (message.includes('ERR_CONTENT_LENGTH_MISMATCH')) {
        contentLengthErrors++;
        errorCount++;
        log.error(`🚨 [TestDirect] ERREUR CONTENT-LENGTH: ${message}`);
      } else if (message.includes('ProgressEvent')) {
        progressEventErrors++;
        errorCount++;
        log.error(`🚨 [TestDirect] ERREUR PROGRESS-EVENT: ${message}`);
      } else if (message.includes('🔧 [DirectFix]')) {
        log.info(`📋 [TestDirect] Log correction: ${message}`);
      } else if (message.includes('✅ [DirectFix]')) {
        log.info(`✅ [TestDirect] Log correction: ${message}`);
      } else if (message.includes('🚨 [DirectFix]')) {
        log.error(`🚨 [TestDirect] Log correction: ${message}`);
      } else if (message.includes('odoo.define is not a function')) {
        errorCount++;
        log.error(`🚨 [TestDirect] ERREUR ODOO-DEFINE: ${message}`);
      } else if (message.includes('Missing dependencies')) {
        errorCount++;
        log.error(`🚨 [TestDirect] ERREUR DEPENDENCIES: ${message}`);
      }
    });

    testWindow.webContents.on('did-finish-load', () => {
      const loadTime = Date.now() - startTime;
      log.info(`✅ [TestDirect] PAGE CHARGÉE EN ${loadTime}ms`);
      loadSuccess = true;
    });

    testWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
      log.error(`❌ [TestDirect] ÉCHEC DE CHARGEMENT: ${errorCode} - ${errorDescription}`);
    });

    // Étape 4: Charger l'URL de test
    const testUrl = 'http://**************:8069/web';
    log.info(`🔗 [TestDirect] Chargement de: ${testUrl}`);
    
    await testWindow.loadURL(testUrl);

    // Étape 5: Attendre et analyser
    log.info('⏳ [TestDirect] Attente de 25 secondes pour analyse complète...');
    await new Promise(resolve => setTimeout(resolve, 25000));

    const totalTime = Date.now() - startTime;

    // Étape 6: Obtenir les statistiques finales
    const finalStats = directFix.getStats();

    // Étape 7: Rapport final détaillé
    log.info('📋 [TestDirect] ========================================');
    log.info('📋 [TestDirect] RAPPORT FINAL - CORRECTION DIRECTE');
    log.info('📋 [TestDirect] ========================================');
    log.info(`⏱️ [TestDirect] Temps total: ${totalTime}ms`);
    log.info(`✅ [TestDirect] Chargement réussi: ${loadSuccess ? 'OUI' : 'NON'}`);
    log.info(`❌ [TestDirect] Total erreurs: ${errorCount}`);
    log.info(`🚨 [TestDirect] Erreurs Content-Length: ${contentLengthErrors}`);
    log.info(`🚨 [TestDirect] Erreurs ProgressEvent: ${progressEventErrors}`);
    log.info(`📊 [TestDirect] Correction appliquée: ${finalStats.isApplied ? 'OUI' : 'NON'}`);
    log.info(`📊 [TestDirect] Requêtes corrigées: ${finalStats.fixedCount}`);
    log.info('📋 [TestDirect] ========================================');

    // Étape 8: Verdict final
    if (contentLengthErrors === 0 && progressEventErrors === 0 && loadSuccess) {
      log.info('🎉 [TestDirect] ✅ SUCCÈS TOTAL ! LA CORRECTION DIRECTE FONCTIONNE !');
      log.info('🎉 [TestDirect] ✅ AUCUNE ERREUR CONTENT-LENGTH !');
      log.info('🎉 [TestDirect] ✅ INTERFACE ODOO CHARGÉE CORRECTEMENT !');
      log.info(`🎉 [TestDirect] ✅ ${finalStats.fixedCount} REQUÊTES CORRIGÉES !`);
    } else if (contentLengthErrors === 0 && progressEventErrors === 0) {
      log.info('🎯 [TestDirect] ✅ SUCCÈS PARTIEL - Erreurs Content-Length éliminées');
      log.info('🎯 [TestDirect] ⚠️ Mais problème de chargement général');
      log.info(`🎯 [TestDirect] ✅ ${finalStats.fixedCount} REQUÊTES CORRIGÉES !`);
    } else {
      log.error('💥 [TestDirect] ❌ ÉCHEC - La correction directe ne fonctionne pas');
      log.error(`💥 [TestDirect] ❌ ${contentLengthErrors} erreurs Content-Length persistent`);
      log.error(`💥 [TestDirect] ❌ ${progressEventErrors} erreurs ProgressEvent persistent`);
      
      if (finalStats.fixedCount === 0) {
        log.error('💥 [TestDirect] ❌ AUCUNE REQUÊTE N\'A ÉTÉ CORRIGÉE !');
        log.error('💥 [TestDirect] ❌ Les intercepteurs ne fonctionnent pas !');
      } else {
        log.info(`💥 [TestDirect] ℹ️ ${finalStats.fixedCount} requêtes ont été corrigées mais des erreurs persistent`);
      }
    }

    log.info('📋 [TestDirect] ========================================');

    // Garder la fenêtre ouverte pour inspection
    log.info('🔍 [TestDirect] Fenêtre gardée ouverte pour inspection...');
    log.info('🔍 [TestDirect] Ouvrez les DevTools (F12) pour vérifier les détails');
    log.info('🔍 [TestDirect] Vérifiez l\'onglet Network pour voir les headers X-Edara-Direct-Fixed');
    log.info('🔍 [TestDirect] Fermez la fenêtre pour terminer le test');

  } catch (error) {
    log.error(`❌ [TestDirect] ERREUR FATALE: ${error.message}`);
    log.error(error.stack);
  }
}

// Lancer le test
app.whenReady().then(async () => {
  await testDirectFix();
});

app.on('window-all-closed', () => {
  app.quit();
});
