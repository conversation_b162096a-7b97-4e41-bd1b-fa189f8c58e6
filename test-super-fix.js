/**
 * Test de la Super-Correction Content-Length
 * Teste la correction super-agressive des erreurs ERR_CONTENT_LENGTH_MISMATCH
 */

const { app, BrowserWindow } = require('electron');
const log = require('electron-log');

// Configuration du test
const TEST_URL = 'http://192.168.100.27:8069/web';

async function testSuperContentLengthFix() {
  log.info('🧪 [SuperTest] ========================================');
  log.info('🧪 [SuperTest] DÉMARRAGE DU TEST SUPER-CORRECTION');
  log.info('🧪 [SuperTest] ========================================');

  // ÉTAPE 1: Activer la super-correction AVANT tout
  log.info('🔧 [SuperTest] ACTIVATION DE LA SUPER-CORRECTION...');
  try {
    const { performCompleteFixAndClearing } = require('./src/main/immediate-content-length-fix');
    await performCompleteFixAndClearing();
    log.info('✅ [SuperTest] SUPER-CORRECTION ACTIVÉE AVEC SUCCÈS');
  } catch (error) {
    log.error(`❌ [SuperTest] ERREUR ACTIVATION: ${error.message}`);
    return;
  }

  // ÉTAPE 2: Créer une fenêtre de test
  log.info('🪟 [SuperTest] CRÉATION DE LA FENÊTRE DE TEST...');
  const testWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    show: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: true
    }
  });

  let errorCount = 0;
  let contentLengthErrors = 0;
  let progressEventErrors = 0;
  let loadSuccess = false;
  const startTime = Date.now();

  // ÉTAPE 3: Surveiller TOUTES les erreurs
  testWindow.webContents.on('console-message', (event, level, message) => {
    if (message.includes('ERR_CONTENT_LENGTH_MISMATCH')) {
      contentLengthErrors++;
      errorCount++;
      log.error(`🚨 [SuperTest] ERREUR CONTENT-LENGTH: ${message}`);
    } else if (message.includes('ProgressEvent')) {
      progressEventErrors++;
      errorCount++;
      log.error(`🚨 [SuperTest] ERREUR PROGRESS-EVENT: ${message}`);
    } else if (message.includes('odoo.define is not a function')) {
      errorCount++;
      log.error(`🚨 [SuperTest] ERREUR ODOO-DEFINE: ${message}`);
    } else if (message.includes('Missing dependencies')) {
      errorCount++;
      log.error(`🚨 [SuperTest] ERREUR DEPENDENCIES: ${message}`);
    }
  });

  testWindow.webContents.on('did-finish-load', () => {
    const loadTime = Date.now() - startTime;
    log.info(`✅ [SuperTest] PAGE CHARGÉE EN ${loadTime}ms`);
    loadSuccess = true;
  });

  testWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    log.error(`❌ [SuperTest] ÉCHEC DE CHARGEMENT: ${errorCode} - ${errorDescription}`);
  });

  // ÉTAPE 4: Charger l'URL et attendre
  try {
    log.info(`🔗 [SuperTest] CHARGEMENT DE: ${TEST_URL}`);
    await testWindow.loadURL(TEST_URL);

    // Attendre 20 secondes pour voir tous les résultats
    log.info('⏳ [SuperTest] ATTENTE DE 20 SECONDES POUR ANALYSE COMPLÈTE...');
    await new Promise(resolve => setTimeout(resolve, 20000));

    const totalTime = Date.now() - startTime;

    // ÉTAPE 5: Rapport final détaillé
    log.info('📋 [SuperTest] ========================================');
    log.info('📋 [SuperTest] RAPPORT FINAL DU SUPER-TEST');
    log.info('📋 [SuperTest] ========================================');
    log.info(`⏱️ [SuperTest] Temps total: ${totalTime}ms`);
    log.info(`✅ [SuperTest] Chargement réussi: ${loadSuccess ? 'OUI' : 'NON'}`);
    log.info(`❌ [SuperTest] Total erreurs: ${errorCount}`);
    log.info(`🚨 [SuperTest] Erreurs Content-Length: ${contentLengthErrors}`);
    log.info(`🚨 [SuperTest] Erreurs ProgressEvent: ${progressEventErrors}`);
    log.info('📋 [SuperTest] ========================================');

    // ÉTAPE 6: Verdict final
    if (contentLengthErrors === 0 && progressEventErrors === 0 && loadSuccess) {
      log.info('🎉 [SuperTest] ✅ SUCCÈS TOTAL ! LA SUPER-CORRECTION FONCTIONNE !');
      log.info('🎉 [SuperTest] ✅ AUCUNE ERREUR CONTENT-LENGTH DÉTECTÉE !');
      log.info('🎉 [SuperTest] ✅ INTERFACE ODOO CHARGÉE CORRECTEMENT !');
    } else if (contentLengthErrors === 0 && progressEventErrors === 0) {
      log.info('🎯 [SuperTest] ✅ SUCCÈS PARTIEL - Erreurs Content-Length éliminées');
      log.info('🎯 [SuperTest] ⚠️ Mais problème de chargement général');
    } else {
      log.error('💥 [SuperTest] ❌ ÉCHEC - La super-correction ne fonctionne pas encore');
      log.error(`💥 [SuperTest] ❌ ${contentLengthErrors} erreurs Content-Length persistent`);
      log.error(`💥 [SuperTest] ❌ ${progressEventErrors} erreurs ProgressEvent persistent`);
    }

    log.info('📋 [SuperTest] ========================================');

  } catch (error) {
    log.error(`❌ [SuperTest] ERREUR FATALE: ${error.message}`);
  }

  // Garder la fenêtre ouverte pour inspection manuelle
  log.info('🔍 [SuperTest] Fenêtre gardée ouverte pour inspection manuelle...');
  log.info('🔍 [SuperTest] Ouvrez les DevTools (F12) pour vérifier les détails');
  log.info('🔍 [SuperTest] Fermez la fenêtre pour terminer le test');
}

// Lancer le test
app.whenReady().then(async () => {
  await testSuperContentLengthFix();
});

app.on('window-all-closed', () => {
  app.quit();
});
