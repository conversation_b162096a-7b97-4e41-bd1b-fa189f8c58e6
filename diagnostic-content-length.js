/**
 * Diagnostic Complet des Erreurs ERR_CONTENT_LENGTH_MISMATCH
 * Analyse pourquoi la correction ne fonctionne pas
 */

const { app, BrowserWindow, session } = require('electron');
const log = require('electron-log');

class ContentLengthDiagnostic {
  constructor() {
    this.interceptorsCalled = 0;
    this.requestsIntercepted = 0;
    this.responsesIntercepted = 0;
    this.errorsDetected = 0;
    this.diagnosticResults = [];
  }

  async runDiagnostic() {
    log.info('🔍 [Diagnostic] ========================================');
    log.info('🔍 [Diagnostic] DÉMARRAGE DU DIAGNOSTIC COMPLET');
    log.info('🔍 [Diagnostic] ========================================');

    // Étape 1: Vérifier l'état des intercepteurs
    await this.checkInterceptorStatus();

    // Étape 2: Installer des intercepteurs de diagnostic
    await this.installDiagnosticInterceptors();

    // Étape 3: Tester une requête directe
    await this.testDirectRequest();

    // Étape 4: Créer une fenêtre de test
    await this.createTestWindow();

    // Étape 5: Générer le rapport
    this.generateDiagnosticReport();
  }

  async checkInterceptorStatus() {
    log.info('🔍 [Diagnostic] Vérification de l\'état des intercepteurs...');

    try {
      // Vérifier si notre correction est chargée
      const { getImmediateFix } = require('./src/main/immediate-content-length-fix');
      const fix = getImmediateFix();
      const stats = fix.getStats();
      
      log.info(`🔍 [Diagnostic] État de la correction: ${JSON.stringify(stats)}`);
      
      if (!stats.isActive) {
        log.error('❌ [Diagnostic] LA CORRECTION N\'EST PAS ACTIVE !');
        log.info('🔧 [Diagnostic] Tentative d\'activation...');
        
        const { performCompleteFixAndClearing } = require('./src/main/immediate-content-length-fix');
        await performCompleteFixAndClearing();
        
        const newStats = fix.getStats();
        log.info(`🔍 [Diagnostic] Nouvel état: ${JSON.stringify(newStats)}`);
      }
      
    } catch (error) {
      log.error(`❌ [Diagnostic] Erreur lors de la vérification: ${error.message}`);
    }
  }

  async installDiagnosticInterceptors() {
    log.info('🔍 [Diagnostic] Installation des intercepteurs de diagnostic...');

    const defaultSession = session.defaultSession;

    // Intercepteur de diagnostic pour les requêtes
    defaultSession.webRequest.onBeforeSendHeaders([], (details, callback) => {
      this.requestsIntercepted++;
      
      if (this.isTargetUrl(details.url)) {
        log.info(`🔍 [Diagnostic] REQUÊTE INTERCEPTÉE: ${details.url}`);
        log.info(`🔍 [Diagnostic] Headers de requête:`, details.requestHeaders);
        
        this.diagnosticResults.push({
          type: 'request',
          url: details.url,
          headers: details.requestHeaders,
          timestamp: Date.now()
        });
      }
      
      callback({ cancel: false });
    });

    // Intercepteur de diagnostic pour les réponses
    defaultSession.webRequest.onHeadersReceived([], (details, callback) => {
      this.responsesIntercepted++;
      
      if (this.isTargetUrl(details.url)) {
        log.info(`🔍 [Diagnostic] RÉPONSE INTERCEPTÉE: ${details.url}`);
        log.info(`🔍 [Diagnostic] Status: ${details.statusCode}`);
        log.info(`🔍 [Diagnostic] Headers de réponse:`, details.responseHeaders);
        
        const hasContentLength = details.responseHeaders['content-length'] || details.responseHeaders['Content-Length'];
        const hasContentEncoding = details.responseHeaders['content-encoding'] || details.responseHeaders['Content-Encoding'];
        
        log.info(`🔍 [Diagnostic] Content-Length présent: ${!!hasContentLength}`);
        log.info(`🔍 [Diagnostic] Content-Encoding présent: ${!!hasContentEncoding}`);
        
        if (hasContentLength) {
          log.info(`🔍 [Diagnostic] Content-Length value: ${hasContentLength[0]}`);
        }
        if (hasContentEncoding) {
          log.info(`🔍 [Diagnostic] Content-Encoding value: ${hasContentEncoding[0]}`);
        }
        
        this.diagnosticResults.push({
          type: 'response',
          url: details.url,
          statusCode: details.statusCode,
          headers: details.responseHeaders,
          hasContentLength: !!hasContentLength,
          hasContentEncoding: !!hasContentEncoding,
          timestamp: Date.now()
        });
      }
      
      callback({ cancel: false });
    });

    // Intercepteur de diagnostic pour les erreurs
    defaultSession.webRequest.onErrorOccurred([], (details) => {
      this.errorsDetected++;
      
      if (this.isTargetUrl(details.url)) {
        log.error(`🔍 [Diagnostic] ERREUR DÉTECTÉE: ${details.url}`);
        log.error(`🔍 [Diagnostic] Type d'erreur: ${details.error}`);
        
        this.diagnosticResults.push({
          type: 'error',
          url: details.url,
          error: details.error,
          timestamp: Date.now()
        });
      }
    });

    log.info('✅ [Diagnostic] Intercepteurs de diagnostic installés');
  }

  async testDirectRequest() {
    log.info('🔍 [Diagnostic] Test de requête directe...');

    const testUrl = 'http://**************:8069/web/content/566-ba15075/web.assets_backend.js';
    
    try {
      const { net } = require('electron');
      
      const request = net.request({
        method: 'GET',
        url: testUrl,
        headers: {
          'Accept-Encoding': 'identity',
          'User-Agent': 'Edara-Diagnostic/1.0'
        }
      });

      request.on('response', (response) => {
        log.info(`🔍 [Diagnostic] Réponse directe reçue:`);
        log.info(`🔍 [Diagnostic] Status: ${response.statusCode}`);
        log.info(`🔍 [Diagnostic] Headers:`, response.headers);
        
        let data = '';
        response.on('data', (chunk) => {
          data += chunk;
        });
        
        response.on('end', () => {
          log.info(`🔍 [Diagnostic] Taille des données reçues: ${data.length} bytes`);
          
          const contentLength = response.headers['content-length'];
          if (contentLength) {
            const expectedLength = parseInt(contentLength[0]);
            const actualLength = data.length;
            
            log.info(`🔍 [Diagnostic] Content-Length attendu: ${expectedLength}`);
            log.info(`🔍 [Diagnostic] Taille réelle: ${actualLength}`);
            log.info(`🔍 [Diagnostic] Correspondance: ${expectedLength === actualLength ? 'OUI' : 'NON'}`);
            
            if (expectedLength !== actualLength) {
              log.error(`❌ [Diagnostic] MISMATCH DÉTECTÉ ! Attendu: ${expectedLength}, Reçu: ${actualLength}`);
            }
          }
        });
      });

      request.on('error', (error) => {
        log.error(`❌ [Diagnostic] Erreur requête directe: ${error.message}`);
      });

      request.end();
      
    } catch (error) {
      log.error(`❌ [Diagnostic] Erreur lors du test direct: ${error.message}`);
    }
  }

  async createTestWindow() {
    log.info('🔍 [Diagnostic] Création de la fenêtre de test...');

    const testWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      show: true,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true
      }
    });

    let consoleErrors = 0;

    testWindow.webContents.on('console-message', (event, level, message) => {
      if (message.includes('ERR_CONTENT_LENGTH_MISMATCH')) {
        consoleErrors++;
        log.error(`🔍 [Diagnostic] ERREUR CONSOLE: ${message}`);
      }
    });

    testWindow.webContents.on('did-finish-load', () => {
      log.info('🔍 [Diagnostic] Page chargée');
    });

    testWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
      log.error(`🔍 [Diagnostic] Échec de chargement: ${errorCode} - ${errorDescription}`);
    });

    try {
      await testWindow.loadURL('http://**************:8069/web');
      
      // Attendre 15 secondes pour collecter les données
      await new Promise(resolve => setTimeout(resolve, 15000));
      
      log.info(`🔍 [Diagnostic] Erreurs console détectées: ${consoleErrors}`);
      
    } catch (error) {
      log.error(`❌ [Diagnostic] Erreur lors du chargement: ${error.message}`);
    }
  }

  generateDiagnosticReport() {
    log.info('📋 [Diagnostic] ========================================');
    log.info('📋 [Diagnostic] RAPPORT DE DIAGNOSTIC COMPLET');
    log.info('📋 [Diagnostic] ========================================');
    
    log.info(`📊 [Diagnostic] Requêtes interceptées: ${this.requestsIntercepted}`);
    log.info(`📊 [Diagnostic] Réponses interceptées: ${this.responsesIntercepted}`);
    log.info(`📊 [Diagnostic] Erreurs détectées: ${this.errorsDetected}`);
    
    log.info('📋 [Diagnostic] Analyse des résultats:');
    
    const targetRequests = this.diagnosticResults.filter(r => r.type === 'request');
    const targetResponses = this.diagnosticResults.filter(r => r.type === 'response');
    const targetErrors = this.diagnosticResults.filter(r => r.type === 'error');
    
    log.info(`📊 [Diagnostic] Requêtes vers assets cibles: ${targetRequests.length}`);
    log.info(`📊 [Diagnostic] Réponses d'assets cibles: ${targetResponses.length}`);
    log.info(`📊 [Diagnostic] Erreurs d'assets cibles: ${targetErrors.length}`);
    
    // Analyser les réponses problématiques
    const problematicResponses = targetResponses.filter(r => 
      r.hasContentLength && r.hasContentEncoding
    );
    
    log.info(`📊 [Diagnostic] Réponses avec Content-Length + Encoding: ${problematicResponses.length}`);
    
    if (problematicResponses.length > 0) {
      log.error('❌ [Diagnostic] PROBLÈME DÉTECTÉ: Réponses avec Content-Length ET Content-Encoding');
      problematicResponses.forEach(r => {
        log.error(`❌ [Diagnostic] URL problématique: ${r.url}`);
      });
    }
    
    // Vérifier si les intercepteurs fonctionnent
    if (this.requestsIntercepted === 0) {
      log.error('❌ [Diagnostic] PROBLÈME MAJEUR: Aucune requête interceptée !');
      log.error('❌ [Diagnostic] Les intercepteurs ne fonctionnent pas du tout !');
    }
    
    if (targetRequests.length === 0) {
      log.error('❌ [Diagnostic] PROBLÈME: Aucune requête vers les assets cibles détectée !');
      log.error('❌ [Diagnostic] La détection des URLs ne fonctionne pas !');
    }
    
    // Recommandations
    log.info('💡 [Diagnostic] RECOMMANDATIONS:');
    
    if (this.requestsIntercepted === 0) {
      log.info('💡 [Diagnostic] 1. Vérifier que les intercepteurs sont bien installés');
      log.info('💡 [Diagnostic] 2. Redémarrer l\'application complètement');
    }
    
    if (problematicResponses.length > 0) {
      log.info('💡 [Diagnostic] 1. Le serveur Odoo envoie Content-Length avec compression');
      log.info('💡 [Diagnostic] 2. Configurer Odoo pour désactiver la compression');
      log.info('💡 [Diagnostic] 3. Ou forcer la suppression côté client');
    }
    
    if (targetErrors.length > 0) {
      log.info('💡 [Diagnostic] 1. Les erreurs persistent malgré les corrections');
      log.info('💡 [Diagnostic] 2. Problème au niveau du serveur Odoo');
    }
    
    log.info('📋 [Diagnostic] ========================================');
    
    // Sauvegarder le rapport
    const fs = require('fs');
    const reportPath = `diagnostic-report-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      stats: {
        requestsIntercepted: this.requestsIntercepted,
        responsesIntercepted: this.responsesIntercepted,
        errorsDetected: this.errorsDetected
      },
      results: this.diagnosticResults
    }, null, 2));
    
    log.info(`💾 [Diagnostic] Rapport sauvegardé: ${reportPath}`);
  }

  isTargetUrl(url) {
    return url.includes('**************:8069') && (
      url.includes('web.assets_backend.js') ||
      url.includes('web.assets_common.js') ||
      url.includes('load_menus')
    );
  }
}

// Lancer le diagnostic
app.whenReady().then(async () => {
  const diagnostic = new ContentLengthDiagnostic();
  await diagnostic.runDiagnostic();
});

app.on('window-all-closed', () => {
  app.quit();
});
