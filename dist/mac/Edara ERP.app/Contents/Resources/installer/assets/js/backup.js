/**
 * Script spécifique pour la page de configuration des sauvegardes
 */
document.addEventListener('DOMContentLoaded', function() {
  // Récupérer les éléments
  const backupPath = document.getElementById('backup-path');
  const selectFolderButton = document.getElementById('select-folder-button');

  // Définir une valeur spéciale pour indiquer d'utiliser le dialogue natif
  if (backupPath) {
    backupPath.value = "DIALOG";
    backupPath.readOnly = true;
    backupPath.placeholder = "Cliquez sur 'Parcourir' pour sélectionner un dossier";
    backupPath.style.color = "#999";
    backupPath.style.fontStyle = "italic";
  }

  // Ajouter un écouteur d'événement pour le bouton Parcourir
  if (selectFolderButton) {
    selectFolderButton.addEventListener('click', async function() {
      console.log('Bouton Parcourir cliqué');

      // Si nous sommes dans Electron
      if (window.electronAPI && window.electronAPI.selectBackupFolder) {
        try {
          console.log('Appel de electronAPI.selectBackupFolder()');
          const result = await window.electronAPI.selectBackupFolder();
          if (!result.canceled && result.filePaths.length > 0) {
            if (backupPath) {
              // Mettre à jour le champ avec le chemin sélectionné
              backupPath.value = result.filePaths[0];
              console.log('Chemin sélectionné:', result.filePaths[0]);

              // Créer immédiatement le dossier pour vérifier les permissions
              try {
                const createResult = await window.electronAPI.saveBackupPath(result.filePaths[0]);
                console.log('Résultat de la création du dossier:', createResult);

                if (createResult && createResult.success) {
                  // Mettre à jour le champ avec le chemin réel utilisé
                  backupPath.value = createResult.path;
                  console.log('Dossier créé avec succès:', createResult.path);

                  // Afficher un message de succès
                  const successMessage = document.createElement('div');
                  successMessage.className = 'success-message';
                  successMessage.textContent = 'Dossier de sauvegarde créé avec succès';
                  successMessage.style.color = 'green';
                  successMessage.style.marginTop = '5px';

                  // Ajouter le message après le champ de saisie
                  backupPath.parentNode.appendChild(successMessage);

                  // Supprimer le message après 3 secondes
                  setTimeout(() => {
                    successMessage.remove();
                  }, 3000);
                } else if (createResult && createResult.warning) {
                  // Afficher l'avertissement
                  alert(createResult.warning);

                  // Mettre à jour le champ avec le chemin de fallback
                  backupPath.value = createResult.path;
                }
              } catch (createError) {
                // Afficher les détails complets de l'erreur dans la console
                console.error('*** ERREUR DÉTAILLÉE LORS DE LA CRÉATION DU DOSSIER (Bouton Parcourir) ***');
                console.error('Message:', createError.message);
                console.error('Stack:', createError.stack);

                if (createError.errorDetails) {
                  console.error('Détails de l\'erreur:', createError.errorDetails);
                }

                console.error('***************************************');

                // Afficher un message d'erreur à l'utilisateur
                alert(`Erreur lors de la création du dossier: ${createError.message || 'Erreur inconnue'}\n\nVeuillez vérifier les permissions de votre système.`);
              }
            }
          }
        } catch (error) {
          console.error('Erreur lors de la sélection du dossier:', error);
        }
      } else {
        // Fallback pour les tests dans le navigateur
        console.log('electronAPI non disponible, simulation de sélection de dossier');
        if (backupPath) {
          backupPath.value = '/Users/<USER>/Documents/Edara Workspace/Backups';
        }
      }
    });
  }

  // S'assurer que le bouton Suivant enregistre le chemin de sauvegarde et crée le dossier
  const nextButton = document.querySelector('.btn-next');
  if (nextButton && backupPath) {
    nextButton.addEventListener('click', async function(event) {
      // Empêcher le comportement par défaut pour gérer manuellement la navigation
      event.preventDefault();

      console.log('Bouton Suivant cliqué');

      // Vérifier si le chemin est toujours "DIALOG" (l'utilisateur n'a pas cliqué sur Parcourir)
      if (backupPath.value === "DIALOG") {
        // Ouvrir automatiquement le dialogue de sélection de dossier
        if (window.electronAPI && window.electronAPI.saveBackupPath) {
          try {
            console.log('Ouverture automatique du dialogue de sélection de dossier');

            // Appeler directement saveBackupPath avec "DIALOG" pour ouvrir le dialogue
            const result = await window.electronAPI.saveBackupPath("DIALOG");
            console.log('Résultat de la sélection et création du dossier:', result);

            if (result && result.success === true) {
              console.log('Dossier de sauvegarde créé avec succès:', result.path);

              // Mettre à jour le champ avec le chemin réel
              backupPath.value = result.path;
              backupPath.style.color = "";
              backupPath.style.fontStyle = "";

              // Continuer vers l'étape suivante
              navigateToNextStep();
            } else if (result && result.error === "Sélection de dossier annulée") {
              // L'utilisateur a annulé la sélection, ne rien faire
              console.log('Sélection de dossier annulée par l\'utilisateur');
              return;
            } else {
              // Erreur lors de la création du dossier
              const errorMsg = result && result.error ? result.error : 'Erreur inconnue';
              console.error('Erreur lors de la création du dossier de sauvegarde:', errorMsg);

              // Afficher les détails de l'erreur si disponibles
              if (result && result.errorDetails) {
                console.error('Détails de l\'erreur:', result.errorDetails);
              }

              // Proposer d'utiliser un dossier temporaire
              const message = 'Erreur lors de la création du dossier de sauvegarde: ' + errorMsg +
                              '\n\nCela peut être dû à des restrictions de permissions sur macOS.' +
                              '\n\nVoulez-vous utiliser un dossier temporaire dans les données de l\'application?';

              if (confirm(message)) {
                console.log('L\'utilisateur a choisi d\'utiliser un dossier temporaire');

                // Créer un dossier temporaire dans userData
                try {
                  const tempResult = await window.electronAPI.saveBackupPath("TEMP");
                  if (tempResult && tempResult.success) {
                    backupPath.value = tempResult.path;
                    navigateToNextStep();
                  } else {
                    alert('Impossible de créer un dossier temporaire. Veuillez réessayer.');
                  }
                } catch (tempError) {
                  console.error('Erreur lors de la création du dossier temporaire:', tempError);
                  alert('Erreur lors de la création du dossier temporaire: ' + (tempError.message || 'Erreur inconnue'));
                }
              }
            }
          } catch (error) {
            console.error('Erreur lors de l\'ouverture du dialogue de sélection:', error);
            alert('Erreur lors de l\'ouverture du dialogue de sélection: ' + (error.message || 'Erreur inconnue'));
          }
        } else {
          // API non disponible, utiliser un chemin par défaut
          console.log('API saveBackupPath non disponible, navigation vers l\'étape suivante sans créer de dossier');
          navigateToNextStep();
        }
      } else {
        // L'utilisateur a déjà sélectionné un dossier, vérifier s'il est valide
        if (!backupPath.value.trim()) {
          alert('Veuillez spécifier un chemin de sauvegarde.');
          return;
        }

        // Le dossier a déjà été créé lors de la sélection, continuer directement
        navigateToNextStep();
      }

      // Fonction pour naviguer vers l'étape suivante
      function navigateToNextStep() {
        if (window.navigation && typeof window.navigation.goToNextStep === 'function') {
          console.log('Navigation vers l\'étape suivante via window.navigation.goToNextStep()');
          window.navigation.goToNextStep();
        } else {
          // Fallback si la fonction de navigation n'est pas disponible
          console.log('Navigation vers l\'étape suivante via window.location.href');
          // Obtenir la page actuelle
          const currentPath = window.location.pathname;
          const currentPageName = currentPath.split('/').pop();
          const pageSequence = [
            'welcome.html',
            'terms.html',
            'product-key.html',
            'backup.html',
            'complete.html'
          ];
          const currentPageIndex = pageSequence.indexOf(currentPageName);

          // Naviguer vers la page suivante
          const nextPageIndex = currentPageIndex + 1;
          if (nextPageIndex < pageSequence.length) {
            window.location.href = pageSequence[nextPageIndex];
          }
        }
      }
    });
  }
});
