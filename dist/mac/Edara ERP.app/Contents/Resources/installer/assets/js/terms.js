/**
 * Script spécifique pour la page des conditions d'utilisation
 */
document.addEventListener('DOMContentLoaded', function() {
  // Récupérer les éléments
  const termsContainer = document.querySelector('.terms-container');
  const termsContent = document.querySelector('.terms-content');
  const termsCheckbox = document.getElementById('accept-terms');
  const nextButton = document.querySelector('.btn-next');

  // Désactiver le bouton suivant par défaut
  if (nextButton && termsCheckbox) {
    // Vérifier l'état initial de la case à cocher
    nextButton.disabled = !termsCheckbox.checked;
    nextButton.style.opacity = termsCheckbox.checked ? '1' : '0.5';

    // Ajouter un écouteur d'événement pour la case à cocher
    termsCheckbox.addEventListener('change', function() {
      nextButton.disabled = !this.checked;
      nextButton.style.opacity = this.checked ? '1' : '0.5';
    });
  }

  // S'assurer que la barre de défilement est visible et fonctionne
  if (termsContainer && termsContent) {
    // Ajouter un peu d'espace en bas du contenu pour s'assurer que tout est visible
    termsContent.style.paddingBottom = '50px';

    // Forcer la barre de défilement à être visible et fonctionnelle
    termsContainer.style.overflowY = 'scroll';

    // Désactiver l'attribut -webkit-app-region pour le conteneur et ses enfants
    termsContainer.style.webkitAppRegion = 'no-drag';
    termsContainer.style.border = 'none'; // Suppression explicite de toute bordure
    termsContainer.style.outline = 'none'; // Suppression de l'outline qui pourrait apparaître lors du focus
    Array.from(termsContainer.querySelectorAll('*')).forEach(el => {
      el.style.webkitAppRegion = 'no-drag';
      el.style.border = 'none'; // Suppression des bordures pour tous les éléments enfants
    });

    // Ajouter un gestionnaire d'événements pour la molette de la souris
    termsContainer.addEventListener('wheel', function(event) {
      // Empêcher le comportement par défaut
      event.preventDefault();

      // Calculer le défilement
      const delta = event.deltaY || event.detail || event.wheelDelta;

      // Appliquer le défilement manuellement
      termsContainer.scrollTop += delta > 0 ? 30 : -30;

      console.log('Défilement de la molette détecté, scrollTop:', termsContainer.scrollTop);
    }, { passive: false });

    // Ajouter un gestionnaire pour les touches fléchées
    termsContainer.addEventListener('keydown', function(event) {
      if (event.key === 'ArrowDown') {
        event.preventDefault();
        termsContainer.scrollTop += 30;
      } else if (event.key === 'ArrowUp') {
        event.preventDefault();
        termsContainer.scrollTop -= 30;
      } else if (event.key === 'PageDown') {
        event.preventDefault();
        termsContainer.scrollTop += termsContainer.clientHeight;
      } else if (event.key === 'PageUp') {
        event.preventDefault();
        termsContainer.scrollTop -= termsContainer.clientHeight;
      }
    });

    // Ajouter un gestionnaire pour le clic sur la barre de défilement
    termsContainer.addEventListener('click', function(event) {
      // Vérifier si le clic est sur la barre de défilement
      const rect = termsContainer.getBoundingClientRect();
      const isScrollbarClick = (event.clientX > rect.right - 15);

      if (isScrollbarClick) {
        // Calculer la position relative du clic
        const clickRatio = (event.clientY - rect.top) / rect.height;
        // Appliquer le défilement
        termsContainer.scrollTop = clickRatio * (termsContent.offsetHeight - rect.height);
      }
    });

    // Donner le focus au conteneur pour permettre l'utilisation des touches
    setTimeout(() => {
      termsContainer.focus();
    }, 500);

    // Ajouter un message visuel pour indiquer que le défilement est possible
    const scrollHint = document.createElement('div');
    scrollHint.textContent = 'Faites défiler pour lire tout le texte';
    scrollHint.style.position = 'absolute';
    scrollHint.style.bottom = '5px';
    scrollHint.style.right = '15px';
    scrollHint.style.fontSize = '12px';
    scrollHint.style.color = 'rgba(255, 255, 255, 0.5)';
    scrollHint.style.pointerEvents = 'none';
    scrollHint.style.zIndex = '10';
    termsContainer.appendChild(scrollHint);

    // Faire disparaître le message après quelques secondes
    setTimeout(() => {
      scrollHint.style.opacity = '0';
      scrollHint.style.transition = 'opacity 1s ease';
    }, 5000);
  }
});
