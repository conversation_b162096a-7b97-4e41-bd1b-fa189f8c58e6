/* Specific styles for page 03 - Product Key */

/* Description text */
.right-panel .description {
    font-family: 'Proxima Nova', sans-serif;
    font-weight: 400;
    font-size: clamp(14px, 1.5vw, 16px);
    line-height: 1.6;
    opacity: 0.9;
    margin-bottom: 30px;
    max-width: 100%;
}

/* Workspace name input area */
.workspace-name-container {
    margin-top: 30px;
    width: 35%;
    max-width: 350px;
}

.workspace-name-input {
    width: 100%;
    height: 40px;
    background-color: var(--color-input-bg);
    border: 1px solid var(--color-input-border);
    border-radius: 6px;
    color: var(--color-input-text);
    font-family: 'Proxima Nova', sans-serif;
    font-size: 14px;
    padding: 0 10px;
    transition: all 0.3s ease;
}

.workspace-name-input:focus {
    outline: none;
    border-color: var(--color-button-primary);
    background-color: var(--color-input-focus);
}

/* Product key input area */
.product-key-container {
    margin-top: 25px;
    display: flex;
    align-items: center;
    gap: 15px;
    width: 80%;
    max-width: 600px;
    margin-bottom: 100px; /* Space for the faded logo */
}

.key-input-group {
    display: flex;
    gap: 10px;
    flex-grow: 1;
    width: 100%;
}

.key-input {
    flex: 1;
    height: 40px;
    min-width: 80px;
    background-color: var(--color-input-bg);
    border: 1px solid var(--color-input-border);
    border-radius: 6px;
    color: var(--color-input-text);
    font-family: 'Proxima Nova', sans-serif;
    font-size: 16px;
    text-align: center;
    letter-spacing: 2px;
    padding: 0 5px;
    transition: all 0.3s ease;
}

.key-input:focus {
    outline: none;
    border-color: var(--color-button-primary);
    background-color: var(--color-input-focus);
}

.key-input.error {
    border-color: #ff4d4d;
    background-color: rgba(255, 77, 77, 0.1);
}

.key-input.valid {
    border-color: #4dff4d;
    background-color: rgba(77, 255, 77, 0.1);
}

.btn-paste {
    width: 50px;
    height: 40px;
    background-color: var(--color-button-secondary);
    border: 1px solid var(--color-input-border);
    border-radius: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin-left: 5px;
    transition: background-color 0.2s ease;
}

.btn-paste:hover {
    opacity: 0.9;
}

.paste-icon {
    width: 24px;
    height: 24px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.paste-icon-inner {
    width: 16px;
    height: 18px;
    border: 2px solid #ffffff;
    border-radius: 2px;
    position: relative;
}

.paste-icon-inner:before {
    content: "";
    position: absolute;
    top: -6px;
    left: 2px;
    width: 10px;
    height: 4px;
    border: 2px solid #ffffff;
    border-bottom: none;
    border-radius: 2px 2px 0 0;
}

/* Error message */
.error-message {
    color: #ff4d4d;
    font-size: 14px;
    margin-top: 10px;
    display: none;
}

.error-message.visible {
    display: block;
}

/* Left panel illustration */
.left-illustration {
    max-width: 85% !important;
    position: relative;
    left: 10%;
    top: 0;
}
