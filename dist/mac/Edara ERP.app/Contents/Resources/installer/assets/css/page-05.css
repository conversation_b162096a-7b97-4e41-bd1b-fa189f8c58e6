/* Specific styles for page 05 - Installation Complete */

/* Description text */
.right-panel .description {
    font-family: 'Proxima Nova', sans-serif;
    font-weight: 400;
    font-size: clamp(14px, 1.5vw, 16px);
    line-height: 1.6;
    opacity: 0.9;
    margin-bottom: 30px;
    max-width: 100%;
}

/* Checkbox container */
.checkbox-container {
    margin-top: 40px;
    margin-bottom: 100px; /* Space for the faded logo */
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
}

.checkbox-input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkbox-custom {
    position: relative;
    display: inline-block;
    height: 18px;
    width: 18px;
    background-color: var(--color-input-bg);
    border: 1px solid var(--color-input-border);
    border-radius: 4px;
    margin-right: 10px;
    transition: all 0.2s ease;
}

.checkbox-label:hover .checkbox-input ~ .checkbox-custom {
    background-color: var(--color-input-focus);
    border-color: var(--color-button-primary);
}

.checkbox-label .checkbox-input:checked ~ .checkbox-custom {
    background-color: var(--color-button-primary);
}

.checkbox-label .checkbox-input:checked ~ .checkbox-custom:after {
    content: "";
    position: absolute;
    display: block;
    left: 6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-text {
    font-family: 'Proxima Nova', sans-serif;
    font-size: 14px;
    color: var(--color-text);
}

/* Left panel illustration */
.left-illustration {
    max-width: 85% !important;
    position: relative;
    left: 0%;
    top: 0;
}
