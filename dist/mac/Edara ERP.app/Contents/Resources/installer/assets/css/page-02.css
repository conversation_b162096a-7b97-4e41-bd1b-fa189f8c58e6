/* Specific styles for page 02 */

.terms-container {
  max-height: 250px;
  overflow-y: scroll !important; /* Force le défilement */
  margin-bottom: 20px;
  border: none; /* Suppression de la bordure */
  border-radius: 6px; /* Coins légèrement arrondis comme les champs de formulaire */
  padding-right: 10px;
  background-color: var(--color-input-bg);
  -webkit-app-region: no-drag; /* Important: empêche la fenêtre d'être déplacée lors du défilement */
  position: relative; /* Pour le positionnement correct */
}

/* Styles pour la barre de défilement */
.terms-container::-webkit-scrollbar {
  width: 10px; /* Barre plus large pour faciliter le clic */
}

.terms-container::-webkit-scrollbar-track {
  background: var(--color-input-bg);
  border-radius: 4px;
}

.terms-container::-webkit-scrollbar-thumb {
  background: rgba(92, 93, 100, 0.8);
  border-radius: 4px;
  border: 1px solid var(--color-divider); /* Bordure pour meilleure visibilité */
}

.terms-container::-webkit-scrollbar-thumb:hover {
  background: var(--color-button-secondary);
}

.terms-content {
  padding: 15px;
  min-height: 400px; /* Assure que le contenu est suffisamment long pour nécessiter un défilement */
}

.terms-content p {
  margin-bottom: 20px;
  font-size: 14px;
  line-height: 1.6;
  opacity: 0.9;
  color: var(--color-text);
}

.terms-acceptance {
  margin-bottom: 100px;
}

.checkbox-container {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  user-select: none;
  margin-top: 15px;
  -webkit-app-region: no-drag;
}

.checkbox-container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
  z-index: 1;
}

.checkmark {
  position: relative;
  display: inline-block;
  height: 20px;
  width: 20px;
  background-color: var(--color-input-bg);
  border: 1px solid var(--color-input-border);
  border-radius: 4px;
  margin-right: 10px;
  transition: all 0.2s ease;
}

.checkbox-container:hover input ~ .checkmark {
  background-color: var(--color-input-focus);
  border-color: var(--color-button-primary);
}

.checkbox-container input:checked ~ .checkmark {
  background-color: var(--color-button-primary);
}

.checkbox-container input:checked ~ .checkmark:after {
  content: "";
  position: absolute;
  display: block;
  left: 7px;
  top: 3px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
