/* Base styles for Edara Workspace */

/* Import Fonts if using @font-face - Example structure */
/*
@font-face {
  font-family: 'Open Sans';
  src: url('../fonts/OpenSans-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
}
@font-face {
  font-family: 'Proxima Nova';
  src: url('../fonts/ProximaNova-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
}
*/

:root {
  /* Color variables - Dark theme (default) - Alignées avec la fenêtre de connexion */
  --color-dark-bg: #181818;
  --color-darker-bg: #1F1E1E;
  --color-button-primary: #0178D5;
  --color-button-secondary: #3a3b40;
  --color-text: #F8F9FA;
  --color-secondary-text: #DEE2E6;
  --color-divider: rgba(255, 255, 255, 0.1);
  --color-input-bg: rgba(255, 255, 255, 0.05);
  --color-input-border: rgba(255, 255, 255, 0.3);
  --color-input-focus: #121111;
  --color-input-text: #F8F9FA;
  --color-button-hover: #0064B5;
  --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Support for light theme */
@media (prefers-color-scheme: light) {
  :root {
    --color-dark-bg: #F5F5F5;
    --color-darker-bg: #FFFFFF;
    --color-button-primary: #0178D5;
    --color-button-secondary: #6C757D;
    --color-text: #212529;
    --color-secondary-text: #6C757D;
    --color-divider: rgba(0, 0, 0, 0.1);
    --color-input-bg: #F8F9FA;
    --color-input-border: rgba(0, 0, 0, 0.2);
    --color-input-focus: #E9ECEF;
    --color-input-text: #212529;
    --color-button-hover: #0064B5;
    --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Proxima Nova', sans-serif; /* Default font */
  background-color: var(--color-dark-bg); /* Use theme variable */
  color: var(--color-text);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh; /* Ensure container takes full height */
  width: 100%;
  height: 100vh;
  overflow: hidden;
  -webkit-app-region: drag; /* Permet de déplacer la fenêtre en cliquant n'importe où */
}

/* Les éléments interactifs ne doivent pas être draggable */
button, input, select, a, .btn {
  -webkit-app-region: no-drag;
}

/* Style pour la zone de titre (pour macOS) */
.titlebar {
  height: 38px;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
  -webkit-app-region: drag;
}

.container {
  display: flex;
  width: 100%; /* Prend toute la largeur */
  height: 100vh; /* Prend toute la hauteur */
  background-color: var(--color-darker-bg);
  overflow: hidden; /* Important for absolute positioning */
  position: relative; /* Pour le positionnement absolu de la ligne */
  border-radius: 0; /* Pas de coins arrondis */
}

.left-panel {
  flex-basis: 40%; /* Adjust ratio */
  background-color: var(--color-darker-bg);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  box-sizing: border-box;
}

.left-panel img {
  max-width: 80%; /* Adjust image size relative to panel */
  height: auto;
  display: block;
}

.right-panel {
  flex-basis: 60%; /* Adjust ratio */
  background-color: var(--color-dark-bg);
  color: var(--color-text);
  padding: 5% 7%; /* Use percentage padding for responsiveness */
  box-sizing: border-box;
  position: relative; /* Needed for absolute positioning of faded logo */
  display: flex;
  flex-direction: column;
}

.right-panel h1 {
  font-family: 'Open Sans', sans-serif;
  font-weight: 700;
  font-size: clamp(26px, 3.5vw, 40px); /* Adjusted clamp */
  line-height: 1.3;
  margin: 0 0 25px 0;
}

/* Footer area and buttons */
.footer-area {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20px 7%;
  border-top: 1px solid var(--color-divider);
}

.button-group {
  display: flex;
  justify-content: flex-end; /* Align buttons to the right */
  gap: 15px;
}

.right-buttons {
  display: flex;
  gap: 15px;
}

.btn {
  color: #ffffff; /* Toujours blanc pour les boutons pour un bon contraste */
  border: none;
  padding: 10px 25px;
  border-radius: 4px;
  font-family: 'Proxima Nova', sans-serif;
  font-size: 15px;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
  font-weight: 500;
  letter-spacing: 0.2px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.btn-next {
  background-color: var(--color-button-primary);
}

.btn-back {
  background-color: var(--color-button-primary);
}

.btn-cancel {
  background-color: var(--color-button-secondary);
}

.btn:hover {
  background-color: var(--color-button-hover);
}

.btn:active {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.faded-logo {
  position: absolute;
  bottom: 70px;
  right: 19px;
  width: 120px;
  height: 110px;
  opacity: 0.17;
  pointer-events: none;
}
