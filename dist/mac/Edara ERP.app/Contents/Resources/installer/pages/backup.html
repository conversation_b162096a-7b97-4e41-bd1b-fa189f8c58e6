<!DOCTYPE html>
<html lang="fr-FR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuration des sauvegardes - Edara Workspace</title>
    <link rel="stylesheet" href="../assets/css/base.css">
    <link rel="stylesheet" href="../assets/css/page-04.css">
    <!-- Add links to fonts if using external provider like Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@700&family=Proxima+Nova:wght@400&display=swap" rel="stylesheet">
    <!-- Note: Proxima Nova might not be free on Google Fonts, you might need an alternative or use @font-face if you have the files -->
</head>
<body>
    <!-- <PERSON><PERSON> de titre pour macOS -->
    <div class="titlebar"></div>

    <div class="container">
        <div class="left-panel">
            <!-- Assurez-vous que le chemin est correct -->
            <img src="../assets/images/illustrations/illustration004.png" alt="Illustration Configuration des sauvegardes" class="left-illustration">
        </div>
        <div class="right-panel">

            <h1>Configuration des sauvegardes</h1>

            <p class="description">
                Choisissez l'emplacement où seront enregistrées les sauvegardes de l'application Edara Workspace.
            </p>

            <div class="backup-location-container">
                <div class="location-input-group">
                    <input type="text" id="backup-path" class="location-input" value="/Documents/Edara Workspace/Backups" placeholder="Chemin de sauvegarde">
                </div>
                <button class="btn-browse" id="select-folder-button">
                    Parcourir
                </button>
            </div>

            <!-- Le logo estompé -->
            <img src="../assets/images/logo-faded.png" alt="Faded Logo" class="faded-logo">

            <div class="footer-area">
                <div class="button-group">
                    <button class="btn btn-back">Retour</button>
                    <div class="right-buttons">
                        <button class="btn btn-next">Suivant</button>
                        <button class="btn btn-cancel">Annuler</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="../assets/js/navigation.js"></script>
    <script src="../assets/js/backup.js"></script>
</body>
</html>
