<!DOCTYPE html>
<html lang="fr-FR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Installation terminée - Edara Workspace</title>
    <link rel="stylesheet" href="../assets/css/base.css">
    <link rel="stylesheet" href="../assets/css/page-05.css">
    <!-- Add links to fonts if using external provider like Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@700&family=Proxima+Nova:wght@400&display=swap" rel="stylesheet">
    <!-- Note: Proxima Nova might not be free on Google Fonts, you might need an alternative or use @font-face if you have the files -->
</head>
<body>
    <!-- Barre de titre pour macOS -->
    <div class="titlebar"></div>

    <div class="container">
        <div class="left-panel">
            <!-- Assurez-vous que le chemin est correct -->
            <img src="../assets/images/illustrations/illustration005.png" alt="Illustration Installation terminée" class="left-illustration">
        </div>
        <div class="right-panel">

            <h1>Installation terminée</h1>

            <p class="description">
                L'installation d'Edara Workspace est terminée.<br>
                Vous pouvez maintenant utiliser l'application depuis votre bureau.
            </p>

            <div class="checkbox-container">
                <label class="checkbox-label">
                    <input type="checkbox" class="checkbox-input" checked>
                    <span class="checkbox-custom"></span>
                    <span class="checkbox-text">Créer un raccourci sur le bureau</span>
                </label>
            </div>

            <!-- Le logo estompé -->
            <img src="../assets/images/logo-faded.png" alt="Faded Logo" class="faded-logo">

            <div class="footer-area">
                <div class="button-group">
                    <button class="btn btn-back">Retour</button>
                    <div class="right-buttons">
                        <button class="btn btn-next">Terminer</button>
                        <button class="btn btn-cancel">Annuler</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="../assets/js/navigation.js"></script>
</body>
</html>
