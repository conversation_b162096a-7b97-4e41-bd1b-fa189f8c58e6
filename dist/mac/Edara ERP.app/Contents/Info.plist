<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0"><dict><key>CFBundleDisplayName</key><string>Edara ERP</string><key>CFBundleExecutable</key><string>Edara ERP</string><key>CFBundleIconFile</key><string>icon.icns</string><key>CFBundleIdentifier</key><string>com.edara.erp</string><key>CFBundleInfoDictionaryVersion</key><string>6.0</string><key>CFBundleName</key><string>Edara ERP</string><key>CFBundlePackageType</key><string>APPL</string><key>CFBundleShortVersionString</key><string>1.0.0</string><key>CFBundleVersion</key><string>1.0.0</string><key>DTCompiler</key><string>com.apple.compilers.llvm.clang.1_0</string><key>DTSDKBuild</key><string>12.3</string><key>DTSDKName</key><string>macosx12.3</string><key>DTXcode</key><string>1331</string><key>DTXcodeBuild</key><string>13E500a</string><key>ElectronAsarIntegrity</key><dict><key>Resources/app.asar</key><dict><key>algorithm</key><string>SHA256</string><key>hash</key><string>062e8193ae5b52c65670f7eeb891edbd2047044a9d73f577e5a31441e1d6c3fc</string></dict></dict><key>LSApplicationCategoryType</key><string>public.app-category.business</string><key>LSEnvironment</key><dict><key>MallocNanoZone</key><string>0</string></dict><key>LSMinimumSystemVersion</key><string>10.11.0</string><key>NSAppTransportSecurity</key><dict><key>NSAllowsArbitraryLoads</key><true/><key>NSAllowsLocalNetworking</key><true/><key>NSExceptionDomains</key><dict><key>127.0.0.1</key><dict><key>NSIncludesSubdomains</key><false/><key>NSTemporaryExceptionAllowsInsecureHTTPLoads</key><true/><key>NSTemporaryExceptionAllowsInsecureHTTPSLoads</key><false/><key>NSTemporaryExceptionMinimumTLSVersion</key><string>1.0</string><key>NSTemporaryExceptionRequiresForwardSecrecy</key><false/></dict><key>localhost</key><dict><key>NSIncludesSubdomains</key><false/><key>NSTemporaryExceptionAllowsInsecureHTTPLoads</key><true/><key>NSTemporaryExceptionAllowsInsecureHTTPSLoads</key><false/><key>NSTemporaryExceptionMinimumTLSVersion</key><string>1.0</string><key>NSTemporaryExceptionRequiresForwardSecrecy</key><false/></dict></dict></dict><key>NSAppleEventsUsageDescription</key><string>Cette application nécessite l&#39;accès aux événements Apple pour fonctionner correctement.</string><key>NSBluetoothAlwaysUsageDescription</key><string>This app needs access to Bluetooth</string><key>NSBluetoothPeripheralUsageDescription</key><string>This app needs access to Bluetooth</string><key>NSBookmarkUsageDescription</key><string>Cette application nécessite de créer des bookmarks pour accéder aux dossiers de sauvegarde.</string><key>NSCameraUsageDescription</key><string>This app needs access to the camera</string><key>NSDesktopFolderUsageDescription</key><string>Cette application nécessite l&#39;accès au dossier Bureau pour les sauvegardes.</string><key>NSDocumentsFolderUsageDescription</key><string>Cette application nécessite l&#39;accès au dossier Documents pour les sauvegardes.</string><key>NSDownloadsFolderUsageDescription</key><string>Cette application nécessite l&#39;accès au dossier Téléchargements pour les sauvegardes.</string><key>NSHighResolutionCapable</key><true/><key>NSHumanReadableCopyright</key><string>Copyright © 2025 Oussama</string><key>NSMainNibFile</key><string>MainMenu</string><key>NSMicrophoneUsageDescription</key><string>This app needs access to the microphone</string><key>NSPrincipalClass</key><string>AtomApplication</string><key>NSQuitAlwaysKeepsWindows</key><false/><key>NSRequiresAquaSystemAppearance</key><false/><key>NSSupportsAutomaticGraphicsSwitching</key><true/></dict></plist>