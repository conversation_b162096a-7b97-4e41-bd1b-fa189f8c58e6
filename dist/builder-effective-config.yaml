directories:
  output: dist
  buildResources: build
appId: com.edara.erp
productName: Edara ERP
mac:
  category: public.app-category.business
  target:
    - dmg
    - zip
  icon: build/icon.icns
  darkModeSupport: true
  hardenedRuntime: true
  gatekeeperAssess: false
  entitlements: build/entitlements.mac.plist
  entitlementsInherit: build/entitlements.mac.plist
  minimumSystemVersion: 10.11.0
  extendInfo:
    NSAppleEventsUsageDescription: Cette application nécessite l'accès aux événements Apple pour fonctionner correctement.
    NSDesktopFolderUsageDescription: Cette application nécessite l'accès au dossier Bureau pour les sauvegardes.
    NSDocumentsFolderUsageDescription: Cette application nécessite l'accès au dossier Documents pour les sauvegardes.
    NSDownloadsFolderUsageDescription: Cette application nécessite l'accès au dossier Téléchargements pour les sauvegardes.
    NSBookmarkUsageDescription: Cette application nécessite de créer des bookmarks pour accéder aux dossiers de sauvegarde.
dmg:
  background: build/background.png
  icon: build/icon.icns
  iconSize: 100
  contents:
    - x: 130
      'y': 220
    - x: 410
      'y': 220
      type: link
      path: /Applications
  window:
    width: 540
    height: 380
win:
  target: nsis
extraResources:
  - from: installer
    to: installer
files: []
electronVersion: 18.3.15
