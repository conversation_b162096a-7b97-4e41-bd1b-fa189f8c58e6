/**
 * Test Immédiat de la Correction Content-Length
 * Lance un test rapide pour vérifier que la correction fonctionne
 */

const { app, BrowserWindow } = require('electron');
const log = require('electron-log');

// Configuration du test
const TEST_URL = 'http://192.168.100.27:8069/web';

async function testContentLengthFix() {
  log.info('🧪 [Test] Démarrage du test de correction Content-Length...');

  // Créer une fenêtre de test
  const testWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    show: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: true
    }
  });

  let errorCount = 0;
  let loadSuccess = false;
  const startTime = Date.now();

  // Surveiller les erreurs
  testWindow.webContents.on('console-message', (event, level, message) => {
    if (message.includes('ERR_CONTENT_LENGTH_MISMATCH')) {
      errorCount++;
      log.error(`❌ [Test] Erreur Content-Length: ${message}`);
    }
  });

  testWindow.webContents.on('did-finish-load', () => {
    const loadTime = Date.now() - startTime;
    log.info(`✅ [Test] Page chargée en ${loadTime}ms`);
    loadSuccess = true;
  });

  testWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    log.error(`❌ [Test] Échec de chargement: ${errorCode} - ${errorDescription}`);
  });

  try {
    // Charger l'URL de test
    log.info(`🔗 [Test] Chargement de: ${TEST_URL}`);
    await testWindow.loadURL(TEST_URL);

    // Attendre 15 secondes pour voir les résultats
    await new Promise(resolve => setTimeout(resolve, 15000));

    const totalTime = Date.now() - startTime;

    // Rapport final
    log.info('📋 [Test] ========== RÉSULTAT DU TEST ==========');
    log.info(`⏱️ Temps total: ${totalTime}ms`);
    log.info(`✅ Chargement réussi: ${loadSuccess ? 'OUI' : 'NON'}`);
    log.info(`❌ Erreurs Content-Length: ${errorCount}`);
    log.info(`🎯 RÉSULTAT: ${errorCount === 0 && loadSuccess ? '✅ SUCCÈS' : '❌ ÉCHEC'}`);
    log.info('============================================');

    if (errorCount === 0 && loadSuccess) {
      log.info('🎉 [Test] LA CORRECTION FONCTIONNE !');
    } else {
      log.error('💥 [Test] La correction ne fonctionne pas encore...');
    }

  } catch (error) {
    log.error(`❌ [Test] Erreur: ${error.message}`);
  }

  // Garder la fenêtre ouverte pour inspection
  log.info('🔍 [Test] Fenêtre gardée ouverte pour inspection...');
}

// Lancer le test
app.whenReady().then(async () => {
  // Importer et activer la correction
  try {
    const { performCompleteFixAndClearing } = require('./src/main/immediate-content-length-fix');
    log.info('🔧 [Test] Activation de la correction...');
    await performCompleteFixAndClearing();
    log.info('✅ [Test] Correction activée');
  } catch (error) {
    log.error(`❌ [Test] Erreur activation: ${error.message}`);
  }

  // Lancer le test
  await testContentLengthFix();
});

app.on('window-all-closed', () => {
  app.quit();
});
