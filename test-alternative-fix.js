/**
 * Test de la Solution Alternative pour ERR_CONTENT_LENGTH_MISMATCH
 * Teste l'approche avec session personnalisée
 */

const { app } = require('electron');
const log = require('electron-log');

async function testAlternativeFix() {
  log.info('🧪 [TestAlternative] ========================================');
  log.info('🧪 [TestAlternative] TEST DE LA SOLUTION ALTERNATIVE');
  log.info('🧪 [TestAlternative] ========================================');

  try {
    // Étape 1: Activer la solution alternative
    log.info('🔧 [TestAlternative] Activation de la solution alternative...');
    const { createAlternativeWindow } = require('./src/main/alternative-content-length-fix');
    
    // Étape 2: Créer une fenêtre avec la solution alternative
    log.info('🪟 [TestAlternative] Création de fenêtre avec session personnalisée...');
    const testWindow = await createAlternativeWindow({
      width: 1200,
      height: 800,
      show: true,
      title: 'Test Solution Alternative - Edara ERP'
    });

    let errorCount = 0;
    let contentLengthErrors = 0;
    let loadSuccess = false;
    const startTime = Date.now();

    // Étape 3: Surveiller les erreurs
    testWindow.webContents.on('console-message', (event, level, message) => {
      if (message.includes('ERR_CONTENT_LENGTH_MISMATCH')) {
        contentLengthErrors++;
        errorCount++;
        log.error(`🚨 [TestAlternative] ERREUR CONTENT-LENGTH: ${message}`);
      } else if (message.includes('ProgressEvent')) {
        errorCount++;
        log.error(`🚨 [TestAlternative] ERREUR PROGRESS-EVENT: ${message}`);
      } else if (message.includes('🔧 [Alternative]')) {
        log.info(`📋 [TestAlternative] Script diagnostic: ${message}`);
      } else if (message.includes('✅ [Alternative]')) {
        log.info(`✅ [TestAlternative] Script diagnostic: ${message}`);
      } else if (message.includes('🚨 [Alternative]')) {
        log.error(`🚨 [TestAlternative] Script diagnostic: ${message}`);
      }
    });

    testWindow.webContents.on('did-finish-load', () => {
      const loadTime = Date.now() - startTime;
      log.info(`✅ [TestAlternative] PAGE CHARGÉE EN ${loadTime}ms`);
      loadSuccess = true;
    });

    testWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
      log.error(`❌ [TestAlternative] ÉCHEC DE CHARGEMENT: ${errorCode} - ${errorDescription}`);
    });

    // Étape 4: Charger l'URL de test
    const testUrl = 'http://**************:8069/web';
    log.info(`🔗 [TestAlternative] Chargement de: ${testUrl}`);
    
    await testWindow.loadURL(testUrl);

    // Étape 5: Attendre et analyser
    log.info('⏳ [TestAlternative] Attente de 20 secondes pour analyse...');
    await new Promise(resolve => setTimeout(resolve, 20000));

    const totalTime = Date.now() - startTime;

    // Étape 6: Rapport final
    log.info('📋 [TestAlternative] ========================================');
    log.info('📋 [TestAlternative] RAPPORT FINAL - SOLUTION ALTERNATIVE');
    log.info('📋 [TestAlternative] ========================================');
    log.info(`⏱️ [TestAlternative] Temps total: ${totalTime}ms`);
    log.info(`✅ [TestAlternative] Chargement réussi: ${loadSuccess ? 'OUI' : 'NON'}`);
    log.info(`❌ [TestAlternative] Total erreurs: ${errorCount}`);
    log.info(`🚨 [TestAlternative] Erreurs Content-Length: ${contentLengthErrors}`);
    log.info('📋 [TestAlternative] ========================================');

    // Étape 7: Verdict
    if (contentLengthErrors === 0 && loadSuccess) {
      log.info('🎉 [TestAlternative] ✅ SUCCÈS ! LA SOLUTION ALTERNATIVE FONCTIONNE !');
      log.info('🎉 [TestAlternative] ✅ AUCUNE ERREUR CONTENT-LENGTH !');
      log.info('🎉 [TestAlternative] ✅ INTERFACE ODOO CHARGÉE !');
    } else if (contentLengthErrors === 0) {
      log.info('🎯 [TestAlternative] ✅ SUCCÈS PARTIEL - Erreurs Content-Length éliminées');
      log.info('🎯 [TestAlternative] ⚠️ Mais problème de chargement général');
    } else {
      log.error('💥 [TestAlternative] ❌ ÉCHEC - La solution alternative ne fonctionne pas');
      log.error(`💥 [TestAlternative] ❌ ${contentLengthErrors} erreurs Content-Length persistent`);
    }

    // Étape 8: Obtenir les statistiques de la solution
    const { getAlternativeFix } = require('./src/main/alternative-content-length-fix');
    const fix = getAlternativeFix();
    const stats = fix.getStats();
    
    log.info('📊 [TestAlternative] Statistiques de la solution:');
    log.info(`📊 [TestAlternative] Active: ${stats.isActive}`);
    log.info(`📊 [TestAlternative] Session personnalisée: ${stats.hasCustomSession}`);
    log.info(`📊 [TestAlternative] Partition: ${stats.sessionPartition}`);

    log.info('📋 [TestAlternative] ========================================');

    // Garder la fenêtre ouverte pour inspection
    log.info('🔍 [TestAlternative] Fenêtre gardée ouverte pour inspection...');
    log.info('🔍 [TestAlternative] Ouvrez les DevTools (F12) pour vérifier');
    log.info('🔍 [TestAlternative] Fermez la fenêtre pour terminer');

  } catch (error) {
    log.error(`❌ [TestAlternative] ERREUR FATALE: ${error.message}`);
    log.error(error.stack);
  }
}

// Lancer le test
app.whenReady().then(async () => {
  await testAlternativeFix();
});

app.on('window-all-closed', () => {
  app.quit();
});
