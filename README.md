# Edara ERP

Application de gestion d'entreprise Edara ERP, basée sur Electron et Odoo.

## Structure du projet

```
edara-erp-app/
├── build/                  # Ressources pour le build (icônes, etc.)
├── dist/                   # Dossier de sortie pour les builds
├── installer/              # Interface d'installation
│   ├── assets/             # Ressources pour l'interface d'installation
│   │   ├── css/            # Styles CSS
│   │   ├── js/             # Scripts JavaScript
│   │   └── images/         # Images et illustrations
│   ├── pages/              # Pages HTML de l'assistant d'installation
│   └── index.html          # Page d'accueil de l'installation
├── logs/                   # Logs de l'application
├── scripts/                # Scripts utilitaires
│   ├── convert-icon.sh     # Script pour convertir les icônes
│   ├── copy-files.sh       # Script pour copier les fichiers
│   ├── deploy-macos.sh     # Script de déploiement pour macOS
│   ├── quick-test.sh       # Script pour tester rapidement l'application
│   └── reset-preferences.sh # Script pour réinitialiser les préférences
├── src/                    # Code source de l'application
│   ├── assets/             # Ressources de l'application
│   ├── main/               # Code du processus principal
│   │   ├── main.js         # Point d'entrée du processus principal
│   │   ├── odoo-auth.js    # Module d'authentification Odoo
│   │   ├── preload.js      # Script de préchargement
│   │   ├── shortcut-manager.js # Gestionnaire de raccourcis
│   │   └── user-preferences.js # Gestionnaire de préférences utilisateur
│   └── renderer/           # Code du processus de rendu
├── package.json            # Configuration du projet
└── README.md               # Documentation du projet
```

## Installation

```bash
# Installer les dépendances
npm install
```

## Développement

```bash
# Démarrer l'application en mode développement
npm start
```

## Build

```bash
# Construire l'application pour macOS (rapide, pour les tests)
npm run build:mac-quick

# Construire l'application pour macOS (complet, avec DMG)
npm run build:mac

# Construire l'application pour Windows
npm run build:win

# Construire l'application pour macOS et Windows
npm run build
```

## Scripts utilitaires

```bash
# Tester rapidement l'application
npm run test

# Réinitialiser les préférences de l'application
npm run reset

# Déployer l'application pour macOS
npm run deploy:mac
```

## Fonctionnalités

- Interface d'installation personnalisée
- Authentification Odoo
- Création de raccourci sur le bureau
- Configuration des sauvegardes
- Interface utilisateur moderne et intuitive

## Licence

MIT
