/**
 * Tests unitaires pour le gestionnaire de session unifié
 */

const { describe, it, beforeEach, afterEach, expect, jest } = require('@jest/globals');
const UnifiedSessionManager = require('../src/main/unified-session-manager');
const SessionRecoveryManager = require('../src/main/session-recovery-manager');
const SessionEventHandler = require('../src/main/session-events');

// Mocks
jest.mock('electron', () => ({
  session: {
    fromPartition: jest.fn(() => ({
      setPermissionRequestHandler: jest.fn(),
      webRequest: {
        onBeforeSendHeaders: jest.fn(),
        onHeadersReceived: jest.fn()
      },
      cookies: {
        set: jest.fn(() => Promise.resolve()),
        get: jest.fn(() => Promise.resolve([{ name: 'session_id', value: 'test_session' }])),
        remove: jest.fn(() => Promise.resolve())
      }
    })),
    defaultSession: {
      cookies: {
        set: jest.fn(() => Promise.resolve()),
        get: jest.fn(() => Promise.resolve([])),
        remove: jest.fn(() => Promise.resolve())
      }
    }
  },
  net: {
    isOnline: jest.fn(() => true)
  }
}));

jest.mock('electron-log', () => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn()
}));

jest.mock('axios');

describe('UnifiedSessionManager', () => {
  let sessionManager;
  let mockAxios;

  beforeEach(() => {
    sessionManager = new UnifiedSessionManager();
    mockAxios = require('axios');
    jest.clearAllMocks();
  });

  afterEach(() => {
    if (sessionManager) {
      sessionManager.stopHealthCheck();
    }
  });

  describe('Initialisation', () => {
    it('devrait initialiser correctement', () => {
      expect(sessionManager).toBeDefined();
      expect(sessionManager.isActive).toBe(false);
      expect(sessionManager.sessionId).toBeNull();
    });

    it('devrait créer une session Electron dédiée', () => {
      const { session } = require('electron');
      expect(session.fromPartition).toHaveBeenCalledWith('persist:odoo-session', { cache: true });
    });
  });

  describe('Authentification', () => {
    const mockCredentials = {
      username: '<EMAIL>',
      password: 'password123',
      serverUrl: 'https://test.odoo.com',
      dbName: 'test-db'
    };

    it('devrait authentifier avec succès', async () => {
      // Mock de la réponse d'authentification réussie
      mockAxios.post.mockResolvedValue({
        data: {
          result: {
            uid: 123,
            username: '<EMAIL>'
          }
        },
        headers: {
          'set-cookie': ['session_id=abc123; Path=/']
        }
      });

      const result = await sessionManager.authenticate(mockCredentials);

      expect(result.success).toBe(true);
      expect(result.sessionData.userId).toBe(123);
      expect(result.sessionData.username).toBe('<EMAIL>');
      expect(sessionManager.isActive).toBe(true);
    });

    it('devrait gérer les échecs d\'authentification', async () => {
      // Mock de la réponse d'authentification échouée
      mockAxios.post.mockResolvedValue({
        data: {
          error: {
            message: 'Identifiants incorrects'
          }
        }
      });

      const result = await sessionManager.authenticate(mockCredentials);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Identifiants incorrects');
      expect(sessionManager.isActive).toBe(false);
    });

    it('devrait gérer les erreurs réseau', async () => {
      // Mock d'une erreur réseau
      mockAxios.post.mockRejectedValue(new Error('Network Error'));

      const result = await sessionManager.authenticate(mockCredentials);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Network Error');
    });
  });

  describe('Gestion des cookies', () => {
    beforeEach(async () => {
      // Établir une session de test
      mockAxios.post.mockResolvedValue({
        data: {
          result: { uid: 123, username: 'test' }
        },
        headers: {
          'set-cookie': ['session_id=test123; Path=/']
        }
      });

      await sessionManager.authenticate({
        username: 'test',
        password: 'pass',
        serverUrl: 'https://test.com'
      });
    });

    it('devrait définir les cookies correctement', async () => {
      const result = await sessionManager.setCookieInSession('test123', 'https://test.com');

      expect(result).toBe(true);
      expect(sessionManager.odooSession.cookies.set).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'session_id',
          value: 'test123',
          url: 'https://test.com'
        })
      );
    });

    it('devrait supprimer les anciens cookies avant d\'en définir de nouveaux', async () => {
      await sessionManager.setCookieInSession('new123', 'https://test.com');

      expect(sessionManager.odooSession.cookies.remove).toHaveBeenCalledWith(
        'https://test.com',
        'session_id'
      );
    });
  });

  describe('Validation de session', () => {
    beforeEach(async () => {
      // Établir une session de test
      mockAxios.post.mockResolvedValue({
        data: {
          result: { uid: 123, username: 'test' }
        },
        headers: {
          'set-cookie': ['session_id=test123; Path=/']
        }
      });

      await sessionManager.authenticate({
        username: 'test',
        password: 'pass',
        serverUrl: 'https://test.com'
      });
    });

    it('devrait valider une session active', async () => {
      // Mock de la vérification côté serveur
      mockAxios.post.mockResolvedValue({
        data: {
          result: { uid: 123 }
        }
      });

      const result = await sessionManager.validateSession();

      expect(result.valid).toBe(true);
      expect(result.sessionData.userId).toBe(123);
    });

    it('devrait détecter une session expirée', async () => {
      // Simuler une session expirée
      sessionManager.sessionState.expiresAt = Date.now() - 1000;

      const result = await sessionManager.validateSession();

      expect(result.valid).toBe(false);
      expect(result.reason).toBe('Session expirée');
    });

    it('devrait détecter une session invalide côté serveur', async () => {
      // Mock d'une réponse invalide du serveur
      mockAxios.post.mockResolvedValue({
        data: {
          result: { uid: 999 } // UID différent
        }
      });

      const result = await sessionManager.validateSession();

      expect(result.valid).toBe(false);
      expect(result.reason).toBe('Session invalide côté serveur');
    });
  });

  describe('Nettoyage de session', () => {
    beforeEach(async () => {
      // Établir une session de test
      mockAxios.post.mockResolvedValue({
        data: {
          result: { uid: 123, username: 'test' }
        },
        headers: {
          'set-cookie': ['session_id=test123; Path=/']
        }
      });

      await sessionManager.authenticate({
        username: 'test',
        password: 'pass',
        serverUrl: 'https://test.com'
      });
    });

    it('devrait nettoyer complètement la session', async () => {
      await sessionManager.clearSession();

      expect(sessionManager.isActive).toBe(false);
      expect(sessionManager.sessionId).toBeNull();
      expect(sessionManager.odooSession.cookies.remove).toHaveBeenCalled();
    });
  });
});

describe('SessionRecoveryManager', () => {
  let sessionManager;
  let recoveryManager;

  beforeEach(() => {
    sessionManager = new UnifiedSessionManager();
    recoveryManager = new SessionRecoveryManager(sessionManager);
    jest.clearAllMocks();
  });

  afterEach(() => {
    if (recoveryManager) {
      recoveryManager.cleanup();
    }
  });

  describe('Initialisation', () => {
    it('devrait initialiser correctement', () => {
      expect(recoveryManager).toBeDefined();
      expect(recoveryManager.isRecovering).toBe(false);
    });
  });

  describe('Gestion des événements', () => {
    it('devrait réagir à l\'expiration de session', async () => {
      const handleSessionExpiredSpy = jest.spyOn(recoveryManager, 'handleSessionExpired');

      sessionManager.emit('session-expired');

      expect(handleSessionExpiredSpy).toHaveBeenCalled();
    });

    it('devrait démarrer la récupération automatique', async () => {
      // Mock des credentials stockés
      recoveryManager.lastValidCredentials = {
        encrypted: 'mock_encrypted',
        key: 'mock_key',
        iv: 'mock_iv'
      };

      const startRecoveryProcessSpy = jest.spyOn(recoveryManager, 'startRecoveryProcess');

      await recoveryManager.handleSessionExpired();

      expect(startRecoveryProcessSpy).toHaveBeenCalledWith('session-expired');
    });
  });

  describe('Processus de récupération', () => {
    it('devrait limiter le nombre de tentatives', async () => {
      recoveryManager.config.maxRetryAttempts = 2;
      recoveryManager.recoveryState.retryCount = 2;
      recoveryManager.recoveryState.isRecovering = true;

      const failRecoverySpy = jest.spyOn(recoveryManager, 'failRecovery');

      await recoveryManager.attemptRecovery();

      expect(failRecoverySpy).toHaveBeenCalledWith('max-retries-exceeded');
    });

    it('devrait calculer le délai de retry avec backoff exponentiel', () => {
      recoveryManager.config.exponentialBackoff = true;
      recoveryManager.config.retryDelay = 1000;
      recoveryManager.recoveryState.retryCount = 3;

      const delay = recoveryManager.calculateRetryDelay();

      expect(delay).toBe(4000); // 1000 * 2^(3-1) = 4000
    });
  });
});

describe('SessionEventHandler', () => {
  let sessionManager;
  let recoveryManager;
  let eventHandler;
  let mockMainWindow;

  beforeEach(() => {
    sessionManager = new UnifiedSessionManager();
    recoveryManager = new SessionRecoveryManager(sessionManager);
    
    mockMainWindow = {
      isDestroyed: jest.fn(() => false),
      webContents: {
        send: jest.fn()
      }
    };

    eventHandler = new SessionEventHandler(sessionManager, recoveryManager, mockMainWindow);
    jest.clearAllMocks();
  });

  afterEach(() => {
    if (eventHandler) {
      eventHandler.cleanup();
    }
  });

  describe('Gestion des événements', () => {
    it('devrait gérer l\'établissement de session', () => {
      const sessionData = {
        username: '<EMAIL>',
        userId: 123,
        serverUrl: 'https://test.com'
      };

      eventHandler.onSessionEstablished(sessionData);

      expect(mockMainWindow.webContents.send).toHaveBeenCalledWith(
        'session-event',
        expect.objectContaining({
          type: 'session-established',
          data: expect.objectContaining({
            username: '<EMAIL>',
            userId: 123
          })
        })
      );
    });

    it('devrait gérer l\'expiration de session', () => {
      eventHandler.onSessionExpired();

      expect(mockMainWindow.webContents.send).toHaveBeenCalledWith(
        'session-event',
        expect.objectContaining({
          type: 'session-expired',
          message: expect.stringContaining('session a expiré')
        })
      );
    });

    it('devrait gérer la récupération réussie', () => {
      const recoveryData = { duration: 5000, attempts: 2 };

      eventHandler.onRecoveryCompleted(recoveryData);

      expect(mockMainWindow.webContents.send).toHaveBeenCalledWith(
        'session-event',
        expect.objectContaining({
          type: 'recovery-completed',
          message: 'Reconnexion réussie'
        })
      );
    });
  });

  describe('Gestion des notifications', () => {
    it('devrait afficher une notification temporaire', () => {
      eventHandler.showTemporaryNotification('Test message', 'success', 3000);

      expect(mockMainWindow.webContents.send).toHaveBeenCalledWith(
        'notification',
        expect.objectContaining({
          message: 'Test message',
          type: 'success',
          temporary: true,
          duration: 3000
        })
      );
    });

    it('devrait masquer les notifications après le délai', (done) => {
      eventHandler.showTemporaryNotification('Test', 'info', 100);

      setTimeout(() => {
        expect(mockMainWindow.webContents.send).toHaveBeenCalledWith(
          'notification',
          expect.objectContaining({
            type: 'hide'
          })
        );
        done();
      }, 150);
    });
  });
});
