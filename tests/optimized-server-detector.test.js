/**
 * Tests pour le détecteur de serveur optimisé
 */

const { describe, it, beforeEach, afterEach, expect, jest } = require('@jest/globals');
const OptimizedServerDetector = require('../src/main/optimized-server-detector');

// Mocks
jest.mock('electron', () => ({
  app: {
    getPath: jest.fn(() => '/tmp/test')
  }
}));

jest.mock('electron-log', () => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn()
}));

jest.mock('axios');
jest.mock('fs', () => ({
  promises: {
    readFile: jest.fn(),
    writeFile: jest.fn(),
    mkdir: jest.fn(),
    rename: jest.fn(),
    unlink: jest.fn()
  }
}));

describe('OptimizedServerDetector', () => {
  let detector;
  let mockAxios;

  beforeEach(() => {
    detector = new OptimizedServerDetector({
      fastScanTimeout: 100,
      normalScanTimeout: 200,
      maxConcurrentScans: 5
    });
    
    mockAxios = require('axios');
    jest.clearAllMocks();
  });

  afterEach(() => {
    if (detector) {
      detector.cleanup();
    }
  });

  describe('Initialisation', () => {
    it('devrait initialiser correctement', () => {
      expect(detector).toBeDefined();
      expect(detector.config.fastScanTimeout).toBe(100);
      expect(detector.detectionState.isDetecting).toBe(false);
    });

    it('devrait configurer les composants', () => {
      expect(detector.enhancedDetector).toBeDefined();
      expect(detector.networkScanner).toBeDefined();
      expect(detector.ipCache).toBeDefined();
    });
  });

  describe('Détection de serveur', () => {
    it('devrait détecter depuis le cache en priorité', async () => {
      // Mock du cache avec un serveur valide
      const mockCachedServer = {
        url: 'http://************:8069',
        ip: '************',
        confidence: 0.9
      };
      
      jest.spyOn(detector, 'detectFromCache').mockResolvedValue({
        url: mockCachedServer.url,
        ip: mockCachedServer.ip,
        type: 'local',
        method: 'cache',
        confidence: mockCachedServer.confidence
      });

      const result = await detector.detectServer();

      expect(result.method).toBe('cache');
      expect(result.url).toBe(mockCachedServer.url);
      expect(detector.detectFromCache).toHaveBeenCalled();
    });

    it('devrait fallback sur les serveurs connus si cache vide', async () => {
      // Mock cache vide
      jest.spyOn(detector, 'detectFromCache').mockResolvedValue(null);
      
      // Mock serveur connu valide
      jest.spyOn(detector, 'detectKnownServers').mockResolvedValue({
        url: 'http://localhost:8069',
        ip: '127.0.0.1',
        type: 'local',
        method: 'known'
      });

      const result = await detector.detectServer();

      expect(result.method).toBe('known');
      expect(result.url).toBe('http://localhost:8069');
    });

    it('devrait utiliser le balayage réseau si nécessaire', async () => {
      // Mock cache et serveurs connus vides
      jest.spyOn(detector, 'detectFromCache').mockResolvedValue(null);
      jest.spyOn(detector, 'detectKnownServers').mockResolvedValue(null);
      
      // Mock balayage réseau réussi
      jest.spyOn(detector, 'detectViaNetworkScan').mockResolvedValue({
        url: 'http://*************:8069',
        ip: '*************',
        type: 'local',
        method: 'network-scan'
      });

      const result = await detector.detectServer();

      expect(result.method).toBe('network-scan');
      expect(result.url).toBe('http://*************:8069');
    });

    it('devrait utiliser le serveur distant en fallback', async () => {
      // Mock toutes les méthodes locales échouées
      jest.spyOn(detector, 'detectFromCache').mockResolvedValue(null);
      jest.spyOn(detector, 'detectKnownServers').mockResolvedValue(null);
      jest.spyOn(detector, 'detectViaNetworkScan').mockResolvedValue(null);
      
      // Mock serveur distant valide
      jest.spyOn(detector, 'detectRemoteServer').mockResolvedValue({
        url: 'https://edara.ligne-digitale.com',
        type: 'remote',
        method: 'remote'
      });

      const result = await detector.detectServer();

      expect(result.method).toBe('remote');
      expect(result.type).toBe('remote');
    });

    it('devrait retourner un fallback en cas d\'échec total', async () => {
      // Mock toutes les méthodes échouées
      jest.spyOn(detector, 'detectFromCache').mockResolvedValue(null);
      jest.spyOn(detector, 'detectKnownServers').mockResolvedValue(null);
      jest.spyOn(detector, 'detectViaNetworkScan').mockResolvedValue(null);
      jest.spyOn(detector, 'detectRemoteServer').mockResolvedValue(null);

      const result = await detector.detectServer();

      expect(result.method).toBe('fallback');
      expect(result.fallback).toBe(true);
    });
  });

  describe('Validation rapide', () => {
    it('devrait valider un serveur Odoo valide', async () => {
      mockAxios.get.mockResolvedValue({
        status: 200,
        data: '<html>Odoo</html>'
      });

      const isValid = await detector.quickValidateServer('http://test.com:8069');

      expect(isValid).toBe(true);
      expect(mockAxios.get).toHaveBeenCalledWith(
        'http://test.com:8069/web/database/selector',
        expect.objectContaining({
          timeout: detector.config.normalScanTimeout
        })
      );
    });

    it('devrait reconnaître un serveur Odoo avec authentification', async () => {
      mockAxios.get.mockRejectedValue({
        response: { status: 401 }
      });

      const isValid = await detector.quickValidateServer('http://test.com:8069');

      expect(isValid).toBe(true); // 401 indique un serveur Odoo
    });

    it('devrait rejeter un serveur non-Odoo', async () => {
      mockAxios.get.mockRejectedValue({
        response: { status: 404 }
      });

      const isValid = await detector.quickValidateServer('http://test.com:8069');

      expect(isValid).toBe(false);
    });
  });

  describe('Gestion des métriques', () => {
    it('devrait mettre à jour les métriques lors de la détection', async () => {
      jest.spyOn(detector, 'detectFromCache').mockResolvedValue({
        url: 'http://test.com:8069',
        method: 'cache'
      });

      await detector.detectServer();

      const metrics = detector.getMetrics();
      expect(metrics.totalDetections).toBe(1);
      expect(metrics.successfulDetections).toBe(1);
      expect(metrics.averageDetectionTime).toBeGreaterThan(0);
    });

    it('devrait calculer correctement le taux de succès', async () => {
      // Première détection réussie
      jest.spyOn(detector, 'detectFromCache').mockResolvedValue({
        url: 'http://test.com:8069',
        method: 'cache'
      });
      await detector.detectServer();

      // Deuxième détection échouée (fallback)
      jest.spyOn(detector, 'detectFromCache').mockResolvedValue(null);
      jest.spyOn(detector, 'detectKnownServers').mockResolvedValue(null);
      jest.spyOn(detector, 'detectViaNetworkScan').mockResolvedValue(null);
      jest.spyOn(detector, 'detectRemoteServer').mockResolvedValue(null);
      await detector.detectServer();

      const metrics = detector.getMetrics();
      expect(metrics.totalDetections).toBe(2);
      expect(metrics.successfulDetections).toBe(1);
    });
  });

  describe('Gestion des événements', () => {
    it('devrait émettre un événement lors de la détection', async () => {
      const eventSpy = jest.fn();
      detector.on('server-detected', eventSpy);

      jest.spyOn(detector, 'detectFromCache').mockResolvedValue({
        url: 'http://test.com:8069',
        method: 'cache'
      });

      await detector.detectServer();

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          server: expect.objectContaining({
            url: 'http://test.com:8069'
          }),
          method: 'cache'
        })
      );
    });

    it('devrait gérer l\'invalidation de serveur', () => {
      const invalidationSpy = jest.fn();
      detector.on('server-invalidated', invalidationSpy);

      // Simuler une invalidation depuis le cache
      detector.ipCache.emit('ip-invalidated', {
        ip: '************',
        url: 'http://************:8069'
      });

      expect(invalidationSpy).toHaveBeenCalled();
    });
  });

  describe('Réinitialisation', () => {
    it('devrait réinitialiser et redécouvrir', async () => {
      // Définir un serveur préféré
      detector.detectionState.preferredServer = {
        url: 'http://old.server:8069'
      };

      // Mock nouvelle détection
      jest.spyOn(detector, 'detectServer').mockResolvedValue({
        url: 'http://new.server:8069',
        method: 'network-scan'
      });

      const result = await detector.resetAndRediscover();

      expect(detector.detectionState.preferredServer).toBeNull();
      expect(result.url).toBe('http://new.server:8069');
    });
  });

  describe('Utilitaires', () => {
    it('devrait obtenir l\'IP locale', () => {
      // Mock os.networkInterfaces
      const mockInterfaces = {
        'eth0': [
          {
            family: 'IPv4',
            address: '*************',
            internal: false
          }
        ]
      };

      jest.doMock('os', () => ({
        networkInterfaces: () => mockInterfaces
      }));

      // Recharger le module pour utiliser le mock
      const ip = detector.getLocalIpAddress();
      expect(ip).toBe('*************');
    });

    it('devrait construire l\'URL du serveur distant', () => {
      // Mock user preferences
      jest.doMock('./user-preferences', () => ({
        getWorkspaceName: () => 'test-workspace'
      }));

      const url = detector.getRemoteServerUrl();
      expect(url).toBe('https://test-workspace.ligne-digitale.com');
    });
  });

  describe('Performance', () => {
    it('devrait détecter rapidement avec cache', async () => {
      jest.spyOn(detector, 'detectFromCache').mockResolvedValue({
        url: 'http://cached.server:8069',
        method: 'cache'
      });

      const startTime = Date.now();
      await detector.detectServer();
      const duration = Date.now() - startTime;

      expect(duration).toBeLessThan(100); // Moins de 100ms
    });

    it('devrait limiter la concurrence lors du balayage', async () => {
      const scanSpy = jest.spyOn(detector.networkScanner, 'scanNetwork');
      scanSpy.mockResolvedValue([]);

      jest.spyOn(detector, 'detectFromCache').mockResolvedValue(null);
      jest.spyOn(detector, 'detectKnownServers').mockResolvedValue(null);
      jest.spyOn(detector, 'getLocalIpAddress').mockReturnValue('*************');

      await detector.detectViaNetworkScan();

      expect(scanSpy).toHaveBeenCalledWith(
        '*************',
        expect.objectContaining({
          maxIps: detector.config.maxNetworkRange
        })
      );
    });
  });
});

describe('Comparaison de performance', () => {
  it('devrait être significativement plus rapide que l\'ancien système', async () => {
    const detector = new OptimizedServerDetector({
      fastScanTimeout: 50,
      normalScanTimeout: 100
    });

    // Simuler une détection depuis le cache
    jest.spyOn(detector, 'detectFromCache').mockResolvedValue({
      url: 'http://fast.server:8069',
      method: 'cache'
    });

    const startTime = Date.now();
    await detector.detectServer();
    const duration = Date.now() - startTime;

    // Le nouveau système devrait être <200ms vs ancien système >5000ms
    expect(duration).toBeLessThan(200);
    
    detector.cleanup();
  });

  it('devrait avoir un taux de succès élevé', async () => {
    const detector = new OptimizedServerDetector();
    let successCount = 0;
    const totalTests = 10;

    // Simuler différents scénarios
    for (let i = 0; i < totalTests; i++) {
      try {
        if (i < 7) {
          // 70% de succès depuis le cache
          jest.spyOn(detector, 'detectFromCache').mockResolvedValue({
            url: `http://server${i}.com:8069`,
            method: 'cache'
          });
        } else if (i < 9) {
          // 20% de succès par balayage réseau
          jest.spyOn(detector, 'detectFromCache').mockResolvedValue(null);
          jest.spyOn(detector, 'detectViaNetworkScan').mockResolvedValue({
            url: `http://server${i}.com:8069`,
            method: 'network-scan'
          });
        } else {
          // 10% fallback
          jest.spyOn(detector, 'detectFromCache').mockResolvedValue(null);
          jest.spyOn(detector, 'detectViaNetworkScan').mockResolvedValue(null);
        }

        const result = await detector.detectServer();
        if (result && !result.fallback) {
          successCount++;
        }
      } catch (error) {
        // Compter les fallbacks comme des échecs partiels
      }
    }

    const successRate = successCount / totalTests;
    expect(successRate).toBeGreaterThan(0.8); // >80% de succès
    
    detector.cleanup();
  });
});
