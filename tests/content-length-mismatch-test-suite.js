/**
 * Suite de Tests Complète pour les Erreurs Content-Length Mismatch
 * Valide l'efficacité des solutions préventives et correctives
 */

const { app, BrowserWindow, session } = require('electron');
const path = require('path');
const log = require('electron-log');
const axios = require('axios');

class ContentLengthMismatchTestSuite {
  constructor() {
    this.testResults = {
      preventive: [],
      corrective: [],
      performance: [],
      integration: []
    };
    
    this.testConfig = {
      testDuration: 60000,        // 1 minute par test
      errorSimulationCount: 10,   // 10 erreurs simulées
      assetUrls: [
        'http://localhost:8069/web/content/566-ba15075/web.assets_backend.js',
        'http://localhost:8069/web/content/567-ba15075/web.assets_common.js',
        'http://localhost:8069/web/content/568-ba15075/web.assets_backend.css',
        'http://localhost:8069/web/content/569-ba15075/web.assets_common.css'
      ],
      serverUrls: [
        'http://localhost:8069',
        'http://**************:8069',
        'https://edara.ligne-digitale.com'
      ]
    };
    
    this.metrics = {
      startTime: 0,
      endTime: 0,
      totalErrors: 0,
      resolvedErrors: 0,
      averageResolutionTime: 0,
      memoryUsage: [],
      networkRequests: []
    };
  }

  /**
   * Lance la suite de tests complète
   */
  async runFullTestSuite() {
    console.log('🧪 Démarrage de la suite de tests Content-Length Mismatch');
    
    try {
      // Phase 1: Tests préventifs
      await this.runPreventiveTests();
      
      // Phase 2: Tests correctifs
      await this.runCorrectiveTests();
      
      // Phase 3: Tests de performance
      await this.runPerformanceTests();
      
      // Phase 4: Tests d'intégration
      await this.runIntegrationTests();
      
      // Générer le rapport final
      const report = this.generateTestReport();
      console.log('📊 Rapport de tests généré:', report);
      
      return report;
    } catch (error) {
      console.error('❌ Erreur lors des tests:', error);
      throw error;
    }
  }

  /**
   * Tests préventifs - Vérification des configurations
   */
  async runPreventiveTests() {
    console.log('🛡️ Tests préventifs en cours...');
    
    const tests = [
      this.testOdooConfiguration.bind(this),
      this.testNginxConfiguration.bind(this),
      this.testElectronConfiguration.bind(this),
      this.testNetworkOptimizations.bind(this),
      this.testCompressionSettings.bind(this)
    ];
    
    for (const test of tests) {
      try {
        const result = await test();
        this.testResults.preventive.push(result);
        console.log(`✅ ${result.name}: ${result.status}`);
      } catch (error) {
        console.error(`❌ Test préventif échoué:`, error);
        this.testResults.preventive.push({
          name: test.name,
          status: 'FAILED',
          error: error.message
        });
      }
    }
  }

  /**
   * Test de configuration Odoo
   */
  async testOdooConfiguration() {
    const testWindow = await this.createTestWindow();
    
    try {
      // Tester la configuration Gzip
      const response = await axios.get(`${this.testConfig.serverUrls[0]}/web/database/selector`, {
        headers: { 'Accept-Encoding': 'gzip, deflate, br' }
      });
      
      const hasGzipIssue = response.headers['content-encoding'] && 
                          response.headers['content-length'] &&
                          response.data.length !== parseInt(response.headers['content-length']);
      
      return {
        name: 'Odoo Configuration',
        status: hasGzipIssue ? 'FAILED' : 'PASSED',
        details: {
          gzipEnabled: !!response.headers['content-encoding'],
          contentLengthMismatch: hasGzipIssue,
          responseSize: response.data.length,
          headerSize: response.headers['content-length']
        }
      };
    } finally {
      testWindow.close();
    }
  }

  /**
   * Test de configuration Nginx/Apache
   */
  async testNginxConfiguration() {
    try {
      // Tester les headers de proxy
      const response = await axios.get(`${this.testConfig.serverUrls[0]}/web/static/src/js/boot.js`, {
        headers: { 'Accept-Encoding': 'identity' }
      });
      
      const hasProxyIssues = response.headers['content-encoding'] || 
                            !response.headers['content-length'] ||
                            response.status !== 200;
      
      return {
        name: 'Nginx/Apache Configuration',
        status: hasProxyIssues ? 'WARNING' : 'PASSED',
        details: {
          statusCode: response.status,
          hasContentEncoding: !!response.headers['content-encoding'],
          hasContentLength: !!response.headers['content-length'],
          responseTime: response.headers['x-response-time'] || 'N/A'
        }
      };
    } catch (error) {
      return {
        name: 'Nginx/Apache Configuration',
        status: 'FAILED',
        error: error.message
      };
    }
  }

  /**
   * Test de configuration Electron
   */
  async testElectronConfiguration() {
    const testWindow = await this.createTestWindow();
    
    try {
      // Vérifier les intercepteurs de requêtes
      let interceptorWorking = false;
      
      testWindow.webContents.session.webRequest.onBeforeSendHeaders((details, callback) => {
        if (details.url.includes('web.assets')) {
          interceptorWorking = true;
        }
        callback({ cancel: false });
      });
      
      // Charger une page de test
      await testWindow.loadURL(`${this.testConfig.serverUrls[0]}/web/login`);
      
      // Attendre que les assets se chargent
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      return {
        name: 'Electron Configuration',
        status: interceptorWorking ? 'PASSED' : 'FAILED',
        details: {
          interceptorActive: interceptorWorking,
          webSecurity: testWindow.webContents.getWebPreferences().webSecurity,
          contextIsolation: testWindow.webContents.getWebPreferences().contextIsolation
        }
      };
    } finally {
      testWindow.close();
    }
  }

  /**
   * Test des optimisations réseau
   */
  async testNetworkOptimizations() {
    const startTime = Date.now();
    const requests = [];
    
    try {
      // Tester le chargement parallèle des assets
      const assetPromises = this.testConfig.assetUrls.map(async (url) => {
        const requestStart = Date.now();
        try {
          const response = await axios.get(url, {
            timeout: 10000,
            headers: { 'Accept-Encoding': 'identity' }
          });
          
          requests.push({
            url,
            status: response.status,
            size: response.data.length,
            time: Date.now() - requestStart,
            success: true
          });
        } catch (error) {
          requests.push({
            url,
            error: error.message,
            time: Date.now() - requestStart,
            success: false
          });
        }
      });
      
      await Promise.allSettled(assetPromises);
      
      const totalTime = Date.now() - startTime;
      const successRate = requests.filter(r => r.success).length / requests.length;
      const averageTime = requests.reduce((sum, r) => sum + r.time, 0) / requests.length;
      
      return {
        name: 'Network Optimizations',
        status: successRate > 0.8 ? 'PASSED' : 'FAILED',
        details: {
          totalTime,
          successRate: successRate * 100,
          averageRequestTime: averageTime,
          requests: requests.length
        }
      };
    } catch (error) {
      return {
        name: 'Network Optimizations',
        status: 'FAILED',
        error: error.message
      };
    }
  }

  /**
   * Test des paramètres de compression
   */
  async testCompressionSettings() {
    try {
      const tests = [];
      
      // Test avec compression
      const gzipResponse = await axios.get(`${this.testConfig.serverUrls[0]}/web/static/src/js/boot.js`, {
        headers: { 'Accept-Encoding': 'gzip' }
      });
      
      // Test sans compression
      const identityResponse = await axios.get(`${this.testConfig.serverUrls[0]}/web/static/src/js/boot.js`, {
        headers: { 'Accept-Encoding': 'identity' }
      });
      
      const compressionWorking = gzipResponse.headers['content-encoding'] === 'gzip';
      const identityWorking = !identityResponse.headers['content-encoding'];
      
      return {
        name: 'Compression Settings',
        status: identityWorking ? 'PASSED' : 'WARNING',
        details: {
          gzipSupported: compressionWorking,
          identitySupported: identityWorking,
          gzipSize: gzipResponse.data.length,
          identitySize: identityResponse.data.length,
          compressionRatio: compressionWorking ? 
            (1 - gzipResponse.data.length / identityResponse.data.length) * 100 : 0
        }
      };
    } catch (error) {
      return {
        name: 'Compression Settings',
        status: 'FAILED',
        error: error.message
      };
    }
  }

  /**
   * Tests correctifs - Simulation d'erreurs et vérification des corrections
   */
  async runCorrectiveTests() {
    console.log('🔧 Tests correctifs en cours...');
    
    const testWindow = await this.createTestWindow();
    
    try {
      // Injecter le script de récupération intelligente
      await this.injectRecoveryScript(testWindow);
      
      // Simuler différents types d'erreurs
      const errorTests = [
        this.simulateContentLengthMismatch.bind(this),
        this.simulateEmptyResponse.bind(this),
        this.simulateNetworkTimeout.bind(this),
        this.simulateCorruptedAsset.bind(this)
      ];
      
      for (const test of errorTests) {
        const result = await test(testWindow);
        this.testResults.corrective.push(result);
        console.log(`🔧 ${result.name}: ${result.status}`);
      }
    } finally {
      testWindow.close();
    }
  }

  /**
   * Simulation d'erreur Content-Length Mismatch
   */
  async simulateContentLengthMismatch(testWindow) {
    const startTime = Date.now();
    let errorDetected = false;
    let errorResolved = false;
    
    // Écouter les événements de récupération
    testWindow.webContents.executeJavaScript(`
      window.addEventListener('error', (event) => {
        if (event.target && event.target.src) {
          console.log('Error detected:', event.target.src);
          window.testErrorDetected = true;
        }
      }, true);
      
      // Simuler une erreur Content-Length Mismatch
      const script = document.createElement('script');
      script.src = '/web/static/src/js/fake-asset.js?simulate-mismatch=1';
      script.onerror = () => {
        window.testErrorDetected = true;
        console.log('Simulated error triggered');
      };
      document.head.appendChild(script);
    `);
    
    // Attendre la détection et résolution
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Vérifier si l'erreur a été détectée et résolue
    errorDetected = await testWindow.webContents.executeJavaScript('window.testErrorDetected || false');
    
    return {
      name: 'Content-Length Mismatch Simulation',
      status: errorDetected ? 'PASSED' : 'FAILED',
      details: {
        errorDetected,
        resolutionTime: Date.now() - startTime,
        recoveryAttempted: true
      }
    };
  }

  /**
   * Tests de performance
   */
  async runPerformanceTests() {
    console.log('⚡ Tests de performance en cours...');
    
    const tests = [
      this.testAssetLoadingSpeed.bind(this),
      this.testMemoryUsage.bind(this),
      this.testRecoverySpeed.bind(this),
      this.testConcurrentLoading.bind(this)
    ];
    
    for (const test of tests) {
      const result = await test();
      this.testResults.performance.push(result);
      console.log(`⚡ ${result.name}: ${result.status}`);
    }
  }

  /**
   * Test de vitesse de chargement des assets
   */
  async testAssetLoadingSpeed() {
    const testWindow = await this.createTestWindow();
    
    try {
      const startTime = Date.now();
      
      // Charger la page Odoo
      await testWindow.loadURL(`${this.testConfig.serverUrls[0]}/web/login`);
      
      // Attendre que tous les assets critiques soient chargés
      await testWindow.webContents.executeJavaScript(`
        new Promise((resolve) => {
          const checkAssets = () => {
            const scripts = document.querySelectorAll('script[src*="web.assets"]');
            const styles = document.querySelectorAll('link[href*="web.assets"]');
            
            const allLoaded = Array.from(scripts).every(s => s.readyState === 'complete') &&
                             Array.from(styles).every(s => s.sheet);
            
            if (allLoaded || Date.now() - ${startTime} > 30000) {
              resolve(allLoaded);
            } else {
              setTimeout(checkAssets, 100);
            }
          };
          checkAssets();
        });
      `);
      
      const loadTime = Date.now() - startTime;
      
      return {
        name: 'Asset Loading Speed',
        status: loadTime < 10000 ? 'PASSED' : 'WARNING',
        details: {
          loadTime,
          threshold: 10000,
          performance: loadTime < 5000 ? 'EXCELLENT' : loadTime < 10000 ? 'GOOD' : 'SLOW'
        }
      };
    } finally {
      testWindow.close();
    }
  }

  /**
   * Test d'utilisation mémoire
   */
  async testMemoryUsage() {
    const testWindow = await this.createTestWindow();
    
    try {
      const initialMemory = process.memoryUsage();
      
      // Charger plusieurs pages pour tester les fuites mémoire
      for (let i = 0; i < 5; i++) {
        await testWindow.loadURL(`${this.testConfig.serverUrls[0]}/web/login`);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
      
      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      
      return {
        name: 'Memory Usage',
        status: memoryIncrease < 50 * 1024 * 1024 ? 'PASSED' : 'WARNING', // 50MB
        details: {
          initialMemory: initialMemory.heapUsed,
          finalMemory: finalMemory.heapUsed,
          memoryIncrease,
          threshold: 50 * 1024 * 1024
        }
      };
    } finally {
      testWindow.close();
    }
  }

  /**
   * Tests d'intégration
   */
  async runIntegrationTests() {
    console.log('🔗 Tests d\'intégration en cours...');
    
    const tests = [
      this.testEndToEndScenario.bind(this),
      this.testMultiServerScenario.bind(this),
      this.testNetworkFailureRecovery.bind(this)
    ];
    
    for (const test of tests) {
      const result = await test();
      this.testResults.integration.push(result);
      console.log(`🔗 ${result.name}: ${result.status}`);
    }
  }

  /**
   * Test de scénario end-to-end
   */
  async testEndToEndScenario() {
    const testWindow = await this.createTestWindow();
    
    try {
      // Simuler un workflow complet utilisateur
      await testWindow.loadURL(`${this.testConfig.serverUrls[0]}/web/login`);
      
      // Attendre le chargement complet
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // Vérifier que l'interface est fonctionnelle
      const isOdooLoaded = await testWindow.webContents.executeJavaScript(`
        !!(window.odoo && window.odoo.define && document.querySelector('.o_main_navbar'));
      `);
      
      return {
        name: 'End-to-End Scenario',
        status: isOdooLoaded ? 'PASSED' : 'FAILED',
        details: {
          odooLoaded: isOdooLoaded,
          pageTitle: await testWindow.webContents.getTitle(),
          url: testWindow.webContents.getURL()
        }
      };
    } finally {
      testWindow.close();
    }
  }

  /**
   * Méthodes utilitaires
   */
  async createTestWindow() {
    return new BrowserWindow({
      width: 1200,
      height: 800,
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: false
      }
    });
  }

  async injectRecoveryScript(testWindow) {
    const scriptPath = path.join(__dirname, '../src/assets/js/intelligent-content-recovery.js');
    const script = require('fs').readFileSync(scriptPath, 'utf8');
    
    await testWindow.webContents.executeJavaScript(script);
  }

  /**
   * Génère le rapport final des tests
   */
  generateTestReport() {
    const allTests = [
      ...this.testResults.preventive,
      ...this.testResults.corrective,
      ...this.testResults.performance,
      ...this.testResults.integration
    ];
    
    const passed = allTests.filter(t => t.status === 'PASSED').length;
    const failed = allTests.filter(t => t.status === 'FAILED').length;
    const warnings = allTests.filter(t => t.status === 'WARNING').length;
    
    const report = {
      summary: {
        total: allTests.length,
        passed,
        failed,
        warnings,
        successRate: (passed / allTests.length) * 100
      },
      categories: {
        preventive: this.testResults.preventive,
        corrective: this.testResults.corrective,
        performance: this.testResults.performance,
        integration: this.testResults.integration
      },
      recommendations: this.generateRecommendations(),
      timestamp: new Date().toISOString()
    };
    
    return report;
  }

  generateRecommendations() {
    const recommendations = [];
    
    // Analyser les résultats pour générer des recommandations
    const failedTests = [
      ...this.testResults.preventive,
      ...this.testResults.corrective,
      ...this.testResults.performance,
      ...this.testResults.integration
    ].filter(t => t.status === 'FAILED');
    
    failedTests.forEach(test => {
      switch (test.name) {
        case 'Odoo Configuration':
          recommendations.push('Désactiver la compression Gzip dans odoo.conf');
          break;
        case 'Nginx/Apache Configuration':
          recommendations.push('Optimiser la configuration du proxy inverse');
          break;
        case 'Asset Loading Speed':
          recommendations.push('Optimiser la taille et le nombre d\'assets');
          break;
        default:
          recommendations.push(`Corriger les problèmes identifiés dans: ${test.name}`);
      }
    });
    
    return [...new Set(recommendations)]; // Supprimer les doublons
  }
}

// Export pour utilisation dans les tests
module.exports = ContentLengthMismatchTestSuite;

// Exécution directe si appelé en tant que script principal
if (require.main === module) {
  app.whenReady().then(async () => {
    const testSuite = new ContentLengthMismatchTestSuite();
    
    try {
      const report = await testSuite.runFullTestSuite();
      console.log('\n📊 RAPPORT FINAL:');
      console.log(JSON.stringify(report, null, 2));
      
      // Sauvegarder le rapport
      require('fs').writeFileSync(
        path.join(__dirname, 'content-length-test-report.json'),
        JSON.stringify(report, null, 2)
      );
      
      console.log('✅ Rapport sauvegardé dans content-length-test-report.json');
    } catch (error) {
      console.error('❌ Erreur lors des tests:', error);
    } finally {
      app.quit();
    }
  });
}
