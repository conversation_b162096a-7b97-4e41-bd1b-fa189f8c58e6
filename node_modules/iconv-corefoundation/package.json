{"name": "iconv-corefoundation", "version": "1.1.7", "description": "Character set conversion using the macOS CoreFoundation API", "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"prepare": "tsc && make -f native.mk", "test": "node -r ts-node/register --expose-gc node_modules/.bin/_mocha test/**.spec.ts", "docs": "api-extractor run && api-documenter markdown --input-folder temp --output-folder docs && ln -s iconv-corefoundation.md docs/index.md", "prepublishOnly": "npm test"}, "repository": "argv-minus-one/iconv-corefoundation", "keywords": ["corefoundation", "iconv", "convert", "charset", "mac"], "author": "argv-minus-one", "license": "MIT", "bugs": {"url": "https://github.com/argv-minus-one/iconv-corefoundation/issues"}, "homepage": "https://github.com/argv-minus-one/iconv-corefoundation#readme", "os": ["darwin"], "engines": {"node": "^8.11.2 || >=10"}, "devDependencies": {"@microsoft/api-documenter": "^7.2.1", "@microsoft/api-extractor": "^7.1.6", "@types/bindings": "^1.3.0", "@types/chai": "^4.1.7", "@types/mocha": "^9.0.0", "@types/node": "^12.0.2", "chai": "^4.2.0", "chai-bytes": "^0.1.2", "mocha": "^9.1.3", "ts-node": "^8.1.0", "typescript": "^3.4.5"}, "dependencies": {"cli-truncate": "^2.1.0", "node-addon-api": "^1.6.3"}, "files": ["lib/**/*.js", "lib/**/*.d.ts", "lib/**/*.node"]}