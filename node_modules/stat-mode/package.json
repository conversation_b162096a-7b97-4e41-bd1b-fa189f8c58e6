{"name": "stat-mode", "version": "1.0.0", "description": "Offers convenient getters and setters for the stat `mode`", "main": "dist/src/index", "typings": "dist/src/index", "files": ["dist/src"], "repository": {"type": "git", "url": "git://github.com/TooTallNate/stat-mode.git"}, "keywords": ["stat", "mode", "owner", "group", "others", "chmod", "octal", "symbolic", "permissions"], "author": "<PERSON> <<EMAIL>> (http://n8.io/)", "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/stat-mode/issues"}, "homepage": "https://github.com/TooTallNate/stat-mode", "devDependencies": {"@types/escodegen": "^0.0.6", "@types/esprima": "^4.0.2", "@types/mocha": "^5.2.7", "@types/node": "^10.5.3", "@typescript-eslint/eslint-plugin": "1.6.0", "@typescript-eslint/parser": "1.1.0", "cpy-cli": "^2.0.0", "eslint": "5.16.0", "eslint-config-airbnb": "17.1.0", "eslint-config-prettier": "4.1.0", "eslint-import-resolver-typescript": "1.1.1", "eslint-plugin-import": "2.16.0", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "mocha": "^6.2.0", "rimraf": "^3.0.0", "typescript": "^3.5.3"}, "engines": {"node": ">= 6"}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "postbuild": "cpy --parents src test '!**/*.ts' dist", "test": "mocha --reporter spec dist/test/test*.js", "test-lint": "eslint src --ext .js,.ts", "prepublishOnly": "npm run build"}}