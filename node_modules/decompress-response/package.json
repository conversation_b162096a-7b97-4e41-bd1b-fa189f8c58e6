{"name": "decompress-response", "version": "3.3.0", "description": "Decompress a HTTP response if needed", "license": "MIT", "repository": "sindresorhus/decompress-response", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}], "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["decompress", "response", "http", "https", "zlib", "gzip", "zip", "deflate", "unzip", "ungzip", "incoming", "message", "stream", "compressed"], "dependencies": {"mimic-response": "^1.0.0"}, "devDependencies": {"ava": "*", "get-stream": "^3.0.0", "pify": "^3.0.0", "xo": "*"}}