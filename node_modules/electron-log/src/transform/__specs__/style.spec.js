'use strict';

var style = require('../style');

describe('transform/style', function () {
  it('applyAnsiStyles', function () {
    expect(style.applyAnsiStyles(['%c 1 %c 2', 'color:red', 'color:green']))
      .toEqual(['\x1b[31m 1 \x1b[32m 2\x1b[0m']);
  });

  it('removeStyles', function () {
    expect(style.removeStyles(['%c 1 %c 2', 'color:red', 'color:green']))
      .toEqual([' 1  2']);
  });

  describe('transformStyles', function () {
    function onStyleFound(s) {
      return s + ': ';
    }

    it('simple data', function () {
      expect(style.transformStyles(['%c1 %c2', 'a', 'b'], onStyleFound))
        .toEqual(['a: 1 b: 2']);
    });

    it('missed styles', function () {
      expect(style.transformStyles(['%c1 %c2'], onStyleFound))
        .toEqual(['%c1 %c2']);
    });

    it('complex data', function () {
      function onStyleApplied(str) {
        return str + ' end';
      }

      var data = ['%c1 %c2', 'a', '%c3', '%c4', 'b'];

      expect(style.transformStyles(data, onStyleFound, onStyleApplied))
        .toEqual(['a: 1 %c3: 2 end', 'b: 4 end']);
    });
  });
});
