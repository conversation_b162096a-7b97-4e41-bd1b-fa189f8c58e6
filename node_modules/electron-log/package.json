{"name": "electron-log", "version": "4.4.8", "description": "Just a very simple logging module for your Electron application", "main": "src/index.js", "scripts": {"lint": "eslint '**/*.js' --ignore-pattern '**/dist/*.js' && tsc --noEmit", "test": "humile 'src/**/*spec.js'", "test:full": "npm run test && npm run lint && npm run test:e2e", "test:e2e": "humile 'e2e/**/*.spec.js' -R list", "postversion": "git push && git push --tags", "prepack": "npm run test:full", "preversion": "npm run test:full", "ttt": "echo ${npm_config_node_version}"}, "typings": "src/index.d.ts", "repository": "megahertz/electron-log", "files": ["!**/__specs__", "src/*"], "keywords": ["electron", "atom", "log", "logger", "logging", "windows", "mac", "osx", "linux", "desktop"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": "https://github.com/megahertz/electron-log/issues", "homepage": "https://github.com/megahertz/electron-log#readme", "devDependencies": {"@types/node": "^14.14.6", "electron": "*", "eslint": "^7.12.1", "eslint-config-airbnb-base": "^14.2.0", "eslint-plugin-import": "^2.22.1", "humile": "^0.3.2", "nw": "0.42.3", "typescript": "^4.0.5", "webpack": "^5.3.2", "webpack-cli": "^4.1.0"}}