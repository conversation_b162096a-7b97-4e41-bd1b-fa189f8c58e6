{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,KAAK,EAAE,MAAM,UAAU,CAAC;AAC/B,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,KAAK,MAAM,MAAM,QAAQ,CAAC;AACjC,OAAO,KAAK,UAAU,MAAM,YAAY,CAAC;AAEzC,OAAO,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,MAAM,kBAAkB,CAAC;AAO7E,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,sBAAsB,EAAE,MAAM,uBAAuB,CAAC;AAC/D,OAAO,EAAE,eAAe,EAAE,MAAM,SAAS,CAAC;AAC1C,OAAO,EACL,mBAAmB,EACnB,gBAAgB,EAChB,WAAW,EACX,WAAW,EACX,oBAAoB,EACpB,2BAA2B,EAC3B,iBAAiB,GAClB,MAAM,SAAS,CAAC;AAEjB,OAAO,EAAE,WAAW,EAAE,MAAM,SAAS,CAAC;AACtC,OAAO,EAAE,eAAe,EAAE,MAAM,SAAS,CAAC;AAG1C,MAAM,CAAC,GAAG,KAAK,CAAC,qBAAqB,CAAC,CAAC;AAEvC,IAAI,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE;IACtC,eAAe,EAAE,CAAC;CACnB;AAED;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB,CACpC,gBAA6D;IAE7D,MAAM,eAAe,qBACf,gBAA4C,CACjD,CAAC;IACF,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE;QAC/B,MAAM,uBAAuB,GAAG,eAAkD,CAAC;QACnF,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE;YACrC,CAAC,CAAC,oDAAoD,CAAC,CAAC;YACxD,uBAAuB,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;SACrD;QACD,IAAI,uBAAuB,CAAC,IAAI,EAAE;YAChC,uBAAuB,CAAC,IAAI,GAAG,WAAW,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;SAC1E;aAAM;YACL,CAAC,CAAC,4CAA4C,CAAC,CAAC;YAChD,uBAAuB,CAAC,IAAI,GAAG,WAAW,EAAE,CAAC;SAC9C;KACF;IACD,oBAAoB,CAAC,eAAe,EAAE,SAAS,CAAC,CAAC;IAEjD,eAAe,CAAC,OAAO,GAAG,gBAAgB,CACxC,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,eAAe,CAAC,OAAO,CAC/D,CAAC;IACF,MAAM,QAAQ,GAAG,mBAAmB,CAAC,eAAe,CAAC,CAAC;IACtD,MAAM,GAAG,GAAG,MAAM,oBAAoB,CAAC,eAAe,CAAC,CAAC;IACxD,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;IAEnD,mEAAmE;IACnE,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE;QAC1B,CAAC,CAAC,uBAAuB,eAAe,CAAC,SAAS,SAAS,QAAQ,KAAK,GAAG,GAAG,CAAC,CAAC;QAChF,MAAM,UAAU,GAAG,MAAM,KAAK,CAAC,qBAAqB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QAEpE,IAAI,UAAU,KAAK,IAAI,EAAE;YACvB,CAAC,CAAC,YAAY,CAAC,CAAC;SACjB;aAAM;YACL,CAAC,CAAC,WAAW,CAAC,CAAC;YACf,OAAO,UAAU,CAAC;SACnB;KACF;IAED,IACE,CAAC,eAAe,CAAC,SAAS;QAC1B,2BAA2B,CACzB,eAAe,CAAC,QAAQ,EACxB,eAAe,CAAC,IAAI,EACpB,eAAe,CAAC,OAAO,EACvB,eAAe,CAAC,aAAa,CAC9B,EACD;QACA,OAAO,CAAC,IAAI,CAAC,4CAA4C,CAAC,CAAC;QAC3D,OAAO,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;KAChF;IAED,OAAO,MAAM,mBAAmB,CAAC,eAAe,CAAC,aAAa,EAAE,KAAK,EAAC,UAAU,EAAC,EAAE;QACjF,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,mBAAmB,CAAC,eAAe,CAAC,CAAC,CAAC;QAExF,MAAM,UAAU,GAAG,eAAe,CAAC,UAAU,IAAI,CAAC,MAAM,sBAAsB,EAAE,CAAC,CAAC;QAClF,CAAC,CACC,eAAe,GAAG,OAAO,gBAAgB,kBAAkB,IAAI,CAAC,SAAS,CACvE,eAAe,CAAC,eAAe,CAChC,EAAE,CACJ,CAAC;QACF,MAAM,UAAU,CAAC,QAAQ,CAAC,GAAG,EAAE,gBAAgB,EAAE,eAAe,CAAC,eAAe,CAAC,CAAC;QAElF,uDAAuD;QACvD,0DAA0D;QAC1D,IACE,CAAC,eAAe,CAAC,YAAY,CAAC,UAAU,CAAC,YAAY,CAAC;YACtD,CAAC,eAAe,CAAC,wBAAwB;YACzC,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,EAC5C;YACA,MAAM,iBAAiB,CAAC,KAAK,EAAC,MAAM,EAAC,EAAE;gBACrC,IAAI,UAAkB,CAAC;gBACvB,MAAM,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC;gBAC5C,IAAI,SAAS,EAAE;oBACb,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC;oBACpD,MAAM,SAAS,GAAa,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBACnD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;wBAC1B,MAAM,IAAI,KAAK,CACb,8EAA8E,CAC/E,CAAC;qBACH;oBACD,MAAM,kBAAkB,GAAG,SAAS;yBACjC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE,CAAC;yBACtD,IAAI,CAAC,IAAI,CAAC,CAAC;oBACd,MAAM,EAAE,CAAC,SAAS,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC;iBACpD;qBAAM;oBACL,UAAU,GAAG,MAAM,gBAAgB,CAAC;wBAClC,SAAS,EAAE,IAAI;wBACf,OAAO,EAAE,eAAe,CAAC,OAAO;wBAChC,YAAY,EAAE,gBAAgB;wBAC9B,KAAK,EAAE,eAAe,CAAC,KAAK;wBAC5B,eAAe,EAAE,eAAe,CAAC,eAAe;wBAChD,SAAS,EAAE,eAAe,CAAC,SAAS;wBACpC,UAAU,EAAE,eAAe,CAAC,UAAU;wBACtC,aAAa,EAAE,eAAe,CAAC,aAAa;qBAC7C,CAAC,CAAC;iBACJ;gBAED,kFAAkF;gBAClF,sEAAsE;gBACtE,IAAI,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,OAAO,EAAE,eAAe,CAAC,EAAE;oBAC9D,MAAM,gBAAgB,GAA+B,EAAE,CAAC;oBACxD,gBAAgB,CAAC,mBAAmB,GAAG,QAAQ,CAAC;oBAChD,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,iBAAiB,CAAC,QAAQ,EAAE,UAAU,EAAE,gBAAgB,CAAC,CAAC;oBACzF,MAAM,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC;iBACzF;qBAAM;oBACL,MAAM,UAAU,CAAC,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE;wBACrE,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;qBAChC,CAAC,CAAC;iBACJ;YACH,CAAC,CAAC,CAAC;SACJ;QAED,OAAO,MAAM,KAAK,CAAC,cAAc,CAAC,GAAG,EAAE,gBAAgB,EAAE,QAAQ,CAAC,CAAC;IACrE,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,QAAQ,CACtB,OAAe,EACf,OAAwC;IAExC,OAAO,gBAAgB,iCAClB,OAAO,KACV,OAAO,EACP,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAC1B,IAAI,EAAE,OAAO,CAAC,IAAI,EAClB,YAAY,EAAE,UAAU,IACxB,CAAC;AACL,CAAC"}