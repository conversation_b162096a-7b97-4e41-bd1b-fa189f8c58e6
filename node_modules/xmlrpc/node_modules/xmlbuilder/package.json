{"name": "xmlbuilder", "version": "8.2.2", "keywords": ["xml", "xmlbuilder"], "homepage": "http://github.com/oozcitak/xmlbuilder-js", "description": "An XML builder for node.js", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "contributors": [], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/oozcitak/xmlbuilder-js.git"}, "bugs": {"url": "http://github.com/oozcitak/xmlbuilder-js/issues"}, "main": "./lib/index", "engines": {"node": ">=4.0"}, "dependencies": {}, "devDependencies": {"coffee-script": "*", "mocha": "*", "coffee-coverage": "*", "istanbul": "*", "coveralls": "*"}, "scripts": {"prepublish": "coffee -co lib src", "postpublish": "rm -rf lib", "test": "mocha && istanbul report text lcov"}}