{"name": "responselike", "version": "1.0.2", "description": "A response-like object for mocking a Node.js HTTP response stream", "main": "src/index.js", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "xo": {"extends": "xo-lukechilds"}, "keywords": ["http", "https", "response", "mock", "request", "responselike"], "repository": {"type": "git", "url": "https://github.com/lukechilds/responselike.git"}, "author": "lukechilds", "license": "MIT", "devDependencies": {"ava": "^0.22.0", "coveralls": "^2.13.1", "eslint-config-xo-lukechilds": "^1.0.0", "get-stream": "^3.0.0", "nyc": "^11.1.0", "xo": "^0.19.0"}, "dependencies": {"lowercase-keys": "^1.0.0"}}