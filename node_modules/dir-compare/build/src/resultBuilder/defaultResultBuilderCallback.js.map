{"version": 3, "file": "defaultResultBuilderCallback.js", "sourceRoot": "", "sources": ["../../../src/resultBuilder/defaultResultBuilderCallback.js"], "names": [], "mappings": "AAAA,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,CAAA;AACjC,MAAM,SAAS,GAAG,OAAO,CAAC,oBAAoB,CAAC,CAAA;AAE/C,MAAM,CAAC,OAAO,GAAG,UAAU,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,qBAAqB;IAC9H,IAAI,OAAO,CAAC,SAAS,EAAE;QACnB,OAAM;KACT;IACD,OAAO,CAAC,IAAI,CAAC;QACT,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;QAC1D,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS;QAC1D,YAAY,EAAE,YAAY;QAC1B,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;QACvC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;QACvC,KAAK,EAAE,KAAK;QACZ,qBAAqB;QACrB,KAAK,EAAE,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC;QAChC,KAAK,EAAE,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC;QAChC,KAAK,EAAE,KAAK;QACZ,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;QAC5C,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;QAC5C,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;QAC7C,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;QAC7C,MAAM,EAAE,MAAM;KACjB,CAAC,CAAA;AACN,CAAC,CAAA"}