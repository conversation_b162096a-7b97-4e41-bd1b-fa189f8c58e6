{"version": 3, "file": "compareSync.js", "sourceRoot": "", "sources": ["../../../../src/fileCompareHandler/lines/compareSync.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAmB;AAEnB,mEAA2C;AAC3C,uEAAmE;AACnE,qEAAiE;AACjE,sEAAkE;AAKlE,MAAM,QAAQ,GAAG,MAAM,CAAA;AAEvB,MAAM,UAAU,GAAe;IAC3B,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC5B,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;IAC5B,IAAI,EAAE,IAAI;CACb,CAAA;AAEM,MAAM,oBAAoB,GAAoB,CAAC,KAAa,EAAE,KAAe,EAAE,KAAa,EAAE,KAAe,EAAE,OAAgB,EAAW,EAAE;;IAC/I,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAA,OAAO,CAAC,0BAA0B,mCAAI,MAAM,CAAC,SAAS,CAAC,CAAA;IAC7F,IAAI,OAA4C,CAAA;IAChD,IAAI;QACA,OAAO,GAAG,IAAI,iDAAuB,CACjC,YAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,EACvB,YAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,EACvB,UAAU,CACb,CAAA;QACD,SAAU;YACN,MAAM,UAAU,GAAG,iBAAiB,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;YACpI,MAAM,UAAU,GAAG,iBAAiB,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;YAEpI,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAA;YACpC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAA;YAEpC,MAAM,aAAa,GAAG,uCAAkB,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,CAAA;YACzE,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE;gBAC7B,OAAO,KAAK,CAAA;aACf;YACD,IAAI,aAAa,CAAC,UAAU,EAAE;gBAC1B,OAAO,aAAa,CAAC,YAAY,CAAA;aACpC;YAED,OAAO,CAAC,SAAS,CAAC,UAAU,GAAG,aAAa,CAAC,SAAS,CAAC,UAAU,CAAA;YACjE,OAAO,CAAC,SAAS,CAAC,UAAU,GAAG,aAAa,CAAC,SAAS,CAAC,UAAU,CAAA;SACpE;KACJ;YAAS;QACN,mBAAU,CAAC,cAAc,CAAC,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,GAAG,CAAC,CAAA;KACxD;AACL,CAAC,CAAA;AA9BY,QAAA,oBAAoB,wBA8BhC;AAED;;;;;;;;;;GAUG;AACH,SAAS,iBAAiB,CAAC,EAAU,EAAE,GAAW,EAAE,UAAkB,EAAE,IAAY,EAAE,SAAmB;IACrG,MAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;IACtD,OAAO,qCAAiB,CAAC,GAAG,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,CAAC,CAAA;AACpE,CAAC"}