{"version": 3, "file": "statisticsLifecycle.js", "sourceRoot": "", "sources": ["../../../src/statistics/statisticsLifecycle.js"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,CAAC,OAAO,GAAG;IACb,SAAS,CAAC,OAAO;QACb,IAAI,iBAAiB,GAAG,SAAS,CAAA;QACjC,IAAI,OAAO,CAAC,cAAc,EAAE;YACxB,iBAAiB,GAAG;gBAChB,gBAAgB,EAAE,CAAC;gBACnB,aAAa,EAAE,CAAC;gBAChB,YAAY,EAAE,CAAC;gBACf,aAAa,EAAE,CAAC;gBAChB,mBAAmB,EAAE,CAAC;gBACtB,aAAa,EAAE,CAAC;aACnB,CAAA;SACJ;QACD,MAAM,qBAAqB,GAAG;YAC1B,eAAe,EAAE,CAAC;YAClB,gBAAgB,EAAE,CAAC;YACnB,mBAAmB,EAAE,CAAC;SACzB,CAAA;QACD,MAAM,0BAA0B,GAAG;YAC/B,oBAAoB,EAAE,CAAC;YACvB,qBAAqB,EAAE,CAAC;YACxB,wBAAwB,EAAE,CAAC;SAC9B,CAAA;QACD,OAAO;YACH,QAAQ,EAAE,CAAC;YACX,KAAK,EAAE,CAAC;YACR,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,CAAC;YACR,aAAa,EAAE,CAAC;YAChB,UAAU,EAAE,CAAC;YACb,SAAS,EAAE,CAAC;YACZ,UAAU,EAAE,CAAC;YACb,YAAY,EAAE,CAAC;YACf,SAAS,EAAE,CAAC;YACZ,QAAQ,EAAE,CAAC;YACX,SAAS,EAAE,CAAC;YACZ,WAAW,EAAE,qBAAqB;YAClC,QAAQ,EAAE,iBAAiB;YAC3B,gBAAgB,EAAE,0BAA0B;YAC5C,IAAI,EAAE,SAAS;SAClB,CAAA;IACL,CAAC;IAED,kBAAkB,CAAC,UAAU,EAAE,OAAO;QAClC,UAAU,CAAC,WAAW,GAAG,UAAU,CAAC,QAAQ,GAAG,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,KAAK,CAAA;QACjF,UAAU,CAAC,gBAAgB,GAAG,UAAU,CAAC,aAAa,GAAG,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC,UAAU,CAAA;QACrG,UAAU,CAAC,eAAe,GAAG,UAAU,CAAC,YAAY,GAAG,UAAU,CAAC,QAAQ,GAAG,UAAU,CAAC,SAAS,CAAA;QACjG,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,WAAW,CAAA;QAC5D,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,gBAAgB,CAAA;QAC3E,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC,eAAe,CAAA;QACxE,MAAM,gBAAgB,GAAG,UAAU,CAAC,WAAW,CAAA;QAC/C,gBAAgB,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,eAAe,GAAG,gBAAgB,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,mBAAmB,CAAA;QAC/I,MAAM,qBAAqB,GAAG,UAAU,CAAC,gBAAgB,CAAA;QACzD,qBAAqB,CAAC,qBAAqB,GAAG,qBAAqB,CAAC,oBAAoB,GAAG,qBAAqB,CAAC,qBAAqB,GAAG,qBAAqB,CAAC,wBAAwB,CAAA;QACvL,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAA;QAEvD,IAAI,OAAO,CAAC,cAAc,EAAE;YACxB,UAAU,CAAC,QAAQ,CAAC,mBAAmB,GAAG,UAAU,CAAC,QAAQ,CAAC,gBAAgB;gBAC1E,UAAU,CAAC,QAAQ,CAAC,YAAY,GAAG,UAAU,CAAC,QAAQ,CAAC,aAAa,CAAA;YACxE,UAAU,CAAC,QAAQ,CAAC,aAAa,GAAG,UAAU,CAAC,QAAQ,CAAC,mBAAmB,GAAG,UAAU,CAAC,QAAQ,CAAC,aAAa,CAAA;SAClH;IACL,CAAC;CAEJ,CAAA"}