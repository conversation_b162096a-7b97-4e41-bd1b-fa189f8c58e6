{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AAAA,sDAAyD;AAEzD,+CAA6D;AAC7D,+DAA8C;AAC9C,yCAAqC;AAErC,yDAAoD;AACpD,6DAAyD;AAEzD,uCAAkD;AAAzC,oGAAA,QAAQ,OAAA;AAEjB,+BAWe;AATb,gGAAA,QAAQ,OAAA;AACR,8FAAA,MAAM,OAAA;AACN,kGAAA,UAAU,OAAA;AAKV,sGAAA,cAAc,OAAA;AAGhB,6CAAkE;AAAzD,6GAAA,aAAa,OAAA;AAAE,oGAAA,IAAI,OAAA;AAAE,8GAAA,cAAc,OAAA;AAgB5C,qCAAmC;AAA1B,kGAAA,OAAO,OAAA;AAWhB,6DAAsE;AAA7D,yHAAA,iBAAiB,OAAA;AAE1B,2DAAyD;AAAhD,gHAAA,cAAc,OAAA;AACvB,uDAAqD;AAA5C,oHAAA,gBAAgB,OAAA;AAEzB,6CAAwD;AAA/C,yGAAA,UAAU,OAAA;AAEnB,MAAM,eAAe,GAAG,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,yBAAyB,EAAE,QAAQ,EAAE,yBAAyB,EAAE,aAAa,CAAC,CAAC,CAAA;AAE3K,SAAgB,wBAAwB,CAAC,OAAyC;IAChF,KAAK,MAAM,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;QAC9C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,IAAK,OAAe,CAAC,UAAU,CAAC,KAAK,SAAS,EAAE,CAAC;YACnF,MAAM,IAAI,wCAAyB,CAAC,mBAAmB,UAAU,GAAG,CAAC,CAAA;QACvE,CAAC;IACH,CAAC;AACH,CAAC;AAND,4DAMC;AAED,SAAgB,KAAK,CAAC,OAAyC,EAAE,WAAqB,IAAI,mBAAQ,CAAC,OAAO,CAAC;IACzG,wBAAwB,CAAC,OAAO,CAAC,CAAA;IAEjC,MAAM,cAAc,GAAG,IAAI,+BAAc,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;IAC5D,MAAM,aAAa,GAAG,GAAG,EAAE;QACzB,kBAAG,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAA;QAC/B,QAAQ,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAA;QACnC,cAAc,CAAC,WAAW,EAAE,CAAA;IAC9B,CAAC,CAAA;IACD,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAA;IAErC,MAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,KAAK,EAAC,WAAW,EAAC,EAAE;QACxD,MAAM,qBAAqB,GAAG,MAAM,IAAA,kCAAe,EAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,WAAW,CAAC,aAAa,CAAC,qBAAqB,EAAE,uBAAuB,CAAC,CAAA;QACpJ,IAAI,qBAAqB,IAAI,IAAI,EAAE,CAAC;YAClC,MAAM,YAAY,GAAG,IAAA,8BAAO,EAAC,MAAM,OAAO,CAAC,OAAO,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;YACvF,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC;gBAC3D,OAAO,WAAW,CAAC,aAAa,CAAA;YAClC,CAAC;YAED,MAAM,qBAAqB,GAAG,MAAM,cAAc,CAAC,8BAA8B,EAAE,CAAA;YACnF,IAAI,qBAAqB,IAAI,IAAI,IAAI,qBAAqB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACxE,OAAO,WAAW,CAAC,aAAa,CAAA;YAClC,CAAC;YAED,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;gBACvC,IAAI,WAAW,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;oBACpD,kBAAG,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,EAAE,iDAAiD,CAAC,CAAA;oBAC5E,SAAQ;gBACV,CAAC;gBACD,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;gBAC3C,KAAK,MAAM,oBAAoB,IAAI,qBAAqB,EAAE,CAAC;oBACzD,cAAc,CAAC,cAAc,CAC3B,oBAAoB,EACpB;wBACE,IAAI,EAAE,WAAW;wBACjB,IAAI,EAAE,IAAI;qBACX,EACD,QAAQ,CAAC,OAAO,CACjB,CAAA;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QACD,OAAO,WAAW,CAAC,aAAa,CAAA;IAClC,CAAC,CAAC,CAAA;IAEF,OAAO,IAAA,wBAAc,EAAC,OAAO,EAAE,eAAe,CAAC,EAAE;QAC/C,IAAI,OAAqB,CAAA;QACzB,IAAI,eAAe,EAAE,CAAC;YACpB,cAAc,CAAC,WAAW,EAAE,CAAA;YAC5B,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QACjC,CAAC;aAAM,CAAC;YACN,OAAO,GAAG,cAAc,CAAC,UAAU,EAAE,CAAA;QACvC,CAAC;QAED,OAAO,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAA;IAC5E,CAAC,CAAC,CAAA;AACJ,CAAC;AAxDD,sBAwDC", "sourcesContent": ["import { executeFinally } from \"builder-util/out/promise\"\nimport { PublishOptions } from \"electron-publish/out/publisher\"\nimport { log, InvalidConfigurationError } from \"builder-util\"\nimport { asArray } from \"builder-util-runtime\"\nimport { Packager } from \"./packager\"\nimport { PackagerOptions } from \"./packagerApi\"\nimport { resolveFunction } from \"./platformPackager\"\nimport { PublishManager } from \"./publish/PublishManager\"\n\nexport { Packager, BuildResult } from \"./packager\"\nexport { PackagerOptions, ArtifactCreated, ArtifactBuildStarted } from \"./packagerApi\"\nexport {\n  TargetConfiguration,\n  Platform,\n  Target,\n  DIR_TARGET,\n  BeforeBuildContext,\n  SourceRepositoryInfo,\n  TargetSpecificOptions,\n  TargetConfigType,\n  DEFAULT_TARGET,\n  CompressionLevel,\n} from \"./core\"\nexport { getArchSuffix, Arch, archFromString } from \"builder-util\"\nexport { Configuration, AfterPackContext, MetadataDirectories } from \"./configuration\"\nexport { ElectronBrandingOptions, ElectronDownloadOptions, ElectronPlatformName } from \"./electron/ElectronFramework\"\nexport { PlatformSpecificBuildOptions, AsarOptions, FileSet, Protocol, ReleaseInfo } from \"./options/PlatformSpecificBuildOptions\"\nexport { FileAssociation } from \"./options/FileAssociation\"\nexport { MacConfiguration, DmgOptions, MasConfiguration, MacOsTargetName, DmgContent, DmgWindow, NotarizeLegacyOptions, NotarizeNotaryOptions } from \"./options/macOptions\"\nexport { PkgOptions, PkgBackgroundOptions, BackgroundAlignment, BackgroundScaling } from \"./options/pkgOptions\"\nexport { WindowsConfiguration } from \"./options/winOptions\"\nexport { AppXOptions } from \"./options/AppXOptions\"\nexport { MsiOptions } from \"./options/MsiOptions\"\nexport { MsiWrappedOptions } from \"./options/MsiWrappedOptions\"\nexport { CommonWindowsInstallerConfiguration } from \"./options/CommonWindowsInstallerConfiguration\"\nexport { NsisOptions, NsisWebOptions, PortableOptions, CommonNsisOptions } from \"./targets/nsis/nsisOptions\"\nexport { LinuxConfiguration, DebOptions, CommonLinuxOptions, LinuxTargetSpecificOptions, AppImageOptions, FlatpakOptions } from \"./options/linuxOptions\"\nexport { SnapOptions, PlugDescriptor, SlotDescriptor } from \"./options/SnapOptions\"\nexport { Metadata, AuthorMetadata, RepositoryInfo } from \"./options/metadata\"\nexport { AppInfo } from \"./appInfo\"\nexport { SquirrelWindowsOptions } from \"./options/SquirrelWindowsOptions\"\nexport { CustomMacSign, CustomMacSignOptions } from \"./macPackager\"\nexport {\n  WindowsSignOptions,\n  CustomWindowsSignTaskConfiguration,\n  WindowsSignTaskConfiguration,\n  CustomWindowsSign,\n  FileCodeSigningInfo,\n  CertificateFromStoreInfo,\n} from \"./codeSign/windowsCodeSign\"\nexport { CancellationToken, ProgressInfo } from \"builder-util-runtime\"\nexport { PublishOptions, UploadTask } from \"electron-publish\"\nexport { PublishManager } from \"./publish/PublishManager\"\nexport { PlatformPackager } from \"./platformPackager\"\nexport { Framework, PrepareApplicationStageDirectoryOptions } from \"./Framework\"\nexport { buildForge, ForgeOptions } from \"./forge-maker\"\n\nconst expectedOptions = new Set([\"publish\", \"targets\", \"mac\", \"win\", \"linux\", \"projectDir\", \"platformPackagerFactory\", \"config\", \"effectiveOptionComputed\", \"prepackaged\"])\n\nexport function checkBuildRequestOptions(options: PackagerOptions & PublishOptions) {\n  for (const optionName of Object.keys(options)) {\n    if (!expectedOptions.has(optionName) && (options as any)[optionName] !== undefined) {\n      throw new InvalidConfigurationError(`Unknown option \"${optionName}\"`)\n    }\n  }\n}\n\nexport function build(options: PackagerOptions & PublishOptions, packager: Packager = new Packager(options)): Promise<Array<string>> {\n  checkBuildRequestOptions(options)\n\n  const publishManager = new PublishManager(packager, options)\n  const sigIntHandler = () => {\n    log.warn(\"cancelled by SIGINT\")\n    packager.cancellationToken.cancel()\n    publishManager.cancelTasks()\n  }\n  process.once(\"SIGINT\", sigIntHandler)\n\n  const promise = packager.build().then(async buildResult => {\n    const afterAllArtifactBuild = await resolveFunction(packager.appInfo.type, buildResult.configuration.afterAllArtifactBuild, \"afterAllArtifactBuild\")\n    if (afterAllArtifactBuild != null) {\n      const newArtifacts = asArray(await Promise.resolve(afterAllArtifactBuild(buildResult)))\n      if (newArtifacts.length === 0 || !publishManager.isPublish) {\n        return buildResult.artifactPaths\n      }\n\n      const publishConfigurations = await publishManager.getGlobalPublishConfigurations()\n      if (publishConfigurations == null || publishConfigurations.length === 0) {\n        return buildResult.artifactPaths\n      }\n\n      for (const newArtifact of newArtifacts) {\n        if (buildResult.artifactPaths.includes(newArtifact)) {\n          log.warn({ newArtifact }, \"skipping publish of artifact, already published\")\n          continue\n        }\n        buildResult.artifactPaths.push(newArtifact)\n        for (const publishConfiguration of publishConfigurations) {\n          publishManager.scheduleUpload(\n            publishConfiguration,\n            {\n              file: newArtifact,\n              arch: null,\n            },\n            packager.appInfo\n          )\n        }\n      }\n    }\n    return buildResult.artifactPaths\n  })\n\n  return executeFinally(promise, isErrorOccurred => {\n    let promise: Promise<any>\n    if (isErrorOccurred) {\n      publishManager.cancelTasks()\n      promise = Promise.resolve(null)\n    } else {\n      promise = publishManager.awaitTasks()\n    }\n\n    return promise.then(() => process.removeListener(\"SIGINT\", sigIntHandler))\n  })\n}\n"]}