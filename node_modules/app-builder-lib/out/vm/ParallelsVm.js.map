{"version": 3, "file": "ParallelsVm.js", "sourceRoot": "", "sources": ["../../src/vm/ParallelsVm.ts"], "names": [], "mappings": ";;;AAAA,+CAA+E;AAC/E,iDAA2E;AAC3E,6BAAgC;AAEhC,gBAAgB;AACT,KAAK,UAAU,WAAW,CAAC,WAAwB;IACxD,oEAAoE;IACpE,IAAI,OAAO,GAAG,MAAM,IAAA,mBAAI,EAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,EAAE,SAAS,EAAE,KAAK,CAAC,CAAA;IAClF,WAAW,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAA;IAE1C,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAA;IAEnD,kCAAkC;IAClC,MAAM,MAAM,GAAuB,EAAE,CAAA;IACrC,KAAK,MAAM,IAAI,IAAI,OAAO;SACvB,KAAK,CAAC,MAAM,CAAC;SACb,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;SACpB,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC;QAC/B,MAAM,EAAE,GAAQ,EAAE,CAAA;QAClB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;YACpC,MAAM,IAAI,GAAG,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC3C,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;gBACjB,SAAQ;YACV,CAAC;YAED,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAA;YACjC,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,MAAM,IAAI,GAAG,KAAK,OAAO,IAAI,GAAG,KAAK,MAAM,EAAE,CAAC;gBACxF,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAA;YAC1B,CAAC;QACH,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;IACjB,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AA5BD,kCA4BC;AAED,gBAAgB;AAChB,MAAa,kBAAmB,SAAQ,cAAS;IAK/C,YAA6B,EAAe;QAC1C,KAAK,EAAE,CAAA;QADoB,OAAE,GAAF,EAAE,CAAa;QAFpC,oBAAe,GAAG,KAAK,CAAA;QAK7B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;IACtC,CAAC;IAED,IAAI,OAAO;QACT,OAAO,GAAG,CAAA;IACZ,CAAC;IAEO,kBAAkB,CAAC,KAAY;QACrC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,oDAAoD,CAAC,EAAE,CAAC;YACjF,MAAM,IAAI,KAAK,CAAC,0CAA0C,IAAI,CAAC,EAAE,CAAC,IAAI,qFAAqF,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;QAC7K,CAAC;QAED,kBAAG,CAAC,IAAI,CAAC,8EAA8E,CAAC,CAAA;QACxF,MAAM,KAAK,CAAA;IACb,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,IAAY,EAAE,IAAmB,EAAE,OAAyB;QACrE,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAA;QAChC,qGAAqG;QACrG,OAAO,MAAM,IAAA,mBAAI,EAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,gBAAgB,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CACrK,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAC/B,CAAA;IACH,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,IAAY,EAAE,IAAmB,EAAE,OAAsB,EAAE,YAAgC;QACrG,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAA;QAChC,OAAO,MAAM,IAAA,oBAAK,EAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAA;IAC7I,CAAC;IAEO,KAAK,CAAC,SAAS;QACrB,MAAM,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,CAAA;QACvB,MAAM,KAAK,GAAG,IAAI,CAAC,EAAE,CAAC,KAAK,CAAA;QAC3B,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;YACxB,OAAM;QACR,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAA;YAC3B,8DAA8D;YAC9D,OAAO,CAAC,iBAAiB,CAAC,CAAC,CAAC,QAA6B,EAAE,EAAE;gBAC3D,MAAM,QAAQ,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;gBAClC,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;oBACrB,IAAA,4BAAY,EAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;gBAClC,CAAC;qBAAM,CAAC;oBACN,IAAA,mBAAI,EAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;gBACzD,CAAC;YACH,CAAC,CAAC,CAAA;QACJ,CAAC;QACD,MAAM,IAAA,mBAAI,EAAC,QAAQ,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAA;IACvC,CAAC;IAEO,mBAAmB;QACzB,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY,CAAA;QACpC,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;YACzB,YAAY,GAAG,IAAI,CAAC,SAAS,EAAE,CAAA;YAC/B,IAAI,CAAC,YAAY,GAAG,YAAY,CAAA;QAClC,CAAC;QACD,OAAO,YAAY,CAAA;IACrB,CAAC;IAED,QAAQ,CAAC,IAAY;QACnB,iHAAiH;QACjH,OAAO,yBAAyB,CAAC,IAAI,CAAC,CAAA;IACxC,CAAC;CACF;AAxED,gDAwEC;AAED,SAAgB,yBAAyB,CAAC,IAAY;IACpD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;QAC5B,OAAO,IAAI,CAAA;IACb,CAAC;IACD,OAAO,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;AACtD,CAAC;AALD,8DAKC", "sourcesContent": ["import { exec, spawn, DebugLogger, ExtraSpawnOptions, log } from \"builder-util\"\nimport { SpawnOptions, execFileSync, ExecFileOptions } from \"child_process\"\nimport { VmManager } from \"./vm\"\n\n/** @internal */\nexport async function parseVmList(debugLogger: DebugLogger) {\n  // do not log output if debug - it is huge, logged using debugLogger\n  let rawList = await exec(\"prlctl\", [\"list\", \"-i\", \"-s\", \"name\"], undefined, false)\n  debugLogger.add(\"parallels.list\", rawList)\n\n  rawList = rawList.substring(rawList.indexOf(\"ID:\"))\n\n  // let match: Array<string> | null\n  const result: Array<ParallelsVm> = []\n  for (const info of rawList\n    .split(\"\\n\\n\")\n    .map(it => it.trim())\n    .filter(it => it.length > 0)) {\n    const vm: any = {}\n    for (const line of info.split(\"\\n\")) {\n      const meta = /^([^:(\"]+): (.*)$/.exec(line)\n      if (meta == null) {\n        continue\n      }\n\n      const key = meta[1].toLowerCase()\n      if (key === \"id\" || key === \"os\" || key === \"name\" || key === \"state\" || key === \"name\") {\n        vm[key] = meta[2].trim()\n      }\n    }\n    result.push(vm)\n  }\n  return result\n}\n\n/** @internal */\nexport class ParallelsVmManager extends VmManager {\n  private startPromise: Promise<any>\n\n  private isExitHookAdded = false\n\n  constructor(private readonly vm: ParallelsVm) {\n    super()\n\n    this.startPromise = this.doStartVm()\n  }\n\n  get pathSep(): string {\n    return \"/\"\n  }\n\n  private handleExecuteError(error: Error): any {\n    if (error.message.includes(\"Unable to open new session in this virtual machine\")) {\n      throw new Error(`Please ensure that your are logged in \"${this.vm.name}\" parallels virtual machine. In the future please do not stop VM, but suspend.\\n\\n${error.message}`)\n    }\n\n    log.warn(\"ensure that 'Share folders' is set to 'All Disks', see https://goo.gl/E6XphP\")\n    throw error\n  }\n\n  async exec(file: string, args: Array<string>, options?: ExecFileOptions): Promise<string> {\n    await this.ensureThatVmStarted()\n    // it is important to use \"--current-user\" to execute command under logged in user - to access certs.\n    return await exec(\"prlctl\", [\"exec\", this.vm.id, \"--current-user\", file.startsWith(\"/\") ? macPathToParallelsWindows(file) : file].concat(args), options).catch(error =>\n      this.handleExecuteError(error)\n    )\n  }\n\n  async spawn(file: string, args: Array<string>, options?: SpawnOptions, extraOptions?: ExtraSpawnOptions): Promise<any> {\n    await this.ensureThatVmStarted()\n    return await spawn(\"prlctl\", [\"exec\", this.vm.id, file].concat(args), options, extraOptions).catch(error => this.handleExecuteError(error))\n  }\n\n  private async doStartVm() {\n    const vmId = this.vm.id\n    const state = this.vm.state\n    if (state === \"running\") {\n      return\n    }\n\n    if (!this.isExitHookAdded) {\n      this.isExitHookAdded = true\n      // eslint-disable-next-line @typescript-eslint/no-var-requires\n      require(\"async-exit-hook\")((callback: (() => void) | null) => {\n        const stopArgs = [\"suspend\", vmId]\n        if (callback == null) {\n          execFileSync(\"prlctl\", stopArgs)\n        } else {\n          exec(\"prlctl\", stopArgs).then(callback).catch(callback)\n        }\n      })\n    }\n    await exec(\"prlctl\", [\"start\", vmId])\n  }\n\n  private ensureThatVmStarted() {\n    let startPromise = this.startPromise\n    if (startPromise == null) {\n      startPromise = this.doStartVm()\n      this.startPromise = startPromise\n    }\n    return startPromise\n  }\n\n  toVmFile(file: string): string {\n    // https://stackoverflow.com/questions/4742992/cannot-access-network-drive-in-powershell-running-as-administrator\n    return macPathToParallelsWindows(file)\n  }\n}\n\nexport function macPathToParallelsWindows(file: string) {\n  if (file.startsWith(\"C:\\\\\")) {\n    return file\n  }\n  return \"\\\\\\\\Mac\\\\Host\\\\\" + file.replace(/\\//g, \"\\\\\")\n}\n\nexport interface ParallelsVm {\n  id: string\n  name: string\n  os: \"win-10\" | \"win-11\" | \"ubuntu\" | \"elementary\"\n  state: \"running\" | \"suspended\" | \"stopped\"\n}\n"]}