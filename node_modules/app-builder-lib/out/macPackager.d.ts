import { Arch, AsyncTaskManager } from "builder-util";
import { SignOptions } from "@electron/osx-sign/dist/cjs/types";
import { Lazy } from "lazy-val";
import { AppInfo } from "./appInfo";
import { CodeSigningInfo, Identity } from "./codeSign/macCodeSign";
import { Target } from "./core";
import { AfterPackContext, ElectronPlatformName } from "./index";
import { MacConfiguration } from "./options/macOptions";
import { Packager } from "./packager";
import { PlatformPackager } from "./platformPackager";
export type CustomMacSignOptions = SignOptions;
export type CustomMacSign = (configuration: CustomMacSignOptions, packager: MacPackager) => Promise<void>;
export default class MacPackager extends PlatformPackager<MacConfiguration> {
    readonly codeSigningInfo: Lazy<CodeSigningInfo>;
    private _iconPath;
    constructor(info: Packager);
    get defaultTarget(): Array<string>;
    protected prepareAppInfo(appInfo: AppInfo): AppInfo;
    getIconPath(): Promise<string | null>;
    createTargets(targets: Array<string>, mapper: (name: string, factory: (outDir: string) => Target) => void): void;
    protected doPack(outDir: string, appOutDir: string, platformName: ElectronPlatformName, arch: Arch, platformSpecificBuildOptions: MacConfiguration, targets: Array<Target>): Promise<any>;
    pack(outDir: string, arch: Arch, targets: Array<Target>, taskManager: AsyncTaskManager): Promise<void>;
    private sign;
    private getOptionsForFile;
    protected doSign(opts: SignOptions, customSignOptions: MacConfiguration): Promise<void>;
    protected doFlat(appPath: string, outFile: string, identity: Identity, keychain: string | null | undefined): Promise<any>;
    getElectronSrcDir(dist: string): string;
    getElectronDestinationDir(appOutDir: string): string;
    applyCommonInfo(appPlist: any, contentsPath: string): Promise<void>;
    protected signApp(packContext: AfterPackContext, isAsar: boolean): Promise<boolean>;
    private notarizeIfProvided;
    private getNotarizeOptions;
    private generateNotarizeOptions;
}
