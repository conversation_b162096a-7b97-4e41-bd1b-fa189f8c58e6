{"version": 3, "file": "NodeModuleCopyHelper.js", "sourceRoot": "", "sources": ["../../src/util/NodeModuleCopyHelper.ts"], "names": [], "mappings": ";;;AAAA,+CAA0C;AAC1C,4CAAiD;AACjD,uCAAyC;AACzC,6BAA4B;AAC5B,gDAA2D;AAE3D,0DAAqD;AACrD,mDAAgD;AAEhD,MAAM,aAAa,GAAG,IAAI,GAAG,CAC3B,CAAC,WAAW,EAAE,cAAc,CAAC,0BAA0B,EAAE,cAAc,EAAE,WAAW,EAAE,cAAc,EAAE,cAAc,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,CAAC,CAAC,MAAM,CACpK,2BAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CACzB,CACF,CAAA;AACD,MAAM,qBAAqB,GAAG,IAAI,GAAG,CAAC;IACpC,eAAe;IACf,gBAAgB;IAChB,WAAW;IACX,iBAAiB;IACjB,QAAQ;IACR,WAAW;IACX,WAAW;IACX,QAAQ;IACR,QAAQ;IACR,MAAM;IACN,OAAO;IACP,WAAW;IACX,cAAc;IACd,SAAS;IACT,UAAU;IACV,MAAM;CACP,CAAC,CAAA;AAEF,gBAAgB;AAChB,MAAa,oBAAqB,SAAQ,8BAAc;IACtD,YAAY,OAAoB,EAAE,QAAkB;QAClD,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,QAAQ,CAAC,CAAA;IAC7E,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,OAAe,EAAE,WAA6B,EAAE,sBAAqC;QAC5G,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAE9B,MAAM,gBAAgB,GAAG,MAAM,IAAA,kCAAe,EAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAA;QAErI,MAAM,MAAM,GAAkB,EAAE,CAAA;QAChC,MAAM,KAAK,GAAkB,EAAE,CAAA;QAC/B,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACrC,MAAM,OAAO,GAAG,OAAO,GAAG,IAAI,CAAC,GAAG,GAAG,UAAU,CAAA;YAC/C,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA;YAChB,6FAA6F;YAC7F,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;YACvC,KAAK,CAAC,CAAC,CAAC,GAAG,OAAO,CAAA;YAElB,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxB,MAAM,OAAO,GAAG,KAAK,CAAC,GAAG,EAAG,CAAA;gBAE5B,MAAM,UAAU,GAAG,MAAM,IAAA,kBAAO,EAAC,OAAO,CAAC,CAAA;gBACzC,UAAU,CAAC,IAAI,EAAE,CAAA;gBAEjB,MAAM,UAAU,GAAG,OAAO,KAAK,OAAO,CAAA;gBACtC,MAAM,IAAI,GAAkB,EAAE,CAAA;gBAC9B,mHAAmH;gBACnH,MAAM,eAAe,GAAG,MAAM,sBAAe,CAAC,GAAG,CAC/C,UAAU,EACV,IAAI,CAAC,EAAE;oBACL,MAAM,QAAQ,GAAG,OAAO,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAA;oBAE1C,MAAM,aAAa,GAAG,gBAAgB,IAAI,IAAI,IAAI,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAA;oBAE9E,IAAI,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;wBACrD,OAAO,IAAI,CAAA;oBACb,CAAC;oBAED,IAAI,CAAC,aAAa,EAAE,CAAC;wBACnB,KAAK,MAAM,GAAG,IAAI,sBAAsB,EAAE,CAAC;4BACzC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gCACvB,OAAO,IAAI,CAAA;4BACb,CAAC;wBACH,CAAC;wBAED,uCAAuC;wBACvC,IAAI,UAAU,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,KAAK,YAAY,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;4BAChJ,OAAO,IAAI,CAAA;wBACb,CAAC;wBAED,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;4BAC9B,IAAI,IAAI,KAAK,cAAc,IAAI,IAAI,KAAK,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gCACnI,OAAO,IAAI,CAAA;4BACb,CAAC;wBACH,CAAC;6BAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,YAAY,CAAC,EAAE,CAAC;4BACtF,OAAO,IAAI,CAAA;wBACb,CAAC;6BAAM,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC;4BACjG,OAAO,IAAI,CAAA;wBACb,CAAC;6BAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,MAAM,CAAC,EAAE,CAAC;4BACpF,OAAO,IAAI,CAAA;wBACb,CAAC;oBACH,CAAC;oBAED,OAAO,IAAA,gBAAK,EAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;wBACjC,IAAI,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;4BAC9C,OAAO,IAAI,CAAA;wBACb,CAAC;wBAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;4BACxB,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;wBAC9B,CAAC;wBACD,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,OAAO,EAAE,IAAI,CAAC,CAAA;wBAC/D,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;4BAC3B,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;gCACvB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gCACf,OAAO,IAAI,CAAA;4BACb,CAAC;iCAAM,CAAC;gCACN,OAAO,QAAQ,CAAA;4BACjB,CAAC;wBACH,CAAC;6BAAM,CAAC;4BACN,OAAO,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;gCAC9B,uDAAuD;gCACvD,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC;oCAC3C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;oCACf,OAAO,IAAI,CAAA;gCACb,CAAC;qCAAM,CAAC;oCACN,OAAO,QAAQ,CAAA;gCACjB,CAAC;4BACH,CAAC,CAAC,CAAA;wBACJ,CAAC;oBACH,CAAC,CAAC,CAAA;gBACJ,CAAC,EACD,gBAAW,CACZ,CAAA;gBAED,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;oBACpC,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;wBAClB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;oBACpB,CAAC;gBACH,CAAC;gBAED,IAAI,CAAC,IAAI,EAAE,CAAA;gBACX,KAAK,MAAM,KAAK,IAAI,IAAI,EAAE,CAAC;oBACzB,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,CAAA;gBACxC,CAAC;YACH,CAAC;QACH,CAAC;QACD,OAAO,MAAM,CAAA;IACf,CAAC;CACF;AA/GD,oDA+GC", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { CONCURRENCY } from \"builder-util/out/fs\"\nimport { lstat, readdir } from \"fs-extra\"\nimport * as path from \"path\"\nimport { excludedNames, FileMatcher } from \"../fileMatcher\"\nimport { Packager } from \"../packager\"\nimport { resolveFunction } from \"../platformPackager\"\nimport { FileCopyHelper } from \"./AppFileWalker\"\n\nconst excludedFiles = new Set(\n  [\".DS_Store\", \"node_modules\" /* already in the queue */, \"CHANGELOG.md\", \"ChangeLog\", \"changelog.md\", \"Changelog.md\", \"Changelog\", \"binding.gyp\", \".npmignore\"].concat(\n    excludedNames.split(\",\")\n  )\n)\nconst topLevelExcludedFiles = new Set([\n  \"karma.conf.js\",\n  \".coveralls.yml\",\n  \"README.md\",\n  \"readme.markdown\",\n  \"README\",\n  \"readme.md\",\n  \"Readme.md\",\n  \"Readme\",\n  \"readme\",\n  \"test\",\n  \"tests\",\n  \"__tests__\",\n  \"powered-test\",\n  \"example\",\n  \"examples\",\n  \".bin\",\n])\n\n/** @internal */\nexport class NodeModuleCopyHelper extends FileCopyHelper {\n  constructor(matcher: FileMatcher, packager: Packager) {\n    super(matcher, matcher.isEmpty() ? null : matcher.createFilter(), packager)\n  }\n\n  async collectNodeModules(baseDir: string, moduleNames: Iterable<string>, nodeModuleExcludedExts: Array<string>): Promise<Array<string>> {\n    const filter = this.filter\n    const metadata = this.metadata\n\n    const onNodeModuleFile = await resolveFunction(this.packager.appInfo.type, this.packager.config.onNodeModuleFile, \"onNodeModuleFile\")\n\n    const result: Array<string> = []\n    const queue: Array<string> = []\n    for (const moduleName of moduleNames) {\n      const tmpPath = baseDir + path.sep + moduleName\n      queue.length = 1\n      // The path should be corrected in Windows that when the moduleName is Scoped packages named.\n      const depPath = path.normalize(tmpPath)\n      queue[0] = depPath\n\n      while (queue.length > 0) {\n        const dirPath = queue.pop()!\n\n        const childNames = await readdir(dirPath)\n        childNames.sort()\n\n        const isTopLevel = dirPath === depPath\n        const dirs: Array<string> = []\n        // our handler is async, but we should add sorted files, so, we add file to result not in the mapper, but after map\n        const sortedFilePaths = await BluebirdPromise.map(\n          childNames,\n          name => {\n            const filePath = dirPath + path.sep + name\n\n            const forceIncluded = onNodeModuleFile != null && !!onNodeModuleFile(filePath)\n\n            if (excludedFiles.has(name) || name.startsWith(\"._\")) {\n              return null\n            }\n\n            if (!forceIncluded) {\n              for (const ext of nodeModuleExcludedExts) {\n                if (name.endsWith(ext)) {\n                  return null\n                }\n              }\n\n              // noinspection SpellCheckingInspection\n              if (isTopLevel && (topLevelExcludedFiles.has(name) || (moduleName === \"libui-node\" && (name === \"build\" || name === \"docs\" || name === \"src\")))) {\n                return null\n              }\n\n              if (dirPath.endsWith(\"build\")) {\n                if (name === \"gyp-mac-tool\" || name === \"Makefile\" || name.endsWith(\".mk\") || name.endsWith(\".gypi\") || name.endsWith(\".Makefile\")) {\n                  return null\n                }\n              } else if (dirPath.endsWith(\"Release\") && (name === \".deps\" || name === \"obj.target\")) {\n                return null\n              } else if (name === \"src\" && (dirPath.endsWith(\"keytar\") || dirPath.endsWith(\"keytar-prebuild\"))) {\n                return null\n              } else if (dirPath.endsWith(\"lzma-native\") && (name === \"build\" || name === \"deps\")) {\n                return null\n              }\n            }\n\n            return lstat(filePath).then(stat => {\n              if (filter != null && !filter(filePath, stat)) {\n                return null\n              }\n\n              if (!stat.isDirectory()) {\n                metadata.set(filePath, stat)\n              }\n              const consumerResult = this.handleFile(filePath, dirPath, stat)\n              if (consumerResult == null) {\n                if (stat.isDirectory()) {\n                  dirs.push(name)\n                  return null\n                } else {\n                  return filePath\n                }\n              } else {\n                return consumerResult.then(it => {\n                  // asarUtil can return modified stat (symlink handling)\n                  if ((it == null ? stat : it).isDirectory()) {\n                    dirs.push(name)\n                    return null\n                  } else {\n                    return filePath\n                  }\n                })\n              }\n            })\n          },\n          CONCURRENCY\n        )\n\n        for (const child of sortedFilePaths) {\n          if (child != null) {\n            result.push(child)\n          }\n        }\n\n        dirs.sort()\n        for (const child of dirs) {\n          queue.push(dirPath + path.sep + child)\n        }\n      }\n    }\n    return result\n  }\n}\n"]}