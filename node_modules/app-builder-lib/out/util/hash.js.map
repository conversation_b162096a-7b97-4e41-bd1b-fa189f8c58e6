{"version": 3, "file": "hash.js", "sourceRoot": "", "sources": ["../../src/util/hash.ts"], "names": [], "mappings": ";;;AAAA,mCAAmC;AACnC,2BAAqC;AAErC,SAAgB,QAAQ,CAAC,IAAY,EAAE,SAAS,GAAG,QAAQ,EAAE,WAA6B,QAAQ,EAAE,OAAa;IAC/G,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC7C,MAAM,IAAI,GAAG,IAAA,mBAAU,EAAC,SAAS,CAAC,CAAA;QAClC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAA;QAE9C,IAAA,qBAAgB,EAAC,IAAI,EAAE,EAAE,GAAG,OAAO,EAAE,aAAa,EAAE,IAAI,GAAG,IAAI,CAAC,+CAA+C,EAAE,CAAC;aAC/G,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;aACnB,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YACd,IAAI,CAAC,GAAG,EAAE,CAAA;YACV,OAAO,CAAC,IAAI,CAAC,IAAI,EAAY,CAAC,CAAA;QAChC,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAA;IAC/B,CAAC,CAAC,CAAA;AACJ,CAAC;AAbD,4BAaC", "sourcesContent": ["import { createHash } from \"crypto\"\nimport { createReadStream } from \"fs\"\n\nexport function hashFile(file: string, algorithm = \"sha512\", encoding: \"base64\" | \"hex\" = \"base64\", options?: any) {\n  return new Promise<string>((resolve, reject) => {\n    const hash = createHash(algorithm)\n    hash.on(\"error\", reject).setEncoding(encoding)\n\n    createReadStream(file, { ...options, highWaterMark: 1024 * 1024 /* better to use more memory but hash faster */ })\n      .on(\"error\", reject)\n      .on(\"end\", () => {\n        hash.end()\n        resolve(hash.read() as string)\n      })\n      .pipe(hash, { end: false })\n  })\n}\n"]}