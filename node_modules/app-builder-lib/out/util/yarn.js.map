{"version": 3, "file": "yarn.js", "sourceRoot": "", "sources": ["../../src/util/yarn.ts"], "names": [], "mappings": ";;;AAAA,+CAAkD;AAClD,uCAAqC;AAErC,2BAA4B;AAC5B,6BAA4B;AAE5B,6CAA4D;AAGrD,KAAK,UAAU,gBAAgB,CAAC,MAAqB,EAAE,MAAc,EAAE,OAAuB,EAAE,YAAY,GAAG,KAAK;IACzH,MAAM,gBAAgB,GAAG;QACvB,eAAe,EAAE,MAAM,CAAC,2BAA2B,KAAK,IAAI;QAC5D,cAAc,EAAE,IAAA,sBAAO,EAAC,MAAM,CAAC,OAAO,CAAC;QACvC,GAAG,OAAO;KACX,CAAA;IACD,IAAI,uBAAuB,GAAG,KAAK,CAAA;IAEnC,KAAK,MAAM,SAAS,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,EAAE,CAAC;QACpD,IAAI,MAAM,IAAA,qBAAU,EAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC;YACnD,uBAAuB,GAAG,IAAI,CAAA;YAE9B,MAAK;QACP,CAAC;IACH,CAAC;IAED,IAAI,YAAY,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC7C,MAAM,mBAAmB,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAA;IACrD,CAAC;SAAM,CAAC;QACN,MAAM,OAAO,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAA;IACzC,CAAC;AACH,CAAC;AArBD,4CAqBC;AAOD,SAAS,sBAAsB;IAC7B,OAAO,IAAI,CAAC,IAAI,CAAC,IAAA,YAAO,GAAE,EAAE,eAAe,CAAC,CAAA;AAC9C,CAAC;AAED,SAAgB,SAAS,CAAC,aAAmC,EAAE,QAAyB,EAAE,IAAY,EAAE,eAAwB;IAC9H,MAAM,aAAa,GAAG,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAA;IACtD,MAAM,MAAM,GAAQ;QAClB,GAAG,OAAO,CAAC,GAAG;QACd,eAAe,EAAE,aAAa;QAC9B,sBAAsB,EAAE,aAAa;QACrC,mBAAmB,EAAE,QAAQ;QAC7B,4BAA4B,EAAE,eAAe;QAC7C,4BAA4B;QAC5B,0BAA0B,EAAE,QAAQ;QACpC,wBAAwB,EAAE,IAAI;QAC9B,4BAA4B,EAAE,IAAI;KACnC,CAAA;IAED,IAAI,QAAQ,KAAK,OAAO,CAAC,QAAQ,EAAE,CAAC;QAClC,MAAM,CAAC,gBAAgB,GAAG,MAAM,CAAA;IAClC,CAAC;IACD,IAAI,QAAQ,KAAK,OAAO,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;QAClD,MAAM,CAAC,sBAAsB,GAAG,SAAS,CAAA;IAC3C,CAAC;IAED,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;QACjC,OAAO,MAAM,CAAA;IACf,CAAC;IAED,+CAA+C;IAC/C,OAAO;QACL,GAAG,MAAM;QACT,kBAAkB,EAAE,gCAAgC;QACpD,iBAAiB,EAAE,aAAa,CAAC,OAAO;QACxC,kBAAkB,EAAE,UAAU;QAC9B,iBAAiB,EAAE,sBAAsB,EAAE;KAC5C,CAAA;AACH,CAAC;AAjCD,8BAiCC;AAED,SAAS,cAAc;;IACrB,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,IAAI,EAAE,CAAA;IAC/D,MAAM,KAAK,GAAG,iBAAiB,CAAA;IAE/B,MAAM,gBAAgB,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;IACjD,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAA,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAG,CAAC,CAAC,mCAAI,CAAC,CAAC,CAAA;IAC3D,OAAO,gBAAgB,IAAI,CAAC,CAAA;AAC9B,CAAC;AAED,SAAS,mBAAmB,CAAC,MAAc,EAAE,OAAuB;IAClE,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAA;IACrD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAA;IACzC,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,CAAA;IAE7C,kBAAG,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,oCAAoC,CAAC,CAAA;IAC1E,IAAI,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,CAAA;IACjE,MAAM,QAAQ,GAAG,CAAC,SAAS,CAAC,CAAA;IAC5B,MAAM,WAAW,GAAG,cAAc,EAAE,CAAA;IACpC,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,KAAK,MAAM,EAAE,CAAC;YAC5C,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;QACjC,CAAC;QACD,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;IAC/B,CAAC;IAED,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC7B,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;IACnC,CAAC;IAED,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;QACrB,QAAQ,GAAG,kBAAkB,EAAE,CAAA;IACjC,CAAC;SAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QACxB,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QAC1B,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,MAAM,CAAA;IAC5E,CAAC;IAED,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;QAC3B,QAAQ,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAA;IAClC,CAAC;IACD,OAAO,IAAA,oBAAK,EAAC,QAAQ,EAAE,QAAQ,EAAE;QAC/B,GAAG,EAAE,MAAM;QACX,GAAG,EAAE,SAAS,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,eAAe,KAAK,IAAI,CAAC;KACxF,CAAC,CAAA;AACJ,CAAC;AAEM,KAAK,UAAU,cAAc,CAAC,QAAyB,EAAE,IAAY,EAAE,aAAmC;IAC/G,kBAAG,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,4BAA4B,CAAC,CAAA;IAC1D,6CAA6C;IAC7C,MAAM,OAAO,GAAG,WAAW,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAA;IACvE,MAAM,IAAI,GAAG,CAAC,SAAS,CAAC,CAAA;IACxB,wEAAwE;IACxE,oEAAoE;IACpE,oCAAoC;IACpC,wDAAwD;IACxD,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,aAAa,CAAC,OAAO;SACzC,KAAK,CAAC,GAAG,CAAC;SACV,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;SACX,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;IAC5B,IAAI,KAAK,IAAI,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC;QAC9E,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAA;IACrC,CAAC;IACD,MAAM,IAAA,oBAAK,EAAC,OAAO,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,SAAS,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;AACrF,CAAC;AAjBD,wCAiBC;AAED,SAAS,kBAAkB;IACzB,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,MAAM,EAAE,CAAC;QACtC,OAAO,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAA;IAC3D,CAAC;SAAM,CAAC;QACN,OAAO,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAA;IACzD,CAAC;AACH,CAAC;AAED,SAAS,aAAa,CAAC,QAAmC;IACxD,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAA;IACnD,OAAO,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,MAAM,IAAI,CAAC,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAA;AACnK,CAAC;AAcD,gBAAgB;AACT,KAAK,UAAU,OAAO,CAAC,MAAc,EAAE,OAAuB;IACnE,MAAM,aAAa,GAAQ;QACzB,YAAY,EAAE,MAAM,OAAO,CAAC,cAAc,CAAC,KAAK;QAChD,YAAY,EAAE,OAAO,CAAC,QAAQ;QAC9B,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ;QAC9C,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI;QAClC,cAAc,EAAE,OAAO,CAAC,cAAc;QACtC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU;QAC5D,eAAe,EAAE,OAAO,CAAC,eAAe,KAAK,IAAI;KAClD,CAAA;IAED,MAAM,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC,QAAQ,EAAE,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,eAAe,KAAK,IAAI,CAAC,CAAA;IAC1H,MAAM,IAAA,0CAA6B,EAAC,CAAC,sBAAsB,CAAC,EAAE,aAAa,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,CAAA;AACpG,CAAC;AAbD,0BAaC", "sourcesContent": ["import { asArray, log, spawn } from \"builder-util\"\nimport { pathExists } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport { homedir } from \"os\"\nimport * as path from \"path\"\nimport { Configuration } from \"../configuration\"\nimport { executeAppBuilderAndWriteJson } from \"./appBuilder\"\nimport { NodeModuleDirInfo } from \"./packageDependencies\"\n\nexport async function installOrRebuild(config: Configuration, appDir: string, options: RebuildOptions, forceInstall = false) {\n  const effectiveOptions = {\n    buildFromSource: config.buildDependenciesFromSource === true,\n    additionalArgs: asArray(config.npmArgs),\n    ...options,\n  }\n  let isDependenciesInstalled = false\n\n  for (const fileOrDir of [\"node_modules\", \".pnp.js\"]) {\n    if (await pathExists(path.join(appDir, fileOrDir))) {\n      isDependenciesInstalled = true\n\n      break\n    }\n  }\n\n  if (forceInstall || !isDependenciesInstalled) {\n    await installDependencies(appDir, effectiveOptions)\n  } else {\n    await rebuild(appDir, effectiveOptions)\n  }\n}\n\nexport interface DesktopFrameworkInfo {\n  version: string\n  useCustomDist: boolean\n}\n\nfunction getElectronGypCacheDir() {\n  return path.join(homedir(), \".electron-gyp\")\n}\n\nexport function getGypEnv(frameworkInfo: DesktopFrameworkInfo, platform: NodeJS.Platform, arch: string, buildFromSource: boolean) {\n  const npmConfigArch = arch === \"armv7l\" ? \"arm\" : arch\n  const common: any = {\n    ...process.env,\n    npm_config_arch: npmConfigArch,\n    npm_config_target_arch: npmConfigArch,\n    npm_config_platform: platform,\n    npm_config_build_from_source: buildFromSource,\n    // required for node-pre-gyp\n    npm_config_target_platform: platform,\n    npm_config_update_binary: true,\n    npm_config_fallback_to_build: true,\n  }\n\n  if (platform !== process.platform) {\n    common.npm_config_force = \"true\"\n  }\n  if (platform === \"win32\" || platform === \"darwin\") {\n    common.npm_config_target_libc = \"unknown\"\n  }\n\n  if (!frameworkInfo.useCustomDist) {\n    return common\n  }\n\n  // https://github.com/nodejs/node-gyp/issues/21\n  return {\n    ...common,\n    npm_config_disturl: \"https://electronjs.org/headers\",\n    npm_config_target: frameworkInfo.version,\n    npm_config_runtime: \"electron\",\n    npm_config_devdir: getElectronGypCacheDir(),\n  }\n}\n\nfunction checkYarnBerry() {\n  const npmUserAgent = process.env[\"npm_config_user_agent\"] || \"\"\n  const regex = /yarn\\/(\\d+)\\./gm\n\n  const yarnVersionMatch = regex.exec(npmUserAgent)\n  const yarnMajorVersion = Number(yarnVersionMatch?.[1] ?? 0)\n  return yarnMajorVersion >= 2\n}\n\nfunction installDependencies(appDir: string, options: RebuildOptions): Promise<any> {\n  const platform = options.platform || process.platform\n  const arch = options.arch || process.arch\n  const additionalArgs = options.additionalArgs\n\n  log.info({ platform, arch, appDir }, `installing production dependencies`)\n  let execPath = process.env.npm_execpath || process.env.NPM_CLI_JS\n  const execArgs = [\"install\"]\n  const isYarnBerry = checkYarnBerry()\n  if (!isYarnBerry) {\n    if (process.env.NPM_NO_BIN_LINKS === \"true\") {\n      execArgs.push(\"--no-bin-links\")\n    }\n    execArgs.push(\"--production\")\n  }\n\n  if (!isRunningYarn(execPath)) {\n    execArgs.push(\"--prefer-offline\")\n  }\n\n  if (execPath == null) {\n    execPath = getPackageToolPath()\n  } else if (!isYarnBerry) {\n    execArgs.unshift(execPath)\n    execPath = process.env.npm_node_execpath || process.env.NODE_EXE || \"node\"\n  }\n\n  if (additionalArgs != null) {\n    execArgs.push(...additionalArgs)\n  }\n  return spawn(execPath, execArgs, {\n    cwd: appDir,\n    env: getGypEnv(options.frameworkInfo, platform, arch, options.buildFromSource === true),\n  })\n}\n\nexport async function nodeGypRebuild(platform: NodeJS.Platform, arch: string, frameworkInfo: DesktopFrameworkInfo) {\n  log.info({ platform, arch }, \"executing node-gyp rebuild\")\n  // this script must be used only for electron\n  const nodeGyp = `node-gyp${process.platform === \"win32\" ? \".cmd\" : \"\"}`\n  const args = [\"rebuild\"]\n  // headers of old Electron versions do not have a valid config.gypi file\n  // and --force-process-config must be passed to node-gyp >= 8.4.0 to\n  // correctly build modules for them.\n  // see also https://github.com/nodejs/node-gyp/pull/2497\n  const [major, minor] = frameworkInfo.version\n    .split(\".\")\n    .slice(0, 2)\n    .map(n => parseInt(n, 10))\n  if (major <= 13 || (major == 14 && minor <= 1) || (major == 15 && minor <= 2)) {\n    args.push(\"--force-process-config\")\n  }\n  await spawn(nodeGyp, args, { env: getGypEnv(frameworkInfo, platform, arch, true) })\n}\n\nfunction getPackageToolPath() {\n  if (process.env.FORCE_YARN === \"true\") {\n    return process.platform === \"win32\" ? \"yarn.cmd\" : \"yarn\"\n  } else {\n    return process.platform === \"win32\" ? \"npm.cmd\" : \"npm\"\n  }\n}\n\nfunction isRunningYarn(execPath: string | null | undefined) {\n  const userAgent = process.env.npm_config_user_agent\n  return process.env.FORCE_YARN === \"true\" || (execPath != null && path.basename(execPath).startsWith(\"yarn\")) || (userAgent != null && /\\byarn\\b/.test(userAgent))\n}\n\nexport interface RebuildOptions {\n  frameworkInfo: DesktopFrameworkInfo\n  productionDeps: Lazy<Array<NodeModuleDirInfo>>\n\n  platform?: NodeJS.Platform\n  arch?: string\n\n  buildFromSource?: boolean\n\n  additionalArgs?: Array<string> | null\n}\n\n/** @internal */\nexport async function rebuild(appDir: string, options: RebuildOptions) {\n  const configuration: any = {\n    dependencies: await options.productionDeps.value,\n    nodeExecPath: process.execPath,\n    platform: options.platform || process.platform,\n    arch: options.arch || process.arch,\n    additionalArgs: options.additionalArgs,\n    execPath: process.env.npm_execpath || process.env.NPM_CLI_JS,\n    buildFromSource: options.buildFromSource === true,\n  }\n\n  const env = getGypEnv(options.frameworkInfo, configuration.platform, configuration.arch, options.buildFromSource === true)\n  await executeAppBuilderAndWriteJson([\"rebuild-node-modules\"], configuration, { env, cwd: appDir })\n}\n"]}