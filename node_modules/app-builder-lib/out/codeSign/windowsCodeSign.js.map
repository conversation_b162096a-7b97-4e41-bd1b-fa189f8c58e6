{"version": 3, "file": "windowsCodeSign.js", "sourceRoot": "", "sources": ["../../src/codeSign/windowsCodeSign.ts"], "names": [], "mappings": ";;;AAAA,gDAA+E;AAC/E,gDAAuC;AAEvC,mDAA4D;AAC5D,qDAA8D;AAC9D,uCAAiC;AACjC,yBAAwB;AACxB,6BAA4B;AAC5B,0DAAqD;AACrD,yCAAmD;AACnD,iCAAoC;AAGpC,SAAgB,iBAAiB;IAC/B,OAAO,IAAA,oBAAM,EAAC,aAAa,CAAC,CAAA;AAC9B,CAAC;AAFD,8CAEC;AA0BM,KAAK,UAAU,IAAI,CAAC,OAA2B,EAAE,QAAqB;IAC3E,IAAI,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,qBAAqB,CAAA;IAClD,oCAAoC;IACpC,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;QAClC,MAAM,GAAG,CAAC,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;IAC3E,CAAC;SAAM,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;QAC1C,MAAM,GAAG,CAAC,QAAQ,CAAC,CAAA;IACrB,CAAC;SAAM,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;QAC1B,MAAM,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;IAC7B,CAAC;SAAM,CAAC;QACN,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;IACpD,CAAC;IAED,MAAM,QAAQ,GAAG,CAAC,MAAM,IAAA,kCAAe,EAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,IAAI,MAAM,CAAA;IACvG,IAAI,MAAM,GAAG,KAAK,CAAA;IAClB,KAAK,MAAM,IAAI,IAAI,MAAM,EAAE,CAAC;QAC1B,MAAM,iBAAiB,GAAiC,EAAE,GAAG,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAA;QACpF,MAAM,OAAO,CAAC,OAAO,CACnB,QAAQ,CACN;YACE,GAAG,iBAAiB;YACpB,mBAAmB,EAAE,KAAK,CAAC,EAAE,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,KAAK,CAAC;SAC5E,EACD,QAAQ,CACT,CACF,CAAA;QACD,MAAM,GAAG,IAAI,CAAA;QACb,IAAI,iBAAiB,CAAC,gBAAgB,IAAI,IAAI,EAAE,CAAC;YAC/C,MAAM,IAAA,iBAAM,EAAC,iBAAiB,CAAC,gBAAgB,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;QAChE,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAjCD,oBAiCC;AAOM,KAAK,UAAU,WAAW,CAAC,IAAY,EAAE,QAAgB;IAC9D,IAAI,MAAM,GAAQ,IAAI,CAAA;IACtB,MAAM,kBAAkB,GAAG,4GAA4G,CAAA;IACvI,IAAI,CAAC;QACH,MAAM,GAAG,MAAM,IAAA,oCAAuB,EAAM,CAAC,kBAAkB,EAAE,SAAS,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAA;IAC5G,CAAC;IAAC,OAAO,CAAM,EAAE,CAAC;QAChB,MAAM,IAAI,KAAK,CAAC,GAAG,kBAAkB,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,CAAA;IACzD,CAAC;IAED,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;QACzB,wCAAwC;QACxC,MAAM,IAAI,gCAAyB,CAAC,GAAG,kBAAkB,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,CAAA;IAC7E,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAdD,kCAcC;AAcM,KAAK,UAAU,2BAA2B,CAAC,OAA6B,EAAE,EAAa;IAC5F,MAAM,sBAAsB,GAAG,OAAO,CAAC,sBAAsB,CAAA;IAC7D,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,CAAA;IACjH,wEAAwE;IACxE,gDAAgD;IAChD,MAAM,SAAS,GAAG,MAAM,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE;QAChD,YAAY;QACZ,iBAAiB;QACjB,UAAU;QACV,oIAAoI;KACrI,CAAC,CAAA;IACF,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAA,cAAO,EAAW,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAA;IACvF,KAAK,MAAM,QAAQ,IAAI,QAAQ,EAAE,CAAC;QAChC,IACE,CAAC,sBAAsB,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;YACtF,CAAC,eAAe,IAAI,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,WAAW,EAAE,KAAK,eAAe,CAAC,EAClF,CAAC;YACD,SAAQ;QACV,CAAC;QAED,MAAM,UAAU,GAAG,QAAQ,CAAC,YAAY,CAAA;QACxC,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;QACpE,UAAG,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,EAAE,+BAA+B,CAAC,CAAA;QAC/E,oEAAoE;QACpE,MAAM,mBAAmB,GAAG,UAAU,CAAC,QAAQ,CAAC,2BAA2B,CAAC,CAAA;QAC5E,UAAG,CAAC,KAAK,CAAC,IAAI,EAAE,yCAAyC,CAAC,CAAA;QAC1D,OAAO;YACL,UAAU,EAAE,QAAQ,CAAC,UAAU;YAC/B,OAAO,EAAE,QAAQ,CAAC,OAAO;YACzB,KAAK;YACL,mBAAmB;SACpB,CAAA;IACH,CAAC;IAED,MAAM,IAAI,KAAK,CAAC,2BAA2B,sBAAsB,IAAI,eAAe,gBAAgB,SAAS,EAAE,CAAC,CAAA;AAClH,CAAC;AAnCD,kEAmCC;AAEM,KAAK,UAAU,MAAM,CAAC,aAAiD,EAAE,QAAqB;IACnG,kEAAkE;IAClE,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAuB,EAAE,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAA;IACnF,mCAAmC;IACnC,IAAI,IAAmB,CAAA;IACvB,IAAI,GAAG,GAAG,OAAO,CAAC,GAAG,CAAA;IACrB,IAAI,EAAa,CAAA;IACjB,MAAM,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,aAAa,CAAC,OAAQ,CAAC,CAAA,CAAC,mDAAmD;IAClJ,MAAM,KAAK,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,UAAU,CAAA;IACxD,MAAM,QAAQ,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC,CAAA;IACzC,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAA;IAC1B,IAAI,UAAU,EAAE,CAAC;QACf,EAAE,GAAG,MAAM,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAA;QAC5B,IAAI,GAAG,mBAAmB,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE,CAAC,CAAA;IACtD,CAAC;SAAM,CAAC;QACN,EAAE,GAAG,IAAI,cAAS,EAAE,CAAA;QACpB,IAAI,GAAG,aAAa,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;QAC/C,IAAI,QAAQ,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;YACzB,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAA;QACpB,CAAC;IACH,CAAC;IAED,IAAI,CAAC;QACH,MAAM,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAA;IAC7C,CAAC;IAAC,OAAO,CAAM,EAAE,CAAC;QAChB,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,2CAA2C,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,4DAA4D,CAAC,EAAE,CAAC;YACxJ,UAAG,CAAC,IAAI,CAAC,kFAAkF,CAAC,CAAC,OAAO,EAAE,CAAC,CAAA;YACvG,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACpC,UAAU,CAAC,GAAG,EAAE;oBACd,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;gBACnE,CAAC,EAAE,KAAK,CAAC,CAAA;YACX,CAAC,CAAC,CAAA;QACJ,CAAC;QACD,MAAM,CAAC,CAAA;IACT,CAAC;AACH,CAAC;AAnCD,wBAmCC;AAQD,qEAAqE;AACrE,SAAS,mBAAmB,CAAC,OAAqC,EAAE,KAAc,EAAE,KAAgB,IAAI,cAAS,EAAE;IACjH,MAAM,SAAS,GAAG,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;IAC3C,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;IAC7E,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO,CAAC,gBAAgB,GAAG,UAAU,CAAA;IACvC,CAAC;IAED,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,CAAC,CAAA;IAEtE,IAAI,OAAO,CAAC,GAAG,CAAC,wBAAwB,KAAK,MAAM,EAAE,CAAC;QACpD,MAAM,sBAAsB,GAAG,OAAO,CAAC,OAAO,CAAC,eAAe,IAAI,+BAA+B,CAAA;QACjG,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,IAAI,CACP,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAC1D,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,sBAAsB,IAAI,+BAA+B,CAAC,CAAC,CAAC,sBAAsB,CACjJ,CAAA;QACH,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,sBAAsB,CAAC,CAAA;QACzC,CAAC;IACH,CAAC;IAED,MAAM,eAAe,GAAI,OAAO,CAAC,OAA+B,CAAC,IAAI,CAAA;IACrE,IAAI,eAAe,IAAI,IAAI,EAAE,CAAC;QAC5B,MAAM,OAAO,GAAG,OAAO,CAAC,OAAmC,CAAA;QAC3D,MAAM,WAAW,GAAG,OAAO,CAAC,UAAU,CAAA;QACtC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CAAC,GAAG,WAAW,IAAI,IAAI,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,wBAAwB,4BAA4B,CAAC,CAAA;QACpH,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC,CAAA;QACtC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,CAAA;QAC9B,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAClB,CAAC;IACH,CAAC;SAAM,CAAC;QACN,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAA;QACnD,IAAI,aAAa,KAAK,MAAM,IAAI,aAAa,KAAK,MAAM,EAAE,CAAC;YACzD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAA;QACnE,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,2CAA2C,eAAe,iBAAiB,CAAC,CAAA;QAC9F,CAAC;IACH,CAAC;IAED,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;QACtC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;QAC7C,IAAI,KAAK,IAAI,OAAO,CAAC,GAAG,CAAC,wBAAwB,KAAK,MAAM,EAAE,CAAC;YAC7D,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;QAC5B,CAAC;IACH,CAAC;IAED,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;QACjB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;IAC9C,CAAC;IAED,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;QACjB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;IAC/C,CAAC;IAED,oCAAoC;IACpC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACnB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAA;IACpC,CAAC;IAED,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAE,OAAO,CAAC,OAA+B,CAAC,QAAQ,CAAA;IACnG,IAAI,QAAQ,EAAE,CAAC;QACb,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;IAC7C,CAAC;IAED,IAAI,OAAO,CAAC,OAAO,CAAC,yBAAyB,EAAE,CAAC;QAC9C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,yBAAyB,CAAC,CAAC,CAAA;IAC1F,CAAC;IAED,MAAM,iBAAiB,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,CAAA;IACjD,IAAI,CAAC,KAAK,IAAI,iBAAiB,IAAI,IAAI,IAAI,iBAAiB,CAAC,MAAM,EAAE,CAAC;QACpE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAA;IACpC,CAAC;IAED,IAAI,KAAK,EAAE,CAAC;QACV,2FAA2F;QAC3F,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QACnB,wBAAwB;QACxB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;IACtB,CAAC;IAED,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAS,aAAa,CAAC,SAAiB,EAAE,IAAY;IACpD,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;IACzC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC,WAAW,IAAI,GAAG,SAAS,EAAE,CAAC,CAAA;AAChH,CAAC;AAED,gBAAgB;AAChB,SAAgB,SAAS;IACvB,MAAM,UAAU,GAAG,EAAE,CAAC,OAAO,EAAE,CAAA;IAC/B,OAAO,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;AACrE,CAAC;AAHD,8BAGC;AAED,SAAS,cAAc,CAAC,UAAkB;IACxC,wEAAwE;IACxE,IAAI,SAAS,EAAE,EAAE,CAAC;QAChB,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,EAAE,cAAc,CAAC,CAAA;IAC3D,CAAC;SAAM,CAAC;QACN,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,EAAE,OAAO,CAAC,IAAI,EAAE,cAAc,CAAC,CAAA;IAC1E,CAAC;AACH,CAAC;AAED,KAAK,UAAU,WAAW,CAAC,KAAK,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO;IAC7D,IAAI,IAAA,2BAAmB,GAAE,EAAE,CAAC;QAC1B,OAAO,EAAE,IAAI,EAAE,cAAc,EAAE,CAAA;IACjC,CAAC;IAED,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,CAAA;IACxC,IAAI,MAAM,EAAE,CAAC;QACX,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAA;IACzB,CAAC;IAED,MAAM,UAAU,GAAG,MAAM,iBAAiB,EAAE,CAAA;IAC5C,IAAI,KAAK,EAAE,CAAC;QACV,wEAAwE;QACxE,OAAO,EAAE,IAAI,EAAE,cAAc,CAAC,UAAU,CAAC,EAAE,CAAA;IAC7C,CAAC;SAAM,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;QACzC,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;QACpE,OAAO;YACL,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC;YAC5C,GAAG,EAAE,IAAA,4BAAc,EAAC,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC,CAAC;SACrD,CAAA;IACH,CAAC;SAAM,CAAC;QACN,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAC,EAAE,CAAA;IAC1E,CAAC;AACH,CAAC", "sourcesContent": ["import { InvalidConfigurationError, asArray, log } from \"builder-util/out/util\"\nimport { getBin } from \"../binDownload\"\nimport { WindowsConfiguration } from \"../options/winOptions\"\nimport { executeAppBuilderAsJson } from \"../util/appBuilder\"\nimport { computeToolEnv, ToolInfo } from \"../util/bundledTool\"\nimport { rename } from \"fs-extra\"\nimport * as os from \"os\"\nimport * as path from \"path\"\nimport { resolveFunction } from \"../platformPackager\"\nimport { isUseSystemSigncode } from \"../util/flags\"\nimport { VmManager } from \"../vm/vm\"\nimport { WinPackager } from \"../winPackager\"\n\nexport function getSignVendorPath() {\n  return getBin(\"winCodeSign\")\n}\n\nexport type CustomWindowsSign = (configuration: CustomWindowsSignTaskConfiguration, packager?: WinPackager) => Promise<any>\n\nexport interface WindowsSignOptions {\n  readonly path: string\n\n  readonly name?: string | null\n  readonly cscInfo?: FileCodeSigningInfo | CertificateFromStoreInfo | null\n  readonly site?: string | null\n\n  readonly options: WindowsConfiguration\n}\n\nexport interface WindowsSignTaskConfiguration extends WindowsSignOptions {\n  // set if output path differs from input (e.g. osslsigncode cannot sign file in-place)\n  resultOutputPath?: string\n\n  hash: string\n  isNest: boolean\n}\n\nexport interface CustomWindowsSignTaskConfiguration extends WindowsSignTaskConfiguration {\n  computeSignToolArgs(isWin: boolean): Array<string>\n}\n\nexport async function sign(options: WindowsSignOptions, packager: WinPackager): Promise<boolean> {\n  let hashes = options.options.signingHashAlgorithms\n  // msi does not support dual-signing\n  if (options.path.endsWith(\".msi\")) {\n    hashes = [hashes != null && !hashes.includes(\"sha1\") ? \"sha256\" : \"sha1\"]\n  } else if (options.path.endsWith(\".appx\")) {\n    hashes = [\"sha256\"]\n  } else if (hashes == null) {\n    hashes = [\"sha1\", \"sha256\"]\n  } else {\n    hashes = Array.isArray(hashes) ? hashes : [hashes]\n  }\n\n  const executor = (await resolveFunction(packager.appInfo.type, options.options.sign, \"sign\")) || doSign\n  let isNest = false\n  for (const hash of hashes) {\n    const taskConfiguration: WindowsSignTaskConfiguration = { ...options, hash, isNest }\n    await Promise.resolve(\n      executor(\n        {\n          ...taskConfiguration,\n          computeSignToolArgs: isWin => computeSignToolArgs(taskConfiguration, isWin),\n        },\n        packager\n      )\n    )\n    isNest = true\n    if (taskConfiguration.resultOutputPath != null) {\n      await rename(taskConfiguration.resultOutputPath, options.path)\n    }\n  }\n\n  return true\n}\n\nexport interface FileCodeSigningInfo {\n  readonly file: string\n  readonly password: string | null\n}\n\nexport async function getCertInfo(file: string, password: string): Promise<CertificateInfo> {\n  let result: any = null\n  const errorMessagePrefix = \"Cannot extract publisher name from code signing certificate. As workaround, set win.publisherName. Error: \"\n  try {\n    result = await executeAppBuilderAsJson<any>([\"certificate-info\", \"--input\", file, \"--password\", password])\n  } catch (e: any) {\n    throw new Error(`${errorMessagePrefix}${e.stack || e}`)\n  }\n\n  if (result.error != null) {\n    // noinspection ExceptionCaughtLocallyJS\n    throw new InvalidConfigurationError(`${errorMessagePrefix}${result.error}`)\n  }\n  return result\n}\n\nexport interface CertificateInfo {\n  readonly commonName: string\n  readonly bloodyMicrosoftSubjectDn: string\n}\n\nexport interface CertificateFromStoreInfo {\n  thumbprint: string\n  subject: string\n  store: string\n  isLocalMachineStore: boolean\n}\n\nexport async function getCertificateFromStoreInfo(options: WindowsConfiguration, vm: VmManager): Promise<CertificateFromStoreInfo> {\n  const certificateSubjectName = options.certificateSubjectName\n  const certificateSha1 = options.certificateSha1 ? options.certificateSha1.toUpperCase() : options.certificateSha1\n  // ExcludeProperty doesn't work, so, we cannot exclude RawData, it is ok\n  // powershell can return object if the only item\n  const rawResult = await vm.exec(\"powershell.exe\", [\n    \"-NoProfile\",\n    \"-NonInteractive\",\n    \"-Command\",\n    \"Get-ChildItem -Recurse Cert: -CodeSigningCert | Select-Object -Property Subject,PSParentPath,Thumbprint | ConvertTo-Json -Compress\",\n  ])\n  const certList = rawResult.length === 0 ? [] : asArray<CertInfo>(JSON.parse(rawResult))\n  for (const certInfo of certList) {\n    if (\n      (certificateSubjectName != null && !certInfo.Subject.includes(certificateSubjectName)) ||\n      (certificateSha1 != null && certInfo.Thumbprint.toUpperCase() !== certificateSha1)\n    ) {\n      continue\n    }\n\n    const parentPath = certInfo.PSParentPath\n    const store = parentPath.substring(parentPath.lastIndexOf(\"\\\\\") + 1)\n    log.debug({ store, PSParentPath: parentPath }, \"auto-detect certificate store\")\n    // https://github.com/electron-userland/electron-builder/issues/1717\n    const isLocalMachineStore = parentPath.includes(\"Certificate::LocalMachine\")\n    log.debug(null, \"auto-detect using of LocalMachine store\")\n    return {\n      thumbprint: certInfo.Thumbprint,\n      subject: certInfo.Subject,\n      store,\n      isLocalMachineStore,\n    }\n  }\n\n  throw new Error(`Cannot find certificate ${certificateSubjectName || certificateSha1}, all certs: ${rawResult}`)\n}\n\nexport async function doSign(configuration: CustomWindowsSignTaskConfiguration, packager: WinPackager) {\n  // https://github.com/electron-userland/electron-builder/pull/1944\n  const timeout = parseInt(process.env.SIGNTOOL_TIMEOUT as any, 10) || 10 * 60 * 1000\n  // decide runtime argument by cases\n  let args: Array<string>\n  let env = process.env\n  let vm: VmManager\n  const vmRequired = configuration.path.endsWith(\".appx\") || !(\"file\" in configuration.cscInfo!) /* certificateSubjectName and other such options */\n  const isWin = process.platform === \"win32\" || vmRequired\n  const toolInfo = await getToolPath(isWin)\n  const tool = toolInfo.path\n  if (vmRequired) {\n    vm = await packager.vm.value\n    args = computeSignToolArgs(configuration, isWin, vm)\n  } else {\n    vm = new VmManager()\n    args = configuration.computeSignToolArgs(isWin)\n    if (toolInfo.env != null) {\n      env = toolInfo.env\n    }\n  }\n\n  try {\n    await vm.exec(tool, args, { timeout, env })\n  } catch (e: any) {\n    if (e.message.includes(\"The file is being used by another process\") || e.message.includes(\"The specified timestamp server either could not be reached\")) {\n      log.warn(`First attempt to code sign failed, another attempt will be made in 15 seconds: ${e.message}`)\n      await new Promise((resolve, reject) => {\n        setTimeout(() => {\n          vm.exec(tool, args, { timeout, env }).then(resolve).catch(reject)\n        }, 15000)\n      })\n    }\n    throw e\n  }\n}\n\ninterface CertInfo {\n  Subject: string\n  Thumbprint: string\n  PSParentPath: string\n}\n\n// on windows be aware of http://stackoverflow.com/a/32640183/1910191\nfunction computeSignToolArgs(options: WindowsSignTaskConfiguration, isWin: boolean, vm: VmManager = new VmManager()): Array<string> {\n  const inputFile = vm.toVmFile(options.path)\n  const outputPath = isWin ? inputFile : getOutputPath(inputFile, options.hash)\n  if (!isWin) {\n    options.resultOutputPath = outputPath\n  }\n\n  const args = isWin ? [\"sign\"] : [\"-in\", inputFile, \"-out\", outputPath]\n\n  if (process.env.ELECTRON_BUILDER_OFFLINE !== \"true\") {\n    const timestampingServiceUrl = options.options.timeStampServer || \"http://timestamp.digicert.com\"\n    if (isWin) {\n      args.push(\n        options.isNest || options.hash === \"sha256\" ? \"/tr\" : \"/t\",\n        options.isNest || options.hash === \"sha256\" ? options.options.rfc3161TimeStampServer || \"http://timestamp.digicert.com\" : timestampingServiceUrl\n      )\n    } else {\n      args.push(\"-t\", timestampingServiceUrl)\n    }\n  }\n\n  const certificateFile = (options.cscInfo as FileCodeSigningInfo).file\n  if (certificateFile == null) {\n    const cscInfo = options.cscInfo as CertificateFromStoreInfo\n    const subjectName = cscInfo.thumbprint\n    if (!isWin) {\n      throw new Error(`${subjectName == null ? \"certificateSha1\" : \"certificateSubjectName\"} supported only on Windows`)\n    }\n\n    args.push(\"/sha1\", cscInfo.thumbprint)\n    args.push(\"/s\", cscInfo.store)\n    if (cscInfo.isLocalMachineStore) {\n      args.push(\"/sm\")\n    }\n  } else {\n    const certExtension = path.extname(certificateFile)\n    if (certExtension === \".p12\" || certExtension === \".pfx\") {\n      args.push(isWin ? \"/f\" : \"-pkcs12\", vm.toVmFile(certificateFile))\n    } else {\n      throw new Error(`Please specify pkcs12 (.p12/.pfx) file, ${certificateFile} is not correct`)\n    }\n  }\n\n  if (!isWin || options.hash !== \"sha1\") {\n    args.push(isWin ? \"/fd\" : \"-h\", options.hash)\n    if (isWin && process.env.ELECTRON_BUILDER_OFFLINE !== \"true\") {\n      args.push(\"/td\", \"sha256\")\n    }\n  }\n\n  if (options.name) {\n    args.push(isWin ? \"/d\" : \"-n\", options.name)\n  }\n\n  if (options.site) {\n    args.push(isWin ? \"/du\" : \"-i\", options.site)\n  }\n\n  // msi does not support dual-signing\n  if (options.isNest) {\n    args.push(isWin ? \"/as\" : \"-nest\")\n  }\n\n  const password = options.cscInfo == null ? null : (options.cscInfo as FileCodeSigningInfo).password\n  if (password) {\n    args.push(isWin ? \"/p\" : \"-pass\", password)\n  }\n\n  if (options.options.additionalCertificateFile) {\n    args.push(isWin ? \"/ac\" : \"-ac\", vm.toVmFile(options.options.additionalCertificateFile))\n  }\n\n  const httpsProxyFromEnv = process.env.HTTPS_PROXY\n  if (!isWin && httpsProxyFromEnv != null && httpsProxyFromEnv.length) {\n    args.push(\"-p\", httpsProxyFromEnv)\n  }\n\n  if (isWin) {\n    // https://github.com/electron-userland/electron-builder/issues/2875#issuecomment-387233610\n    args.push(\"/debug\")\n    // must be last argument\n    args.push(inputFile)\n  }\n\n  return args\n}\n\nfunction getOutputPath(inputPath: string, hash: string) {\n  const extension = path.extname(inputPath)\n  return path.join(path.dirname(inputPath), `${path.basename(inputPath, extension)}-signed-${hash}${extension}`)\n}\n\n/** @internal */\nexport function isOldWin6() {\n  const winVersion = os.release()\n  return winVersion.startsWith(\"6.\") && !winVersion.startsWith(\"6.3\")\n}\n\nfunction getWinSignTool(vendorPath: string): string {\n  // use modern signtool on Windows Server 2012 R2 to be able to sign AppX\n  if (isOldWin6()) {\n    return path.join(vendorPath, \"windows-6\", \"signtool.exe\")\n  } else {\n    return path.join(vendorPath, \"windows-10\", process.arch, \"signtool.exe\")\n  }\n}\n\nasync function getToolPath(isWin = process.platform === \"win32\"): Promise<ToolInfo> {\n  if (isUseSystemSigncode()) {\n    return { path: \"osslsigncode\" }\n  }\n\n  const result = process.env.SIGNTOOL_PATH\n  if (result) {\n    return { path: result }\n  }\n\n  const vendorPath = await getSignVendorPath()\n  if (isWin) {\n    // use modern signtool on Windows Server 2012 R2 to be able to sign AppX\n    return { path: getWinSignTool(vendorPath) }\n  } else if (process.platform === \"darwin\") {\n    const toolDirPath = path.join(vendorPath, process.platform, \"10.12\")\n    return {\n      path: path.join(toolDirPath, \"osslsigncode\"),\n      env: computeToolEnv([path.join(toolDirPath, \"lib\")]),\n    }\n  } else {\n    return { path: path.join(vendorPath, process.platform, \"osslsigncode\") }\n  }\n}\n"]}