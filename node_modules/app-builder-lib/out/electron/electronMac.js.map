{"version": 3, "file": "electronMac.js", "sourceRoot": "", "sources": ["../../src/electron/electronMac.ts"], "names": [], "mappings": ";;;AAAA,+CAA0C;AAC1C,+CAA+F;AAC/F,4CAAoE;AACpE,0CAA4C;AAC5C,6BAA4B;AAC5B,wCAAqD;AAGrD,0DAAkD;AAClD,mDAA2F;AAC3F,2DAAwD;AAExD,SAAS,QAAQ,CAAC,QAAgB,EAAE,OAAe,EAAE,OAAe;IAClE,OAAO,IAAA,iBAAM,EAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAA;AAC3E,CAAC;AAED,SAAS,WAAW,CAAC,cAA6B,EAAE,cAAsB,EAAE,OAAe,EAAE,MAAc;IACzG,OAAO,sBAAe,CAAC,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE;QAClD,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,MAAM,GAAG,MAAM,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,CAAA;QACnG,OAAO,QAAQ,CAAC,kBAAkB,EAAE,GAAG,MAAM,GAAG,MAAM,EAAE,EAAE,OAAO,GAAG,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,cAAc,EAAE,GAAG,MAAM,GAAG,MAAM,MAAM,EAAE,GAAG,OAAO,GAAG,MAAM,MAAM,CAAC,CAAC,CAAA;IACvK,CAAC,CAAC,CAAA;AACJ,CAAC;AAED,SAAS,0BAA0B,CACjC,aAA4B,EAC5B,aAA4B,EAC5B,mBAAkC,EAClC,iBAAgC,EAChC,cAA6B;IAE7B,MAAM,MAAM,GAAG,CAAC,SAAS,CAAC,CAAA;IAC1B,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;QAC1B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;IAC3B,CAAC;IACD,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;QAC1B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;IAC3B,CAAC;IACD,IAAI,mBAAmB,IAAI,IAAI,EAAE,CAAC;QAChC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAA;IACnC,CAAC;IACD,IAAI,iBAAiB,IAAI,IAAI,EAAE,CAAC;QAC9B,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;IACjC,CAAC;IACD,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;QAC3B,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;IAC9B,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAED,gBAAgB;AACT,KAAK,UAAU,YAAY,CAAC,QAAqB,EAAE,SAAiB,EAAE,aAAmC,EAAE,KAAc;;IAC9H,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAA;IAChC,0EAA0E;IAC1E,yFAAyF;IACzF,oEAAoE;IACpE,MAAM,WAAW,GAAG,OAAO,CAAC,oBAAoB,CAAA;IAChD,MAAM,gBAAgB,GAAG,IAAA,sCAAkB,EAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;IAE5D,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAA;IAC/F,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC,CAAA;IAC5D,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,SAAS,EAAE,YAAY,CAAC,CAAA;IAEtE,MAAM,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,YAAY,CAAC,CAAA;IAC9D,MAAM,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,gBAAgB,CAAC,WAAW,aAAa,EAAE,UAAU,EAAE,YAAY,CAAC,CAAA;IAC7H,MAAM,qBAAqB,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,gBAAgB,CAAC,WAAW,gBAAgB,EAAE,UAAU,EAAE,YAAY,CAAC,CAAA;IAClI,MAAM,qBAAqB,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,gBAAgB,CAAC,WAAW,gBAAgB,EAAE,UAAU,EAAE,YAAY,CAAC,CAAA;IAClI,MAAM,2BAA2B,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,gBAAgB,CAAC,WAAW,wBAAwB,EAAE,UAAU,EAAE,YAAY,CAAC,CAAA;IAChJ,MAAM,yBAAyB,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,gBAAgB,CAAC,WAAW,sBAAsB,EAAE,UAAU,EAAE,YAAY,CAAC,CAAA;IAC5I,MAAM,sBAAsB,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,gBAAgB,CAAC,WAAW,mBAAmB,EAAE,UAAU,EAAE,YAAY,CAAC,CAAA;IACtI,MAAM,wBAAwB,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,gBAAgB,CAAC,WAAW,mBAAmB,EAAE,UAAU,EAAE,YAAY,CAAC,CAAA;IAEvI,MAAM,YAAY,GAAe,MAAM,IAAA,oCAAuB,EAAC;QAC7D,cAAc;QACd,IAAI;QACJ,gBAAgB;QAChB,IAAI;QACJ,mBAAmB;QACnB,IAAI;QACJ,qBAAqB;QACrB,IAAI;QACJ,qBAAqB;QACrB,IAAI;QACJ,2BAA2B;QAC3B,IAAI;QACJ,yBAAyB;QACzB,IAAI;QACJ,sBAAsB;QACtB,IAAI;QACJ,wBAAwB;KACzB,CAAC,CAAA;IAEF,IAAI,YAAY,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;QAC5B,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAA;IAC5C,CAAC;IAED,MAAM,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAE,CAAA;IACjC,MAAM,WAAW,GAAG,YAAY,CAAC,CAAC,CAAE,CAAA;IACpC,MAAM,aAAa,GAAG,YAAY,CAAC,CAAC,CAAC,CAAA;IACrC,MAAM,aAAa,GAAG,YAAY,CAAC,CAAC,CAAC,CAAA;IACrC,MAAM,mBAAmB,GAAG,YAAY,CAAC,CAAC,CAAC,CAAA;IAC3C,MAAM,iBAAiB,GAAG,YAAY,CAAC,CAAC,CAAC,CAAA;IACzC,MAAM,cAAc,GAAG,YAAY,CAAC,CAAC,CAAC,CAAA;IACtC,MAAM,gBAAgB,GAAG,YAAY,CAAC,CAAC,CAAC,CAAA;IAExC,kEAAkE;IAClE,IAAI,YAAY,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;QAC5B,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAA;IAC1C,CAAC;IAED,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAA;IAErC;;;;;OAKG;IAEH,MAAM,iBAAiB,GAAI,aAAqB,CAAC,kBAAkB,CAAC,CAAA;IACpE,IAAI,iBAAiB,IAAI,IAAI,EAAE,CAAC;QAC9B,kBAAG,CAAC,IAAI,CAAC,8EAA8E,CAAC,CAAA;IAC1F,CAAC;IAED,MAAM,YAAY,GAAG,QAAQ,CAAC,4BAA4B,CAAC,KAAK,CAAA;IAChE,MAAM,kBAAkB,GAAG,IAAA,kCAAwB,EAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAA,QAAQ,CAAC,MAAM,CAAC,GAAG,0CAAE,KAAK,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,YAAY,IAAI,OAAO,CAAC,mBAAmB,CAAC,CAAA;IAEvJ,MAAM,eAAe,GAAG,QAAQ,CAAC,4BAA4B,CAAC,cAAc,CAAA;IAC5E,MAAM,sBAAsB,GAAG,IAAA,kCAAwB,EACrD,CAAC,KAAK,CAAC,CAAC,CAAC,MAAA,QAAQ,CAAC,MAAM,CAAC,GAAG,0CAAE,cAAc,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,eAAe,IAAI,iBAAiB,IAAI,GAAG,kBAAkB,SAAS,CAC1I,CAAA;IAED,QAAQ,CAAC,kBAAkB,GAAG,kBAAkB,CAAA;IAEhD,MAAM,QAAQ,CAAC,eAAe,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAA;IAEtD,sCAAsC;IACtC,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,qBAAqB,CAAC,QAAQ,CAAC,CAAA;IACjC,CAAC;IAED,WAAW,CAAC,kBAAkB,GAAG,GAAG,WAAW,SAAS,CAAA;IACxD,WAAW,CAAC,mBAAmB,GAAG,GAAG,OAAO,CAAC,WAAW,SAAS,CAAA;IACjE,WAAW,CAAC,kBAAkB,GAAG,sBAAsB,CAAA;IACvD,WAAW,CAAC,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAA;IAEtD;;;;;;;OAOG;IAEH,SAAS,eAAe,CAAC,MAAW,EAAE,OAAe,EAAE,4BAA4C;QACjG,MAAM,CAAC,kBAAkB,GAAG,GAAG,WAAW,WAAW,OAAO,EAAE,CAAA;QAC9D,MAAM,CAAC,mBAAmB,GAAG,GAAG,OAAO,CAAC,WAAW,WAAW,OAAO,EAAE,CAAA;QACvE,MAAM,CAAC,kBAAkB,GAAG,4BAA4B;YACtD,CAAC,CAAC,IAAA,kCAAwB,EAAC,4BAA4B,CAAC;YACxD,CAAC,CAAC,IAAA,kCAAwB,EAAC,GAAG,sBAAsB,IAAI,OAAO,EAAE,CAAC,CAAA;QACpE,MAAM,CAAC,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAA;IACnD,CAAC;IAED,IAAI,mBAAmB,IAAI,IAAI,EAAE,CAAC;QAChC,eAAe,CAAC,mBAAmB,EAAE,YAAY,EAAE,QAAQ,CAAC,4BAA4B,CAAC,sBAAsB,CAAC,CAAA;IAClH,CAAC;IACD,IAAI,iBAAiB,IAAI,IAAI,EAAE,CAAC;QAC9B,eAAe,CAAC,iBAAiB,EAAE,UAAU,EAAE,QAAQ,CAAC,4BAA4B,CAAC,oBAAoB,CAAC,CAAA;IAC5G,CAAC;IACD,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;QAC3B,eAAe,CAAC,cAAc,EAAE,OAAO,EAAE,QAAQ,CAAC,4BAA4B,CAAC,iBAAiB,CAAC,CAAA;IACnG,CAAC;IACD,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;QAC1B,eAAe,CAAC,aAAa,EAAE,IAAI,EAAE,QAAQ,CAAC,4BAA4B,CAAC,gBAAgB,CAAC,CAAA;IAC9F,CAAC;IACD,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;QAC1B,eAAe,CAAC,aAAa,EAAE,IAAI,EAAE,QAAQ,CAAC,4BAA4B,CAAC,gBAAgB,CAAC,CAAA;IAC9F,CAAC;IACD,IAAI,gBAAgB,IAAI,IAAI,EAAE,CAAC;QAC7B,gBAAgB,CAAC,kBAAkB,GAAG,GAAG,WAAW,eAAe,CAAA;QACnE,gBAAgB,CAAC,mBAAmB,GAAG,GAAG,OAAO,CAAC,WAAW,eAAe,CAAA;QAC5E,uCAAuC;QACvC,gBAAgB,CAAC,kBAAkB,GAAG,GAAG,kBAAkB,cAAc,CAAA;QACzE,gBAAgB,CAAC,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAA;IAC7D,CAAC;IAED,MAAM,SAAS,GAAG,IAAA,sBAAO,EAAC,aAAa,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,IAAA,sBAAO,EAAC,QAAQ,CAAC,4BAA4B,CAAC,SAAS,CAAC,CAAC,CAAA;IACnH,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACzB,QAAQ,CAAC,gBAAgB,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YACnD,MAAM,OAAO,GAAG,IAAA,sBAAO,EAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;YACzC,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzB,MAAM,IAAI,wCAAyB,CAAC,aAAa,QAAQ,CAAC,IAAI,0CAA0C,CAAC,CAAA;YAC3G,CAAC;YACD,OAAO;gBACL,eAAe,EAAE,QAAQ,CAAC,IAAI;gBAC9B,gBAAgB,EAAE,QAAQ,CAAC,IAAI,IAAI,QAAQ;gBAC3C,kBAAkB,EAAE,OAAO,CAAC,KAAK,EAAE;aACpC,CAAA;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,MAAM,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAA;IAClD,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAChC,MAAM,aAAa,GAAG,MAAM,sBAAe,CAAC,GAAG,CAAC,gBAAgB,EAAE,KAAK,EAAC,eAAe,EAAC,EAAE;YACxF,MAAM,UAAU,GAAG,IAAA,sBAAO,EAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,+BAAY,CAAC,CAAA;YACjE,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,WAAW,CAAC,IAAA,sCAAuB,EAAC,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAA;YAC3H,IAAI,QAAQ,GAAG,QAAQ,CAAC,gBAAgB,CAAA;YACxC,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;gBACvB,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;gBACpC,MAAM,IAAA,mBAAc,EAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,WAAW,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAA;YAC7F,CAAC;YAED,MAAM,MAAM,GAAG;gBACb,sBAAsB,EAAE,UAAU;gBAClC,gBAAgB,EAAE,eAAe,CAAC,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC;gBACvD,gBAAgB,EAAE,eAAe,CAAC,IAAI,IAAI,QAAQ;gBAClD,aAAa,EAAE,eAAe,CAAC,IAAI,IAAI,SAAS;gBAChD,oBAAoB,EAAE,QAAQ;aACxB,CAAA;YAER,IAAI,eAAe,CAAC,SAAS,EAAE,CAAC;gBAC9B,MAAM,CAAC,eAAe,GAAG,IAAI,CAAA;YAC/B,CAAC;YACD,OAAO,MAAM,CAAA;QACf,CAAC,CAAC,CAAA;QAEF,kGAAkG;QAClG,QAAQ,CAAC,qBAAqB,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,qBAAqB,IAAI,EAAE,CAAC,EAAE,GAAG,aAAa,CAAC,CAAA;IAChG,CAAC;IAED,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;QAC1B,QAAQ,CAAC,qBAAqB,GAAG,aAAa,CAAA;IAChD,CAAC;IAED,MAAM,gBAAgB,GAAQ;QAC5B,CAAC,gBAAgB,CAAC,EAAE,QAAQ;QAC5B,CAAC,mBAAmB,CAAC,EAAE,WAAW;KACnC,CAAA;IACD,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;QAC1B,gBAAgB,CAAC,qBAAqB,CAAC,GAAG,aAAa,CAAA;IACzD,CAAC;IACD,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;QAC1B,gBAAgB,CAAC,qBAAqB,CAAC,GAAG,aAAa,CAAA;IACzD,CAAC;IACD,IAAI,mBAAmB,IAAI,IAAI,EAAE,CAAC;QAChC,gBAAgB,CAAC,2BAA2B,CAAC,GAAG,mBAAmB,CAAA;IACrE,CAAC;IACD,IAAI,iBAAiB,IAAI,IAAI,EAAE,CAAC;QAC9B,gBAAgB,CAAC,yBAAyB,CAAC,GAAG,iBAAiB,CAAA;IACjE,CAAC;IACD,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;QAC3B,gBAAgB,CAAC,sBAAsB,CAAC,GAAG,cAAc,CAAA;IAC3D,CAAC;IACD,IAAI,gBAAgB,IAAI,IAAI,EAAE,CAAC;QAC7B,gBAAgB,CAAC,wBAAwB,CAAC,GAAG,gBAAgB,CAAA;IAC/D,CAAC;IAED,MAAM,OAAO,CAAC,GAAG,CAAC;QAChB,IAAA,0CAA6B,EAAC,CAAC,cAAc,CAAC,EAAE,gBAAgB,CAAC;QACjE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,EAAE,gBAAgB,CAAC,WAAW,EAAE,QAAQ,CAAC,kBAAkB,CAAC;QACrG,IAAA,mBAAc,EAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QAC/C,IAAA,mBAAc,EAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,wBAAwB,CAAC,CAAC;KAC/D,CAAC,CAAA;IAEF,MAAM,WAAW,CACf,0BAA0B,CAAC,aAAa,EAAE,aAAa,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,cAAc,CAAC,EAChH,cAAc,EACd,WAAW,EACX,gBAAgB,CAAC,WAAW,CAC7B,CAAA;IAED,IAAI,gBAAgB,IAAI,IAAI,EAAE,CAAC;QAC7B,MAAM,MAAM,GAAG,gBAAgB,CAAC,WAAW,CAAA;QAC3C,MAAM,MAAM,GAAG,eAAe,CAAA;QAC9B,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,MAAM,GAAG,MAAM,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,CAAA;QAClG,MAAM,QAAQ,CAAC,kBAAkB,EAAE,GAAG,MAAM,GAAG,MAAM,EAAE,EAAE,WAAW,GAAG,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,aAAa,EAAE,GAAG,MAAM,GAAG,MAAM,MAAM,EAAE,GAAG,WAAW,GAAG,MAAM,MAAM,CAAC,CAAC,CAAA;IAC7K,CAAC;IAED,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,OAAO,CAAC,eAAe,MAAM,CAAC,CAAA;IACtE,MAAM,IAAA,iBAAM,EAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,OAAO,CAAC,CAAA;IACjD,mEAAmE;IACnE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAA;IAC7B,MAAM,IAAA,iBAAM,EAAC,OAAO,EAAE,GAAG,EAAE,GAAG,CAAC,CAAA;AACjC,CAAC;AAzOD,oCAyOC;AAED,SAAS,qBAAqB,CAAC,QAAa;IAC1C,yEAAyE;IACzE,IAAI,GAAG,GAAG,QAAQ,CAAC,sBAAsB,CAAA;IACzC,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QAChB,GAAG,GAAG,EAAE,CAAA;QACR,QAAQ,CAAC,sBAAsB,GAAG,GAAG,CAAA;IACvC,CAAC;IAED,GAAG,CAAC,uBAAuB,GAAG,IAAI,CAAA;IAClC,2FAA2F;IAC3F,GAAG,CAAC,sBAAsB,GAAG,IAAI,CAAA;IAEjC,IAAI,gBAAgB,GAAG,GAAG,CAAC,kBAAkB,CAAA;IAC7C,IAAI,gBAAgB,IAAI,IAAI,EAAE,CAAC;QAC7B,gBAAgB,GAAG,EAAE,CAAA;QACrB,GAAG,CAAC,kBAAkB,GAAG,gBAAgB,CAAA;IAC3C,CAAC;IAED,IAAI,gBAAgB,CAAC,SAAS,IAAI,IAAI,EAAE,CAAC;QACvC,MAAM,SAAS,GAAG;YAChB,4CAA4C,EAAE,KAAK;YACnD,oBAAoB,EAAE,KAAK;YAC3B,2CAA2C,EAAE,IAAI;YACjD,qCAAqC,EAAE,KAAK;YAC5C,0CAA0C,EAAE,KAAK;SAClD,CAAA;QACD,gBAAgB,CAAC,SAAS,GAAG,SAAS,CAAA;QACtC,gBAAgB,CAAC,WAAW,CAAC,GAAG,SAAS,CAAA;IAC3C,CAAC;AACH,CAAC", "sourcesContent": ["import BluebirdPromise from \"bluebird-lst\"\nimport { asArray, getPlatformIconFileName, InvalidConfigurationError, log } from \"builder-util\"\nimport { copyOrLinkFile, unlinkIfExists } from \"builder-util/out/fs\"\nimport { rename, utimes } from \"fs/promises\"\nimport * as path from \"path\"\nimport { filterCFBundleIdentifier } from \"../appInfo\"\nimport { AsarIntegrity } from \"../asar/integrity\"\nimport MacPackager from \"../macPackager\"\nimport { normalizeExt } from \"../platformPackager\"\nimport { executeAppBuilderAndWriteJson, executeAppBuilderAsJson } from \"../util/appBuilder\"\nimport { createBrandingOpts } from \"./ElectronFramework\"\n\nfunction doRename(basePath: string, oldName: string, newName: string) {\n  return rename(path.join(basePath, oldName), path.join(basePath, newName))\n}\n\nfunction moveHelpers(helperSuffixes: Array<string>, frameworksPath: string, appName: string, prefix: string): Promise<any> {\n  return BluebirdPromise.map(helperSuffixes, suffix => {\n    const executableBasePath = path.join(frameworksPath, `${prefix}${suffix}.app`, \"Contents\", \"MacOS\")\n    return doRename(executableBasePath, `${prefix}${suffix}`, appName + suffix).then(() => doRename(frameworksPath, `${prefix}${suffix}.app`, `${appName}${suffix}.app`))\n  })\n}\n\nfunction getAvailableHelperSuffixes(\n  helperEHPlist: string | null,\n  helperNPPlist: string | null,\n  helperRendererPlist: string | null,\n  helperPluginPlist: string | null,\n  helperGPUPlist: string | null\n) {\n  const result = [\" Helper\"]\n  if (helperEHPlist != null) {\n    result.push(\" Helper EH\")\n  }\n  if (helperNPPlist != null) {\n    result.push(\" Helper NP\")\n  }\n  if (helperRendererPlist != null) {\n    result.push(\" Helper (Renderer)\")\n  }\n  if (helperPluginPlist != null) {\n    result.push(\" Helper (Plugin)\")\n  }\n  if (helperGPUPlist != null) {\n    result.push(\" Helper (GPU)\")\n  }\n  return result\n}\n\n/** @internal */\nexport async function createMacApp(packager: MacPackager, appOutDir: string, asarIntegrity: AsarIntegrity | null, isMas: boolean) {\n  const appInfo = packager.appInfo\n  // Electon uses the application name (CFBundleName) to resolve helper apps\n  // https://github.com/electron/electron/blob/main/shell/app/electron_main_delegate_mac.mm\n  // https://github.com/electron-userland/electron-builder/issues/6962\n  const appFilename = appInfo.sanitizedProductName\n  const electronBranding = createBrandingOpts(packager.config)\n\n  const contentsPath = path.join(appOutDir, packager.info.framework.distMacOsAppName, \"Contents\")\n  const frameworksPath = path.join(contentsPath, \"Frameworks\")\n  const loginItemPath = path.join(contentsPath, \"Library\", \"LoginItems\")\n\n  const appPlistFilename = path.join(contentsPath, \"Info.plist\")\n  const helperPlistFilename = path.join(frameworksPath, `${electronBranding.productName} Helper.app`, \"Contents\", \"Info.plist\")\n  const helperEHPlistFilename = path.join(frameworksPath, `${electronBranding.productName} Helper EH.app`, \"Contents\", \"Info.plist\")\n  const helperNPPlistFilename = path.join(frameworksPath, `${electronBranding.productName} Helper NP.app`, \"Contents\", \"Info.plist\")\n  const helperRendererPlistFilename = path.join(frameworksPath, `${electronBranding.productName} Helper (Renderer).app`, \"Contents\", \"Info.plist\")\n  const helperPluginPlistFilename = path.join(frameworksPath, `${electronBranding.productName} Helper (Plugin).app`, \"Contents\", \"Info.plist\")\n  const helperGPUPlistFilename = path.join(frameworksPath, `${electronBranding.productName} Helper (GPU).app`, \"Contents\", \"Info.plist\")\n  const helperLoginPlistFilename = path.join(loginItemPath, `${electronBranding.productName} Login Helper.app`, \"Contents\", \"Info.plist\")\n\n  const plistContent: Array<any> = await executeAppBuilderAsJson([\n    \"decode-plist\",\n    \"-f\",\n    appPlistFilename,\n    \"-f\",\n    helperPlistFilename,\n    \"-f\",\n    helperEHPlistFilename,\n    \"-f\",\n    helperNPPlistFilename,\n    \"-f\",\n    helperRendererPlistFilename,\n    \"-f\",\n    helperPluginPlistFilename,\n    \"-f\",\n    helperGPUPlistFilename,\n    \"-f\",\n    helperLoginPlistFilename,\n  ])\n\n  if (plistContent[0] == null) {\n    throw new Error(\"corrupted Electron dist\")\n  }\n\n  const appPlist = plistContent[0]!\n  const helperPlist = plistContent[1]!\n  const helperEHPlist = plistContent[2]\n  const helperNPPlist = plistContent[3]\n  const helperRendererPlist = plistContent[4]\n  const helperPluginPlist = plistContent[5]\n  const helperGPUPlist = plistContent[6]\n  const helperLoginPlist = plistContent[7]\n\n  // if an extend-info file was supplied, copy its contents in first\n  if (plistContent[8] != null) {\n    Object.assign(appPlist, plistContent[8])\n  }\n\n  const buildMetadata = packager.config\n\n  /**\n   * Configure bundleIdentifier for the generic Electron Helper process\n   *\n   * This was the only Helper in Electron 5 and before. Allow users to configure\n   * the bundleIdentifier for continuity.\n   */\n\n  const oldHelperBundleId = (buildMetadata as any)[\"helper-bundle-id\"]\n  if (oldHelperBundleId != null) {\n    log.warn(\"build.helper-bundle-id is deprecated, please set as build.mac.helperBundleId\")\n  }\n\n  const defaultAppId = packager.platformSpecificBuildOptions.appId\n  const cfBundleIdentifier = filterCFBundleIdentifier((isMas ? packager.config.mas?.appId : defaultAppId) || defaultAppId || appInfo.macBundleIdentifier)\n\n  const defaultHelperId = packager.platformSpecificBuildOptions.helperBundleId\n  const helperBundleIdentifier = filterCFBundleIdentifier(\n    (isMas ? packager.config.mas?.helperBundleId : defaultHelperId) || defaultHelperId || oldHelperBundleId || `${cfBundleIdentifier}.helper`\n  )\n\n  appPlist.CFBundleIdentifier = cfBundleIdentifier\n\n  await packager.applyCommonInfo(appPlist, contentsPath)\n\n  // required for electron-updater proxy\n  if (!isMas) {\n    configureLocalhostAts(appPlist)\n  }\n\n  helperPlist.CFBundleExecutable = `${appFilename} Helper`\n  helperPlist.CFBundleDisplayName = `${appInfo.productName} Helper`\n  helperPlist.CFBundleIdentifier = helperBundleIdentifier\n  helperPlist.CFBundleVersion = appPlist.CFBundleVersion\n\n  /**\n   * Configure bundleIdentifier for Electron 5+ Helper processes\n   *\n   * In Electron 6, parts of the generic Electron Helper process were split into\n   * individual helper processes. Allow users to configure the bundleIdentifiers\n   * for continuity, specifically because macOS keychain access relies on\n   * bundleIdentifiers not changing (i.e. across versions of Electron).\n   */\n\n  function configureHelper(helper: any, postfix: string, userProvidedBundleIdentifier?: string | null) {\n    helper.CFBundleExecutable = `${appFilename} Helper ${postfix}`\n    helper.CFBundleDisplayName = `${appInfo.productName} Helper ${postfix}`\n    helper.CFBundleIdentifier = userProvidedBundleIdentifier\n      ? filterCFBundleIdentifier(userProvidedBundleIdentifier)\n      : filterCFBundleIdentifier(`${helperBundleIdentifier}.${postfix}`)\n    helper.CFBundleVersion = appPlist.CFBundleVersion\n  }\n\n  if (helperRendererPlist != null) {\n    configureHelper(helperRendererPlist, \"(Renderer)\", packager.platformSpecificBuildOptions.helperRendererBundleId)\n  }\n  if (helperPluginPlist != null) {\n    configureHelper(helperPluginPlist, \"(Plugin)\", packager.platformSpecificBuildOptions.helperPluginBundleId)\n  }\n  if (helperGPUPlist != null) {\n    configureHelper(helperGPUPlist, \"(GPU)\", packager.platformSpecificBuildOptions.helperGPUBundleId)\n  }\n  if (helperEHPlist != null) {\n    configureHelper(helperEHPlist, \"EH\", packager.platformSpecificBuildOptions.helperEHBundleId)\n  }\n  if (helperNPPlist != null) {\n    configureHelper(helperNPPlist, \"NP\", packager.platformSpecificBuildOptions.helperNPBundleId)\n  }\n  if (helperLoginPlist != null) {\n    helperLoginPlist.CFBundleExecutable = `${appFilename} Login Helper`\n    helperLoginPlist.CFBundleDisplayName = `${appInfo.productName} Login Helper`\n    // noinspection SpellCheckingInspection\n    helperLoginPlist.CFBundleIdentifier = `${cfBundleIdentifier}.loginhelper`\n    helperLoginPlist.CFBundleVersion = appPlist.CFBundleVersion\n  }\n\n  const protocols = asArray(buildMetadata.protocols).concat(asArray(packager.platformSpecificBuildOptions.protocols))\n  if (protocols.length > 0) {\n    appPlist.CFBundleURLTypes = protocols.map(protocol => {\n      const schemes = asArray(protocol.schemes)\n      if (schemes.length === 0) {\n        throw new InvalidConfigurationError(`Protocol \"${protocol.name}\": must be at least one scheme specified`)\n      }\n      return {\n        CFBundleURLName: protocol.name,\n        CFBundleTypeRole: protocol.role || \"Editor\",\n        CFBundleURLSchemes: schemes.slice(),\n      }\n    })\n  }\n\n  const fileAssociations = packager.fileAssociations\n  if (fileAssociations.length > 0) {\n    const documentTypes = await BluebirdPromise.map(fileAssociations, async fileAssociation => {\n      const extensions = asArray(fileAssociation.ext).map(normalizeExt)\n      const customIcon = await packager.getResource(getPlatformIconFileName(fileAssociation.icon, true), `${extensions[0]}.icns`)\n      let iconFile = appPlist.CFBundleIconFile\n      if (customIcon != null) {\n        iconFile = path.basename(customIcon)\n        await copyOrLinkFile(customIcon, path.join(path.join(contentsPath, \"Resources\"), iconFile))\n      }\n\n      const result = {\n        CFBundleTypeExtensions: extensions,\n        CFBundleTypeName: fileAssociation.name || extensions[0],\n        CFBundleTypeRole: fileAssociation.role || \"Editor\",\n        LSHandlerRank: fileAssociation.rank || \"Default\",\n        CFBundleTypeIconFile: iconFile,\n      } as any\n\n      if (fileAssociation.isPackage) {\n        result.LSTypeIsPackage = true\n      }\n      return result\n    })\n\n    // `CFBundleDocumentTypes` may be defined in `mac.extendInfo`, so we need to merge it in that case\n    appPlist.CFBundleDocumentTypes = [...(appPlist.CFBundleDocumentTypes || []), ...documentTypes]\n  }\n\n  if (asarIntegrity != null) {\n    appPlist.ElectronAsarIntegrity = asarIntegrity\n  }\n\n  const plistDataToWrite: any = {\n    [appPlistFilename]: appPlist,\n    [helperPlistFilename]: helperPlist,\n  }\n  if (helperEHPlist != null) {\n    plistDataToWrite[helperEHPlistFilename] = helperEHPlist\n  }\n  if (helperNPPlist != null) {\n    plistDataToWrite[helperNPPlistFilename] = helperNPPlist\n  }\n  if (helperRendererPlist != null) {\n    plistDataToWrite[helperRendererPlistFilename] = helperRendererPlist\n  }\n  if (helperPluginPlist != null) {\n    plistDataToWrite[helperPluginPlistFilename] = helperPluginPlist\n  }\n  if (helperGPUPlist != null) {\n    plistDataToWrite[helperGPUPlistFilename] = helperGPUPlist\n  }\n  if (helperLoginPlist != null) {\n    plistDataToWrite[helperLoginPlistFilename] = helperLoginPlist\n  }\n\n  await Promise.all([\n    executeAppBuilderAndWriteJson([\"encode-plist\"], plistDataToWrite),\n    doRename(path.join(contentsPath, \"MacOS\"), electronBranding.productName, appPlist.CFBundleExecutable),\n    unlinkIfExists(path.join(appOutDir, \"LICENSE\")),\n    unlinkIfExists(path.join(appOutDir, \"LICENSES.chromium.html\")),\n  ])\n\n  await moveHelpers(\n    getAvailableHelperSuffixes(helperEHPlist, helperNPPlist, helperRendererPlist, helperPluginPlist, helperGPUPlist),\n    frameworksPath,\n    appFilename,\n    electronBranding.productName\n  )\n\n  if (helperLoginPlist != null) {\n    const prefix = electronBranding.productName\n    const suffix = \" Login Helper\"\n    const executableBasePath = path.join(loginItemPath, `${prefix}${suffix}.app`, \"Contents\", \"MacOS\")\n    await doRename(executableBasePath, `${prefix}${suffix}`, appFilename + suffix).then(() => doRename(loginItemPath, `${prefix}${suffix}.app`, `${appFilename}${suffix}.app`))\n  }\n\n  const appPath = path.join(appOutDir, `${appInfo.productFilename}.app`)\n  await rename(path.dirname(contentsPath), appPath)\n  // https://github.com/electron-userland/electron-builder/issues/840\n  const now = Date.now() / 1000\n  await utimes(appPath, now, now)\n}\n\nfunction configureLocalhostAts(appPlist: any) {\n  // https://bencoding.com/2015/07/20/app-transport-security-and-localhost/\n  let ats = appPlist.NSAppTransportSecurity\n  if (ats == null) {\n    ats = {}\n    appPlist.NSAppTransportSecurity = ats\n  }\n\n  ats.NSAllowsLocalNetworking = true\n  // https://github.com/electron-userland/electron-builder/issues/3377#issuecomment-446035814\n  ats.NSAllowsArbitraryLoads = true\n\n  let exceptionDomains = ats.NSExceptionDomains\n  if (exceptionDomains == null) {\n    exceptionDomains = {}\n    ats.NSExceptionDomains = exceptionDomains\n  }\n\n  if (exceptionDomains.localhost == null) {\n    const allowHttp = {\n      NSTemporaryExceptionAllowsInsecureHTTPSLoads: false,\n      NSIncludesSubdomains: false,\n      NSTemporaryExceptionAllowsInsecureHTTPLoads: true,\n      NSTemporaryExceptionMinimumTLSVersion: \"1.0\",\n      NSTemporaryExceptionRequiresForwardSecrecy: false,\n    }\n    exceptionDomains.localhost = allowHttp\n    exceptionDomains[\"127.0.0.1\"] = allowHttp\n  }\n}\n"]}