{"version": 3, "file": "CommonWindowsInstallerConfiguration.js", "sourceRoot": "", "sources": ["../../src/options/CommonWindowsInstallerConfiguration.ts"], "names": [], "mappings": ";;;AAAA,+CAAyE;AACzE,+CAAmD;AAqDnD,SAAgB,mBAAmB,CAAC,OAA4C,EAAE,QAAqB;IACrG,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAA;IAEhC,IAAI,YAAY,GAAkB,IAAI,CAAA;IACtC,IAAI,OAAO,CAAC,YAAY,IAAI,IAAI,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,EAAE,CAAC;QACnE,IAAI,OAAO,CAAC,YAAY,KAAK,IAAI,EAAE,CAAC;YAClC,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAA;YAChD,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;gBACxB,MAAM,IAAI,wCAAyB,CAAC,iHAAiH,CAAC,CAAA;YACxJ,CAAC;YACD,YAAY,GAAG,IAAA,2BAAgB,EAAC,WAAW,CAAC,CAAA;QAC9C,CAAC;aAAM,CAAC;YACN,YAAY,GAAG,OAAO,CAAC,YAAY;iBAChC,KAAK,CAAC,OAAO,CAAC;iBACd,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,IAAA,2BAAgB,EAAC,EAAE,CAAC,CAAC;iBAC/B,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,CAAC;IACH,CAAC;IAED,OAAO;QACL,YAAY,EAAE,OAAO,CAAC,UAAU,KAAK,IAAI;QACzC,UAAU,EAAE,OAAO,CAAC,QAAQ,KAAK,KAAK;QAEtC,YAAY,EAAE,IAAA,8BAAe,EAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,oBAAoB,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,YAAY,CAAC;QAC/H,uBAAuB,EAAE,sCAAsC,CAAC,OAAO,CAAC,qBAAqB,CAAC;QAC9F,yBAAyB,EAAE,OAAO,CAAC,uBAAuB,KAAK,KAAK;QACpE,YAAY;KACb,CAAA;AACH,CAAC;AA5BD,kDA4BC;AAED,SAAS,sCAAsC,CAAC,KAAmC;IACjF,IAAI,KAAK,KAAK,KAAK,EAAE,CAAC;QACpB,OAAO,6BAA6B,CAAC,KAAK,CAAA;IAC5C,CAAC;SAAM,IAAI,KAAK,KAAK,QAAQ,EAAE,CAAC;QAC9B,OAAO,6BAA6B,CAAC,MAAM,CAAA;IAC7C,CAAC;SAAM,CAAC;QACN,OAAO,6BAA6B,CAAC,aAAa,CAAA;IACpD,CAAC;AACH,CAAC;AAED,IAAY,6BAIX;AAJD,WAAY,6BAA6B;IACvC,mGAAa,CAAA;IACb,qFAAM,CAAA;IACN,mFAAK,CAAA;AACP,CAAC,EAJW,6BAA6B,6CAA7B,6BAA6B,QAIxC", "sourcesContent": ["import { InvalidConfigurationError, isEmptyOrSpaces } from \"builder-util\"\nimport { sanitizeFileName } from \"../util/filename\"\nimport { WinPackager } from \"../winPackager\"\n\nexport interface CommonWindowsInstallerConfiguration {\n  readonly oneClick?: boolean\n\n  /**\n   * Whether to install per all users (per-machine).\n   * @default false\n   */\n  readonly perMachine?: boolean\n\n  /**\n   * Whether to run the installed application after finish. For assisted installer corresponding checkbox will be removed.\n   * @default true\n   */\n  readonly runAfterFinish?: boolean\n\n  /**\n   * Whether to create desktop shortcut. Set to `always` if to recreate also on reinstall (even if removed by user).\n   * @default true\n   */\n  readonly createDesktopShortcut?: boolean | \"always\"\n\n  /**\n   * Whether to create start menu shortcut.\n   * @default true\n   */\n  readonly createStartMenuShortcut?: boolean\n\n  /**\n   * Whether to create submenu for start menu shortcut and program files directory. If `true`, company name will be used. Or string value.\n   * @default false\n   */\n  readonly menuCategory?: boolean | string\n\n  /**\n   * The name that will be used for all shortcuts. Defaults to the application name.\n   */\n  readonly shortcutName?: string | null\n}\n\nexport interface FinalCommonWindowsInstallerOptions {\n  isAssisted: boolean\n  isPerMachine: boolean\n\n  shortcutName: string\n  menuCategory: string | null\n\n  isCreateDesktopShortcut: DesktopShortcutCreationPolicy\n  isCreateStartMenuShortcut: boolean\n}\n\nexport function getEffectiveOptions(options: CommonWindowsInstallerConfiguration, packager: WinPackager): FinalCommonWindowsInstallerOptions {\n  const appInfo = packager.appInfo\n\n  let menuCategory: string | null = null\n  if (options.menuCategory != null && options.menuCategory !== false) {\n    if (options.menuCategory === true) {\n      const companyName = packager.appInfo.companyName\n      if (companyName == null) {\n        throw new InvalidConfigurationError(`Please specify \"author\" in the application package.json — it is required because \"menuCategory\" is set to true.`)\n      }\n      menuCategory = sanitizeFileName(companyName)\n    } else {\n      menuCategory = options.menuCategory\n        .split(/[/\\\\]/)\n        .map(it => sanitizeFileName(it))\n        .join(\"\\\\\")\n    }\n  }\n\n  return {\n    isPerMachine: options.perMachine === true,\n    isAssisted: options.oneClick === false,\n\n    shortcutName: isEmptyOrSpaces(options.shortcutName) ? appInfo.sanitizedProductName : packager.expandMacro(options.shortcutName),\n    isCreateDesktopShortcut: convertToDesktopShortcutCreationPolicy(options.createDesktopShortcut),\n    isCreateStartMenuShortcut: options.createStartMenuShortcut !== false,\n    menuCategory,\n  }\n}\n\nfunction convertToDesktopShortcutCreationPolicy(value: boolean | undefined | string): DesktopShortcutCreationPolicy {\n  if (value === false) {\n    return DesktopShortcutCreationPolicy.NEVER\n  } else if (value === \"always\") {\n    return DesktopShortcutCreationPolicy.ALWAYS\n  } else {\n    return DesktopShortcutCreationPolicy.FRESH_INSTALL\n  }\n}\n\nexport enum DesktopShortcutCreationPolicy {\n  FRESH_INSTALL,\n  ALWAYS,\n  NEVER,\n}\n"]}