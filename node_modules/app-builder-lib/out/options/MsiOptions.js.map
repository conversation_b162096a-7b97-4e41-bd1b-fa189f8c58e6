{"version": 3, "file": "MsiOptions.js", "sourceRoot": "", "sources": ["../../src/options/MsiOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["import { TargetSpecificOptions } from \"../core\"\nimport { CommonWindowsInstallerConfiguration } from \"./CommonWindowsInstallerConfiguration\"\n\nexport interface MsiOptions extends CommonWindowsInstallerConfiguration, TargetSpecificOptions {\n  /**\n   * One-click installation.\n   * @default true\n   */\n  readonly oneClick?: boolean\n\n  /**\n   * The [upgrade code](https://msdn.microsoft.com/en-us/library/windows/desktop/aa372375(v=vs.85).aspx). Optional, by default generated using app id.\n   */\n  readonly upgradeCode?: string | null\n\n  /**\n   * If `warningsAsErrors` is `true` (default): treat warnings as errors. If `warningsAsErrors` is `false`: allow warnings.\n   * @default true\n   */\n  readonly warningsAsErrors?: boolean\n\n  /**\n   * Any additional arguments to be passed to the WiX installer compiler, such as `[\"-ext\", \"WixUtilExtension\"]`\n   */\n  readonly additionalWixArgs?: Array<string> | null\n}\n"]}