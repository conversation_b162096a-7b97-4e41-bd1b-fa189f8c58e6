<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="3caed8aa-31ae-4b3d-ad18-6f9796663516" name="Default" comment="">
      <change type="MODIFICATION" beforePath="$PROJECT_DIR$/.travis.yml" afterPath="$PROJECT_DIR$/.travis.yml" />
      <change type="MODIFICATION" beforePath="$PROJECT_DIR$/CHANGELOG.md" afterPath="$PROJECT_DIR$/CHANGELOG.md" />
    </list>
    <ignored path="$PROJECT_DIR$/.tmp/" />
    <ignored path="$PROJECT_DIR$/temp/" />
    <ignored path="$PROJECT_DIR$/tmp/" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="TRACKING_ENABLED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="300">
      <file leaf-file-name="package.json" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/package.json">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="34">
              <caret line="2" column="19" lean-forward="false" selection-start-line="2" selection-start-column="19" selection-end-line="2" selection-end-column="19" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="README.md" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/README.md">
          <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
            <state split_layout="SPLIT">
              <first_editor relative-caret-position="2312">
                <caret line="136" column="67" lean-forward="false" selection-start-line="136" selection-start-column="67" selection-end-line="136" selection-end-column="67" />
                <folding>
                  <marker date="1497272379133" expanded="true" signature="590:646" ph="{...}" />
                  <marker date="1497272379133" expanded="true" signature="601:644" ph="{&quot;host&quot;: 'localhost'...}" />
                  <marker date="1497272379133" expanded="true" signature="674:737" ph="{&quot;host&quot;: 'example.com'...}" />
                  <marker date="1497272379133" expanded="true" signature="884:1330" ph="{&quot;maxSockets&quot;: poolSize...}" />
                  <marker date="1497272379133" expanded="true" signature="964:1328" ph="{&quot;host&quot;: proxyHost...}" />
                  <marker date="1497272379133" expanded="true" signature="1103:1192" ph="//..." />
                  <marker date="1497272379133" expanded="true" signature="1290:1324" ph="{&quot;User-Agent&quot;: 'Node'...}" />
                  <marker date="1497272379133" expanded="true" signature="1357:1419" ph="{&quot;host&quot;: 'example.com'...}" />
                  <marker date="1497272379133" expanded="true" signature="1514:2209" ph="{&quot;maxSockets&quot;: poolSize...}" />
                  <marker date="1497272379133" expanded="true" signature="1540:1623" ph="//..." />
                  <marker date="1497272379133" expanded="true" signature="1842:2207" ph="{&quot;host&quot;: proxyHost...}" />
                  <marker date="1497272379133" expanded="true" signature="1981:2070" ph="//..." />
                  <marker date="1497272379133" expanded="true" signature="2168:2202" ph="{&quot;User-Agent&quot;: 'Node'...}" />
                  <marker date="1497272379133" expanded="true" signature="2237:2300" ph="{&quot;host&quot;: 'example.com'...}" />
                  <marker date="1497272379133" expanded="true" signature="2395:3180" ph="{&quot;maxSockets&quot;: poolSize...}" />
                  <marker date="1497272379133" expanded="true" signature="2475:3178" ph="{&quot;host&quot;: proxyHost...}" />
                  <marker date="1497272379133" expanded="true" signature="2615:2704" ph="//..." />
                  <marker date="1497272379133" expanded="true" signature="2802:2836" ph="{&quot;User-Agent&quot;: 'Node'...}" />
                  <marker date="1497272379133" expanded="true" signature="3207:3269" ph="{&quot;host&quot;: 'example.com'...}" />
                  <marker date="1497272379133" expanded="true" signature="3366:4398" ph="{&quot;maxSockets&quot;: poolSize...}" />
                  <marker date="1497272379133" expanded="true" signature="3392:3475" ph="//..." />
                  <marker date="1497272379133" expanded="true" signature="3694:4396" ph="{&quot;host&quot;: proxyHost...}" />
                  <marker date="1497272379133" expanded="true" signature="3834:3923" ph="//..." />
                  <marker date="1497272379133" expanded="true" signature="4021:4055" ph="{&quot;User-Agent&quot;: 'Node'...}" />
                  <marker date="1497272379133" expanded="true" signature="4426:4489" ph="{&quot;host&quot;: 'example.com'...}" />
                </folding>
              </first_editor>
              <second_editor />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name=".travis.yml" pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/.travis.yml">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="102">
              <caret line="6" column="0" lean-forward="true" selection-start-line="6" selection-start-column="0" selection-end-line="6" selection-end-column="0" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="tunnel.js" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/lib/tunnel.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="697">
              <caret line="41" column="19" lean-forward="false" selection-start-line="41" selection-start-column="19" selection-end-line="41" selection-end-column="19" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="http-over-http-error.js" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/test/http-over-http-error.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="935">
              <caret line="55" column="26" lean-forward="true" selection-start-line="55" selection-start-column="26" selection-end-line="55" selection-end-column="26" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="http-over-http-error2.js" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/test/http-over-http-error2.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="1207">
              <caret line="71" column="0" lean-forward="false" selection-start-line="71" selection-start-column="0" selection-end-line="71" selection-end-column="0" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="https-over-http.js" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/test/https-over-http.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="1479">
              <caret line="87" column="0" lean-forward="false" selection-start-line="87" selection-start-column="0" selection-end-line="87" selection-end-column="0" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="https-over-https.js" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/test/https-over-https.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="0">
              <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="http-over-http.js" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/test/http-over-http.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="1088">
              <caret line="64" column="26" lean-forward="true" selection-start-line="64" selection-start-column="26" selection-end-line="64" selection-end-column="26" />
              <folding />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="CHANGELOG.md" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/CHANGELOG.md">
          <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
            <state split_layout="SPLIT">
              <first_editor relative-caret-position="102">
                <caret line="6" column="0" lean-forward="false" selection-start-line="6" selection-start-column="0" selection-end-line="6" selection-end-column="0" />
                <folding />
              </first_editor>
              <second_editor />
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="JavaScript File" />
      </list>
    </option>
  </component>
  <component name="FindInProjectRecents">
    <findStrings>
      <find>max</find>
      <find>onconne</find>
    </findStrings>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/test/http-over-http-error.js" />
        <option value="$PROJECT_DIR$/README.md" />
        <option value="$PROJECT_DIR$/package.json" />
        <option value="$PROJECT_DIR$/test/http-over-http-error2.js" />
        <option value="$PROJECT_DIR$/test/https-over-http-localaddress.js" />
        <option value="$PROJECT_DIR$/test/https-over-http.js" />
        <option value="$PROJECT_DIR$/lib/tunnel.js" />
        <option value="$PROJECT_DIR$/CHANGELOG.md" />
        <option value="$PROJECT_DIR$/.travis.yml" />
      </list>
    </option>
  </component>
  <component name="JsBuildToolGruntFileManager" detection-done="true" sorting="DEFINITION_ORDER" />
  <component name="JsBuildToolPackageJson" detection-done="true" sorting="DEFINITION_ORDER">
    <package-json value="$PROJECT_DIR$/package.json" />
  </component>
  <component name="JsFlowSettings">
    <service-enabled>false</service-enabled>
    <exe-path />
    <annotation-enable>false</annotation-enable>
    <other-services-enabled>false</other-services-enabled>
    <auto-save>true</auto-save>
  </component>
  <component name="JsGulpfileManager">
    <detection-done>true</detection-done>
    <sorting>DEFINITION_ORDER</sorting>
  </component>
  <component name="NodeModulesDirectoryManager">
    <handled-path value="$PROJECT_DIR$/node_modules" />
  </component>
  <component name="ProjectFrameBounds">
    <option name="x" value="785" />
    <option name="y" value="40" />
    <option name="width" value="1788" />
    <option name="height" value="1407" />
  </component>
  <component name="ProjectView">
    <navigator currentView="ProjectPane" proportions="" version="1">
      <flattenPackages />
      <showMembers />
      <showModules />
      <showLibraryContents />
      <hideEmptyPackages />
      <abbreviatePackageNames />
      <autoscrollToSource />
      <autoscrollFromSource ProjectPane="true" />
      <sortByType />
      <manualOrder />
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="Scope" />
      <pane id="Scratches" />
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="node-tunnel" type="b2602c69:ProjectViewProjectNode" />
              <item name="node-tunnel" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="node-tunnel" type="b2602c69:ProjectViewProjectNode" />
              <item name="node-tunnel" type="462c0819:PsiDirectoryNode" />
              <item name="lib" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="node-tunnel" type="b2602c69:ProjectViewProjectNode" />
              <item name="node-tunnel" type="462c0819:PsiDirectoryNode" />
              <item name="test" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="HbShouldOpenHtmlAsHb" value="" />
    <property name="nodejs_interpreter_path" value="$PROJECT_DIR$/../../nvmw/v6.10.3/node" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\git\koichik\node-tunnel\test" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="RunManager">
    <configuration default="true" type="js.build_tools.gulp" factoryName="Gulp.js">
      <node-interpreter>project</node-interpreter>
      <node-options />
      <gulpfile />
      <tasks />
      <arguments />
      <envs />
    </configuration>
    <configuration default="true" type="DartCommandLineRunConfigurationType" factoryName="Dart Command Line Application">
      <method />
    </configuration>
    <configuration default="true" type="DartTestRunConfigurationType" factoryName="Dart Test">
      <method />
    </configuration>
    <configuration default="true" type="JavaScriptTestRunnerJest" factoryName="Jest">
      <node-interpreter value="project" />
      <working-dir value="" />
      <envs />
      <scope-kind value="ALL" />
      <method />
    </configuration>
    <configuration default="true" type="JavaScriptTestRunnerKarma" factoryName="Karma">
      <config-file value="" />
      <node-interpreter value="project" />
      <envs />
      <method />
    </configuration>
    <configuration default="true" type="JavaScriptTestRunnerProtractor" factoryName="Protractor">
      <config-file value="" />
      <node-interpreter value="project" />
      <envs />
      <method />
    </configuration>
    <configuration default="true" type="JavascriptDebugType" factoryName="JavaScript Debug">
      <method />
    </configuration>
    <configuration default="true" type="NodeJSConfigurationType" factoryName="Node.js" path-to-node="project" working-dir="">
      <method />
    </configuration>
    <configuration default="true" type="cucumber.js" factoryName="Cucumber.js">
      <option name="cucumberJsArguments" value="" />
      <option name="executablePath" />
      <option name="filePath" />
      <method />
    </configuration>
    <configuration default="true" type="js.build_tools.npm" factoryName="npm">
      <command value="run" />
      <scripts />
      <node-interpreter value="project" />
      <envs />
      <method />
    </configuration>
    <configuration default="true" type="mocha-javascript-test-runner" factoryName="Mocha">
      <node-interpreter>project</node-interpreter>
      <node-options />
      <working-directory />
      <pass-parent-env>true</pass-parent-env>
      <envs />
      <ui />
      <extra-mocha-options />
      <test-kind>DIRECTORY</test-kind>
      <test-directory />
      <recursive>false</recursive>
      <method />
    </configuration>
  </component>
  <component name="ShelveChangesManager" show_recycled="false">
    <option name="remove_strategy" value="false" />
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="3caed8aa-31ae-4b3d-ad18-6f9796663516" name="Default" comment="" />
      <created>1497256565348</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1497256565348</updated>
      <workItem from="1497256566573" duration="8794000" />
      <workItem from="1497272051717" duration="2328000" />
      <workItem from="1536577850117" duration="8708000" />
      <workItem from="1536636907096" duration="739000" />
    </task>
    <servers />
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="20569000" />
  </component>
  <component name="ToolWindowManager">
    <frame x="785" y="40" width="1788" height="1407" extended-state="0" />
    <editor active="true" />
    <layout>
      <window_info id="Project" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="0" side_tool="false" content_ui="combo" />
      <window_info id="TODO" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="6" side_tool="false" content_ui="tabs" />
      <window_info id="SvgViewer" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
      <window_info id="Event Log" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="7" side_tool="true" content_ui="tabs" />
      <window_info id="Run" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="2" side_tool="false" content_ui="tabs" />
      <window_info id="Version Control" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="true" show_stripe_button="true" weight="0.32967034" sideWeight="0.5" order="7" side_tool="false" content_ui="tabs" />
      <window_info id="npm" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="2" side_tool="true" content_ui="tabs" />
      <window_info id="Structure" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Terminal" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="7" side_tool="false" content_ui="tabs" />
      <window_info id="Debug" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="3" side_tool="false" content_ui="tabs" />
      <window_info id="Favorites" active="false" anchor="left" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="2" side_tool="true" content_ui="tabs" />
      <window_info id="Cvs" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="4" side_tool="false" content_ui="tabs" />
      <window_info id="Message" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="0" side_tool="false" content_ui="tabs" />
      <window_info id="Commander" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="0" side_tool="false" content_ui="tabs" />
      <window_info id="Inspection" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.4" sideWeight="0.5" order="5" side_tool="false" content_ui="tabs" />
      <window_info id="Hierarchy" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="2" side_tool="false" content_ui="combo" />
      <window_info id="Find" active="false" anchor="bottom" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.33" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
      <window_info id="Ant Build" active="false" anchor="right" auto_hide="false" internal_type="DOCKED" type="DOCKED" visible="false" show_stripe_button="true" weight="0.25" sideWeight="0.5" order="1" side_tool="false" content_ui="tabs" />
    </layout>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="VcsContentAnnotationSettings">
    <option name="myLimit" value="**********" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager />
    <watches-manager />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/package.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="34">
          <caret line="2" column="19" lean-forward="false" selection-start-line="2" selection-start-column="19" selection-end-line="2" selection-end-column="19" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/README.md">
      <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
        <state split_layout="SPLIT">
          <first_editor relative-caret-position="2312">
            <caret line="136" column="67" lean-forward="false" selection-start-line="136" selection-start-column="67" selection-end-line="136" selection-end-column="67" />
            <folding>
              <marker date="1497272379133" expanded="true" signature="590:646" ph="{...}" />
              <marker date="1497272379133" expanded="true" signature="601:644" ph="{&quot;host&quot;: 'localhost'...}" />
              <marker date="1497272379133" expanded="true" signature="674:737" ph="{&quot;host&quot;: 'example.com'...}" />
              <marker date="1497272379133" expanded="true" signature="884:1330" ph="{&quot;maxSockets&quot;: poolSize...}" />
              <marker date="1497272379133" expanded="true" signature="964:1328" ph="{&quot;host&quot;: proxyHost...}" />
              <marker date="1497272379133" expanded="true" signature="1103:1192" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="1290:1324" ph="{&quot;User-Agent&quot;: 'Node'...}" />
              <marker date="1497272379133" expanded="true" signature="1357:1419" ph="{&quot;host&quot;: 'example.com'...}" />
              <marker date="1497272379133" expanded="true" signature="1514:2209" ph="{&quot;maxSockets&quot;: poolSize...}" />
              <marker date="1497272379133" expanded="true" signature="1540:1623" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="1842:2207" ph="{&quot;host&quot;: proxyHost...}" />
              <marker date="1497272379133" expanded="true" signature="1981:2070" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="2168:2202" ph="{&quot;User-Agent&quot;: 'Node'...}" />
              <marker date="1497272379133" expanded="true" signature="2237:2300" ph="{&quot;host&quot;: 'example.com'...}" />
              <marker date="1497272379133" expanded="true" signature="2395:3180" ph="{&quot;maxSockets&quot;: poolSize...}" />
              <marker date="1497272379133" expanded="true" signature="2475:3178" ph="{&quot;host&quot;: proxyHost...}" />
              <marker date="1497272379133" expanded="true" signature="2615:2704" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="2802:2836" ph="{&quot;User-Agent&quot;: 'Node'...}" />
              <marker date="1497272379133" expanded="true" signature="3207:3269" ph="{&quot;host&quot;: 'example.com'...}" />
              <marker date="1497272379133" expanded="true" signature="3366:4398" ph="{&quot;maxSockets&quot;: poolSize...}" />
              <marker date="1497272379133" expanded="true" signature="3392:3475" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="3694:4396" ph="{&quot;host&quot;: proxyHost...}" />
              <marker date="1497272379133" expanded="true" signature="3834:3923" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="4021:4055" ph="{&quot;User-Agent&quot;: 'Node'...}" />
              <marker date="1497272379133" expanded="true" signature="4426:4489" ph="{&quot;host&quot;: 'example.com'...}" />
            </folding>
          </first_editor>
          <second_editor />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/.travis.yml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="102">
          <caret line="6" column="0" lean-forward="true" selection-start-line="6" selection-start-column="0" selection-end-line="6" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/http-over-http-error.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="935">
          <caret line="55" column="26" lean-forward="true" selection-start-line="55" selection-start-column="26" selection-end-line="55" selection-end-column="26" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/http-over-http-error2.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="1207">
          <caret line="71" column="0" lean-forward="false" selection-start-line="71" selection-start-column="0" selection-end-line="71" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/https-over-http.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="1479">
          <caret line="87" column="0" lean-forward="false" selection-start-line="87" selection-start-column="0" selection-end-line="87" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/https-over-https.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/http-over-http.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="1088">
          <caret line="64" column="26" lean-forward="true" selection-start-line="64" selection-start-column="26" selection-end-line="64" selection-end-column="26" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/tunnel.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="697">
          <caret line="41" column="19" lean-forward="false" selection-start-line="41" selection-start-column="19" selection-end-line="41" selection-end-column="19" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/package.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="34">
          <caret line="2" column="19" lean-forward="false" selection-start-line="2" selection-start-column="19" selection-end-line="2" selection-end-column="19" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/README.md">
      <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
        <state split_layout="SPLIT">
          <first_editor relative-caret-position="2312">
            <caret line="136" column="67" lean-forward="false" selection-start-line="136" selection-start-column="67" selection-end-line="136" selection-end-column="67" />
            <folding>
              <marker date="1497272379133" expanded="true" signature="590:646" ph="{...}" />
              <marker date="1497272379133" expanded="true" signature="601:644" ph="{&quot;host&quot;: 'localhost'...}" />
              <marker date="1497272379133" expanded="true" signature="674:737" ph="{&quot;host&quot;: 'example.com'...}" />
              <marker date="1497272379133" expanded="true" signature="884:1330" ph="{&quot;maxSockets&quot;: poolSize...}" />
              <marker date="1497272379133" expanded="true" signature="964:1328" ph="{&quot;host&quot;: proxyHost...}" />
              <marker date="1497272379133" expanded="true" signature="1103:1192" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="1290:1324" ph="{&quot;User-Agent&quot;: 'Node'...}" />
              <marker date="1497272379133" expanded="true" signature="1357:1419" ph="{&quot;host&quot;: 'example.com'...}" />
              <marker date="1497272379133" expanded="true" signature="1514:2209" ph="{&quot;maxSockets&quot;: poolSize...}" />
              <marker date="1497272379133" expanded="true" signature="1540:1623" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="1842:2207" ph="{&quot;host&quot;: proxyHost...}" />
              <marker date="1497272379133" expanded="true" signature="1981:2070" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="2168:2202" ph="{&quot;User-Agent&quot;: 'Node'...}" />
              <marker date="1497272379133" expanded="true" signature="2237:2300" ph="{&quot;host&quot;: 'example.com'...}" />
              <marker date="1497272379133" expanded="true" signature="2395:3180" ph="{&quot;maxSockets&quot;: poolSize...}" />
              <marker date="1497272379133" expanded="true" signature="2475:3178" ph="{&quot;host&quot;: proxyHost...}" />
              <marker date="1497272379133" expanded="true" signature="2615:2704" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="2802:2836" ph="{&quot;User-Agent&quot;: 'Node'...}" />
              <marker date="1497272379133" expanded="true" signature="3207:3269" ph="{&quot;host&quot;: 'example.com'...}" />
              <marker date="1497272379133" expanded="true" signature="3366:4398" ph="{&quot;maxSockets&quot;: poolSize...}" />
              <marker date="1497272379133" expanded="true" signature="3392:3475" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="3694:4396" ph="{&quot;host&quot;: proxyHost...}" />
              <marker date="1497272379133" expanded="true" signature="3834:3923" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="4021:4055" ph="{&quot;User-Agent&quot;: 'Node'...}" />
              <marker date="1497272379133" expanded="true" signature="4426:4489" ph="{&quot;host&quot;: 'example.com'...}" />
            </folding>
          </first_editor>
          <second_editor />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/tunnel.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="2550">
          <caret line="150" column="0" lean-forward="false" selection-start-line="150" selection-start-column="0" selection-end-line="150" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/CHANGELOG.md">
      <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
        <state split_layout="SPLIT">
          <first_editor relative-caret-position="51">
            <caret line="3" column="21" lean-forward="false" selection-start-line="3" selection-start-column="21" selection-end-line="3" selection-end-column="21" />
            <folding />
          </first_editor>
          <second_editor />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/.travis.yml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="119">
          <caret line="7" column="0" lean-forward="true" selection-start-line="7" selection-start-column="0" selection-end-line="7" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/package.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/README.md">
      <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
        <state split_layout="SPLIT">
          <first_editor relative-caret-position="0">
            <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
            <folding>
              <marker date="1497272379133" expanded="true" signature="590:646" ph="{...}" />
              <marker date="1497272379133" expanded="true" signature="601:644" ph="{&quot;host&quot;: 'localhost'...}" />
              <marker date="1497272379133" expanded="true" signature="674:737" ph="{&quot;host&quot;: 'example.com'...}" />
              <marker date="1497272379133" expanded="true" signature="884:1330" ph="{&quot;maxSockets&quot;: poolSize...}" />
              <marker date="1497272379133" expanded="true" signature="964:1328" ph="{&quot;host&quot;: proxyHost...}" />
              <marker date="1497272379133" expanded="true" signature="1103:1192" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="1290:1324" ph="{&quot;User-Agent&quot;: 'Node'...}" />
              <marker date="1497272379133" expanded="true" signature="1357:1419" ph="{&quot;host&quot;: 'example.com'...}" />
              <marker date="1497272379133" expanded="true" signature="1514:2209" ph="{&quot;maxSockets&quot;: poolSize...}" />
              <marker date="1497272379133" expanded="true" signature="1540:1623" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="1842:2207" ph="{&quot;host&quot;: proxyHost...}" />
              <marker date="1497272379133" expanded="true" signature="1981:2070" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="2168:2202" ph="{&quot;User-Agent&quot;: 'Node'...}" />
              <marker date="1497272379133" expanded="true" signature="2237:2300" ph="{&quot;host&quot;: 'example.com'...}" />
              <marker date="1497272379133" expanded="true" signature="2395:3180" ph="{&quot;maxSockets&quot;: poolSize...}" />
              <marker date="1497272379133" expanded="true" signature="2475:3178" ph="{&quot;host&quot;: proxyHost...}" />
              <marker date="1497272379133" expanded="true" signature="2615:2704" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="2802:2836" ph="{&quot;User-Agent&quot;: 'Node'...}" />
              <marker date="1497272379133" expanded="true" signature="3207:3269" ph="{&quot;host&quot;: 'example.com'...}" />
              <marker date="1497272379133" expanded="true" signature="3366:4398" ph="{&quot;maxSockets&quot;: poolSize...}" />
              <marker date="1497272379133" expanded="true" signature="3392:3475" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="3694:4396" ph="{&quot;host&quot;: proxyHost...}" />
              <marker date="1497272379133" expanded="true" signature="3834:3923" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="4021:4055" ph="{&quot;User-Agent&quot;: 'Node'...}" />
              <marker date="1497272379133" expanded="true" signature="4426:4489" ph="{&quot;host&quot;: 'example.com'...}" />
            </folding>
          </first_editor>
          <second_editor />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/.travis.yml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/tunnel.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="2550">
          <caret line="150" column="0" lean-forward="false" selection-start-line="150" selection-start-column="0" selection-end-line="150" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/https-over-https.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/https-over-https-error.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/http-over-http.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="136">
          <caret line="8" column="0" lean-forward="false" selection-start-line="7" selection-start-column="0" selection-end-line="8" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/http-over-http-error.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="1309">
          <caret line="77" column="0" lean-forward="false" selection-start-line="77" selection-start-column="0" selection-end-line="77" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/http-over-https.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/http-over-https.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/https-over-https-error.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/README.md">
      <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
        <state split_layout="SPLIT">
          <first_editor relative-caret-position="2312">
            <caret line="136" column="67" lean-forward="false" selection-start-line="136" selection-start-column="67" selection-end-line="136" selection-end-column="67" />
            <folding>
              <marker date="1497272379133" expanded="true" signature="590:646" ph="{...}" />
              <marker date="1497272379133" expanded="true" signature="601:644" ph="{&quot;host&quot;: 'localhost'...}" />
              <marker date="1497272379133" expanded="true" signature="674:737" ph="{&quot;host&quot;: 'example.com'...}" />
              <marker date="1497272379133" expanded="true" signature="884:1330" ph="{&quot;maxSockets&quot;: poolSize...}" />
              <marker date="1497272379133" expanded="true" signature="964:1328" ph="{&quot;host&quot;: proxyHost...}" />
              <marker date="1497272379133" expanded="true" signature="1103:1192" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="1290:1324" ph="{&quot;User-Agent&quot;: 'Node'...}" />
              <marker date="1497272379133" expanded="true" signature="1357:1419" ph="{&quot;host&quot;: 'example.com'...}" />
              <marker date="1497272379133" expanded="true" signature="1514:2209" ph="{&quot;maxSockets&quot;: poolSize...}" />
              <marker date="1497272379133" expanded="true" signature="1540:1623" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="1842:2207" ph="{&quot;host&quot;: proxyHost...}" />
              <marker date="1497272379133" expanded="true" signature="1981:2070" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="2168:2202" ph="{&quot;User-Agent&quot;: 'Node'...}" />
              <marker date="1497272379133" expanded="true" signature="2237:2300" ph="{&quot;host&quot;: 'example.com'...}" />
              <marker date="1497272379133" expanded="true" signature="2395:3180" ph="{&quot;maxSockets&quot;: poolSize...}" />
              <marker date="1497272379133" expanded="true" signature="2475:3178" ph="{&quot;host&quot;: proxyHost...}" />
              <marker date="1497272379133" expanded="true" signature="2615:2704" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="2802:2836" ph="{&quot;User-Agent&quot;: 'Node'...}" />
              <marker date="1497272379133" expanded="true" signature="3207:3269" ph="{&quot;host&quot;: 'example.com'...}" />
              <marker date="1497272379133" expanded="true" signature="3366:4398" ph="{&quot;maxSockets&quot;: poolSize...}" />
              <marker date="1497272379133" expanded="true" signature="3392:3475" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="3694:4396" ph="{&quot;host&quot;: proxyHost...}" />
              <marker date="1497272379133" expanded="true" signature="3834:3923" ph="//..." />
              <marker date="1497272379133" expanded="true" signature="4021:4055" ph="{&quot;User-Agent&quot;: 'Node'...}" />
              <marker date="1497272379133" expanded="true" signature="4426:4489" ph="{&quot;host&quot;: 'example.com'...}" />
            </folding>
          </first_editor>
          <second_editor />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/package.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="34">
          <caret line="2" column="19" lean-forward="false" selection-start-line="2" selection-start-column="19" selection-end-line="2" selection-end-column="19" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/http-over-http-error.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="935">
          <caret line="55" column="26" lean-forward="true" selection-start-line="55" selection-start-column="26" selection-end-line="55" selection-end-column="26" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/http-over-http-error2.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="1207">
          <caret line="71" column="0" lean-forward="false" selection-start-line="71" selection-start-column="0" selection-end-line="71" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/https-over-http-localaddress.js" />
    <entry file="file://$PROJECT_DIR$/test/https-over-https.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="0">
          <caret line="0" column="0" lean-forward="false" selection-start-line="0" selection-start-column="0" selection-end-line="0" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/http-over-http.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="1088">
          <caret line="64" column="26" lean-forward="true" selection-start-line="64" selection-start-column="26" selection-end-line="64" selection-end-column="26" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/https-over-http.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="1479">
          <caret line="87" column="0" lean-forward="false" selection-start-line="87" selection-start-column="0" selection-end-line="87" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/tunnel.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="697">
          <caret line="41" column="19" lean-forward="false" selection-start-line="41" selection-start-column="19" selection-end-line="41" selection-end-column="19" />
          <folding />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/CHANGELOG.md">
      <provider selected="true" editor-type-id="split-provider[text-editor;markdown-preview-editor]">
        <state split_layout="SPLIT">
          <first_editor relative-caret-position="102">
            <caret line="6" column="0" lean-forward="false" selection-start-line="6" selection-start-column="0" selection-end-line="6" selection-end-column="0" />
            <folding />
          </first_editor>
          <second_editor />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/.travis.yml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="102">
          <caret line="6" column="0" lean-forward="true" selection-start-line="6" selection-start-column="0" selection-end-line="6" selection-end-column="0" />
          <folding />
        </state>
      </provider>
    </entry>
  </component>
</project>