{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../src/util.ts"], "names": [], "mappings": ";;;AAAA,qDAAgD;AAChD,+DAAwD;AACxD,+BAA8B;AAC9B,iDAAqF;AACrF,6CAA6C;AAC7C,mCAAmC;AACnC,iCAA0B;AAC1B,qCAA8B;AAC9B,6BAA4B;AAC5B,+BAAkC;AAClC,2DAAgE;AAChE,gCAAkC;AAElC,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,IAAI,EAAE,CAAC;IACvC,IAAA,4BAAgB,GAAE,CAAA;AACpB,CAAC;AAED,6DAAwD;AAA/C,yHAAA,iBAAiB,OAAA;AAC1B,uCAAkC;AAAzB,mGAAA,MAAM,OAAA;AACf,6BAAkC;AAAzB,0FAAA,GAAG,OAAA;AAAE,4FAAA,KAAK,OAAA;AACnB,+BAAiI;AAAxH,4FAAA,IAAI,OAAA;AAAE,uGAAA,eAAe,OAAA;AAAE,yGAAA,iBAAiB,OAAA;AAAE,qGAAA,aAAa,OAAA;AAAY,sGAAA,cAAc,OAAA;AAAE,6GAAA,qBAAqB,OAAA;AACjH,uDAAqD;AAA5C,oHAAA,gBAAgB,OAAA;AACzB,6CAA2C;AAAlC,0GAAA,WAAW,OAAA;AAEpB,2BAAuC;AAA9B,8FAAA,QAAQ,OAAA;AAAE,4FAAA,MAAM,OAAA;AACzB,6DAA8C;AAArC,+GAAA,OAAO,OAAA;AAEhB,2CAAyC;AAAhC,wGAAA,UAAU,OAAA;AAEnB,8BAA6C;AAApC,kGAAA,UAAU,OAAA;AAAE,iGAAA,SAAS,OAAA;AAEjB,QAAA,OAAO,GAAG,IAAA,eAAM,EAAC,qBAAqB,CAAC,CAAA;AAEpD,SAAgB,eAAe,CAAC,MAAW,EAAE,WAAW,GAAG,KAAK,EAAE,MAAM,GAAG,KAAK;IAC9E,OAAO,IAAA,cAAI,EAAC,MAAM,EAAE;QAClB,SAAS,EAAE,IAAI;QACf,WAAW;QACX,MAAM;KACP,CAAC,CAAA;AACJ,CAAC;AAND,0CAMC;AAED,SAAgB,cAAc,CAAC,KAAa;IAC1C,OAAO,KAAK,CAAC,OAAO,CAAC,yEAAyE,EAAE,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QAChH,IAAI,EAAE,CAAC,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC,mBAAmB,CAAC,EAAE,CAAC;YAC7D,UAAU;YACV,OAAO,GAAG,EAAE,GAAG,EAAE,EAAE,CAAA;QACrB,CAAC;QACD,OAAO,GAAG,EAAE,GAAG,IAAA,mBAAU,EAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAA;IAC9E,CAAC,CAAC,CAAA;AACJ,CAAC;AARD,wCAQC;AAED,SAAS,aAAa,CAAC,GAA6D;IAClF,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;QACjC,OAAO,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAA;IACtC,CAAC;IAED,MAAM,QAAQ,GAAG;QACf,GAAG,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,CAAC;KACxB,CAAA;IAED,4DAA4D;IAC5D,kIAAkI;IAClI,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,SAAS,CAAC,CAAC,CAAC,aAAa,CAAA;IAC3F,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAA;IACtB,QAAQ,CAAC,QAAQ,GAAG,MAAM,CAAA;IAC1B,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAA;IACxB,OAAO,QAAQ,CAAA;AACjB,CAAC;AAED,SAAgB,IAAI,CAAC,IAAY,EAAE,IAA2B,EAAE,OAAyB,EAAE,eAAe,GAAG,IAAI;IAC/G,IAAI,SAAG,CAAC,cAAc,EAAE,CAAC;QACvB,MAAM,SAAS,GAAQ;YACrB,IAAI;YACJ,IAAI,EAAE,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACzD,CAAA;QACD,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;YACpB,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;gBACxB,SAAS,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAA;YAC7B,CAAC;YAED,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;gBACxB,MAAM,OAAO,GAAG,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAA;gBAClC,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;oBAC5C,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC5C,OAAO,OAAO,CAAC,IAAI,CAAC,CAAA;oBACtB,CAAC;gBACH,CAAC;gBACD,SAAS,CAAC,GAAG,GAAG,IAAA,wCAAiB,EAAC,OAAO,CAAC,CAAA;YAC5C,CAAC;QACH,CAAC;QAED,SAAG,CAAC,KAAK,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACnC,CAAC;IAED,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC7C,IAAA,wBAAQ,EACN,IAAI,EACJ,IAAI,EACJ;YACE,GAAG,OAAO;YACV,SAAS,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI;YAC7B,GAAG,EAAE,aAAa,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC;SACzD,EACD,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE;YACxB,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;gBAClB,IAAI,eAAe,IAAI,SAAG,CAAC,cAAc,EAAE,CAAC;oBAC1C,MAAM,SAAS,GAAQ;wBACrB,IAAI;qBACL,CAAA;oBACD,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACtB,SAAS,CAAC,MAAM,GAAG,MAAM,CAAA;oBAC3B,CAAC;oBACD,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACtB,SAAS,CAAC,MAAM,GAAG,MAAM,CAAA;oBAC3B,CAAC;oBAED,SAAG,CAAC,KAAK,CAAC,SAAS,EAAE,UAAU,CAAC,CAAA;gBAClC,CAAC;gBACD,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAA;YAC5B,CAAC;iBAAM,CAAC;gBACN,IAAI,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,cAAc,CAAC,cAAe,KAAa,CAAC,IAAI,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA;gBAC9F,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACxB,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;wBAC1B,MAAM,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAA;oBAC5B,CAAC;oBACD,OAAO,IAAI,KAAK,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAA;gBACnD,CAAC;gBACD,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACxB,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;wBAC1B,MAAM,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAA;oBAC5B,CAAC;oBACD,OAAO,IAAI,KAAK,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAA;gBAChD,CAAC;gBAED,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAA;YAC5B,CAAC;QACH,CAAC,CACF,CAAA;IACH,CAAC,CAAC,CAAA;AACJ,CAAC;AAtED,oBAsEC;AAMD,SAAS,QAAQ,CAAC,OAAe,EAAE,IAAmB,EAAE,OAAqB;IAC3E,gIAAgI;IAChI,IAAI,CAAC,SAAG,CAAC,cAAc,EAAE,CAAC;QACxB,OAAM;IACR,CAAC;IAED,MAAM,UAAU,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAA;IACjD,MAAM,SAAS,GAAQ;QACrB,OAAO,EAAE,OAAO,GAAG,GAAG,GAAG,CAAC,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;KAC1F,CAAA;IACD,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;QAC3C,SAAS,CAAC,GAAG,GAAG,OAAO,CAAC,GAAG,CAAA;IAC7B,CAAC;IACD,SAAG,CAAC,KAAK,CAAC,SAAS,EAAE,UAAU,CAAC,CAAA;AAClC,CAAC;AAED,SAAgB,OAAO,CAAC,OAAe,EAAE,IAAmB,EAAE,OAAsB,EAAE,YAAgC;IACpH,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,OAAO,GAAG,EAAE,CAAA;IACd,CAAC;IAED,OAAO,CAAC,GAAG,GAAG,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;IAExC,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;QAC1B,MAAM,cAAc,GAAG,WAAK,CAAC,OAAO,CAAA;QACpC,6GAA6G;QAC7G,OAAO,CAAC,KAAK,GAAG,CAAC,YAAY,IAAI,IAAI,IAAI,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAQ,CAAA;IACzK,CAAC;IAED,QAAQ,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAA;IAChC,IAAI,CAAC;QACH,OAAO,IAAA,mBAAM,EAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAA;IACvC,CAAC;IAAC,OAAO,CAAM,EAAE,CAAC;QAChB,MAAM,IAAI,KAAK,CAAC,gBAAgB,OAAO,KAAK,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,CAAA;IAC7D,CAAC;AACH,CAAC;AAnBD,0BAmBC;AAED,SAAgB,aAAa,CAAC,OAAe,EAAE,IAAmB,EAAE,IAAY,EAAE,OAAsB;IACtG,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAA;IAC3E,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAA;IACpE,OAAO,IAAI,OAAO,CAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC1C,aAAa,CACX,OAAO,EACP,YAAY,EACZ,OAAO,EACP,GAAG,EAAE;YACH,IAAI,CAAC;gBACH,YAAY,CAAC,OAAO,CAAC,CAAA;YACvB,CAAC;oBAAS,CAAC;gBACT,OAAO,CAAC,SAAS,CAAC,CAAA;YACpB,CAAC;QACH,CAAC,EACD,KAAK,CAAC,EAAE;YACN,IAAI,CAAC;gBACH,YAAY,CAAC,OAAO,CAAC,CAAA;YACvB,CAAC;oBAAS,CAAC;gBACT,MAAM,CAAC,KAAK,CAAC,CAAA;YACf,CAAC;QACH,CAAC,CACF,CAAA;QAED,YAAY,CAAC,KAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IAC/B,CAAC,CAAC,CAAA;AACJ,CAAC;AA1BD,sCA0BC;AAED,SAAgB,KAAK,CAAC,OAAe,EAAE,IAA2B,EAAE,OAAsB,EAAE,YAAgC;IAC1H,OAAO,IAAI,OAAO,CAAM,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC1C,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,IAAI,IAAI,EAAE,EAAE,OAAO,EAAE,YAAY,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAA;IACvG,CAAC,CAAC,CAAA;AACJ,CAAC;AAJD,sBAIC;AAED,SAAS,aAAa,CAAC,KAAa,EAAE,YAA0B,EAAE,OAAe,EAAE,OAAuC,EAAE,MAA8B;IACxJ,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;IAEhC,IAAI,GAAG,GAAG,EAAE,CAAA;IACZ,IAAI,YAAY,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;QAChC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;YAC9C,GAAG,IAAI,IAAI,CAAA;QACb,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,IAAI,QAAQ,GAAG,EAAE,CAAA;IACjB,IAAI,YAAY,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;QAChC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAY,EAAE,EAAE;YAC9C,QAAQ,IAAI,IAAI,CAAA;QAClB,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,IAAY,EAAE,EAAE;QACxC,IAAI,SAAG,CAAC,cAAc,EAAE,CAAC;YACvB,MAAM,MAAM,GAAQ;gBAClB,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAC/B,IAAI;gBACJ,GAAG,EAAE,YAAY,CAAC,GAAG;aACtB,CAAA;YACD,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACnB,MAAM,CAAC,GAAG,GAAG,GAAG,CAAA;YAClB,CAAC;YACD,SAAG,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;QAC7B,CAAC;QAED,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC;YACf,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;gBACpB,OAAO,CAAC,GAAG,CAAC,CAAA;YACd,CAAC;QACH,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,IAAI,SAAS,CAAC,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAA;QACrD,CAAC;IACH,CAAC,CAAC,CAAA;AACJ,CAAC;AAED,SAAS,SAAS,CAAC,IAAY,EAAE,KAAa;IAC5C,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,KAAK,MAAM,IAAI,EAAE,CAAA;AACxD,CAAC;AAED,MAAa,SAAU,SAAQ,KAAK;IAGlC,YACE,OAAe,EACN,QAAgB,EACzB,GAAW,EACX,QAAgB,EAChB,IAAI,GAAG,qCAAqC;QAE5C,KAAK,CAAC,GAAG,OAAO,mBAAmB,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,WAAW,CAAC,GAAG,SAAS,CAAC,GAAG,EAAE,QAAQ,CAAC,GAAG,SAAS,CAAC,QAAQ,EAAE,cAAc,CAAC,EAAE,CAAC,CACrJ;QANQ,aAAQ,GAAR,QAAQ,CAAQ;QAJ3B,kBAAa,GAAG,KAAK,CAAA;QAUjB,IAA8B,CAAC,IAAI,GAAG,IAAI,CAAA;IAC9C,CAAC;CACF;AAbD,8BAaC;AAGD,SAAgB,GAAG,CAAO,KAAkB,EAAE,IAAqB;IACjE,OAAO,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;AAC3C,CAAC;AAFD,kBAEC;AAED,SAAgB,eAAe,CAAC,CAA4B;IAC1D,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,CAAA;AAC3C,CAAC;AAFD,0CAEC;AAED,SAAgB,gBAAgB,CAAC,KAAa;IAC5C,OAAO,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;AACnC,CAAC;AAFD,4CAEC;AAED,SAAgB,QAAQ,CAAO,GAAqB,EAAE,GAAM,EAAE,KAAQ;IACpE,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;IACzB,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;QACjB,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAA;IACvB,CAAC;SAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;QACjC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IAClB,CAAC;AACH,CAAC;AAPD,4BAOC;AAED,SAAgB,cAAc,CAAC,MAAwC,EAAE,WAA0B;IACjG,IAAI,MAAM,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,EAAE,CAAC;QACvE,OAAO,WAAW,CAAA;IACpB,CAAC;IAED,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;IACvC,IAAI,KAAK,IAAI,CAAC,EAAE,CAAC;QACf,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;QACnC,IAAI,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAA;QACzB,IAAI,KAAK,KAAK,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAA;QACvC,CAAC;QACD,MAAM,GAAG,IAAI,CAAA;IACf,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC;AAfD,wCAeC;AAED,SAAgB,uBAAuB,CAAC,KAAgC,EAAE,KAAc;IACtF,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;QACxB,OAAO,SAAS,CAAA;IAClB,CAAC;IACD,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;QACnB,OAAO,IAAI,CAAA;IACb,CAAC;IAED,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QACzB,OAAO,GAAG,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE,CAAA;IAC7C,CAAC;IAED,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAA;AAC1E,CAAC;AAbD,0DAaC;AAED,SAAgB,aAAa;IAC3B,0HAA0H;IAC1H,SAAS,KAAK,CAAC,KAAyB;QACtC,wCAAwC;QACxC,OAAO,KAAK,IAAI,KAAK,KAAK,OAAO,CAAA;IACnC,CAAC;IAED,OAAO,CACL,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC;QACtC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC;QACtC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC;QACvC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC;QAC/C,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CACnC,CAAA;AACH,CAAC;AAdD,sCAcC;AAED,SAAgB,SAAS,CAAC,KAAgC;IACxD,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;QAClB,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAA;IACtB,CAAC;IACD,OAAO,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,GAAG,CAAA;AAC1D,CAAC;AALD,8BAKC;AAED,MAAa,yBAA0B,SAAQ,KAAK;IAClD,YAAY,OAAe,EAAE,IAAI,GAAG,4CAA4C;QAC9E,KAAK,CAAC,OAAO,CAAC,CACb;QAAC,IAA8B,CAAC,IAAI,GAAG,IAAI,CAAA;IAC9C,CAAC;CACF;AALD,8DAKC;AAEM,KAAK,UAAU,iBAAiB,CACrC,IAAmB,EACnB,oBAA2D,EAC3D,eAA6B,EAAE,EAC/B,UAAU,GAAG,CAAC;IAEd,MAAM,OAAO,GAAG,gCAAc,CAAA;IAC9B,MAAM,GAAG,GAAQ;QACf,GAAG,OAAO,CAAC,GAAG;QACd,QAAQ,EAAE,MAAM,IAAA,iBAAU,GAAE;QAC5B,WAAW,EAAE,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;KAC3C,CAAA;IACD,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAA;IACnD,IAAI,QAAQ,IAAI,IAAI,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC5C,GAAG,CAAC,sBAAsB,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;IACrD,CAAC;IAED,IAAI,YAAY,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,YAAY,CAAC,GAAG,CAAC,CAAA;IACtC,CAAC;IAED,SAAS,UAAU;QACjB,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC7C,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE;gBAC1C,KAAK,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC;gBACzC,GAAG,YAAY;gBACf,GAAG;aACJ,CAAC,CAAA;YACF,IAAI,oBAAoB,IAAI,IAAI,EAAE,CAAC;gBACjC,oBAAoB,CAAC,YAAY,CAAC,CAAA;YACpC,CAAC;YACD,aAAa,CAAC,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,EAAE;gBAC7D,IAAI,KAAK,YAAY,SAAS,IAAI,KAAK,CAAC,QAAQ,KAAK,CAAC,EAAE,CAAC;oBACvD,KAAK,CAAC,aAAa,GAAG,IAAI,CAAA;gBAC5B,CAAC;gBACD,MAAM,CAAC,KAAK,CAAC,CAAA;YACf,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC;QACrB,OAAO,UAAU,EAAE,CAAA;IACrB,CAAC;SAAM,CAAC;QACN,OAAO,KAAK,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;IAC5C,CAAC;AACH,CAAC;AA7CD,8CA6CC;AAEM,KAAK,UAAU,KAAK,CAAI,IAAsB,EAAE,WAAmB,EAAE,QAAgB,EAAE,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC;IACpH,IAAI,CAAC;QACH,OAAO,MAAM,IAAI,EAAE,CAAA;IACrB,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,SAAG,CAAC,IAAI,CAAC,kCAAkC,WAAW,aAAa,CAAC,CAAA;QACpE,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;YACpB,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,QAAQ,GAAG,OAAO,GAAG,OAAO,CAAC,CAAC,CAAA;YAC/E,OAAO,MAAM,KAAK,CAAC,IAAI,EAAE,WAAW,GAAG,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,GAAG,CAAC,CAAC,CAAA;QAC3E,CAAC;aAAM,CAAC;YACN,MAAM,KAAK,CAAA;QACb,CAAC;IACH,CAAC;AACH,CAAC;AAZD,sBAYC", "sourcesContent": ["import { appBuilderPath } from \"app-builder-bin\"\nimport { safeStringifyJson } from \"builder-util-runtime\"\nimport * as chalk from \"chalk\"\nimport { ChildProcess, execFile, ExecFileOptions, SpawnOptions } from \"child_process\"\nimport { spawn as _spawn } from \"cross-spawn\"\nimport { createHash } from \"crypto\"\nimport _debug from \"debug\"\nimport { dump } from \"js-yaml\"\nimport * as path from \"path\"\nimport { debug, log } from \"./log\"\nimport { install as installSourceMap } from \"source-map-support\"\nimport { getPath7za } from \"./7za\"\n\nif (process.env.JEST_WORKER_ID == null) {\n  installSourceMap()\n}\n\nexport { safeStringifyJson } from \"builder-util-runtime\"\nexport { TmpDir } from \"temp-file\"\nexport { log, debug } from \"./log\"\nexport { Arch, getArchCliNames, toLinuxArchString, getArchSuffix, ArchType, archFromString, defaultArchFromString } from \"./arch\"\nexport { AsyncTaskManager } from \"./asyncTaskManager\"\nexport { DebugLogger } from \"./DebugLogger\"\n\nexport { copyFile, exists } from \"./fs\"\nexport { asArray } from \"builder-util-runtime\"\n\nexport { deepAssign } from \"./deepAssign\"\n\nexport { getPath7za, getPath7x } from \"./7za\"\n\nexport const debug7z = _debug(\"electron-builder:7z\")\n\nexport function serializeToYaml(object: any, skipInvalid = false, noRefs = false) {\n  return dump(object, {\n    lineWidth: 8000,\n    skipInvalid,\n    noRefs,\n  })\n}\n\nexport function removePassword(input: string) {\n  return input.replace(/(-String |-P |pass:| \\/p |-pass |--secretKey |--accessKey |-p )([^ ]+)/g, (match, p1, p2) => {\n    if (p1.trim() === \"/p\" && p2.startsWith(\"\\\\\\\\Mac\\\\Host\\\\\\\\\")) {\n      // appx /p\n      return `${p1}${p2}`\n    }\n    return `${p1}${createHash(\"sha256\").update(p2).digest(\"hex\")} (sha256 hash)`\n  })\n}\n\nfunction getProcessEnv(env: { [key: string]: string | undefined } | undefined | null): NodeJS.ProcessEnv | undefined {\n  if (process.platform === \"win32\") {\n    return env == null ? undefined : env\n  }\n\n  const finalEnv = {\n    ...(env || process.env),\n  }\n\n  // without LC_CTYPE dpkg can returns encoded unicode symbols\n  // set LC_CTYPE to avoid crash https://github.com/electron-userland/electron-builder/issues/503 Even \"en_DE.UTF-8\" leads to error.\n  const locale = process.platform === \"linux\" ? process.env.LANG || \"C.UTF-8\" : \"en_US.UTF-8\"\n  finalEnv.LANG = locale\n  finalEnv.LC_CTYPE = locale\n  finalEnv.LC_ALL = locale\n  return finalEnv\n}\n\nexport function exec(file: string, args?: Array<string> | null, options?: ExecFileOptions, isLogOutIfDebug = true): Promise<string> {\n  if (log.isDebugEnabled) {\n    const logFields: any = {\n      file,\n      args: args == null ? \"\" : removePassword(args.join(\" \")),\n    }\n    if (options != null) {\n      if (options.cwd != null) {\n        logFields.cwd = options.cwd\n      }\n\n      if (options.env != null) {\n        const diffEnv = { ...options.env }\n        for (const name of Object.keys(process.env)) {\n          if (process.env[name] === options.env[name]) {\n            delete diffEnv[name]\n          }\n        }\n        logFields.env = safeStringifyJson(diffEnv)\n      }\n    }\n\n    log.debug(logFields, \"executing\")\n  }\n\n  return new Promise<string>((resolve, reject) => {\n    execFile(\n      file,\n      args,\n      {\n        ...options,\n        maxBuffer: 1000 * 1024 * 1024,\n        env: getProcessEnv(options == null ? null : options.env),\n      },\n      (error, stdout, stderr) => {\n        if (error == null) {\n          if (isLogOutIfDebug && log.isDebugEnabled) {\n            const logFields: any = {\n              file,\n            }\n            if (stdout.length > 0) {\n              logFields.stdout = stdout\n            }\n            if (stderr.length > 0) {\n              logFields.stderr = stderr\n            }\n\n            log.debug(logFields, \"executed\")\n          }\n          resolve(stdout.toString())\n        } else {\n          let message = chalk.red(removePassword(`Exit code: ${(error as any).code}. ${error.message}`))\n          if (stdout.length !== 0) {\n            if (file.endsWith(\"wine\")) {\n              stdout = stdout.toString()\n            }\n            message += `\\n${chalk.yellow(stdout.toString())}`\n          }\n          if (stderr.length !== 0) {\n            if (file.endsWith(\"wine\")) {\n              stderr = stderr.toString()\n            }\n            message += `\\n${chalk.red(stderr.toString())}`\n          }\n\n          reject(new Error(message))\n        }\n      }\n    )\n  })\n}\n\nexport interface ExtraSpawnOptions {\n  isPipeInput?: boolean\n}\n\nfunction logSpawn(command: string, args: Array<string>, options: SpawnOptions) {\n  // use general debug.enabled to log spawn, because it doesn't produce a lot of output (the only line), but important in any case\n  if (!log.isDebugEnabled) {\n    return\n  }\n\n  const argsString = removePassword(args.join(\" \"))\n  const logFields: any = {\n    command: command + \" \" + (command === \"docker\" ? argsString : removePassword(argsString)),\n  }\n  if (options != null && options.cwd != null) {\n    logFields.cwd = options.cwd\n  }\n  log.debug(logFields, \"spawning\")\n}\n\nexport function doSpawn(command: string, args: Array<string>, options?: SpawnOptions, extraOptions?: ExtraSpawnOptions): ChildProcess {\n  if (options == null) {\n    options = {}\n  }\n\n  options.env = getProcessEnv(options.env)\n\n  if (options.stdio == null) {\n    const isDebugEnabled = debug.enabled\n    // do not ignore stdout/stderr if not debug, because in this case we will read into buffer and print on error\n    options.stdio = [extraOptions != null && extraOptions.isPipeInput ? \"pipe\" : \"ignore\", isDebugEnabled ? \"inherit\" : \"pipe\", isDebugEnabled ? \"inherit\" : \"pipe\"] as any\n  }\n\n  logSpawn(command, args, options)\n  try {\n    return _spawn(command, args, options)\n  } catch (e: any) {\n    throw new Error(`Cannot spawn ${command}: ${e.stack || e}`)\n  }\n}\n\nexport function spawnAndWrite(command: string, args: Array<string>, data: string, options?: SpawnOptions) {\n  const childProcess = doSpawn(command, args, options, { isPipeInput: true })\n  const timeout = setTimeout(() => childProcess.kill(), 4 * 60 * 1000)\n  return new Promise<any>((resolve, reject) => {\n    handleProcess(\n      \"close\",\n      childProcess,\n      command,\n      () => {\n        try {\n          clearTimeout(timeout)\n        } finally {\n          resolve(undefined)\n        }\n      },\n      error => {\n        try {\n          clearTimeout(timeout)\n        } finally {\n          reject(error)\n        }\n      }\n    )\n\n    childProcess.stdin!.end(data)\n  })\n}\n\nexport function spawn(command: string, args?: Array<string> | null, options?: SpawnOptions, extraOptions?: ExtraSpawnOptions): Promise<any> {\n  return new Promise<any>((resolve, reject) => {\n    handleProcess(\"close\", doSpawn(command, args || [], options, extraOptions), command, resolve, reject)\n  })\n}\n\nfunction handleProcess(event: string, childProcess: ChildProcess, command: string, resolve: ((value?: any) => void) | null, reject: (reason?: any) => void) {\n  childProcess.on(\"error\", reject)\n\n  let out = \"\"\n  if (childProcess.stdout != null) {\n    childProcess.stdout.on(\"data\", (data: string) => {\n      out += data\n    })\n  }\n\n  let errorOut = \"\"\n  if (childProcess.stderr != null) {\n    childProcess.stderr.on(\"data\", (data: string) => {\n      errorOut += data\n    })\n  }\n\n  childProcess.once(event, (code: number) => {\n    if (log.isDebugEnabled) {\n      const fields: any = {\n        command: path.basename(command),\n        code,\n        pid: childProcess.pid,\n      }\n      if (out.length > 0) {\n        fields.out = out\n      }\n      log.debug(fields, \"exited\")\n    }\n\n    if (code === 0) {\n      if (resolve != null) {\n        resolve(out)\n      }\n    } else {\n      reject(new ExecError(command, code, out, errorOut))\n    }\n  })\n}\n\nfunction formatOut(text: string, title: string) {\n  return text.length === 0 ? \"\" : `\\n${title}:\\n${text}`\n}\n\nexport class ExecError extends Error {\n  alreadyLogged = false\n\n  constructor(\n    command: string,\n    readonly exitCode: number,\n    out: string,\n    errorOut: string,\n    code = \"ERR_ELECTRON_BUILDER_CANNOT_EXECUTE\"\n  ) {\n    super(`${command} process failed ${code}${formatOut(String(exitCode), \"Exit code\")}${formatOut(out, \"Output\")}${formatOut(errorOut, \"Error output\")}`)\n    ;(this as NodeJS.ErrnoException).code = code\n  }\n}\n\ntype Nullish = null | undefined\nexport function use<T, R>(value: T | Nullish, task: (value: T) => R): R | null {\n  return value == null ? null : task(value)\n}\n\nexport function isEmptyOrSpaces(s: string | null | undefined): s is \"\" | null | undefined {\n  return s == null || s.trim().length === 0\n}\n\nexport function isTokenCharValid(token: string) {\n  return /^[.\\w/=+-]+$/.test(token)\n}\n\nexport function addValue<K, T>(map: Map<K, Array<T>>, key: K, value: T) {\n  const list = map.get(key)\n  if (list == null) {\n    map.set(key, [value])\n  } else if (!list.includes(value)) {\n    list.push(value)\n  }\n}\n\nexport function replaceDefault(inList: Array<string> | null | undefined, defaultList: Array<string>): Array<string> {\n  if (inList == null || (inList.length === 1 && inList[0] === \"default\")) {\n    return defaultList\n  }\n\n  const index = inList.indexOf(\"default\")\n  if (index >= 0) {\n    const list = inList.slice(0, index)\n    list.push(...defaultList)\n    if (index !== inList.length - 1) {\n      list.push(...inList.slice(index + 1))\n    }\n    inList = list\n  }\n  return inList\n}\n\nexport function getPlatformIconFileName(value: string | null | undefined, isMac: boolean) {\n  if (value === undefined) {\n    return undefined\n  }\n  if (value === null) {\n    return null\n  }\n\n  if (!value.includes(\".\")) {\n    return `${value}.${isMac ? \"icns\" : \"ico\"}`\n  }\n\n  return value.replace(isMac ? \".ico\" : \".icns\", isMac ? \".icns\" : \".ico\")\n}\n\nexport function isPullRequest() {\n  // TRAVIS_PULL_REQUEST is set to the pull request number if the current job is a pull request build, or false if it’s not.\n  function isSet(value: string | undefined) {\n    // value can be or null, or empty string\n    return value && value !== \"false\"\n  }\n\n  return (\n    isSet(process.env.TRAVIS_PULL_REQUEST) ||\n    isSet(process.env.CIRCLE_PULL_REQUEST) ||\n    isSet(process.env.BITRISE_PULL_REQUEST) ||\n    isSet(process.env.APPVEYOR_PULL_REQUEST_NUMBER) ||\n    isSet(process.env.GITHUB_BASE_REF)\n  )\n}\n\nexport function isEnvTrue(value: string | null | undefined) {\n  if (value != null) {\n    value = value.trim()\n  }\n  return value === \"true\" || value === \"\" || value === \"1\"\n}\n\nexport class InvalidConfigurationError extends Error {\n  constructor(message: string, code = \"ERR_ELECTRON_BUILDER_INVALID_CONFIGURATION\") {\n    super(message)\n    ;(this as NodeJS.ErrnoException).code = code\n  }\n}\n\nexport async function executeAppBuilder(\n  args: Array<string>,\n  childProcessConsumer?: (childProcess: ChildProcess) => void,\n  extraOptions: SpawnOptions = {},\n  maxRetries = 0\n): Promise<string> {\n  const command = appBuilderPath\n  const env: any = {\n    ...process.env,\n    SZA_PATH: await getPath7za(),\n    FORCE_COLOR: chalk.level === 0 ? \"0\" : \"1\",\n  }\n  const cacheEnv = process.env.ELECTRON_BUILDER_CACHE\n  if (cacheEnv != null && cacheEnv.length > 0) {\n    env.ELECTRON_BUILDER_CACHE = path.resolve(cacheEnv)\n  }\n\n  if (extraOptions.env != null) {\n    Object.assign(env, extraOptions.env)\n  }\n\n  function runCommand() {\n    return new Promise<string>((resolve, reject) => {\n      const childProcess = doSpawn(command, args, {\n        stdio: [\"ignore\", \"pipe\", process.stdout],\n        ...extraOptions,\n        env,\n      })\n      if (childProcessConsumer != null) {\n        childProcessConsumer(childProcess)\n      }\n      handleProcess(\"close\", childProcess, command, resolve, error => {\n        if (error instanceof ExecError && error.exitCode === 2) {\n          error.alreadyLogged = true\n        }\n        reject(error)\n      })\n    })\n  }\n\n  if (maxRetries === 0) {\n    return runCommand()\n  } else {\n    return retry(runCommand, maxRetries, 1000)\n  }\n}\n\nexport async function retry<T>(task: () => Promise<T>, retriesLeft: number, interval: number, backoff = 0, attempt = 0): Promise<T> {\n  try {\n    return await task()\n  } catch (error: any) {\n    log.info(`Above command failed, retrying ${retriesLeft} more times`)\n    if (retriesLeft > 0) {\n      await new Promise(resolve => setTimeout(resolve, interval + backoff * attempt))\n      return await retry(task, retriesLeft - 1, interval, backoff, attempt + 1)\n    } else {\n      throw error\n    }\n  }\n}\n"]}