{"main": "index.js", "types": "electron.d.ts", "bin": {"electron": "cli.js"}, "scripts": {"postinstall": "node install.js"}, "dependencies": {"@electron/get": "^1.13.0", "@types/node": "^16.11.26", "extract-zip": "^1.0.3"}, "engines": {"node": ">= 8.6"}, "name": "electron", "version": "18.3.15", "repository": "https://github.com/electron/electron", "description": "Build cross platform desktop apps with JavaScript, HTML, and CSS", "license": "MIT", "author": "Electron Community", "keywords": ["electron"]}