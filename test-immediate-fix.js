/**
 * Script de Test Immédiat pour la Correction Content-Length
 * Teste la correction immédiate des erreurs ERR_CONTENT_LENGTH_MISMATCH
 */

const { app, BrowserWindow } = require('electron');
const log = require('electron-log');
const { performCompleteFixAndClearing, getImmediateFix } = require('./src/main/immediate-content-length-fix');

// Configuration du test
const TEST_CONFIG = {
  localOdooUrl: 'http://**************:8069/web',
  testTimeout: 15000 // 15 secondes
};

async function runImmediateTest() {
  log.info('🧪 [Test Immédiat] Démarrage du test de correction Content-Length...');

  try {
    // Étape 1: Activer la correction AVANT de créer la fenêtre
    log.info('🔧 [Test] Activation de la correction immédiate...');
    await performCompleteFixAndClearing();
    
    const fix = getImmediateFix();
    const stats = fix.getStats();
    log.info(`✅ [Test] Correction activée - Stats: ${JSON.stringify(stats)}`);

    // Étape 2: Créer une fenêtre de test
    log.info('🪟 [Test] Création de la fenêtre de test...');
    const testWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      show: true, // Afficher pour voir le résultat
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true
      }
    });

    // Étape 3: Surveiller les erreurs
    let errorCount = 0;
    let loadSuccess = false;
    const startTime = Date.now();

    testWindow.webContents.on('console-message', (event, level, message) => {
      if (message.includes('ERR_CONTENT_LENGTH_MISMATCH')) {
        errorCount++;
        log.error(`❌ [Test] Erreur Content-Length détectée: ${message}`);
      } else if (message.includes('odoo.define is not a function')) {
        log.error(`❌ [Test] Erreur odoo.define détectée: ${message}`);
      } else if (message.includes('ProgressEvent')) {
        log.error(`❌ [Test] Erreur ProgressEvent détectée: ${message}`);
      }
    });

    testWindow.webContents.on('did-finish-load', () => {
      const loadTime = Date.now() - startTime;
      log.info(`✅ [Test] Page chargée avec succès en ${loadTime}ms`);
      loadSuccess = true;
    });

    testWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
      log.error(`❌ [Test] Échec de chargement: ${errorCode} - ${errorDescription}`);
    });

    // Étape 4: Charger l'URL Odoo
    log.info(`🔗 [Test] Chargement de l'URL: ${TEST_CONFIG.localOdooUrl}`);
    await testWindow.loadURL(TEST_CONFIG.localOdooUrl);

    // Étape 5: Attendre et analyser
    log.info('⏳ [Test] Attente du chargement complet...');
    await new Promise(resolve => setTimeout(resolve, TEST_CONFIG.testTimeout));

    // Étape 6: Vérifier les statistiques de la correction
    const finalStats = fix.getStats();
    const loadTime = Date.now() - startTime;

    // Étape 7: Générer le rapport
    const report = {
      timestamp: new Date().toISOString(),
      testUrl: TEST_CONFIG.localOdooUrl,
      loadTime,
      loadSuccess,
      errorCount,
      fixStats: finalStats,
      result: errorCount === 0 && loadSuccess ? 'SUCCESS' : 'FAILURE'
    };

    log.info('📋 [Test] ========== RAPPORT DE TEST IMMÉDIAT ==========');
    log.info(`📅 Date: ${report.timestamp}`);
    log.info(`🔗 URL testée: ${report.testUrl}`);
    log.info(`⏱️ Temps de chargement: ${report.loadTime}ms`);
    log.info(`✅ Chargement réussi: ${report.loadSuccess ? 'OUI' : 'NON'}`);
    log.info(`❌ Erreurs Content-Length: ${report.errorCount}`);
    log.info(`🔧 Requêtes corrigées: ${finalStats.fixedRequests}`);
    log.info(`📊 Erreurs détectées par le fix: ${finalStats.errorCount}`);
    log.info(`🎯 RÉSULTAT: ${report.result === 'SUCCESS' ? '✅ SUCCÈS' : '❌ ÉCHEC'}`);
    log.info('================================================');

    // Sauvegarder le rapport
    const fs = require('fs');
    const reportPath = `test-immediate-report-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    log.info(`💾 Rapport sauvegardé: ${reportPath}`);

    // Garder la fenêtre ouverte pour inspection manuelle
    log.info('🔍 Fenêtre gardée ouverte pour inspection manuelle...');
    log.info('   - Vérifiez visuellement que l\'interface Odoo est chargée');
    log.info('   - Ouvrez les DevTools (F12) pour vérifier l\'absence d\'erreurs');
    log.info('   - Fermez la fenêtre pour terminer le test');

    // Attendre que l'utilisateur ferme la fenêtre
    testWindow.on('closed', () => {
      log.info('🏁 Test terminé par l\'utilisateur');
      app.quit();
    });

    return report;

  } catch (error) {
    log.error(`❌ [Test] Erreur fatale: ${error.message}`);
    log.error(error.stack);
    app.quit();
  }
}

// Lancement du test quand Electron est prêt
app.whenReady().then(async () => {
  await runImmediateTest();
});

app.on('window-all-closed', () => {
  app.quit();
});

module.exports = { runImmediateTest };
