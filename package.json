{"name": "edara-erp", "version": "1.0.0", "description": "Edara ERP - Application de gestion d'entreprise", "main": "src/main/main.js", "scripts": {"start": "electron .", "build": "electron-builder --mac --win", "build:mac": "electron-builder --mac", "build:win": "electron-builder --win", "build:mac-quick": "electron-builder --mac dir", "test": "scripts/quick-test.sh", "reset": "scripts/reset-preferences.sh", "deploy:mac": "scripts/deploy-macos.sh"}, "author": "Oussama", "license": "MIT", "dependencies": {"axios": "^0.27.2", "electron-log": "^4.4.8", "http-proxy": "^1.18.1", "xmlrpc": "^1.3.2"}, "devDependencies": {"electron": "18.3.15", "electron-builder": "^24.9.1"}, "build": {"appId": "com.edara.erp", "productName": "Edara ERP", "mac": {"category": "public.app-category.business", "target": ["dmg", "zip"], "icon": "build/icon.icns", "darkModeSupport": true, "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist", "minimumSystemVersion": "10.11.0", "extendInfo": {"NSAppleEventsUsageDescription": "Cette application nécessite l'accès aux événements Apple pour fonctionner correctement.", "NSDesktopFolderUsageDescription": "Cette application nécessite l'accès au dossier Bureau pour les sauvegardes.", "NSDocumentsFolderUsageDescription": "Cette application nécessite l'accès au dossier Documents pour les sauvegardes.", "NSDownloadsFolderUsageDescription": "Cette application nécessite l'accès au dossier Téléchargements pour les sauvegardes.", "NSBookmarkUsageDescription": "Cette application nécessite de créer des bookmarks pour accéder aux dossiers de sauvegarde."}}, "dmg": {"background": "build/background.png", "icon": "build/icon.icns", "iconSize": 100, "contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}], "window": {"width": 540, "height": 380}}, "win": {"target": "nsis"}, "directories": {"output": "dist", "buildResources": "build"}, "extraResources": [{"from": "installer", "to": "installer"}]}}