/**
 * Test de la Solution ULTIME pour ERR_CONTENT_LENGTH_MISMATCH
 * Teste la solution au niveau le plus bas d'Electron
 */

const { app, BrowserWindow } = require('electron');
const log = require('electron-log');

async function testUltimateFix() {
  log.info('🧪 [TestUltimate] ========================================');
  log.info('🧪 [TestUltimate] TEST DE LA SOLUTION ULTIME');
  log.info('🧪 [TestUltimate] ========================================');

  try {
    // Étape 1: Vérifier que la solution ultime est chargée
    log.info('🔍 [TestUltimate] Vérification de la solution ultime...');
    const { getUltimateFix } = require('./src/main/ultimate-content-length-fix');
    const ultimateFix = getUltimateFix();
    const stats = ultimateFix.getStats();
    
    log.info(`📊 [TestUltimate] État de la solution ultime: ${JSON.stringify(stats)}`);
    
    if (!stats.isActive) {
      log.error('❌ [TestUltimate] LA SOLUTION ULTIME N\'EST PAS ACTIVE !');
      log.info('🔧 [TestUltimate] Tentative d\'activation manuelle...');
      
      const { activateUltimateFixAfterReady } = require('./src/main/ultimate-content-length-fix');
      activateUltimateFixAfterReady();
      
      const newStats = ultimateFix.getStats();
      log.info(`📊 [TestUltimate] Nouvel état: ${JSON.stringify(newStats)}`);
    } else {
      log.info('✅ [TestUltimate] Solution ultime déjà active');
    }

    // Étape 2: Créer une fenêtre de test
    log.info('🪟 [TestUltimate] Création de la fenêtre de test...');
    const testWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      show: true,
      title: 'Test Solution Ultime - Edara ERP',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true
      }
    });

    let errorCount = 0;
    let contentLengthErrors = 0;
    let progressEventErrors = 0;
    let odooDefineErrors = 0;
    let loadSuccess = false;
    const startTime = Date.now();

    // Étape 3: Surveiller TOUTES les erreurs
    testWindow.webContents.on('console-message', (event, level, message) => {
      if (message.includes('ERR_CONTENT_LENGTH_MISMATCH')) {
        contentLengthErrors++;
        errorCount++;
        log.error(`🚨 [TestUltimate] ERREUR CONTENT-LENGTH: ${message}`);
      } else if (message.includes('ProgressEvent')) {
        progressEventErrors++;
        errorCount++;
        log.error(`🚨 [TestUltimate] ERREUR PROGRESS-EVENT: ${message}`);
      } else if (message.includes('odoo.define is not a function')) {
        odooDefineErrors++;
        errorCount++;
        log.error(`🚨 [TestUltimate] ERREUR ODOO-DEFINE: ${message}`);
      } else if (message.includes('🚀 [ULTIMATE]')) {
        log.info(`📋 [TestUltimate] Log solution: ${message}`);
      } else if (message.includes('✅ [ULTIMATE]')) {
        log.info(`✅ [TestUltimate] Log solution: ${message}`);
      } else if (message.includes('🚨 [ULTIMATE]')) {
        log.error(`🚨 [TestUltimate] Log solution: ${message}`);
      } else if (message.includes('Missing dependencies')) {
        errorCount++;
        log.error(`🚨 [TestUltimate] ERREUR DEPENDENCIES: ${message}`);
      }
    });

    testWindow.webContents.on('did-finish-load', () => {
      const loadTime = Date.now() - startTime;
      log.info(`✅ [TestUltimate] PAGE CHARGÉE EN ${loadTime}ms`);
      loadSuccess = true;
    });

    testWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
      log.error(`❌ [TestUltimate] ÉCHEC DE CHARGEMENT: ${errorCode} - ${errorDescription}`);
    });

    // Étape 4: Charger l'URL de test
    const testUrl = 'http://**************:8069/web';
    log.info(`🔗 [TestUltimate] Chargement de: ${testUrl}`);
    
    await testWindow.loadURL(testUrl);

    // Étape 5: Attendre et analyser
    log.info('⏳ [TestUltimate] Attente de 30 secondes pour analyse ultra-complète...');
    await new Promise(resolve => setTimeout(resolve, 30000));

    const totalTime = Date.now() - startTime;

    // Étape 6: Obtenir les statistiques finales
    const finalStats = ultimateFix.getStats();

    // Étape 7: Rapport final ultra-détaillé
    log.info('📋 [TestUltimate] ========================================');
    log.info('📋 [TestUltimate] RAPPORT FINAL - SOLUTION ULTIME');
    log.info('📋 [TestUltimate] ========================================');
    log.info(`⏱️ [TestUltimate] Temps total: ${totalTime}ms`);
    log.info(`✅ [TestUltimate] Chargement réussi: ${loadSuccess ? 'OUI' : 'NON'}`);
    log.info(`❌ [TestUltimate] Total erreurs: ${errorCount}`);
    log.info(`🚨 [TestUltimate] Erreurs Content-Length: ${contentLengthErrors}`);
    log.info(`🚨 [TestUltimate] Erreurs ProgressEvent: ${progressEventErrors}`);
    log.info(`🚨 [TestUltimate] Erreurs Odoo.define: ${odooDefineErrors}`);
    log.info(`📊 [TestUltimate] Solution active: ${finalStats.isActive ? 'OUI' : 'NON'}`);
    log.info(`📊 [TestUltimate] Requêtes corrigées: ${finalStats.fixedRequests}`);
    log.info(`📊 [TestUltimate] URLs interceptées: ${finalStats.interceptedUrls.length}`);
    
    if (finalStats.interceptedUrls.length > 0) {
      log.info('📊 [TestUltimate] URLs interceptées:');
      finalStats.interceptedUrls.forEach((url, index) => {
        log.info(`📊 [TestUltimate]   ${index + 1}. ${url}`);
      });
    }
    
    log.info('📋 [TestUltimate] ========================================');

    // Étape 8: Verdict final ultra-précis
    if (contentLengthErrors === 0 && progressEventErrors === 0 && odooDefineErrors === 0 && loadSuccess) {
      log.info('🎉 [TestUltimate] ✅ SUCCÈS TOTAL ! LA SOLUTION ULTIME FONCTIONNE PARFAITEMENT !');
      log.info('🎉 [TestUltimate] ✅ AUCUNE ERREUR CONTENT-LENGTH !');
      log.info('🎉 [TestUltimate] ✅ AUCUNE ERREUR PROGRESS-EVENT !');
      log.info('🎉 [TestUltimate] ✅ AUCUNE ERREUR ODOO.DEFINE !');
      log.info('🎉 [TestUltimate] ✅ INTERFACE ODOO CHARGÉE PARFAITEMENT !');
      log.info(`🎉 [TestUltimate] ✅ ${finalStats.fixedRequests} REQUÊTES CORRIGÉES !`);
    } else if (contentLengthErrors === 0 && progressEventErrors === 0) {
      log.info('🎯 [TestUltimate] ✅ SUCCÈS PARTIEL - Erreurs Content-Length éliminées');
      log.info('🎯 [TestUltimate] ⚠️ Mais autres problèmes persistent');
      log.info(`🎯 [TestUltimate] ✅ ${finalStats.fixedRequests} REQUÊTES CORRIGÉES !`);
      
      if (odooDefineErrors > 0) {
        log.warn(`⚠️ [TestUltimate] ${odooDefineErrors} erreurs odoo.define (problème de chargement JS)`);
      }
    } else {
      log.error('💥 [TestUltimate] ❌ ÉCHEC - La solution ultime ne fonctionne pas');
      log.error(`💥 [TestUltimate] ❌ ${contentLengthErrors} erreurs Content-Length persistent`);
      log.error(`💥 [TestUltimate] ❌ ${progressEventErrors} erreurs ProgressEvent persistent`);
      log.error(`💥 [TestUltimate] ❌ ${odooDefineErrors} erreurs Odoo.define persistent`);
      
      if (finalStats.fixedRequests === 0) {
        log.error('💥 [TestUltimate] ❌ AUCUNE REQUÊTE N\'A ÉTÉ CORRIGÉE !');
        log.error('💥 [TestUltimate] ❌ Les intercepteurs ne fonctionnent pas du tout !');
        log.error('💥 [TestUltimate] ❌ PROBLÈME FONDAMENTAL AVEC ELECTRON !');
      } else {
        log.info(`💥 [TestUltimate] ℹ️ ${finalStats.fixedRequests} requêtes ont été corrigées mais des erreurs persistent`);
        log.error('💥 [TestUltimate] ❌ PROBLÈME AU NIVEAU DU SERVEUR ODOO !');
      }
    }

    // Étape 9: Diagnostic avancé
    log.info('🔍 [TestUltimate] ========================================');
    log.info('🔍 [TestUltimate] DIAGNOSTIC AVANCÉ');
    log.info('🔍 [TestUltimate] ========================================');
    
    if (finalStats.isActive) {
      log.info('✅ [TestUltimate] Solution ultime correctement activée');
    } else {
      log.error('❌ [TestUltimate] Solution ultime PAS activée - PROBLÈME CRITIQUE');
    }
    
    if (finalStats.fixedRequests > 0) {
      log.info(`✅ [TestUltimate] ${finalStats.fixedRequests} requêtes interceptées et corrigées`);
    } else {
      log.error('❌ [TestUltimate] AUCUNE requête interceptée - Les intercepteurs ne fonctionnent pas');
    }
    
    if (finalStats.interceptedUrls.length > 0) {
      log.info(`✅ [TestUltimate] ${finalStats.interceptedUrls.length} URLs différentes interceptées`);
    } else {
      log.error('❌ [TestUltimate] AUCUNE URL interceptée - Problème de détection des URLs');
    }

    log.info('📋 [TestUltimate] ========================================');

    // Garder la fenêtre ouverte pour inspection
    log.info('🔍 [TestUltimate] Fenêtre gardée ouverte pour inspection...');
    log.info('🔍 [TestUltimate] Ouvrez les DevTools (F12) pour vérifier les détails');
    log.info('🔍 [TestUltimate] Vérifiez l\'onglet Network pour voir les headers X-Ultimate-Fixed');
    log.info('🔍 [TestUltimate] Fermez la fenêtre pour terminer le test');

  } catch (error) {
    log.error(`❌ [TestUltimate] ERREUR FATALE: ${error.message}`);
    log.error(error.stack);
  }
}

// Lancer le test
app.whenReady().then(async () => {
  await testUltimateFix();
});

app.on('window-all-closed', () => {
  app.quit();
});
