/**
 * Script de test pour vérifier le fix Content-Length Mismatch
 * Exécuter avec: node test-content-length-fix.js
 */

const { app, BrowserWindow } = require('electron');
const path = require('path');

let testWindow = null;

function createTestWindow() {
  console.log('🧪 Création de la fenêtre de test...');
  
  testWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    show: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: false
    }
  });

  // Charger une page Odoo de test
  const testUrl = 'http://**************:8069/web';
  console.log(`🌐 Chargement de l'URL de test: ${testUrl}`);
  
  testWindow.loadURL(testUrl);

  // Injecter le script de correction dès que le DOM est prêt
  testWindow.webContents.on('dom-ready', () => {
    console.log('📄 DOM prêt, injection du script de correction...');
    
    const fs = require('fs');
    const fixScriptPath = path.join(__dirname, 'src/assets/js/content-length-mismatch-fix.js');
    
    try {
      const fixScript = fs.readFileSync(fixScriptPath, 'utf8');
      
      testWindow.webContents.executeJavaScript(fixScript)
        .then(() => {
          console.log('✅ Script de correction injecté avec succès');
          
          // Attendre 10 secondes puis vérifier l'état
          setTimeout(() => {
            testWindow.webContents.executeJavaScript(`
              if (window.EdaraContentLengthFix) {
                console.log('🔍 Vérification de l\\'état du fix...');
                const failedAssets = window.EdaraContentLengthFix.getFailedAssets();
                const odooLoaded = window.EdaraContentLengthFix.checkOdooLoaded();
                
                return {
                  fixActive: true,
                  failedAssets: failedAssets,
                  odooLoaded: odooLoaded,
                  hasOdoo: typeof window.odoo !== 'undefined',
                  hasJQuery: typeof window.$ !== 'undefined',
                  hasWebClient: document.querySelector('.o_web_client') !== null
                };
              } else {
                return { fixActive: false };
              }
            `).then(result => {
              console.log('📊 Résultats du test:', JSON.stringify(result, null, 2));
              
              if (result.fixActive) {
                console.log('✅ Le fix Content-Length Mismatch est actif');
                console.log(`📈 Assets échoués: ${result.failedAssets.length}`);
                console.log(`🎯 Odoo chargé: ${result.odooLoaded ? 'OUI' : 'NON'}`);
                console.log(`🔧 window.odoo: ${result.hasOdoo ? 'OUI' : 'NON'}`);
                console.log(`💰 jQuery: ${result.hasJQuery ? 'OUI' : 'NON'}`);
                console.log(`🖥️ WebClient: ${result.hasWebClient ? 'OUI' : 'NON'}`);
                
                if (result.failedAssets.length > 0) {
                  console.log('⚠️ Assets qui ont échoué:', result.failedAssets);
                }
              } else {
                console.log('❌ Le fix Content-Length Mismatch n\'est pas actif');
              }
            }).catch(error => {
              console.error('❌ Erreur lors de la vérification:', error);
            });
          }, 10000);
        })
        .catch(error => {
          console.error('❌ Erreur lors de l\'injection du script:', error);
        });
    } catch (error) {
      console.error('❌ Erreur lors de la lecture du script:', error);
    }
  });

  // Écouter les erreurs de console
  testWindow.webContents.on('console-message', (event, level, message, line, sourceId) => {
    if (message.includes('[Edara ERP]')) {
      console.log(`🖥️ Console: ${message}`);
    }
    if (message.includes('ERR_CONTENT_LENGTH_MISMATCH')) {
      console.log(`🚨 Erreur Content-Length détectée: ${message}`);
    }
  });

  // Fermer l'application après 30 secondes
  setTimeout(() => {
    console.log('⏰ Test terminé, fermeture de l\'application...');
    app.quit();
  }, 30000);
}

app.whenReady().then(() => {
  console.log('🚀 Application Electron prête, démarrage du test...');
  createTestWindow();
});

app.on('window-all-closed', () => {
  console.log('👋 Toutes les fenêtres fermées, arrêt de l\'application');
  app.quit();
});

console.log('🧪 Script de test Content-Length Mismatch Fix démarré');
console.log('📝 Ce script va:');
console.log('   1. Ouvrir une fenêtre Electron');
console.log('   2. Charger une page Odoo');
console.log('   3. Injecter le script de correction');
console.log('   4. Vérifier que le fix fonctionne');
console.log('   5. Afficher les résultats');
console.log('⏱️ Durée du test: 30 secondes');