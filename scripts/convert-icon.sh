#!/bin/bash

# <PERSON>ript to convert PNG to ICNS for macOS
echo "Converting icon.png to icon.icns..."

# Create temporary iconset directory
mkdir -p build/icon.iconset

# Generate different sizes
sips -z 16 16 build/icon.png --out build/icon.iconset/icon_16x16.png
sips -z 32 32 build/icon.png --out build/icon.iconset/<EMAIL>
sips -z 32 32 build/icon.png --out build/icon.iconset/icon_32x32.png
sips -z 64 64 build/icon.png --out build/icon.iconset/<EMAIL>
sips -z 128 128 build/icon.png --out build/icon.iconset/icon_128x128.png
sips -z 256 256 build/icon.png --out build/icon.iconset/<EMAIL>
sips -z 256 256 build/icon.png --out build/icon.iconset/icon_256x256.png
sips -z 512 512 build/icon.png --out build/icon.iconset/<EMAIL>
sips -z 512 512 build/icon.png --out build/icon.iconset/icon_512x512.png
sips -z 1024 1024 build/icon.png --out build/icon.iconset/<EMAIL>

# Convert iconset to icns
iconutil -c icns build/icon.iconset -o build/icon.icns

# Clean up
rm -rf build/icon.iconset

echo "Conversion complete. Icon saved to build/icon.icns"
