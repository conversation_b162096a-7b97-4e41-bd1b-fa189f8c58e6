#!/bin/bash

# Script pour réinitialiser les préférences de l'application Edara ERP
echo "Réinitialisation des préférences de l'application Edara ERP..."

# Chemin vers le dossier des préférences
PREFS_DIR="$HOME/Library/Application Support/edara-erp"

# Vérifier si le dossier existe
if [ -d "$PREFS_DIR" ]; then
    echo "Suppression du fichier de préférences..."
    rm -f "$PREFS_DIR/preferences.json"
    echo "Préférences réinitialisées avec succès !"
    echo "L'assistant d'installation s'affichera au prochain lancement de l'application."
else
    echo "Le dossier des préférences n'existe pas encore: $PREFS_DIR"
    echo "Aucune action nécessaire."
fi
