#!/bin/bash

# Script pour copier les fichiers de l'ancien projet Electron vers le nouveau projet Edara ERP
# Créé le: $(date)

# Définition des chemins source et destination
SOURCE_DIR="/Users/<USER>/Desktop/PROJET OUSSAMA /edara-electron-app/edara-electron-app copie 2"
DEST_DIR="/Users/<USER>/Desktop/PROJET OUSSAMA /edara-erp"

# Fonction pour créer un répertoire s'il n'existe pas
create_dir_if_not_exists() {
    if [ ! -d "$1" ]; then
        echo "Création du répertoire: $1"
        mkdir -p "$1"
    fi
}

# Fonction pour copier un fichier avec vérification
copy_file() {
    if [ -f "$1" ]; then
        echo "Copie de $1 vers $2"
        cp "$1" "$2"
    else
        echo "ATTENTION: Le fichier source $1 n'existe pas!"
    fi
}

# Création des répertoires nécessaires dans le nouveau projet
echo "Création des répertoires nécessaires..."
create_dir_if_not_exists "$DEST_DIR/src/main"
create_dir_if_not_exists "$DEST_DIR/src/renderer"
create_dir_if_not_exists "$DEST_DIR/src/assets/css"
create_dir_if_not_exists "$DEST_DIR/src/assets/js"
create_dir_if_not_exists "$DEST_DIR/src/assets/images/illustrations"

# Copie des fichiers principaux
echo -e "\nCopie des fichiers principaux..."
copy_file "$SOURCE_DIR/main.js" "$DEST_DIR/src/main/main.js"
copy_file "$SOURCE_DIR/preload.js" "$DEST_DIR/src/main/preload.js"
copy_file "$SOURCE_DIR/logger.js" "$DEST_DIR/logger.js"
copy_file "$SOURCE_DIR/package.json" "$DEST_DIR/package.json"

# Copie des fichiers HTML
echo -e "\nCopie des fichiers HTML..."
copy_file "$SOURCE_DIR/splash.html" "$DEST_DIR/src/renderer/splash.html"
copy_file "$SOURCE_DIR/custom-login.html" "$DEST_DIR/src/renderer/custom-login.html"

# Copie des fichiers JS
echo -e "\nCopie des fichiers JS..."
# Le fichier splash.js n'a pas été trouvé dans l'ancien projet
echo "ATTENTION: Le fichier splash.js n'a pas été trouvé dans l'ancien projet. Vous devrez peut-être le créer manuellement."
copy_file "$SOURCE_DIR/custom-login.js" "$DEST_DIR/src/assets/js/custom-login.js"
copy_file "$SOURCE_DIR/odoo-auth.js" "$DEST_DIR/src/assets/js/odoo-auth.js"

# Vérification pour odoo_auth.py (au cas où il s'agirait du fichier odoo-auth.js)
if [ -f "$SOURCE_DIR/odoo_auth.py" ] && [ ! -f "$SOURCE_DIR/odoo-auth.js" ]; then
    echo "Copie de odoo_auth.py vers odoo-auth.js (conversion possible)"
    cp "$SOURCE_DIR/odoo_auth.py" "$DEST_DIR/src/assets/js/odoo-auth.js"
fi

# Copie des fichiers CSS
echo -e "\nCopie des fichiers CSS..."
# Les fichiers CSS n'ont pas été trouvés dans l'ancien projet
echo "ATTENTION: Les fichiers CSS (theme.css, splash.css, custom-login.css) n'ont pas été trouvés dans l'ancien projet."
echo "Vous devrez peut-être les créer manuellement ou les chercher dans un autre emplacement."

# Copie des images
echo -e "\nCopie des images..."
# Le fichier logo.png n'a pas été trouvé, mais il y a peut-être icon.png qui pourrait servir
if [ -f "$SOURCE_DIR/assets/images/icon.png" ]; then
    echo "Copie de icon.png vers logo.png (renommage)"
    cp "$SOURCE_DIR/assets/images/icon.png" "$DEST_DIR/src/assets/images/logo.png"
else
    echo "ATTENTION: Le fichier logo.png n'a pas été trouvé dans l'ancien projet."
fi

# Copie des illustrations si elles existent
if [ -f "$SOURCE_DIR/assets/images/illustration.png" ]; then
    echo "Copie de illustration.png vers illustrations/"
    cp "$SOURCE_DIR/assets/images/illustration.png" "$DEST_DIR/src/assets/images/illustrations/login-illustration.png"
fi

if [ -f "$SOURCE_DIR/assets/images/illustration.svg" ]; then
    echo "Copie de illustration.svg vers illustrations/"
    cp "$SOURCE_DIR/assets/images/illustration.svg" "$DEST_DIR/src/assets/images/illustrations/login-illustration.svg"
fi

echo -e "\nProcessus de copie terminé!"
echo "Veuillez vérifier les avertissements ci-dessus pour les fichiers qui n'ont pas été trouvés."
echo "Vous devrez peut-être ajuster certains chemins dans les fichiers copiés pour qu'ils correspondent à la nouvelle structure."
