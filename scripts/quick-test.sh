#!/bin/bash

# Script pour construire et tester rapidement l'application Edara ERP
echo "Construction rapide de l'application Edara ERP pour macOS..."

# Nettoyer les builds précédents (optionnel, décommenter si nécessaire)
# echo "Nettoyage des builds précédents..."
# rm -rf dist/mac

# Construire l'application rapidement (sans créer de DMG ou ZIP)
echo "Construction de l'application..."
npm run build:mac-quick

# Vérifier si la construction a réussi
if [ $? -eq 0 ]; then
    echo "Construction réussie !"
    
    # Afficher le chemin de l'application
    APP_PATH="$(pwd)/dist/mac/Edara ERP.app"
    echo "Application créée à: $APP_PATH"
    
    # Ouvrir l'application
    echo "Ouverture de l'application..."
    open "$APP_PATH"
    
    echo "Test rapide lancé avec succès !"
else
    echo "Erreur lors de la construction de l'application."
    exit 1
fi
