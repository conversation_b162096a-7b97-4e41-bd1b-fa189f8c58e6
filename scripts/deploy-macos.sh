#!/bin/bash

# Script de déploiement pour macOS
echo "Démarrage du déploiement pour macOS..."

# Nettoyer les builds précédents
echo "Nettoyage des builds précédents..."
rm -rf dist

# Installer les dépendances
echo "Installation des dépendances..."
npm install

# Construire l'application pour macOS
echo "Construction de l'application pour macOS..."
npm run build:mac

# Vérifier si la construction a réussi
if [ $? -eq 0 ]; then
    echo "Construction réussie !"
    echo "Le package d'installation se trouve dans le dossier 'dist'"
    
    # Afficher le contenu du dossier dist
    echo "Contenu du dossier dist :"
    ls -la dist
    
    echo "Déploiement terminé avec succès !"
else
    echo "Erreur lors de la construction de l'application."
    exit 1
fi
