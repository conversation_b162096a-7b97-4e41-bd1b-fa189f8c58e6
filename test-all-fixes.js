/**
 * Script de test complet pour valider toutes les solutions Content-Length Mismatch
 * Exécuter avec: node test-all-fixes.js
 */

const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

let testWindow = null;
let testResults = {
  timestamp: Date.now(),
  tests: [],
  diagnosticReport: null,
  summary: {
    passed: 0,
    failed: 0,
    total: 0
  }
};

function addTestResult(name, passed, details = {}) {
  const result = {
    name,
    passed,
    timestamp: Date.now(),
    details
  };
  
  testResults.tests.push(result);
  testResults.summary.total++;
  
  if (passed) {
    testResults.summary.passed++;
    console.log(`✅ ${name}: PASSÉ`);
  } else {
    testResults.summary.failed++;
    console.log(`❌ ${name}: ÉCHOUÉ`);
  }
  
  if (details && Object.keys(details).length > 0) {
    console.log(`   Détails:`, details);
  }
}

function createTestWindow() {
  console.log('🧪 Création de la fenêtre de test complète...');
  
  testWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    show: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: false,
      preload: path.join(__dirname, 'src/main/preload.js')
    }
  });

  // URL de test Odoo
  const testUrl = 'http://**************:8069/web';
  console.log(`🌐 Chargement de l'URL de test: ${testUrl}`);
  
  testWindow.loadURL(testUrl);

  // Injecter tous les scripts de correction
  testWindow.webContents.on('dom-ready', () => {
    console.log('📄 DOM prêt, injection de tous les scripts de correction...');
    
    injectAllFixScripts();
  });

  // Écouter les messages de diagnostic
  testWindow.webContents.on('console-message', (event, level, message, line, sourceId) => {
    if (message.includes('[Edara ERP]')) {
      console.log(`🖥️ Console: ${message}`);
    }
    
    if (message.includes('ERR_CONTENT_LENGTH_MISMATCH')) {
      addTestResult('Détection erreur Content-Length', true, { message });
    }
  });

  // Programmer les tests
  scheduleTests();
}

function injectAllFixScripts() {
  const scripts = [
    'src/assets/js/diagnostic-only.js',
    'src/assets/js/immediate-reload-fix.js',
    'src/assets/js/aggressive-content-fix.js',
    'src/assets/js/content-length-mismatch-fix.js',
    'src/assets/js/auto-reload-fix.js'
  ];

  scripts.forEach((scriptPath, index) => {
    setTimeout(() => {
      try {
        const scriptContent = fs.readFileSync(scriptPath, 'utf8');
        const scriptName = path.basename(scriptPath);
        
        console.log(`💉 Injection de ${scriptName}...`);
        
        testWindow.webContents.executeJavaScript(scriptContent)
          .then(() => {
            addTestResult(`Injection ${scriptName}`, true);
          })
          .catch(error => {
            addTestResult(`Injection ${scriptName}`, false, { error: error.message });
          });
      } catch (error) {
        addTestResult(`Lecture ${path.basename(scriptPath)}`, false, { error: error.message });
      }
    }, index * 500); // Délai progressif entre les injections
  });
}

function scheduleTests() {
  console.log('⏰ Programmation des tests...');

  // Test 1: Vérifier l'injection des scripts (5 secondes)
  setTimeout(() => {
    testScriptInjection();
  }, 5000);

  // Test 2: Vérifier la détection d'erreurs (10 secondes)
  setTimeout(() => {
    testErrorDetection();
  }, 10000);

  // Test 3: Vérifier l'état d'Odoo (15 secondes)
  setTimeout(() => {
    testOdooState();
  }, 15000);

  // Test 4: Récupérer le rapport de diagnostic (20 secondes)
  setTimeout(() => {
    retrieveDiagnosticReport();
  }, 20000);

  // Test 5: Tester les fonctions de correction (25 secondes)
  setTimeout(() => {
    testFixFunctions();
  }, 25000);

  // Générer le rapport final (30 secondes)
  setTimeout(() => {
    generateFinalReport();
  }, 30000);

  // Fermer l'application (35 secondes)
  setTimeout(() => {
    console.log('⏰ Tests terminés, fermeture de l\'application...');
    app.quit();
  }, 35000);
}

function testScriptInjection() {
  console.log('🔍 Test d\'injection des scripts...');
  
  testWindow.webContents.executeJavaScript(`
    const results = {
      diagnostic: typeof window.EdaraDiagnosticReport !== 'undefined',
      immediateReload: typeof window.EdaraImmediateReload !== 'undefined',
      aggressiveFix: typeof window.EdaraAggressiveFix !== 'undefined',
      contentLengthFix: typeof window.EdaraContentLengthFix !== 'undefined',
      autoReload: typeof window.EdaraAutoReload !== 'undefined'
    };
    return results;
  `).then(results => {
    Object.entries(results).forEach(([script, injected]) => {
      addTestResult(`Script ${script} injecté`, injected);
    });
  }).catch(error => {
    addTestResult('Test injection scripts', false, { error: error.message });
  });
}

function testErrorDetection() {
  console.log('🔍 Test de détection d\'erreurs...');
  
  testWindow.webContents.executeJavaScript(`
    if (window.EdaraContentLengthFix) {
      const failedAssets = window.EdaraContentLengthFix.getFailedAssets();
      return {
        hasFailedAssets: failedAssets.length > 0,
        failedAssetsCount: failedAssets.length,
        failedAssets: failedAssets
      };
    }
    return { error: 'EdaraContentLengthFix non disponible' };
  `).then(results => {
    if (results.error) {
      addTestResult('Détection erreurs', false, results);
    } else {
      addTestResult('Détection erreurs', true, results);
    }
  }).catch(error => {
    addTestResult('Test détection erreurs', false, { error: error.message });
  });
}

function testOdooState() {
  console.log('🔍 Test de l\'état d\'Odoo...');
  
  testWindow.webContents.executeJavaScript(`
    const odooState = {
      hasOdoo: typeof window.odoo !== 'undefined',
      hasJQuery: typeof window.$ !== 'undefined' || typeof window.jQuery !== 'undefined',
      hasWebClient: document.querySelector('.o_web_client') !== null,
      hasNavbar: document.querySelector('.o_main_navbar') !== null,
      hasContent: document.querySelector('.o_content') !== null,
      bodyChildren: document.body.children.length,
      errorDialogs: document.querySelectorAll('.o_error_dialog').length
    };
    
    if (window.EdaraContentLengthFix) {
      odooState.odooLoadedCheck = window.EdaraContentLengthFix.checkOdooLoaded();
    }
    
    if (window.EdaraAutoReload) {
      odooState.healthCheck = window.EdaraAutoReload.checkHealth();
    }
    
    return odooState;
  `).then(state => {
    const isHealthy = state.hasOdoo && state.hasWebClient && state.bodyChildren > 1;
    addTestResult('État Odoo', isHealthy, state);
  }).catch(error => {
    addTestResult('Test état Odoo', false, { error: error.message });
  });
}

function retrieveDiagnosticReport() {
  console.log('🔍 Récupération du rapport de diagnostic...');
  
  testWindow.webContents.executeJavaScript(`
    if (window.EdaraDiagnosticReport) {
      return window.EdaraDiagnosticReport;
    }
    return { error: 'Rapport de diagnostic non disponible' };
  `).then(report => {
    if (report.error) {
      addTestResult('Rapport diagnostic', false, report);
    } else {
      testResults.diagnosticReport = report;
      addTestResult('Rapport diagnostic', true, {
        totalErrors: report.summary?.totalErrors || 0,
        criticalAssetErrors: report.summary?.criticalAssetErrors || 0,
        totalRequests: report.summary?.totalRequests || 0,
        failedRequests: report.summary?.failedRequests || 0
      });
    }
  }).catch(error => {
    addTestResult('Récupération rapport diagnostic', false, { error: error.message });
  });
}

function testFixFunctions() {
  console.log('🔍 Test des fonctions de correction...');
  
  testWindow.webContents.executeJavaScript(`
    const functionTests = {};
    
    if (window.EdaraContentLengthFix) {
      try {
        functionTests.contentLengthFix = {
          getFailedAssets: typeof window.EdaraContentLengthFix.getFailedAssets === 'function',
          checkOdooLoaded: typeof window.EdaraContentLengthFix.checkOdooLoaded === 'function',
          reloadJavaScriptAsset: typeof window.EdaraContentLengthFix.reloadJavaScriptAsset === 'function'
        };
      } catch (e) {
        functionTests.contentLengthFix = { error: e.message };
      }
    }
    
    if (window.EdaraAggressiveFix) {
      try {
        functionTests.aggressiveFix = {
          forceReloadCriticalAssets: typeof window.EdaraAggressiveFix.forceReloadCriticalAssets === 'function',
          getFailedAssets: typeof window.EdaraAggressiveFix.getFailedAssets === 'function'
        };
      } catch (e) {
        functionTests.aggressiveFix = { error: e.message };
      }
    }
    
    if (window.EdaraAutoReload) {
      try {
        functionTests.autoReload = {
          checkHealth: typeof window.EdaraAutoReload.checkHealth === 'function',
          forceReload: typeof window.EdaraAutoReload.forceReload === 'function'
        };
      } catch (e) {
        functionTests.autoReload = { error: e.message };
      }
    }
    
    return functionTests;
  `).then(functions => {
    Object.entries(functions).forEach(([fixName, funcs]) => {
      if (funcs.error) {
        addTestResult(`Fonctions ${fixName}`, false, funcs);
      } else {
        const allFunctionsWork = Object.values(funcs).every(f => f === true);
        addTestResult(`Fonctions ${fixName}`, allFunctionsWork, funcs);
      }
    });
  }).catch(error => {
    addTestResult('Test fonctions correction', false, { error: error.message });
  });
}

function generateFinalReport() {
  console.log('📊 Génération du rapport final...');
  
  const finalReport = {
    ...testResults,
    conclusion: {
      success: testResults.summary.passed > testResults.summary.failed,
      successRate: (testResults.summary.passed / testResults.summary.total * 100).toFixed(1),
      recommendations: []
    }
  };

  // Ajouter des recommandations basées sur les résultats
  if (finalReport.summary.failed > 0) {
    finalReport.conclusion.recommendations.push('Vérifier les scripts qui ont échoué à s\'injecter');
  }
  
  if (finalReport.diagnosticReport && finalReport.diagnosticReport.summary.criticalAssetErrors > 0) {
    finalReport.conclusion.recommendations.push('Des erreurs d\'assets critiques ont été détectées');
  }
  
  if (finalReport.conclusion.successRate < 80) {
    finalReport.conclusion.recommendations.push('Le taux de succès est faible, investigation approfondie nécessaire');
  }

  // Sauvegarder le rapport
  const reportPath = path.join(__dirname, 'test-results.json');
  fs.writeFileSync(reportPath, JSON.stringify(finalReport, null, 2));
  
  console.log('\n📋 RAPPORT FINAL:');
  console.log('================');
  console.log(`✅ Tests réussis: ${finalReport.summary.passed}`);
  console.log(`❌ Tests échoués: ${finalReport.summary.failed}`);
  console.log(`📊 Taux de succès: ${finalReport.conclusion.successRate}%`);
  console.log(`🎯 Conclusion: ${finalReport.conclusion.success ? 'SUCCÈS' : 'ÉCHEC'}`);
  
  if (finalReport.conclusion.recommendations.length > 0) {
    console.log('\n💡 Recommandations:');
    finalReport.conclusion.recommendations.forEach(rec => {
      console.log(`   - ${rec}`);
    });
  }
  
  console.log(`\n📄 Rapport détaillé sauvegardé: ${reportPath}`);
}

app.whenReady().then(() => {
  console.log('🚀 Application de test prête, démarrage des tests complets...');
  createTestWindow();
});

app.on('window-all-closed', () => {
  console.log('👋 Tests terminés, arrêt de l\'application');
  app.quit();
});

console.log('🧪 Script de test complet Content-Length Mismatch Fix démarré');
console.log('📝 Ce script va:');
console.log('   1. Ouvrir une fenêtre Electron');
console.log('   2. Charger une page Odoo');
console.log('   3. Injecter TOUS les scripts de correction');
console.log('   4. Tester chaque composant individuellement');
console.log('   5. Générer un rapport détaillé');
console.log('⏱️ Durée totale des tests: 35 secondes');