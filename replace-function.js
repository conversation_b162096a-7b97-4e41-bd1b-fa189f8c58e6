const fs = require('fs');
const path = require('path');

// Chemin du fichier main.js
const filePath = path.join(__dirname, 'src', 'main', 'main.js');

// Lire le contenu du fichier
let content = fs.readFileSync(filePath, 'utf8');

// Remplacer toutes les occurrences de setOdooSessionCookie( par setOdooSessionCookieInternal(
// mais seulement si ce n'est pas dans une importation ou une déclaration de fonction
content = content.replace(/([^{}\w])setOdooSessionCookie\(/g, '$1setOdooSessionCookieInternal(');

// Écrire le contenu modifié dans le fichier
fs.writeFileSync(filePath, content, 'utf8');

console.log('Remplacement terminé avec succès !');
