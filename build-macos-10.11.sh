#!/bin/bash

# Script pour construire l'application Edara ERP compatible avec macOS 10.11+
echo "Début du processus de build pour macOS 10.11+"

# Vérifier si npm est installé
if ! command -v npm &> /dev/null; then
    echo "npm n'est pas installé. Veuillez installer Node.js et npm."
    exit 1
fi

# Nettoyer les installations précédentes
echo "Nettoyage des installations précédentes..."
rm -rf node_modules
rm -rf dist

# Installer les dépendances
echo "Installation des dépendances..."
npm install

# Vérifier si l'installation a réussi
if [ $? -ne 0 ]; then
    echo "Erreur lors de l'installation des dépendances."
    exit 1
fi

# Construire l'application pour macOS
echo "Construction de l'application pour macOS 10.11+..."
npm run build:mac

# Vérifier si la construction a réussi
if [ $? -ne 0 ]; then
    echo "Erreur lors de la construction de l'application."
    exit 1
fi

echo "Construction terminée avec succès!"
echo "Le fichier DMG se trouve dans le dossier 'dist'."
