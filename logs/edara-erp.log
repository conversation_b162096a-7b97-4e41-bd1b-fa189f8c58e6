2025-04-21 18:47:09 [info]  Application démarrée
2025-04-21 18:47:09 [info]  Création de la fenêtre de démarrage
2025-04-21 18:47:14 [info]  Création de la fenêtre de connexion
2025-04-21 18:51:19 [info]  Application démarrée
2025-04-21 18:51:19 [info]  Création de la fenêtre de démarrage
2025-04-21 18:51:24 [info]  Création de la fenêtre de connexion
2025-04-21 18:55:14 [info]  Application démarrée
2025-04-21 18:55:14 [info]  Création de la fenêtre de démarrage
2025-04-21 18:55:20 [info]  Création de la fenêtre de connexion
2025-04-21 19:40:03 [info]  Application démarrée
2025-04-21 19:40:03 [info]  Création de la fenêtre de démarrage
2025-04-21 19:40:08 [info]  Création de la fenêtre de connexion
2025-04-21 19:41:49 [info]  Application démarrée
2025-04-21 19:41:49 [info]  Création de la fenêtre de démarrage
2025-04-21 19:41:54 [info]  Création de la fenêtre de connexion
2025-04-21 19:43:12 [info]  Application démarrée
2025-04-21 19:43:12 [info]  Création de la fenêtre de démarrage
2025-04-21 19:43:18 [info]  Création de la fenêtre de connexion
2025-04-21 19:58:55 [info]  Application démarrée
2025-04-21 19:58:55 [info]  Création de la fenêtre de démarrage
2025-04-21 19:59:01 [info]  Création de la fenêtre de connexion
2025-04-21 20:01:13 [info]  Application démarrée
2025-04-21 20:01:13 [info]  Création de la fenêtre de démarrage
2025-04-21 20:01:19 [info]  Création de la fenêtre de connexion
2025-04-21 20:11:35 [info]  Application démarrée
2025-04-21 20:11:35 [info]  Création de la fenêtre de démarrage
2025-04-21 20:11:40 [info]  Création de la fenêtre de connexion
2025-04-21 20:20:39 [info]  Application démarrée
2025-04-21 20:20:39 [info]  Création de la fenêtre de démarrage
2025-04-21 20:20:44 [info]  Création de la fenêtre de connexion
2025-04-21 20:33:21 [info]  Application démarrée
2025-04-21 20:33:21 [info]  Création de la fenêtre de démarrage
2025-04-21 20:33:26 [info]  Création de la fenêtre de connexion
2025-04-21 20:45:37 [info]  Application démarrée
2025-04-21 20:45:37 [info]  Création de la fenêtre de démarrage
2025-04-21 20:45:43 [info]  Création de la fenêtre de connexion
2025-04-21 20:54:51 [info]  Application démarrée
2025-04-21 20:54:51 [info]  Création de la fenêtre de démarrage
2025-04-21 20:54:56 [info]  Création de la fenêtre de connexion
2025-04-21 21:02:10 [info]  Application démarrée
2025-04-21 21:02:10 [info]  Création de la fenêtre de démarrage
2025-04-21 21:02:15 [info]  Création de la fenêtre de connexion
2025-04-21 21:07:07 [info]  Application démarrée
2025-04-21 21:07:07 [info]  Création de la fenêtre de démarrage
2025-04-21 21:07:12 [info]  Création de la fenêtre de connexion
2025-04-21 21:10:56 [info]  Application démarrée
2025-04-21 21:10:56 [info]  Création de la fenêtre de démarrage
2025-04-21 21:11:01 [info]  Création de la fenêtre de connexion
2025-04-21 21:11:38 [info]  Application démarrée
2025-04-21 21:11:38 [info]  Création de la fenêtre de démarrage
2025-04-21 21:11:44 [info]  Création de la fenêtre de connexion
2025-04-21 21:13:20 [info]  Application démarrée
2025-04-21 21:13:20 [info]  Création de la fenêtre de démarrage
2025-04-21 21:13:25 [info]  Création de la fenêtre de connexion
2025-04-21 21:13:56 [info]  Application démarrée
2025-04-21 21:13:56 [info]  Création de la fenêtre de démarrage
2025-04-21 21:14:01 [info]  Création de la fenêtre de connexion
2025-04-21 21:14:25 [info]  Application démarrée
2025-04-21 21:14:26 [info]  Création de la fenêtre de démarrage
2025-04-21 21:14:31 [info]  Création de la fenêtre de connexion
2025-04-21 21:18:03 [info]  Application démarrée
2025-04-21 21:18:03 [info]  Création de la fenêtre de démarrage
2025-04-21 21:18:08 [info]  Création de la fenêtre de connexion
2025-04-21 21:19:09 [info]  Application démarrée
2025-04-21 21:19:09 [info]  Création de la fenêtre de démarrage
2025-04-21 21:19:14 [info]  Création de la fenêtre de connexion
2025-04-21 21:20:38 [info]  Application démarrée
2025-04-21 21:20:38 [info]  Création de la fenêtre de démarrage
2025-04-21 21:20:43 [info]  Création de la fenêtre de connexion
2025-04-21 21:22:44 [info]  Application démarrée
2025-04-21 21:22:44 [info]  Création de la fenêtre de démarrage
2025-04-21 21:22:49 [info]  Création de la fenêtre de connexion
2025-04-21 21:25:40 [info]  Application démarrée
2025-04-21 21:25:40 [info]  Création de la fenêtre de démarrage
2025-04-21 21:25:45 [info]  Création de la fenêtre de connexion
2025-04-21 21:26:20 [info]  Application démarrée
2025-04-21 21:26:20 [info]  Création de la fenêtre de démarrage
2025-04-21 21:26:25 [info]  Création de la fenêtre de connexion
2025-04-21 21:29:08 [info]  Application démarrée
2025-04-21 21:29:08 [info]  Création de la fenêtre de démarrage
2025-04-21 21:29:13 [info]  Création de la fenêtre de connexion
2025-04-21 21:55:29 [info]  Application démarrée
2025-04-21 21:55:29 [info]  Création de la fenêtre de démarrage
2025-04-21 21:55:34 [info]  Création de la fenêtre de connexion
2025-04-21 21:55:57 [info]  Application démarrée
2025-04-21 21:55:57 [info]  Création de la fenêtre de démarrage
2025-04-21 21:56:02 [info]  Création de la fenêtre de connexion
2025-04-21 21:56:19 [info]  Application démarrée
2025-04-21 21:56:19 [info]  Création de la fenêtre de démarrage
2025-04-21 21:56:24 [info]  Création de la fenêtre de connexion
2025-04-21 21:57:59 [info]  Application démarrée
2025-04-21 21:57:59 [info]  Création de la fenêtre de démarrage
2025-04-21 21:58:04 [info]  Création de la fenêtre de connexion
2025-04-21 22:08:32 [info]  Application démarrée
2025-04-21 22:08:32 [info]  Création de la fenêtre de démarrage
2025-04-21 22:08:37 [info]  Création de la fenêtre de connexion
2025-04-21 23:16:47 [info]  Application démarrée
2025-04-21 23:16:47 [info]  Création de la fenêtre de démarrage
2025-04-21 23:16:53 [info]  Création de la fenêtre de connexion
2025-04-21 23:22:56 [info]  Application démarrée
2025-04-21 23:22:56 [info]  Création de la fenêtre de démarrage
2025-04-21 23:23:01 [info]  Création de la fenêtre de connexion
2025-04-22 00:06:57 [info]  Application démarrée
2025-04-22 00:06:57 [info]  Création de la fenêtre de démarrage
2025-04-22 00:07:03 [info]  Création de la fenêtre de connexion
2025-04-22 00:14:50 [info]  Application démarrée
2025-04-22 00:14:50 [info]  Création de la fenêtre de démarrage
2025-04-22 00:14:56 [info]  Création de la fenêtre de connexion
2025-04-22 00:31:48 [info]  Application démarrée
2025-04-22 00:31:48 [info]  Création de la fenêtre de démarrage
2025-04-22 00:31:53 [info]  Création de la fenêtre de connexion
2025-04-22 00:39:45 [info]  Application démarrée
2025-04-22 00:39:45 [info]  Création de la fenêtre de démarrage
2025-04-22 00:39:50 [info]  Création de la fenêtre de connexion
2025-04-22 00:40:03 [info]  Données d'authentification reçues: login
2025-04-22 00:40:03 [info]  Tentative de connexion pour l'utilisateur: Oussama
2025-04-22 00:40:03 [info]  Création de la fenêtre principale
2025-04-22 00:48:07 [info]  Application démarrée
2025-04-22 00:48:07 [info]  Création de la fenêtre de démarrage
2025-04-22 00:48:13 [info]  Création de la fenêtre de connexion
2025-04-22 00:48:22 [info]  Données d'authentification reçues: login
2025-04-22 00:48:22 [info]  Tentative de connexion pour l'utilisateur: Oussama
2025-04-22 00:48:22 [info]  Création de la fenêtre principale
2025-04-22 00:48:26 [info]  Données d'authentification reçues: logout
2025-04-22 00:48:26 [info]  Déconnexion de l'utilisateur
2025-04-22 00:48:26 [info]  Création de la fenêtre de connexion
2025-04-22 01:04:36 [info]  Application démarrée
2025-04-22 01:04:36 [info]  Création de la fenêtre de démarrage
2025-04-22 01:04:41 [info]  Création de la fenêtre de connexion
2025-04-22 01:04:52 [info]  Données d'authentification reçues: login
2025-04-22 01:04:52 [info]  Tentative de connexion pour l'utilisateur: Oussama
2025-04-22 01:04:52 [error] Données d'authentification incomplètes: {
  action: 'login',
  token: 'dc91b634bda7653391b3570cca353827',
  userId: 2,
  name: 'Oussama',
  server: 'http://localhost:8069',
  expiration_date: '2025-04-23 00:04:52',
  session_id: null,
  odooWebUrl: 'http://localhost:8069/web'
}
2025-04-22 01:06:42 [info]  Données d'authentification reçues: login
2025-04-22 01:06:42 [info]  Tentative de connexion pour l'utilisateur: Oussama
2025-04-22 01:06:42 [error] Données d'authentification incomplètes: {
  action: 'login',
  token: '6cbe198ffe52daf4131925bcf37df1f7',
  userId: 2,
  name: 'Oussama',
  server: 'http://localhost:8069',
  expiration_date: '2025-04-23 00:06:42',
  session_id: null,
  odooWebUrl: 'http://localhost:8069/web'
}
2025-04-22 01:09:43 [info]  Application démarrée
2025-04-22 01:09:43 [info]  Création de la fenêtre de démarrage
2025-04-22 01:09:48 [info]  Création de la fenêtre de connexion
2025-04-22 01:09:58 [info]  Données d'authentification reçues: login
2025-04-22 01:09:58 [info]  Tentative de connexion pour l'utilisateur: Oussama
2025-04-22 01:09:58 [info]  Connexion réussie, redirection vers l'interface Odoo: http://localhost:8069/web
2025-04-22 01:09:58 [info]  Création de la fenêtre principale avec WebView
2025-04-22 01:18:59 [info]  Application démarrée
2025-04-22 01:18:59 [info]  Création de la fenêtre de démarrage
2025-04-22 01:19:04 [info]  Création de la fenêtre de connexion
2025-04-22 01:19:15 [info]  Données d'authentification reçues: login
2025-04-22 01:19:15 [info]  Tentative de connexion pour l'utilisateur: Oussama
2025-04-22 01:19:15 [info]  Connexion réussie, redirection vers l'interface Odoo: http://localhost:8069/web
2025-04-22 01:19:15 [info]  Création de la fenêtre principale avec WebView
2025-04-22 01:30:25 [info]  Application démarrée
2025-04-22 01:30:25 [info]  Création de la fenêtre de démarrage
2025-04-22 01:30:31 [info]  Création de la fenêtre de connexion
2025-04-22 01:30:45 [info]  Données d'authentification reçues: login
2025-04-22 01:30:45 [info]  Tentative de connexion pour l'utilisateur: Oussama
2025-04-22 01:30:45 [info]  Connexion réussie, redirection vers l'interface Odoo: http://localhost:8069/web
2025-04-22 01:30:45 [info]  Création de la fenêtre principale avec WebView
2025-04-22 01:50:37 [info]  Application démarrée
2025-04-22 01:50:37 [info]  Création de la fenêtre de démarrage
2025-04-22 01:50:43 [info]  Création de la fenêtre de connexion
2025-04-22 01:50:54 [info]  Données d'authentification reçues: login
2025-04-22 01:50:54 [info]  Tentative de connexion pour l'utilisateur: Oussama
2025-04-22 01:50:54 [info]  Connexion réussie, redirection vers l'interface Odoo: http://localhost:8069/web
2025-04-22 01:50:54 [info]  Création de la fenêtre principale avec WebView
2025-04-22 01:51:06 [info]  Suppression du cookie session_id pour http://localhost:8069
2025-04-22 01:51:06 [info]  Données d'authentification reçues: logout
2025-04-22 01:51:06 [info]  Déconnexion de l'utilisateur
2025-04-22 01:51:06 [info]  Création de la fenêtre de connexion
2025-04-22 01:51:19 [info]  Données d'authentification reçues: login
2025-04-22 01:51:19 [info]  Tentative de connexion pour l'utilisateur: Oussama
2025-04-22 01:51:19 [info]  Connexion réussie, redirection vers l'interface Odoo: http://localhost:8069/web
2025-04-22 01:51:19 [info]  Création de la fenêtre principale avec WebView
2025-04-22 02:06:59 [info]  Application démarrée
2025-04-22 02:06:59 [info]  Création de la fenêtre de démarrage
2025-04-22 02:07:05 [info]  Création de la fenêtre de connexion
2025-04-22 02:07:15 [info]  Données d'authentification reçues: login
2025-04-22 02:07:15 [info]  Tentative de connexion pour l'utilisateur: Oussama
2025-04-22 02:07:15 [info]  Suppression de l'ancien cookie session_id pour http://localhost:8069
2025-04-22 02:07:15 [info]  Ancien cookie session_id supprimé avec succès pour http://localhost:8069
2025-04-22 02:07:15 [info]  Définition du nouveau cookie session_id pour http://localhost:8069: 8ca900885b1c5a02f8dd40e928d0a582
2025-04-22 02:07:15 [info]  Nouveau cookie session_id défini avec succès pour http://localhost:8069
2025-04-22 02:07:15 [info]  Connexion réussie, redirection vers l'interface Odoo: http://localhost:8069/web
2025-04-22 02:07:15 [info]  Création de la fenêtre principale avec WebView
2025-04-22 02:16:12 [info]  Application démarrée
2025-04-22 02:16:12 [info]  Création de la fenêtre de démarrage
2025-04-22 02:16:17 [info]  Création de la fenêtre de connexion
2025-04-22 02:16:33 [info]  Données d'authentification reçues: login
2025-04-22 02:16:33 [info]  Tentative de connexion pour l'utilisateur: Oussama
2025-04-22 02:16:33 [info]  Suppression de l'ancien cookie session_id pour http://localhost:8069
2025-04-22 02:16:33 [info]  Ancien cookie session_id supprimé avec succès pour http://localhost:8069
2025-04-22 02:16:33 [info]  Définition du nouveau cookie session_id pour http://localhost:8069: dc1f48ef9b58ed9822e60a3d08ca015d
2025-04-22 02:16:33 [info]  Nouveau cookie session_id défini avec succès pour http://localhost:8069
2025-04-22 02:16:33 [info]  Connexion réussie, redirection vers l'interface Odoo: http://localhost:8069/web
2025-04-22 02:16:33 [info]  Création de la fenêtre principale avec WebView
2025-04-22 02:28:57 [info]  Application démarrée
2025-04-22 02:28:57 [info]  Création de la fenêtre de démarrage
2025-04-22 02:29:02 [info]  Création de la fenêtre de connexion
2025-04-22 02:29:17 [info]  Données d'authentification reçues: login
2025-04-22 02:29:17 [info]  Tentative de connexion pour l'utilisateur: Oussama
2025-04-22 02:29:17 [info]  Suppression de l'ancien cookie session_id pour http://localhost:8069
2025-04-22 02:29:17 [info]  Ancien cookie session_id supprimé avec succès pour http://localhost:8069
2025-04-22 02:29:17 [info]  Définition du nouveau cookie session_id pour http://localhost:8069: 407e26ba252c75586b6e2e1daa65f479
2025-04-22 02:29:17 [info]  Nouveau cookie session_id défini avec succès pour http://localhost:8069
2025-04-22 02:29:17 [info]  Connexion réussie, redirection vers l'interface Odoo: http://localhost:8069/web
2025-04-22 02:29:17 [info]  Création de la fenêtre principale avec WebView
2025-04-22 02:46:10 [info]  Application démarrée
2025-04-22 02:46:10 [info]  Création de la fenêtre de démarrage
2025-04-22 02:46:16 [info]  Création de la fenêtre de connexion
2025-04-22 02:50:38 [info]  Application démarrée
2025-04-22 02:50:38 [info]  Création de la fenêtre de démarrage
2025-04-22 02:50:43 [info]  Création de la fenêtre de connexion
2025-04-22 02:50:52 [info]  Données d'authentification reçues: login
2025-04-22 02:50:52 [info]  Tentative de connexion pour l'utilisateur: Oussama
2025-04-22 02:50:52 [info]  Suppression de l'ancien cookie session_id pour http://localhost:8069
2025-04-22 02:50:52 [info]  Ancien cookie session_id supprimé avec succès pour http://localhost:8069
2025-04-22 02:50:52 [info]  Définition du nouveau cookie session_id pour http://localhost:8069: 794ecf68a08a2b465c0f6ea73a0a3990
2025-04-22 02:50:52 [info]  Nouveau cookie session_id défini avec succès pour http://localhost:8069
2025-04-22 02:50:52 [info]  Connexion réussie, redirection vers l'interface Odoo: http://localhost:8069/web
2025-04-22 02:50:52 [info]  Création de la fenêtre principale avec WebView
2025-04-22 03:09:01 [info]  Application démarrée
2025-04-22 03:09:01 [info]  Création de la fenêtre de démarrage
2025-04-22 03:09:06 [info]  Création de la fenêtre de connexion
2025-04-22 03:09:20 [info]  Données d'authentification reçues: login
2025-04-22 03:09:20 [info]  Tentative de connexion pour l'utilisateur: Oussama
2025-04-22 03:09:20 [info]  Suppression de l'ancien cookie session_id pour http://localhost:8069
2025-04-22 03:09:20 [info]  Ancien cookie session_id supprimé avec succès pour http://localhost:8069
2025-04-22 03:09:20 [info]  Définition du nouveau cookie session_id pour http://localhost:8069: c9966eefdfb86d5f32e54d08135f83b7
2025-04-22 03:09:20 [info]  Nouveau cookie session_id défini avec succès pour http://localhost:8069
2025-04-22 03:09:20 [info]  Connexion réussie, redirection vers l'interface Odoo: http://localhost:8069/web
2025-04-22 03:09:20 [info]  Création de la fenêtre principale avec WebView
2025-04-22 03:13:17 [info]  Application démarrée
2025-04-22 03:13:17 [info]  Création de la fenêtre de démarrage
2025-04-22 03:13:22 [info]  Création de la fenêtre de connexion
2025-04-22 03:13:36 [info]  Données d'authentification reçues: login
2025-04-22 03:13:36 [info]  Tentative de connexion pour l'utilisateur: Oussama
2025-04-22 03:13:36 [info]  Suppression de l'ancien cookie session_id pour http://localhost:8069
2025-04-22 03:13:36 [info]  Ancien cookie session_id supprimé avec succès pour http://localhost:8069
2025-04-22 03:13:36 [info]  Définition du nouveau cookie session_id pour http://localhost:8069: 9a1baa9c5ee2ae32b826c5fe12f4d14d
2025-04-22 03:13:36 [info]  Nouveau cookie session_id défini avec succès pour http://localhost:8069
2025-04-22 03:13:36 [info]  Connexion réussie, redirection vers l'interface Odoo: http://localhost:8069/web
2025-04-22 03:13:36 [info]  Création de la fenêtre principale avec WebView
2025-04-22 03:21:58 [info]  Application démarrée
2025-04-22 03:21:58 [info]  Création de la fenêtre de démarrage
2025-04-22 03:22:03 [info]  Création de la fenêtre de connexion
2025-04-22 03:22:13 [info]  Données d'authentification reçues: login
2025-04-22 03:22:13 [info]  Tentative de connexion pour l'utilisateur: Oussama
2025-04-22 03:22:13 [info]  Suppression de l'ancien cookie session_id pour http://localhost:8069
2025-04-22 03:22:13 [info]  Ancien cookie session_id supprimé avec succès pour http://localhost:8069
2025-04-22 03:22:13 [info]  Définition du nouveau cookie session_id pour http://localhost:8069: 631d81abc48ecffa599aee83ec68bafc
2025-04-22 03:22:13 [info]  Nouveau cookie session_id défini avec succès pour http://localhost:8069
2025-04-22 03:22:13 [info]  Connexion réussie, redirection vers l'interface Odoo: http://localhost:8069/web
2025-04-22 03:22:13 [info]  Création de la fenêtre principale avec chargement direct d'Odoo
2025-04-22 03:22:13 [info]  Chargement direct de l'URL Odoo: http://localhost:8069/web
2025-04-22 03:28:59 [info]  Application démarrée
2025-04-22 03:28:59 [info]  Création de la fenêtre de démarrage
2025-04-22 03:29:04 [info]  Création de la fenêtre de connexion
2025-04-22 03:29:14 [info]  Données d'authentification reçues: login
2025-04-22 03:29:14 [info]  Tentative de connexion pour l'utilisateur: Oussama
2025-04-22 03:29:14 [info]  Suppression de l'ancien cookie session_id pour http://localhost:8069
2025-04-22 03:29:14 [info]  Ancien cookie session_id supprimé avec succès pour http://localhost:8069
2025-04-22 03:29:14 [info]  Définition du nouveau cookie session_id pour http://localhost:8069: 0e019342d92189ca09c5bccc1f5f9512
2025-04-22 03:29:14 [info]  Nouveau cookie session_id défini avec succès pour http://localhost:8069
2025-04-22 03:29:14 [info]  Cookie session_id vérifié avant redirection: 0e019342d92189ca09c5bccc1f5f9512
2025-04-22 03:29:14 [info]  Connexion réussie, redirection vers l'interface Odoo: http://localhost:8069/web
2025-04-22 03:29:14 [info]  Création de la fenêtre principale avec chargement direct d'Odoo
2025-04-22 03:29:14 [info]  Chargement direct de l'URL Odoo: http://localhost:8069/web
2025-04-22 03:29:15 [info]  Cookies disponibles pour http://localhost:8069: [
  {
    "name": "session_id",
    "value": "7f3dc08cde171f665f6de95797358d0d86037577",
    "domain": "localhost",
    "hostOnly": true,
    "path": "/",
    "secure": false,
    "httpOnly": true,
    "session": false,
    "expirationDate": 1753064955.139021,
    "sameSite": "unspecified"
  }
]
2025-04-22 03:29:15 [info]  Cookie session_id trouvé: 7f3dc08cde171f665f6de95797358d0d86037577
2025-04-22 03:32:36 [info]  Application démarrée
2025-04-22 03:32:36 [info]  Création de la fenêtre de démarrage
2025-04-22 03:32:42 [info]  Création de la fenêtre de connexion
2025-04-22 03:32:51 [info]  Données d'authentification reçues: login
2025-04-22 03:32:51 [info]  Tentative de connexion pour l'utilisateur: Oussama
2025-04-22 03:32:51 [info]  Suppression de l'ancien cookie session_id pour http://localhost:8069
2025-04-22 03:32:51 [info]  Ancien cookie session_id supprimé avec succès pour http://localhost:8069
2025-04-22 03:32:51 [info]  Définition du nouveau cookie session_id pour http://localhost:8069: e452329fc89596026ef4821f9000b2ce
2025-04-22 03:32:51 [info]  Nouveau cookie session_id défini avec succès pour http://localhost:8069
2025-04-22 03:32:51 [info]  Cookie session_id vérifié avant redirection: e452329fc89596026ef4821f9000b2ce
2025-04-22 03:32:51 [info]  Connexion réussie, redirection vers l'interface Odoo: http://localhost:8069/web
2025-04-22 03:32:51 [info]  Création de la fenêtre principale avec chargement direct d'Odoo
2025-04-22 03:32:51 [info]  Chargement direct de l'URL Odoo: http://localhost:8069/web
2025-04-22 03:32:51 [info]  Cookies disponibles pour http://localhost:8069: [
  {
    "name": "session_id",
    "value": "f2e6525b9e8816952243f75699725bd6ff444a33",
    "domain": "localhost",
    "hostOnly": true,
    "path": "/",
    "secure": false,
    "httpOnly": true,
    "session": false,
    "expirationDate": 1753065171.606839,
    "sameSite": "unspecified"
  }
]
2025-04-22 03:32:51 [info]  Cookie session_id trouvé: f2e6525b9e8816952243f75699725bd6ff444a33
2025-04-22 03:51:38 [info]  Application démarrée
2025-04-22 03:51:38 [info]  Création de la fenêtre de démarrage
2025-04-22 03:51:43 [info]  Création de la fenêtre de connexion
2025-04-22 04:11:02 [info]  Application démarrée
2025-04-22 04:11:02 [info]  Création de la fenêtre de démarrage
2025-04-22 04:11:07 [info]  Création de la fenêtre de connexion
2025-04-22 04:26:19 [info]  Application démarrée
2025-04-22 04:26:19 [info]  Création de la fenêtre de démarrage
2025-04-22 04:26:24 [info]  Création de la fenêtre de connexion
2025-04-22 04:36:15 [info]  Application démarrée
2025-04-22 04:36:15 [info]  Création de la fenêtre de démarrage
2025-04-22 04:36:20 [info]  Création de la fenêtre de connexion
2025-04-22 04:50:18 [info]  Application démarrée
2025-04-22 04:50:18 [info]  Création de la fenêtre de démarrage
2025-04-22 04:50:23 [info]  Création de la fenêtre de connexion
2025-04-22 04:56:26 [info]  Application démarrée
2025-04-22 04:56:26 [info]  Création de la fenêtre de démarrage
2025-04-22 04:56:32 [info]  Création de la fenêtre de connexion
2025-04-22 04:57:21 [info]  Application démarrée
2025-04-22 04:57:21 [info]  Création de la fenêtre de démarrage
2025-04-22 04:57:26 [info]  Création de la fenêtre de connexion
2025-04-22 04:57:37 [info]  Données d'authentification reçues: login
2025-04-22 04:57:37 [info]  Tentative de connexion pour l'utilisateur: Oussama
2025-04-22 04:57:37 [info]  Recherche des cookies session_id existants pour http://localhost:8069
2025-04-22 04:57:37 [info]  1 cookie(s) session_id trouvé(s) pour http://localhost:8069
2025-04-22 04:57:37 [info]  Suppression de 1 cookie(s) session_id existant(s)
2025-04-22 04:57:37 [info]  Cookie #1 à supprimer: {
  name: 'session_id',
  value: 'f2e6525b...',
  domain: 'localhost',
  path: '/'
}
2025-04-22 04:57:38 [info]  Cookie session_id supprimé avec succès: f2e6525b...
2025-04-22 04:57:38 [info]  Définition du nouveau cookie session_id pour http://localhost:8069: {
  value: '824943d8...',
  domain: 'localhost',
  path: '/',
  secure: false,
  httpOnly: true
}
2025-04-22 04:57:38 [info]  Nouveau cookie session_id défini avec succès pour http://localhost:8069
2025-04-22 04:57:38 [info]  Cookie session_id vérifié avant redirection: { value: '824943d8...', domain: 'localhost', path: '/' }
2025-04-22 04:57:38 [info]  Connexion réussie, redirection vers l'interface Odoo: http://localhost:8069/web
2025-04-22 04:57:38 [info]  Création de la fenêtre principale avec chargement direct d'Odoo
2025-04-22 04:57:38 [info]  Chargement direct de l'URL Odoo: http://localhost:8069/web
2025-04-22 04:57:39 [info]  Cookies disponibles pour http://localhost:8069: [
  {
    "name": "session_id",
    "value": "887dcdcfb77893df6edaac95027d3a358b407875",
    "domain": "localhost",
    "hostOnly": true,
    "path": "/",
    "secure": false,
    "httpOnly": true,
    "session": false,
    "expirationDate": 1753070259.003869,
    "sameSite": "unspecified"
  }
]
2025-04-22 04:57:39 [info]  Cookie session_id trouvé: 887dcdcfb77893df6edaac95027d3a358b407875
2025-04-22 05:10:51 [info]  Application démarrée
2025-04-22 05:10:51 [info]  Création de la fenêtre de démarrage
2025-04-22 05:10:56 [info]  Création de la fenêtre de connexion
2025-04-22 05:11:07 [info]  Données d'authentification reçues: login
2025-04-22 05:11:07 [info]  Tentative de connexion pour l'utilisateur: Oussama
2025-04-22 05:11:07 [info]  Recherche des cookies session_id existants pour http://localhost:8069
2025-04-22 05:11:07 [info]  1 cookie(s) session_id trouvé(s) pour http://localhost:8069
2025-04-22 05:11:07 [info]  Suppression de 1 cookie(s) session_id existant(s)
2025-04-22 05:11:07 [info]  Cookie #1 à supprimer: {
  name: 'session_id',
  value: '887dcdcf...',
  domain: 'localhost',
  path: '/'
}
2025-04-22 05:11:07 [info]  Cookie session_id supprimé avec succès: 887dcdcf...
2025-04-22 05:11:07 [info]  Définition du nouveau cookie session_id pour http://localhost:8069: {
  value: '9f5f309b...',
  domain: 'localhost',
  path: '/',
  secure: false,
  httpOnly: true
}
2025-04-22 05:11:07 [info]  Nouveau cookie session_id défini avec succès pour http://localhost:8069
2025-04-22 05:11:07 [info]  Cookie session_id vérifié avant redirection: { value: '9f5f309b...', domain: 'localhost', path: '/' }
2025-04-22 05:11:07 [info]  Connexion réussie, redirection vers l'interface Odoo: http://localhost:8069/web
2025-04-22 05:11:08 [info]  Création de la fenêtre principale avec chargement direct d'Odoo
2025-04-22 05:11:08 [info]  Vérification du cookie session_id avant chargement pour http://localhost:8069
2025-04-22 05:11:08 [info]  Configuration de la Content Security Policy (CSP) pour la fenêtre Odoo
2025-04-22 05:11:08 [info]  Chargement direct de l'URL Odoo: http://localhost:8069/web
2025-04-22 05:11:08 [info]  Cookie session_id trouvé avant chargement: { value: '9f5f309b...', domain: 'localhost', path: '/' }
2025-04-22 05:11:08 [info]  Application de la CSP pour la requête: http://localhost:8069/web
2025-04-22 05:11:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:08 [info]  Application de la CSP pour la requête: http://localhost:8069/web/login
2025-04-22 05:11:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:08 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/461-0f2c52b/web.assets_common.css
2025-04-22 05:11:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:08 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/465-ab2ef5f/web.assets_frontend.css
2025-04-22 05:11:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:08 [info]  Application de la CSP pour la requête: http://localhost:8069/web/binary/company_logo
2025-04-22 05:11:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:08 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/314-ed746a2/web.assets_common_minimal_js.js
2025-04-22 05:11:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:08 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/315-3a5eadd/web.assets_frontend_minimal_js.js
2025-04-22 05:11:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:08 [info]  Application de la CSP pour la requête: http://localhost:8069/sh_back_theme_config/static/src/fonts/Roboto/KFOlCnqEu92Fr1MmWUlfBBc4.woff2
2025-04-22 05:11:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:08 [info]  Application de la CSP pour la requête: http://localhost:8069/sh_back_theme_config/static/src/fonts/Roboto/KFOmCnqEu92Fr1Mu4mxK.woff2
2025-04-22 05:11:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:08 [info]  Vérification des cookies après chargement pour http://localhost:8069
2025-04-22 05:11:08 [info]  1 cookie(s) disponible(s) pour http://localhost:8069 après chargement
2025-04-22 05:11:08 [info]  Cookie #1: {
  name: 'session_id',
  value: '4a205c0c...',
  domain: 'localhost',
  path: '/',
  httpOnly: true,
  secure: false
}
2025-04-22 05:11:08 [info]  Cookie session_id trouvé après chargement: { value: '4a205c0c...', domain: 'localhost', path: '/' }
2025-04-22 05:11:08 [info]  Application de la CSP pour la requête: http://localhost:8069/web/image?model=sh.back.theme.config.settings&id=1&field=login_page_background_image
2025-04-22 05:11:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:08 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/466-303ecf4/web.assets_common_lazy.js
2025-04-22 05:11:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:08 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/467-887db93/web.assets_frontend_lazy.js
2025-04-22 05:11:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:08 [info]  Application de la CSP pour la requête: http://localhost:8069/website/translations/3f5c0ecc31ad1cdc03233b950521bd83894efe7a?mods=&lang=
2025-04-22 05:11:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:08 [info]  Application de la CSP pour la requête: http://localhost:8069/web/webclient/locale/en_US
2025-04-22 05:11:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:23 [info]  Application de la CSP pour la requête: http://localhost:8069/web/login
2025-04-22 05:11:23 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:24 [info]  Application de la CSP pour la requête: http://localhost:8069/web
2025-04-22 05:11:24 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:24 [info]  Application de la CSP pour la requête: http://localhost:8069/web/webclient/load_menus/be5bc79df7e29cc74fbf08fe86754b3b6016ade4
2025-04-22 05:11:24 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:24 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/463-0f2c52b/web.assets_common.js
2025-04-22 05:11:24 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:24 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/474-798c67d/web.assets_backend.css
2025-04-22 05:11:24 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:24 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/475-798c67d/web.assets_backend.js
2025-04-22 05:11:24 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:24 [info]  Application de la CSP pour la requête: http://localhost:8069/web/_config
2025-04-22 05:11:24 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:24 [info]  Application de la CSP pour la requête: http://localhost:8069/web/dataset/call_kw/ir.config_parameter/get_debranding_parameters
2025-04-22 05:11:24 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:24 [info]  Application de la CSP pour la requête: http://localhost:8069/web/webclient/qweb/a5cf5a53b74ac209e33c147dc6668a48831ac8ea?mods=base,web,base_setup,bus,web_tour,mail,contacts,aos_contacts,advanced_web_domain_widget,simplify_access_management,hide_powered_by_and_manage_db,product,web_editor,auth_signup,portal,resource,digest,account,ir_rule_protected,access_restricted,access_apps,access_settings_menu,account_bank_statement_import,iap,sms,account_reports,account_followup,base_import,l10n_dz,mail_bot,nati_arabic_font,oi_login_as,partner_autocomplete,payment,payment_transfer,sh_back_theme_config,sh_backmate_theme,snailmail,snailmail_account,web_cohort,web_debranding,web_diagram,web_gantt,web_grid,web_kanban_gauge,web_map,web_mobile,web_unsplash
2025-04-22 05:11:24 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:24 [info]  Application de la CSP pour la requête: http://localhost:8069/firebase-messaging-sw.js
2025-04-22 05:11:24 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:24 [info]  Application de la CSP pour la requête: http://localhost:8069/sh_backmate_theme/static/index.js
2025-04-22 05:11:24 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:24 [info]  Application de la CSP pour la requête: http://localhost:8069/web/webclient/translations/dc6d9fd2fb56dde20158c223317a5941b2a9c5ee?mods=base%2Cweb%2Cbase_setup%2Cbus%2Cweb_tour%2Cmail%2Ccontacts%2Caos_contacts%2Cadvanced_web_domain_widget%2Csimplify_access_management%2Chide_powered_by_and_manage_db%2Cproduct%2Cweb_editor%2Cauth_signup%2Cportal%2Cresource%2Cdigest%2Caccount%2Cir_rule_protected%2Caccess_restricted%2Caccess_apps%2Caccess_settings_menu%2Caccount_bank_statement_import%2Ciap%2Csms%2Caccount_reports%2Caccount_followup%2Cbase_import%2Cl10n_dz%2Cmail_bot%2Cnati_arabic_font%2Coi_login_as%2Cpartner_autocomplete%2Cpayment%2Cpayment_transfer%2Csh_back_theme_config%2Csh_backmate_theme%2Csnailmail%2Csnailmail_account%2Cweb_cohort%2Cweb_debranding%2Cweb_diagram%2Cweb_gantt%2Cweb_grid%2Cweb_kanban_gauge%2Cweb_map%2Cweb_mobile%2Cweb_unsplash&lang=fr_FR
2025-04-22 05:11:24 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:24 [info]  Application de la CSP pour la requête: http://localhost:8069/web/webclient/locale/fr_FR
2025-04-22 05:11:24 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:24 [info]  Application de la CSP pour la requête: http://localhost:8069/web/dataset/call_kw/res.users/systray_get_activities
2025-04-22 05:11:24 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:24 [info]  Application de la CSP pour la requête: http://localhost:8069/web/static/lib/fontawesome/fonts/fontawesome-webfont.woff2?v=4.7.0
2025-04-22 05:11:24 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:24 [info]  Application de la CSP pour la requête: http://localhost:8069/web/image?model=ir.ui.menu&field=web_icon_data&id=190
2025-04-22 05:11:24 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:24 [info]  Application de la CSP pour la requête: http://localhost:8069/web/image?model=ir.ui.menu&field=web_icon_data&id=98
2025-04-22 05:11:24 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:24 [info]  Application de la CSP pour la requête: http://localhost:8069/web/image?model=ir.ui.menu&field=web_icon_data&id=5
2025-04-22 05:11:24 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:24 [info]  Application de la CSP pour la requête: http://localhost:8069/web/image?model=ir.ui.menu&field=web_icon_data&id=83
2025-04-22 05:11:24 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:25 [info]  Application de la CSP pour la requête: http://localhost:8069/account/static/description/icon.png
2025-04-22 05:11:25 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:25 [info]  Application de la CSP pour la requête: http://localhost:8069/mail/init_messaging
2025-04-22 05:11:25 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:25 [info]  Application de la CSP pour la requête: http://localhost:8069/web/image?model=res.users&field=image_128&id=2
2025-04-22 05:11:25 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:25 [info]  Application de la CSP pour la requête: http://localhost:8069/web/image?model=ir.ui.menu&field=web_icon_data&id=4
2025-04-22 05:11:25 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:25 [info]  Application de la CSP pour la requête: http://localhost:8069/web/dataset/call_kw/res.users/read
2025-04-22 05:11:25 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:25 [info]  Application de la CSP pour la requête: http://localhost:8069/web/action/load
2025-04-22 05:11:25 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:26 [info]  Application de la CSP pour la requête: http://localhost:8069/web/dataset/call_kw/account.move/load_views
2025-04-22 05:11:26 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:26 [info]  Application de la CSP pour la requête: http://localhost:8069/web/dataset/search_read
2025-04-22 05:11:26 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:26 [info]  Application de la CSP pour la requête: http://localhost:8069/account/account_invoice_onboarding
2025-04-22 05:11:26 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:26 [info]  Application de la CSP pour la requête: http://localhost:8069/account/static/src/img/account_invoice_onboarding_bg.jpg
2025-04-22 05:11:26 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:26 [info]  Application de la CSP pour la requête: http://localhost:8069/web/static/src/img/smiling_face.svg
2025-04-22 05:11:26 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:26 [info]  Application de la CSP pour la requête: http://localhost:8069/sh_back_theme_config/static/src/fonts/Roboto/KFOlCnqEu92Fr1MmEU9fBBc4.woff2
2025-04-22 05:11:26 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:44 [info]  Application de la CSP pour la requête: http://localhost:8069/web/dataset/call_kw/account.move/default_get
2025-04-22 05:11:44 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:44 [info]  Application de la CSP pour la requête: http://localhost:8069/web/dataset/call_kw/account.move/onchange
2025-04-22 05:11:44 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:44 [info]  Application de la CSP pour la requête: http://localhost:8069/web/dataset/call_kw/res.users/name_get
2025-04-22 05:11:44 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:44 [info]  Application de la CSP pour la requête: http://localhost:8069/web/static/src/xml/ribbon.xml
2025-04-22 05:11:44 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:44 [info]  Application de la CSP pour la requête: http://localhost:8069/partner_autocomplete/static/lib/jsvat.js
2025-04-22 05:11:44 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:44 [info]  Application de la CSP pour la requête: http://localhost:8069/web/image/res.partner/3/image_128
2025-04-22 05:11:44 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:44 [info]  Application de la CSP pour la requête: http://localhost:8069/nati_arabic_font/static/src/fonts/Almarai/Almarai-Regular.woff
2025-04-22 05:11:44 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:47 [info]  Application de la CSP pour la requête: http://localhost:8069/web/dataset/search_read
2025-04-22 05:11:47 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:47 [info]  Application de la CSP pour la requête: http://localhost:8069/account/account_invoice_onboarding
2025-04-22 05:11:47 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:50 [info]  Application de la CSP pour la requête: http://localhost:8069/web/session/logout
2025-04-22 05:11:50 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:50 [info]  Application de la CSP pour la requête: http://localhost:8069/web
2025-04-22 05:11:50 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:50 [info]  Application de la CSP pour la requête: http://localhost:8069/web/login
2025-04-22 05:11:50 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:50 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/461-0f2c52b/web.assets_common.css
2025-04-22 05:11:50 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:50 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/465-ab2ef5f/web.assets_frontend.css
2025-04-22 05:11:50 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:50 [info]  Application de la CSP pour la requête: http://localhost:8069/web/binary/company_logo
2025-04-22 05:11:50 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:50 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/315-3a5eadd/web.assets_frontend_minimal_js.js
2025-04-22 05:11:50 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:50 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/314-ed746a2/web.assets_common_minimal_js.js
2025-04-22 05:11:50 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:50 [info]  Application de la CSP pour la requête: http://localhost:8069/sh_back_theme_config/static/src/fonts/Roboto/KFOlCnqEu92Fr1MmWUlfBBc4.woff2
2025-04-22 05:11:50 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:50 [info]  Application de la CSP pour la requête: http://localhost:8069/sh_back_theme_config/static/src/fonts/Roboto/KFOmCnqEu92Fr1Mu4mxK.woff2
2025-04-22 05:11:50 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:50 [info]  Application de la CSP pour la requête: http://localhost:8069/web/image?model=sh.back.theme.config.settings&id=1&field=login_page_background_image
2025-04-22 05:11:50 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:50 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/466-303ecf4/web.assets_common_lazy.js
2025-04-22 05:11:50 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:50 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/467-887db93/web.assets_frontend_lazy.js
2025-04-22 05:11:50 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:50 [info]  Application de la CSP pour la requête: http://localhost:8069/website/translations/3f5c0ecc31ad1cdc03233b950521bd83894efe7a?mods=&lang=
2025-04-22 05:11:50 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:50 [info]  Application de la CSP pour la requête: http://localhost:8069/web/webclient/locale/en_US
2025-04-22 05:11:50 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:11:52 [info]  Application de la CSP pour la requête: http://localhost:8069/firebase-messaging-sw.js
2025-04-22 05:11:52 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:12:02 [info]  Application de la CSP pour la requête: http://localhost:8069/web/login
2025-04-22 05:12:02 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:22:48 [info]  Application démarrée
2025-04-22 05:22:48 [info]  Création de la fenêtre de démarrage
2025-04-22 05:22:53 [info]  Création de la fenêtre de connexion
2025-04-22 05:30:48 [info]  Application démarrée
2025-04-22 05:30:48 [info]  Création de la fenêtre de démarrage
2025-04-22 05:30:53 [info]  Création de la fenêtre de connexion
2025-04-22 05:39:42 [info]  Application démarrée
2025-04-22 05:39:42 [info]  Création de la fenêtre de démarrage
2025-04-22 05:39:48 [info]  Création de la fenêtre de connexion
2025-04-22 05:40:07 [info]  Données d'authentification reçues: login
2025-04-22 05:40:07 [info]  Tentative de connexion pour l'utilisateur: Oussama
2025-04-22 05:40:07 [info]  Recherche des cookies session_id existants pour http://localhost:8069
2025-04-22 05:40:07 [info]  1 cookie(s) session_id trouvé(s) pour http://localhost:8069
2025-04-22 05:40:07 [info]  Suppression de 1 cookie(s) session_id existant(s)
2025-04-22 05:40:07 [info]  Cookie #1 à supprimer: {
  name: 'session_id',
  value: '774cd1d1...',
  domain: 'localhost',
  path: '/'
}
2025-04-22 05:40:07 [info]  Cookie session_id supprimé avec succès: 774cd1d1...
2025-04-22 05:40:07 [info]  Définition du nouveau cookie session_id pour http://localhost:8069: {
  value: '244a6363...',
  domain: 'localhost',
  path: '/',
  secure: false,
  httpOnly: true
}
2025-04-22 05:40:07 [info]  Nouveau cookie session_id défini avec succès pour http://localhost:8069
2025-04-22 05:40:07 [info]  Cookie session_id vérifié avant redirection: { value: '244a6363...', domain: 'localhost', path: '/' }
2025-04-22 05:40:07 [info]  Connexion réussie, redirection vers l'interface Odoo: http://localhost:8069/web
2025-04-22 05:40:07 [info]  Création de la fenêtre principale avec chargement direct d'Odoo
2025-04-22 05:40:07 [info]  Vérification du cookie session_id avant chargement pour http://localhost:8069
2025-04-22 05:40:07 [info]  Configuration de la Content Security Policy (CSP) pour la fenêtre Odoo
2025-04-22 05:40:07 [info]  Vérification finale du cookie session_id avant chargement de l'URL pour http://localhost:8069
2025-04-22 05:40:07 [info]  Cookie session_id trouvé avant chargement: { value: '244a6363...', domain: 'localhost', path: '/' }
2025-04-22 05:40:07 [info]  Cookie session_id trouvé avant chargement de l'URL: {
  value: '244a6363...',
  domain: 'localhost',
  path: '/',
  httpOnly: true,
  secure: false
}
2025-04-22 05:40:07 [info]  1 cookie(s) disponible(s) pour http://localhost:8069 avant chargement:
2025-04-22 05:40:07 [info]  Cookie #1: {
  name: 'session_id',
  value: '244a6363...',
  domain: 'localhost',
  path: '/'
}
2025-04-22 05:40:07 [info]  Chargement direct de l'URL Odoo: http://localhost:8069/web
2025-04-22 05:40:08 [info]  Application de la CSP pour la requête: http://localhost:8069/web
2025-04-22 05:40:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:40:08 [info]  Application de la CSP pour la requête: http://localhost:8069/web/login
2025-04-22 05:40:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:40:08 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/461-0f2c52b/web.assets_common.css
2025-04-22 05:40:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:40:08 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/465-ab2ef5f/web.assets_frontend.css
2025-04-22 05:40:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:40:08 [info]  Application de la CSP pour la requête: http://localhost:8069/web/binary/company_logo
2025-04-22 05:40:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:40:08 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/314-ed746a2/web.assets_common_minimal_js.js
2025-04-22 05:40:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:40:08 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/315-3a5eadd/web.assets_frontend_minimal_js.js
2025-04-22 05:40:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:40:08 [info]  Application de la CSP pour la requête: http://localhost:8069/sh_back_theme_config/static/src/fonts/Roboto/KFOlCnqEu92Fr1MmWUlfBBc4.woff2
2025-04-22 05:40:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:40:08 [info]  Application de la CSP pour la requête: http://localhost:8069/sh_back_theme_config/static/src/fonts/Roboto/KFOmCnqEu92Fr1Mu4mxK.woff2
2025-04-22 05:40:08 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069
2025-04-22 05:40:08 [info]  Vérification détaillée des cookies après chargement pour http://localhost:8069
2025-04-22 05:40:08 [info]  Cookie session_id trouvé après chargement: {
  value: 'bd5a3674...',
  domain: 'localhost',
  path: '/',
  httpOnly: true,
  secure: false,
  expirationDate: '2025-07-21T04:40:08.808Z'
}
2025-04-22 05:40:08 [info]  1 cookie(s) disponible(s) pour http://localhost:8069 après chargement:
2025-04-22 05:40:08 [info]  Cookie #1: {
  name: 'session_id',
  value: 'bd5a3674...',
  domain: 'localhost',
  path: '/',
  httpOnly: true,
  secure: false,
  expirationDate: '2025-07-21T04:40:08.808Z'
}
2025-04-22 05:40:08 [info]  Vérification des en-têtes de la page chargée
2025-04-22 05:51:02 [info]  Application démarrée
2025-04-22 05:51:02 [info]  Création de la fenêtre de démarrage
2025-04-22 05:51:08 [info]  Création de la fenêtre de connexion
2025-04-22 05:51:20 [info]  Données d'authentification reçues: login
2025-04-22 05:51:20 [info]  Tentative de connexion pour l'utilisateur: Oussama
2025-04-22 05:51:20 [info]  Recherche des cookies session_id existants pour http://localhost:8069
2025-04-22 05:51:20 [info]  1 cookie(s) session_id trouvé(s) pour http://localhost:8069
2025-04-22 05:51:20 [info]  Suppression de 1 cookie(s) session_id existant(s)
2025-04-22 05:51:20 [info]  Cookie #1 à supprimer: {
  name: 'session_id',
  value: 'bd5a3674...',
  domain: 'localhost',
  path: '/'
}
2025-04-22 05:51:20 [info]  Cookie session_id supprimé avec succès: bd5a3674...
2025-04-22 05:51:20 [info]  Définition du nouveau cookie session_id pour http://localhost:8069: {
  value: '3b2200a1...',
  domain: 'localhost',
  path: '/',
  secure: false,
  httpOnly: true
}
2025-04-22 05:51:20 [info]  Nouveau cookie session_id défini avec succès pour http://localhost:8069
2025-04-22 05:51:20 [info]  Cookie session_id vérifié avant redirection: { value: '3b2200a1...', domain: 'localhost', path: '/' }
2025-04-22 05:51:20 [info]  Connexion réussie, redirection vers l'interface Odoo: http://localhost:8069/web
2025-04-22 05:51:20 [info]  Cookie session_id vérifié avant création de la fenêtre principale: {
  value: '3b2200a1...',
  domain: 'localhost',
  path: '/',
  httpOnly: true,
  secure: false
}
2025-04-22 05:51:20 [info]  Création de la fenêtre principale avec chargement direct d'Odoo
2025-04-22 05:51:20 [info]  Vérification du cookie session_id avant chargement pour http://localhost:8069
2025-04-22 05:51:20 [info]  Configuration de la Content Security Policy (CSP) pour la fenêtre Odoo
2025-04-22 05:51:20 [info]  Vérification finale du cookie session_id avant chargement de l'URL pour http://localhost:8069
2025-04-22 05:51:20 [info]  Cookie session_id trouvé avant chargement: { value: '3b2200a1...', domain: 'localhost', path: '/' }
2025-04-22 05:51:20 [info]  Cookie session_id trouvé avant chargement de l'URL: {
  value: '3b2200a1...',
  domain: 'localhost',
  path: '/',
  httpOnly: true,
  secure: false
}
2025-04-22 05:51:20 [info]  1 cookie(s) disponible(s) pour http://localhost:8069 avant chargement:
2025-04-22 05:51:20 [info]  Cookie #1: {
  name: 'session_id',
  value: '3b2200a1...',
  domain: 'localhost',
  path: '/'
}
2025-04-22 05:51:20 [info]  Vérification ultime du cookie session_id avant chargement de l'URL
2025-04-22 05:51:20 [info]  Cookie session_id trouvé juste avant chargement: { value: '3b2200a1...', domain: 'localhost', path: '/' }
2025-04-22 05:51:20 [info]  Chargement direct de l'URL Odoo: http://localhost:8069/web
2025-04-22 05:51:21 [info]  Application de la CSP pour la requête: http://localhost:8069/web
2025-04-22 05:51:21 [info]  CSP mise à jour avec font-src: 'self' data: http://localhost:8069 https://fonts.odoocdn.com
2025-04-22 05:51:21 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069 https://fonts.odoocdn.com
2025-04-22 05:51:21 [info]  Application de la CSP pour la requête: http://localhost:8069/web/login
2025-04-22 05:51:21 [info]  CSP mise à jour avec font-src: 'self' data: http://localhost:8069 https://fonts.odoocdn.com
2025-04-22 05:51:21 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069 https://fonts.odoocdn.com
2025-04-22 05:51:21 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/461-0f2c52b/web.assets_common.css
2025-04-22 05:51:21 [info]  CSP mise à jour avec font-src: 'self' data: http://localhost:8069 https://fonts.odoocdn.com
2025-04-22 05:51:21 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069 https://fonts.odoocdn.com
2025-04-22 05:51:21 [info]  Application de la CSP pour la requête: http://localhost:8069/web/binary/company_logo
2025-04-22 05:51:21 [info]  CSP mise à jour avec font-src: 'self' data: http://localhost:8069 https://fonts.odoocdn.com
2025-04-22 05:51:21 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069 https://fonts.odoocdn.com
2025-04-22 05:51:21 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/465-ab2ef5f/web.assets_frontend.css
2025-04-22 05:51:21 [info]  CSP mise à jour avec font-src: 'self' data: http://localhost:8069 https://fonts.odoocdn.com
2025-04-22 05:51:21 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069 https://fonts.odoocdn.com
2025-04-22 05:51:21 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/314-ed746a2/web.assets_common_minimal_js.js
2025-04-22 05:51:21 [info]  CSP mise à jour avec font-src: 'self' data: http://localhost:8069 https://fonts.odoocdn.com
2025-04-22 05:51:21 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069 https://fonts.odoocdn.com
2025-04-22 05:51:21 [info]  Application de la CSP pour la requête: http://localhost:8069/web/content/315-3a5eadd/web.assets_frontend_minimal_js.js
2025-04-22 05:51:21 [info]  CSP mise à jour avec font-src: 'self' data: http://localhost:8069 https://fonts.odoocdn.com
2025-04-22 05:51:21 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069 https://fonts.odoocdn.com
2025-04-22 05:51:21 [info]  Application de la CSP pour la requête: http://localhost:8069/sh_back_theme_config/static/src/fonts/Roboto/KFOlCnqEu92Fr1MmWUlfBBc4.woff2
2025-04-22 05:51:21 [info]  CSP mise à jour avec font-src: 'self' data: http://localhost:8069 https://fonts.odoocdn.com
2025-04-22 05:51:21 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069 https://fonts.odoocdn.com
2025-04-22 05:51:21 [info]  Application de la CSP pour la requête: http://localhost:8069/sh_back_theme_config/static/src/fonts/Roboto/KFOmCnqEu92Fr1Mu4mxK.woff2
2025-04-22 05:51:21 [info]  CSP mise à jour avec font-src: 'self' data: http://localhost:8069 https://fonts.odoocdn.com
2025-04-22 05:51:21 [info]  CSP appliquée: default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: http://localhost:8069; script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: http://localhost:8069; style-src 'self' 'unsafe-inline' http://localhost:8069; img-src 'self' data: blob: http://localhost:8069; connect-src 'self' http://localhost:8069 ws: wss:; font-src 'self' data: http://localhost:8069 https://fonts.odoocdn.com
2025-04-22 05:51:21 [info]  Vérification détaillée des cookies après chargement pour http://localhost:8069
2025-04-22 05:51:21 [info]  Cookie session_id trouvé après chargement: {
  value: 'b09aa99a...',
  domain: 'localhost',
  path: '/',
  httpOnly: true,
  secure: false,
  expirationDate: '2025-07-21T04:51:21.501Z'
}
2025-04-22 05:51:21 [info]  1 cookie(s) disponible(s) pour http://localhost:8069 après chargement:
2025-04-22 05:51:21 [info]  Cookie #1: {
  name: 'session_id',
  value: 'b09aa99a...',
  domain: 'localhost',
  path: '/',
  httpOnly: true,
  secure: false,
  expirationDate: '2025-07-21T04:51:21.501Z'
}
2025-04-22 05:51:21 [info]  Vérification des en-têtes de la page chargée
2025-04-22 05:51:21 [info]  Vérification du cookie session_id immédiatement après chargement
2025-04-22 05:51:21 [info]  Cookie session_id toujours présent après chargement: { value: 'b09aa99a...', domain: 'localhost', path: '/' }
2025-04-22 12:32:20 [info]  Application démarrée
2025-04-22 12:32:20 [info]  Création de la fenêtre de démarrage
2025-04-22 12:32:25 [info]  Création de la fenêtre de connexion
2025-04-22 12:40:00 [info]  Application démarrée
2025-04-22 12:40:00 [info]  Création de la fenêtre de démarrage
2025-04-22 12:40:05 [info]  Création de la fenêtre de connexion
2025-04-22 12:45:46 [info]  Application démarrée
2025-04-22 12:45:46 [info]  Création de la fenêtre de démarrage
2025-04-22 12:45:51 [info]  Création de la fenêtre de connexion
2025-04-22 12:55:05 [info]  Application démarrée
2025-04-22 12:55:05 [info]  Création de la fenêtre de démarrage
2025-04-22 12:55:11 [info]  Création de la fenêtre de connexion
2025-04-22 13:05:55 [info]  Application démarrée
2025-04-22 13:05:55 [info]  Création de la fenêtre de démarrage
2025-04-22 13:06:00 [info]  Création de la fenêtre de connexion
2025-04-22 13:07:46 [info]  Application démarrée
2025-04-22 13:07:46 [info]  Création de la fenêtre de démarrage
2025-04-22 13:07:51 [info]  Création de la fenêtre de connexion
2025-04-22 13:14:54 [info]  Application démarrée
2025-04-22 13:14:54 [info]  Création de la fenêtre de démarrage
2025-04-22 13:14:59 [info]  Création de la fenêtre de connexion
2025-04-22 13:20:05 [info]  Application démarrée
2025-04-22 13:20:05 [info]  Création de la fenêtre de démarrage
2025-04-22 13:20:10 [info]  Création de la fenêtre de connexion
2025-04-22 13:24:17 [info]  Application démarrée
2025-04-22 13:24:17 [info]  Mode développement: Activé
2025-04-22 13:24:17 [info]  Création de la fenêtre de démarrage
2025-04-22 13:24:17 [info]  Mode développement détecté, activation des DevTools
2025-04-22 13:24:22 [info]  Création de la fenêtre de connexion
2025-04-22 13:24:22 [info]  Mode développement détecté, activation des DevTools
2025-04-22 13:24:22 [info]  Mode développement détecté, activation des DevTools
2025-04-22 13:25:01 [info]  Application démarrée
2025-04-22 13:25:01 [info]  Mode développement: Activé
2025-04-22 13:25:01 [info]  Création de la fenêtre de démarrage
2025-04-22 13:25:01 [info]  Mode développement détecté, activation des DevTools
2025-04-22 13:25:07 [info]  Création de la fenêtre de connexion
2025-04-22 13:25:07 [info]  Mode développement détecté, activation des DevTools
2025-04-22 13:25:07 [info]  Mode développement détecté, activation des DevTools
2025-04-22 13:54:31 [info]  Module d'authentification Odoo importé dans le main process
2025-04-22 13:54:31 [info]  Application démarrée
2025-04-22 13:54:31 [info]  Mode développement: Activé
2025-04-22 13:54:31 [info]  Création de la fenêtre de démarrage
2025-04-22 13:54:31 [info]  Mode développement détecté, activation des DevTools
2025-04-22 13:54:36 [info]  Création de la fenêtre de connexion
2025-04-22 13:54:36 [info]  Mode développement détecté, activation des DevTools
2025-04-22 13:54:36 [info]  Mode développement détecté, activation des DevTools
2025-04-22 13:58:06 [info]  Module d'authentification Odoo importé dans le main process
2025-04-22 13:58:06 [info]  Application démarrée
2025-04-22 13:58:06 [info]  Mode développement: Activé
2025-04-22 13:58:06 [info]  Création de la fenêtre de démarrage
2025-04-22 13:58:06 [info]  Mode développement détecté, activation des DevTools
2025-04-22 13:58:11 [info]  Création de la fenêtre de connexion
2025-04-22 13:58:11 [info]  Mode développement détecté, activation des DevTools
2025-04-22 13:58:12 [info]  Mode développement détecté, activation des DevTools
2025-04-22 14:05:09 [info]  Module d'authentification Odoo importé dans le main process
2025-04-22 14:05:09 [info]  Application démarrée
2025-04-22 14:05:09 [info]  Mode développement: Activé
2025-04-22 14:05:09 [info]  Création de la fenêtre de démarrage
2025-04-22 14:05:09 [info]  Mode développement détecté, activation des DevTools
2025-04-22 14:05:14 [info]  Création de la fenêtre de connexion
2025-04-22 14:05:14 [info]  Mode développement détecté, activation des DevTools
2025-04-22 14:05:14 [info]  Mode développement détecté, activation des DevTools
