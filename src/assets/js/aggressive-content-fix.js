/**
 * Fix agressif pour les erreurs ERR_CONTENT_LENGTH_MISMATCH
 * Cette solution intercepte et corrige les problèmes au niveau le plus bas possible
 */
(function() {
  'use strict';
  
  console.log('[Edara ERP] Aggressive Content Fix: Initialisation du fix agressif');

  // Configuration agressive
  const CONFIG = {
    maxRetries: 5,
    retryDelay: 500,
    forceReloadAfter: 3000,
    aggressiveMode: true,
    debug: true
  };

  // Suivi des assets problématiques
  let failedAssets = new Map();
  let retryCount = 0;
  let isFixing = false;

  /**
   * Intercepte et remplace fetch pour forcer le rechargement
   */
  function interceptFetch() {
    const originalFetch = window.fetch;
    
    window.fetch = async function(resource, options = {}) {
      const url = typeof resource === 'string' ? resource : resource.url;
      
      // Vérifier si c'est un asset critique
      if (url && (url.includes('web.assets_backend') || url.includes('web.assets_common') || url.includes('load_menus'))) {
        console.log(`[Edara ERP] Aggressive Fix: Interception fetch pour: ${url}`);
        
        // Forcer les options pour éviter les problèmes de cache et de longueur
        const aggressiveOptions = {
          ...options,
          method: 'GET',
          cache: 'no-cache',
          credentials: 'same-origin',
          headers: {
            ...options.headers,
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0',
            'Accept': '*/*',
            'Accept-Encoding': 'identity' // Désactiver la compression
          }
        };

        try {
          const response = await originalFetch(resource, aggressiveOptions);
          
          if (!response.ok) {
            console.log(`[Edara ERP] Aggressive Fix: Réponse non-OK pour ${url}, tentative de correction`);
            return await retryAssetLoad(url, aggressiveOptions);
          }
          
          return response;
        } catch (error) {
          console.error(`[Edara ERP] Aggressive Fix: Erreur fetch pour ${url}:`, error);
          return await retryAssetLoad(url, aggressiveOptions);
        }
      }
      
      return originalFetch(resource, options);
    };
  }

  /**
   * Retry agressif pour charger un asset
   */
  async function retryAssetLoad(url, options, attempt = 1) {
    if (attempt > CONFIG.maxRetries) {
      console.error(`[Edara ERP] Aggressive Fix: Échec définitif après ${CONFIG.maxRetries} tentatives pour: ${url}`);
      throw new Error(`Failed to load ${url} after ${CONFIG.maxRetries} attempts`);
    }

    console.log(`[Edara ERP] Aggressive Fix: Tentative ${attempt}/${CONFIG.maxRetries} pour: ${url}`);
    
    // Ajouter un délai progressif
    await new Promise(resolve => setTimeout(resolve, CONFIG.retryDelay * attempt));
    
    // Créer une nouvelle URL avec cache-busting agressif
    const urlObj = new URL(url, window.location.origin);
    urlObj.searchParams.set('_retry', attempt);
    urlObj.searchParams.set('_ts', Date.now());
    urlObj.searchParams.set('_rand', Math.random().toString(36).substr(2, 9));
    
    const retryUrl = urlObj.toString();
    
    try {
      const response = await fetch(retryUrl, {
        ...options,
        headers: {
          ...options.headers,
          'Accept-Encoding': 'identity',
          'Range': 'bytes=0-' // Forcer le téléchargement complet
        }
      });
      
      if (response.ok) {
        console.log(`[Edara ERP] Aggressive Fix: ✅ Succès tentative ${attempt} pour: ${url}`);
        return response;
      } else {
        return await retryAssetLoad(url, options, attempt + 1);
      }
    } catch (error) {
      console.error(`[Edara ERP] Aggressive Fix: Erreur tentative ${attempt} pour ${url}:`, error);
      return await retryAssetLoad(url, options, attempt + 1);
    }
  }

  /**
   * Remplace XMLHttpRequest pour les assets critiques
   */
  function interceptXHR() {
    const OriginalXHR = window.XMLHttpRequest;
    
    window.XMLHttpRequest = function() {
      const xhr = new OriginalXHR();
      const originalOpen = xhr.open;
      const originalSend = xhr.send;
      
      xhr.open = function(method, url, ...args) {
        // Vérifier si c'est un asset critique
        if (url && (url.includes('web.assets_backend') || url.includes('web.assets_common') || url.includes('load_menus'))) {
          console.log(`[Edara ERP] Aggressive Fix: Interception XHR pour: ${url}`);
          
          // Ajouter cache-busting
          const urlObj = new URL(url, window.location.origin);
          urlObj.searchParams.set('_xhr', Date.now());
          url = urlObj.toString();
        }
        
        return originalOpen.call(this, method, url, ...args);
      };
      
      xhr.send = function(data) {
        // Configurer les headers pour éviter les problèmes
        xhr.setRequestHeader('Cache-Control', 'no-cache');
        xhr.setRequestHeader('Pragma', 'no-cache');
        xhr.setRequestHeader('Accept-Encoding', 'identity');
        
        return originalSend.call(this, data);
      };
      
      return xhr;
    };
  }

  /**
   * Force le rechargement de tous les assets critiques
   */
  async function forceReloadCriticalAssets() {
    console.log('[Edara ERP] Aggressive Fix: Force rechargement de tous les assets critiques');
    
    const criticalAssets = [
      '/web/content/566-ba15075/web.assets_backend.js',
      '/web/content/567-ba15075/web.assets_common.js',
      '/web/content/568-ba15075/web.assets_backend.css',
      '/web/content/569-ba15075/web.assets_common.css'
    ];

    for (const assetPath of criticalAssets) {
      try {
        const fullUrl = new URL(assetPath, window.location.origin).toString();
        console.log(`[Edara ERP] Aggressive Fix: Rechargement forcé de: ${fullUrl}`);
        
        await forceLoadAsset(fullUrl);
      } catch (error) {
        console.error(`[Edara ERP] Aggressive Fix: Erreur lors du rechargement forcé de ${assetPath}:`, error);
      }
    }
  }

  /**
   * Force le chargement d'un asset spécifique
   */
  async function forceLoadAsset(url) {
    return new Promise((resolve, reject) => {
      // Créer une URL avec cache-busting agressif
      const urlObj = new URL(url);
      urlObj.searchParams.set('_force', Date.now());
      urlObj.searchParams.set('_aggressive', '1');
      const cacheBustedUrl = urlObj.toString();

      if (url.endsWith('.js')) {
        // Pour les fichiers JavaScript
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.async = false;
        script.defer = false;
        
        script.onload = () => {
          console.log(`[Edara ERP] Aggressive Fix: ✅ Script chargé: ${url}`);
          resolve();
        };
        
        script.onerror = (error) => {
          console.error(`[Edara ERP] Aggressive Fix: ❌ Erreur script: ${url}`, error);
          
          // Essayer avec fetch + eval en fallback
          fetch(cacheBustedUrl, {
            cache: 'no-cache',
            headers: {
              'Accept-Encoding': 'identity',
              'Cache-Control': 'no-cache'
            }
          })
          .then(response => response.text())
          .then(code => {
            try {
              eval(code);
              console.log(`[Edara ERP] Aggressive Fix: ✅ Script exécuté via eval: ${url}`);
              resolve();
            } catch (evalError) {
              console.error(`[Edara ERP] Aggressive Fix: ❌ Erreur eval: ${url}`, evalError);
              reject(evalError);
            }
          })
          .catch(fetchError => {
            console.error(`[Edara ERP] Aggressive Fix: ❌ Erreur fetch fallback: ${url}`, fetchError);
            reject(fetchError);
          });
        };
        
        script.src = cacheBustedUrl;
        document.head.appendChild(script);
        
      } else if (url.endsWith('.css')) {
        // Pour les fichiers CSS
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.type = 'text/css';
        
        link.onload = () => {
          console.log(`[Edara ERP] Aggressive Fix: ✅ CSS chargé: ${url}`);
          resolve();
        };
        
        link.onerror = (error) => {
          console.error(`[Edara ERP] Aggressive Fix: ❌ Erreur CSS: ${url}`, error);
          reject(error);
        };
        
        link.href = cacheBustedUrl;
        document.head.appendChild(link);
      }
    });
  }

  /**
   * Surveille et corrige les erreurs en temps réel
   */
  function setupErrorMonitoring() {
    // Intercepter les erreurs globales
    window.addEventListener('error', (event) => {
      if (event.target && (event.target.src || event.target.href)) {
        const url = event.target.src || event.target.href;
        
        if (url.includes('web.assets_backend') || url.includes('web.assets_common')) {
          console.log(`[Edara ERP] Aggressive Fix: 🚨 Erreur détectée sur: ${url}`);
          
          event.preventDefault();
          event.stopPropagation();
          
          // Marquer comme échoué et essayer de corriger
          failedAssets.set(url, (failedAssets.get(url) || 0) + 1);
          
          setTimeout(() => {
            forceLoadAsset(url).catch(error => {
              console.error(`[Edara ERP] Aggressive Fix: Impossible de corriger: ${url}`, error);
            });
          }, 100);
        }
      }
    }, true);

    // Surveiller les erreurs de console
    const originalConsoleError = console.error;
    console.error = function(...args) {
      const message = args.join(' ');
      
      if (message.includes('ERR_CONTENT_LENGTH_MISMATCH') || message.includes('Failed to load resource')) {
        console.log(`[Edara ERP] Aggressive Fix: 🚨 Erreur console détectée: ${message}`);
        
        // Déclencher un rechargement forcé après un délai
        setTimeout(() => {
          if (!isFixing) {
            isFixing = true;
            forceReloadCriticalAssets().finally(() => {
              isFixing = false;
            });
          }
        }, 1000);
      }
      
      return originalConsoleError.apply(console, args);
    };
  }

  /**
   * Initialisation du fix agressif
   */
  function init() {
    console.log('[Edara ERP] Aggressive Fix: 🚀 Démarrage du fix agressif');

    // Intercepter fetch et XHR
    interceptFetch();
    interceptXHR();
    
    // Configurer la surveillance d'erreurs
    setupErrorMonitoring();
    
    // Force rechargement après un délai
    setTimeout(() => {
      if (document.querySelectorAll('script[src*="web.assets_backend"]').length === 0) {
        console.log('[Edara ERP] Aggressive Fix: Assets backend manquants, rechargement forcé');
        forceReloadCriticalAssets();
      }
    }, CONFIG.forceReloadAfter);

    // Exposer l'interface de débogage
    window.EdaraAggressiveFix = {
      forceReloadCriticalAssets,
      forceLoadAsset,
      getFailedAssets: () => Object.fromEntries(failedAssets),
      isFixing: () => isFixing,
      retryCount: () => retryCount
    };

    console.log('[Edara ERP] Aggressive Fix: ✅ Fix agressif initialisé');
  }

  // Démarrer immédiatement
  init();

})();