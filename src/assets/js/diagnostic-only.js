/**
 * Script de diagnostic pour identifier les problèmes ERR_CONTENT_LENGTH_MISMATCH
 * Ce script collecte des informations détaillées sans corriger, pour analyse
 */
(function() {
  'use strict';
  
  console.log('[Edara ERP] Diagnostic: Initialisation du diagnostic Content-Length');

  // Collecteur de données de diagnostic
  const diagnosticData = {
    timestamp: Date.now(),
    userAgent: navigator.userAgent,
    url: window.location.href,
    errors: [],
    requests: [],
    assets: [],
    timing: {},
    environment: {}
  };

  /**
   * Collecte les informations sur l'environnement
   */
  function collectEnvironmentInfo() {
    diagnosticData.environment = {
      electronVersion: process?.versions?.electron || 'N/A',
      nodeVersion: process?.versions?.node || 'N/A',
      chromeVersion: process?.versions?.chrome || 'N/A',
      platform: navigator.platform,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine,
      hardwareConcurrency: navigator.hardwareConcurrency,
      maxTouchPoints: navigator.maxTouchPoints,
      webdriver: navigator.webdriver
    };

    console.log('[Edara ERP] Diagnostic: Environnement collecté:', diagnosticData.environment);
  }

  /**
   * Surveille toutes les requêtes réseau
   */
  function monitorNetworkRequests() {
    // Intercepter fetch
    const originalFetch = window.fetch;
    window.fetch = async function(...args) {
      const startTime = performance.now();
      const url = args[0];
      const options = args[1] || {};

      const requestInfo = {
        type: 'fetch',
        url: url,
        method: options.method || 'GET',
        headers: options.headers || {},
        startTime: startTime,
        timestamp: Date.now()
      };

      try {
        const response = await originalFetch.apply(this, args);
        const endTime = performance.now();
        
        requestInfo.endTime = endTime;
        requestInfo.duration = endTime - startTime;
        requestInfo.status = response.status;
        requestInfo.statusText = response.statusText;
        requestInfo.ok = response.ok;
        requestInfo.headers = {};
        
        // Collecter les en-têtes de réponse
        for (const [key, value] of response.headers.entries()) {
          requestInfo.headers[key] = value;
        }

        // Vérifier si c'est un asset critique
        if (typeof url === 'string' && 
            (url.includes('web.assets_backend') || url.includes('web.assets_common') || url.includes('load_menus'))) {
          
          requestInfo.isCriticalAsset = true;
          
          console.log(`[Edara ERP] Diagnostic: Requête asset critique:`, requestInfo);
          
          // Analyser les problèmes potentiels
          if (!response.ok) {
            requestInfo.problem = 'HTTP_ERROR';
          } else if (requestInfo.headers['content-length'] && requestInfo.headers['content-encoding']) {
            requestInfo.problem = 'CONTENT_LENGTH_WITH_ENCODING';
          } else if (!requestInfo.headers['content-length'] && !requestInfo.headers['transfer-encoding']) {
            requestInfo.problem = 'NO_LENGTH_INFO';
          }
        }

        diagnosticData.requests.push(requestInfo);
        return response;
      } catch (error) {
        const endTime = performance.now();
        
        requestInfo.endTime = endTime;
        requestInfo.duration = endTime - startTime;
        requestInfo.error = error.message;
        requestInfo.errorType = error.name;
        
        if (typeof url === 'string' && 
            (url.includes('web.assets_backend') || url.includes('web.assets_common') || url.includes('load_menus'))) {
          requestInfo.isCriticalAsset = true;
          requestInfo.problem = 'FETCH_EXCEPTION';
          
          console.error(`[Edara ERP] Diagnostic: Erreur fetch sur asset critique:`, requestInfo);
        }

        diagnosticData.requests.push(requestInfo);
        throw error;
      }
    };

    // Intercepter XMLHttpRequest
    const OriginalXHR = window.XMLHttpRequest;
    window.XMLHttpRequest = function() {
      const xhr = new OriginalXHR();
      const originalOpen = xhr.open;
      const originalSend = xhr.send;
      
      let requestInfo = {
        type: 'xhr',
        timestamp: Date.now()
      };

      xhr.open = function(method, url, ...args) {
        requestInfo.method = method;
        requestInfo.url = url;
        requestInfo.startTime = performance.now();
        
        return originalOpen.call(this, method, url, ...args);
      };

      xhr.send = function(data) {
        requestInfo.data = data;
        
        xhr.addEventListener('loadend', () => {
          requestInfo.endTime = performance.now();
          requestInfo.duration = requestInfo.endTime - requestInfo.startTime;
          requestInfo.status = xhr.status;
          requestInfo.statusText = xhr.statusText;
          requestInfo.responseHeaders = xhr.getAllResponseHeaders();
          
          if (requestInfo.url && 
              (requestInfo.url.includes('web.assets_backend') || 
               requestInfo.url.includes('web.assets_common') || 
               requestInfo.url.includes('load_menus'))) {
            
            requestInfo.isCriticalAsset = true;
            console.log(`[Edara ERP] Diagnostic: Requête XHR asset critique:`, requestInfo);
          }
          
          diagnosticData.requests.push(requestInfo);
        });
        
        return originalSend.call(this, data);
      };
      
      return xhr;
    };
  }

  /**
   * Surveille les erreurs de chargement d'assets
   */
  function monitorAssetErrors() {
    // Erreurs d'éléments
    window.addEventListener('error', (event) => {
      if (event.target && (event.target.src || event.target.href)) {
        const errorInfo = {
          type: 'asset_error',
          timestamp: Date.now(),
          tagName: event.target.tagName,
          src: event.target.src || event.target.href,
          message: event.message || 'Asset load error',
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        };

        if (errorInfo.src && 
            (errorInfo.src.includes('web.assets_backend') || 
             errorInfo.src.includes('web.assets_common') || 
             errorInfo.src.includes('load_menus'))) {
          
          errorInfo.isCriticalAsset = true;
          console.error(`[Edara ERP] Diagnostic: Erreur asset critique:`, errorInfo);
        }

        diagnosticData.errors.push(errorInfo);
      }
    }, true);

    // Erreurs de console
    const originalConsoleError = console.error;
    console.error = function(...args) {
      const message = args.join(' ');
      
      const errorInfo = {
        type: 'console_error',
        timestamp: Date.now(),
        message: message,
        args: args
      };

      if (message.includes('ERR_CONTENT_LENGTH_MISMATCH') || 
          message.includes('Failed to load resource') ||
          message.includes('net::ERR_')) {
        
        errorInfo.isNetworkError = true;
        console.log(`[Edara ERP] Diagnostic: Erreur réseau détectée:`, errorInfo);
      }

      diagnosticData.errors.push(errorInfo);
      return originalConsoleError.apply(console, args);
    };
  }

  /**
   * Analyse les assets présents dans la page
   */
  function analyzePageAssets() {
    const scripts = Array.from(document.getElementsByTagName('script'));
    const links = Array.from(document.getElementsByTagName('link'));
    
    [...scripts, ...links].forEach(element => {
      const assetInfo = {
        tagName: element.tagName,
        src: element.src || element.href,
        type: element.type,
        rel: element.rel,
        loaded: !element.hasAttribute('data-error'),
        timestamp: Date.now()
      };

      if (assetInfo.src && 
          (assetInfo.src.includes('web.assets_backend') || 
           assetInfo.src.includes('web.assets_common') || 
           assetInfo.src.includes('load_menus'))) {
        
        assetInfo.isCriticalAsset = true;
      }

      diagnosticData.assets.push(assetInfo);
    });

    console.log(`[Edara ERP] Diagnostic: ${diagnosticData.assets.length} assets analysés`);
  }

  /**
   * Collecte les informations de timing
   */
  function collectTimingInfo() {
    if (window.performance && window.performance.timing) {
      const timing = window.performance.timing;
      
      diagnosticData.timing = {
        navigationStart: timing.navigationStart,
        domainLookupStart: timing.domainLookupStart,
        domainLookupEnd: timing.domainLookupEnd,
        connectStart: timing.connectStart,
        connectEnd: timing.connectEnd,
        requestStart: timing.requestStart,
        responseStart: timing.responseStart,
        responseEnd: timing.responseEnd,
        domLoading: timing.domLoading,
        domInteractive: timing.domInteractive,
        domContentLoadedEventStart: timing.domContentLoadedEventStart,
        domContentLoadedEventEnd: timing.domContentLoadedEventEnd,
        domComplete: timing.domComplete,
        loadEventStart: timing.loadEventStart,
        loadEventEnd: timing.loadEventEnd
      };

      // Calculer les durées
      diagnosticData.timing.calculated = {
        domainLookup: timing.domainLookupEnd - timing.domainLookupStart,
        connect: timing.connectEnd - timing.connectStart,
        request: timing.responseStart - timing.requestStart,
        response: timing.responseEnd - timing.responseStart,
        domProcessing: timing.domComplete - timing.domLoading,
        totalLoad: timing.loadEventEnd - timing.navigationStart
      };
    }
  }

  /**
   * Génère un rapport de diagnostic complet
   */
  function generateDiagnosticReport() {
    const report = {
      ...diagnosticData,
      summary: {
        totalErrors: diagnosticData.errors.length,
        criticalAssetErrors: diagnosticData.errors.filter(e => e.isCriticalAsset).length,
        totalRequests: diagnosticData.requests.length,
        criticalAssetRequests: diagnosticData.requests.filter(r => r.isCriticalAsset).length,
        failedRequests: diagnosticData.requests.filter(r => !r.ok && r.status).length,
        totalAssets: diagnosticData.assets.length,
        criticalAssets: diagnosticData.assets.filter(a => a.isCriticalAsset).length
      }
    };

    console.log('[Edara ERP] Diagnostic: Rapport complet:', report);
    
    // Exposer le rapport pour récupération
    window.EdaraDiagnosticReport = report;
    
    // Notifier l'application Electron
    if (window.electronAPI && window.electronAPI.sendAuthData) {
      window.electronAPI.sendAuthData({
        action: 'diagnostic-report',
        report: report
      });
    }

    return report;
  }

  /**
   * Initialisation du diagnostic
   */
  function init() {
    console.log('[Edara ERP] Diagnostic: 🔍 Démarrage du diagnostic');

    // Collecter les informations d'environnement
    collectEnvironmentInfo();

    // Configurer la surveillance
    monitorNetworkRequests();
    monitorAssetErrors();

    // Analyser la page après chargement
    setTimeout(() => {
      analyzePageAssets();
      collectTimingInfo();
    }, 2000);

    // Générer le rapport après 10 secondes
    setTimeout(() => {
      generateDiagnosticReport();
    }, 10000);

    console.log('[Edara ERP] Diagnostic: ✅ Diagnostic initialisé');
  }

  // Démarrer le diagnostic
  init();

})();