/**
 * Script d'auto-reload intelligent pour Edara ERP
 * Détecte automatiquement les problèmes de chargement et recharge la page si nécessaire
 */
(function() {
  'use strict';
  
  console.log('[Edara ERP] Auto-Reload Fix: Initialisation');

  // Configuration
  const CONFIG = {
    checkInterval: 5000,        // Vérifier toutes les 5 secondes
    maxReloadAttempts: 3,       // Maximum 3 tentatives de rechargement
    reloadDelay: 2000,          // Délai avant rechargement
    whiteScreenTimeout: 10000,  // Timeout pour écran blanc
    debug: true
  };

  // Variables de suivi
  let reloadAttempts = 0;
  let lastCheck = Date.now();
  let isReloading = false;
  let checkInterval = null;

  /**
   * Vérifie si la page Odoo est correctement chargée
   */
  function checkOdooHealth() {
    const checks = {
      hasOdoo: typeof window.odoo !== 'undefined',
      hasJQuery: typeof window.$ !== 'undefined' || typeof window.jQuery !== 'undefined',
      hasWebClient: document.querySelector('.o_web_client') !== null,
      hasNavbar: document.querySelector('.o_main_navbar') !== null,
      hasContent: document.querySelector('.o_content') !== null,
      noWhiteScreen: document.body.children.length > 1,
      noErrorMessages: document.querySelectorAll('.o_error_dialog').length === 0
    };

    const passedChecks = Object.values(checks).filter(Boolean).length;
    const totalChecks = Object.keys(checks).length;
    const healthScore = (passedChecks / totalChecks) * 100;

    if (CONFIG.debug) {
      console.log(`[Edara ERP] Auto-Reload: Santé Odoo: ${healthScore.toFixed(1)}% (${passedChecks}/${totalChecks})`);
      console.log('[Edara ERP] Auto-Reload: Détails:', checks);
    }

    return {
      healthy: healthScore >= 60, // Au moins 60% des checks doivent passer
      score: healthScore,
      checks: checks,
      details: {
        criticalMissing: !checks.hasOdoo && !checks.hasWebClient,
        whiteScreen: !checks.noWhiteScreen,
        hasErrors: !checks.noErrorMessages
      }
    };
  }

  /**
   * Détecte les erreurs de console liées aux assets
   */
  function detectAssetErrors() {
    // Intercepter les erreurs de console
    const originalConsoleError = console.error;
    let assetErrors = [];

    console.error = function(...args) {
      const message = args.join(' ');
      
      if (message.includes('ERR_CONTENT_LENGTH_MISMATCH') ||
          message.includes('net::ERR_') ||
          message.includes('Failed to load resource') ||
          message.includes('web.assets_backend') ||
          message.includes('web.assets_common') ||
          message.includes('load_menus')) {
        
        assetErrors.push({
          message: message,
          timestamp: Date.now()
        });
        
        console.log(`[Edara ERP] Auto-Reload: Erreur d'asset détectée: ${message}`);
      }
      
      return originalConsoleError.apply(console, args);
    };

    return {
      getErrors: () => assetErrors,
      hasRecentErrors: () => {
        const recentErrors = assetErrors.filter(error => 
          Date.now() - error.timestamp < 30000 // Erreurs des 30 dernières secondes
        );
        return recentErrors.length > 0;
      }
    };
  }

  /**
   * Effectue un rechargement intelligent de la page
   */
  function performIntelligentReload() {
    if (isReloading || reloadAttempts >= CONFIG.maxReloadAttempts) {
      console.log(`[Edara ERP] Auto-Reload: Rechargement ignoré (isReloading: ${isReloading}, attempts: ${reloadAttempts})`);
      return;
    }

    isReloading = true;
    reloadAttempts++;

    console.log(`[Edara ERP] Auto-Reload: Tentative de rechargement ${reloadAttempts}/${CONFIG.maxReloadAttempts}`);

    // Notifier l'application Electron
    if (window.electronAPI && window.electronAPI.sendAuthData) {
      window.electronAPI.sendAuthData({
        action: 'auto-reload-triggered',
        attempt: reloadAttempts,
        timestamp: Date.now()
      });
    }

    // Ajouter un paramètre de cache-busting
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('_reload', Date.now());
    currentUrl.searchParams.set('_attempt', reloadAttempts);

    setTimeout(() => {
      console.log(`[Edara ERP] Auto-Reload: Rechargement vers: ${currentUrl.toString()}`);
      window.location.href = currentUrl.toString();
    }, CONFIG.reloadDelay);
  }

  /**
   * Surveille la santé de la page
   */
  function startHealthMonitoring() {
    console.log('[Edara ERP] Auto-Reload: Démarrage de la surveillance de santé');

    const assetErrorDetector = detectAssetErrors();

    checkInterval = setInterval(() => {
      if (isReloading) return;

      const health = checkOdooHealth();
      const hasAssetErrors = assetErrorDetector.hasRecentErrors();

      // Conditions de rechargement
      const shouldReload = (
        !health.healthy ||
        health.details.criticalMissing ||
        health.details.whiteScreen ||
        hasAssetErrors
      );

      if (shouldReload) {
        console.log('[Edara ERP] Auto-Reload: Problème détecté, rechargement nécessaire');
        console.log('[Edara ERP] Auto-Reload: Raisons:', {
          unhealthy: !health.healthy,
          criticalMissing: health.details.criticalMissing,
          whiteScreen: health.details.whiteScreen,
          assetErrors: hasAssetErrors
        });

        clearInterval(checkInterval);
        performIntelligentReload();
      } else if (CONFIG.debug) {
        console.log('[Edara ERP] Auto-Reload: Page en bonne santé, surveillance continue');
      }

      lastCheck = Date.now();
    }, CONFIG.checkInterval);
  }

  /**
   * Détecte l'écran blanc au démarrage
   */
  function detectWhiteScreenOnLoad() {
    setTimeout(() => {
      if (isReloading) return;

      const health = checkOdooHealth();
      
      if (health.details.whiteScreen || health.score < 30) {
        console.log('[Edara ERP] Auto-Reload: Écran blanc détecté au démarrage');
        performIntelligentReload();
      }
    }, CONFIG.whiteScreenTimeout);
  }

  /**
   * Initialisation
   */
  function init() {
    console.log('[Edara ERP] Auto-Reload: Initialisation du système de rechargement automatique');

    // Vérifier si on est dans une boucle de rechargement
    const urlParams = new URLSearchParams(window.location.search);
    const reloadAttempt = parseInt(urlParams.get('_attempt')) || 0;
    
    if (reloadAttempt > 0) {
      reloadAttempts = reloadAttempt;
      console.log(`[Edara ERP] Auto-Reload: Rechargement détecté, tentative ${reloadAttempts}`);
    }

    // Démarrer la surveillance
    startHealthMonitoring();
    
    // Détecter l'écran blanc au démarrage
    detectWhiteScreenOnLoad();

    // Exposer des fonctions pour le débogage
    window.EdaraAutoReload = {
      checkHealth: checkOdooHealth,
      forceReload: performIntelligentReload,
      getAttempts: () => reloadAttempts,
      isReloading: () => isReloading,
      stopMonitoring: () => {
        if (checkInterval) {
          clearInterval(checkInterval);
          checkInterval = null;
        }
      }
    };

    console.log('[Edara ERP] Auto-Reload: Système initialisé avec succès');
  }

  // Démarrer dès que possible
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

})();