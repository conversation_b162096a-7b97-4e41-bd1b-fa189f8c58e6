/**
 * Fix de rechargement immédiat pour les erreurs ERR_CONTENT_LENGTH_MISMATCH
 * Cette solution recharge immédiatement la page dès qu'une erreur est détectée
 */
(function() {
  'use strict';
  
  console.log('[Edara ERP] Immediate Reload Fix: Initialisation du rechargement immédiat');

  // Configuration
  const CONFIG = {
    maxReloads: 5,
    reloadDelay: 1000,
    errorThreshold: 2, // Nombre d'erreurs avant rechargement
    debug: true
  };

  // Compteurs
  let errorCount = 0;
  let reloadCount = 0;
  let isReloading = false;
  let errorTimer = null;

  /**
   * Effectue un rechargement immédiat avec cache-busting agressif
   */
  function performImmediateReload() {
    if (isReloading || reloadCount >= CONFIG.maxReloads) {
      console.log(`[Edara ERP] Immediate Reload: Rechargement ignoré (isReloading: ${isReloading}, count: ${reloadCount})`);
      return;
    }

    isReloading = true;
    reloadCount++;

    console.log(`[Edara ERP] Immediate Reload: 🔄 RECHARGEMENT IMMÉDIAT ${reloadCount}/${CONFIG.maxReloads}`);

    // Notifier l'application Electron
    if (window.electronAPI && window.electronAPI.sendAuthData) {
      window.electronAPI.sendAuthData({
        action: 'immediate-reload-triggered',
        errorCount: errorCount,
        reloadCount: reloadCount,
        timestamp: Date.now()
      });
    }

    // Créer une URL avec cache-busting ultra-agressif
    const currentUrl = new URL(window.location.href);
    
    // Supprimer tous les paramètres existants
    currentUrl.search = '';
    
    // Ajouter de nouveaux paramètres
    currentUrl.searchParams.set('_immediate_reload', reloadCount);
    currentUrl.searchParams.set('_timestamp', Date.now());
    currentUrl.searchParams.set('_random', Math.random().toString(36).substr(2, 9));
    currentUrl.searchParams.set('_error_count', errorCount);
    currentUrl.searchParams.set('_force_refresh', '1');

    setTimeout(() => {
      console.log(`[Edara ERP] Immediate Reload: Rechargement vers: ${currentUrl.toString()}`);
      window.location.replace(currentUrl.toString());
    }, CONFIG.reloadDelay);
  }

  /**
   * Détecte les erreurs Content-Length et déclenche le rechargement
   */
  function setupErrorDetection() {
    console.log('[Edara ERP] Immediate Reload: Configuration de la détection d\'erreurs');

    // Intercepter les erreurs d'éléments
    window.addEventListener('error', (event) => {
      if (event.target && (event.target.src || event.target.href)) {
        const url = event.target.src || event.target.href;
        
        if (url.includes('web.assets_backend') || url.includes('web.assets_common') || url.includes('load_menus')) {
          errorCount++;
          console.log(`[Edara ERP] Immediate Reload: 🚨 Erreur ${errorCount} détectée sur: ${url}`);
          
          // Empêcher la propagation
          event.preventDefault();
          event.stopPropagation();
          
          // Déclencher le rechargement si seuil atteint
          if (errorCount >= CONFIG.errorThreshold) {
            console.log(`[Edara ERP] Immediate Reload: Seuil d'erreurs atteint (${errorCount}), rechargement immédiat`);
            performImmediateReload();
          }
        }
      }
    }, true);

    // Intercepter les erreurs de console
    const originalConsoleError = console.error;
    console.error = function(...args) {
      const message = args.join(' ');
      
      if (message.includes('ERR_CONTENT_LENGTH_MISMATCH') || 
          message.includes('Failed to load resource') ||
          message.includes('net::ERR_')) {
        
        errorCount++;
        console.log(`[Edara ERP] Immediate Reload: 🚨 Erreur console ${errorCount}: ${message}`);
        
        // Déclencher le rechargement si seuil atteint
        if (errorCount >= CONFIG.errorThreshold) {
          console.log(`[Edara ERP] Immediate Reload: Seuil d'erreurs console atteint (${errorCount}), rechargement immédiat`);
          performImmediateReload();
        }
      }
      
      return originalConsoleError.apply(console, args);
    };

    // Surveiller les requêtes fetch échouées
    const originalFetch = window.fetch;
    window.fetch = async function(...args) {
      try {
        const response = await originalFetch.apply(this, args);
        
        // Vérifier si c'est un asset critique qui a échoué
        const url = args[0];
        if (typeof url === 'string' && 
            (url.includes('web.assets_backend') || url.includes('web.assets_common') || url.includes('load_menus')) &&
            !response.ok) {
          
          errorCount++;
          console.log(`[Edara ERP] Immediate Reload: 🚨 Fetch échoué ${errorCount} pour: ${url} (Status: ${response.status})`);
          
          if (errorCount >= CONFIG.errorThreshold) {
            console.log(`[Edara ERP] Immediate Reload: Seuil d'erreurs fetch atteint (${errorCount}), rechargement immédiat`);
            performImmediateReload();
          }
        }
        
        return response;
      } catch (error) {
        const url = args[0];
        if (typeof url === 'string' && 
            (url.includes('web.assets_backend') || url.includes('web.assets_common') || url.includes('load_menus'))) {
          
          errorCount++;
          console.log(`[Edara ERP] Immediate Reload: 🚨 Exception fetch ${errorCount} pour: ${url}`, error);
          
          if (errorCount >= CONFIG.errorThreshold) {
            console.log(`[Edara ERP] Immediate Reload: Seuil d'exceptions fetch atteint (${errorCount}), rechargement immédiat`);
            performImmediateReload();
          }
        }
        throw error;
      }
    };
  }

  /**
   * Vérifie si la page est dans un état de rechargement
   */
  function checkReloadState() {
    const urlParams = new URLSearchParams(window.location.search);
    const immediateReload = parseInt(urlParams.get('_immediate_reload')) || 0;
    const previousErrorCount = parseInt(urlParams.get('_error_count')) || 0;
    
    if (immediateReload > 0) {
      reloadCount = immediateReload;
      errorCount = 0; // Reset le compteur d'erreurs après rechargement
      console.log(`[Edara ERP] Immediate Reload: Page rechargée ${reloadCount} fois, erreurs précédentes: ${previousErrorCount}`);
    }
  }

  /**
   * Surveille l'état de la page pour détecter les problèmes
   */
  function monitorPageHealth() {
    // Vérifier après 5 secondes si la page est correctement chargée
    setTimeout(() => {
      const hasOdoo = typeof window.odoo !== 'undefined';
      const hasWebClient = document.querySelector('.o_web_client') !== null;
      const hasContent = document.body.children.length > 1;
      
      if (!hasOdoo && !hasWebClient && !hasContent) {
        console.log('[Edara ERP] Immediate Reload: Page non chargée correctement, rechargement immédiat');
        performImmediateReload();
      }
    }, 5000);

    // Vérifier périodiquement l'état de la page
    setInterval(() => {
      if (isReloading) return;
      
      const hasErrors = document.querySelectorAll('.o_error_dialog').length > 0;
      const isBlank = document.body.children.length <= 1;
      
      if (hasErrors || isBlank) {
        console.log('[Edara ERP] Immediate Reload: Problème détecté lors de la surveillance, rechargement immédiat');
        performImmediateReload();
      }
    }, 10000); // Vérifier toutes les 10 secondes
  }

  /**
   * Initialisation
   */
  function init() {
    console.log('[Edara ERP] Immediate Reload: 🚀 Initialisation du rechargement immédiat');

    // Vérifier l'état de rechargement
    checkReloadState();

    // Configurer la détection d'erreurs
    setupErrorDetection();

    // Surveiller la santé de la page
    monitorPageHealth();

    // Exposer l'interface de débogage
    window.EdaraImmediateReload = {
      getErrorCount: () => errorCount,
      getReloadCount: () => reloadCount,
      forceReload: performImmediateReload,
      isReloading: () => isReloading,
      resetCounters: () => {
        errorCount = 0;
        reloadCount = 0;
        isReloading = false;
      }
    };

    console.log('[Edara ERP] Immediate Reload: ✅ Rechargement immédiat initialisé');
  }

  // Démarrer immédiatement
  init();

})();