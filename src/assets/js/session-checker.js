// Script de vérification de session Odoo
(function() {
  console.log('🔍 Vérification de la session Odoo...');

  // Vérifier si le cookie session_id est présent
  const cookies = document.cookie.split(';').map(c => c.trim());
  const sessionCookie = cookies.find(c => c.startsWith('session_id='));
  const hasSessionCookie = !!sessionCookie;

  // Afficher le résultat dans la console
  if (hasSessionCookie) {
    const sessionId = sessionCookie.split('=')[1];
    console.log(`✅ Cookie session_id trouvé: ${sessionId.substring(0, 8)}...`);
  } else {
    console.log('❌ Cookie session_id absent');
    console.log('📋 Cookies disponibles:', cookies);
  }

  // Vérifier si l'utilisateur est authentifié en vérifiant l'objet odoo.session_info
  let sessionInfo = null;

  if (window.odoo && window.odoo.session_info) {
    sessionInfo = window.odoo.session_info;
    console.log('✅ Session Odoo active:');
    console.log(`   - Utilisateur: ${sessionInfo.username}`);
    console.log(`   - ID Utilisateur: ${sessionInfo.uid}`);
    console.log(`   - Base de données: ${sessionInfo.db}`);
    console.log(`   - Entreprise: ${sessionInfo.company_id}`);
  } else {
    console.log('❌ Session Odoo non détectée ou utilisateur non connecté');

    // Vérifier si l'objet odoo existe
    if (window.odoo) {
      console.log('ℹ️ Objet odoo trouvé mais session_info manquante');
    } else {
      console.log('❌ Objet odoo non trouvé');
    }
  }

  // Vérifier si nous sommes sur la page de connexion
  const isLoginPage = document.querySelector('.oe_login_form') !== null;
  if (isLoginPage) {
    console.log('⚠️ Page de connexion détectée - Session probablement expirée ou invalide');
  }

  // Envoyer le résultat au processus principal
  if (window.electron && window.electron.ipcRenderer) {
    window.electron.ipcRenderer.send('session-check-result', {
      hasSessionCookie: hasSessionCookie,
      sessionCookieValue: hasSessionCookie ? sessionCookie.split('=')[1] : null,
      sessionInfo: sessionInfo,
      isLoginPage: isLoginPage,
      allCookies: cookies
    });
  } else {
    console.log('⚠️ API Electron non disponible - Impossible d\'envoyer les résultats au processus principal');
  }

  return {
    hasSessionCookie,
    sessionInfo,
    isLoginPage
  };
})();
