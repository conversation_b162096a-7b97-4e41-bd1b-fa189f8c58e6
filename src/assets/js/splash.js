/**
 * Script pour l'écran de démarrage (splash screen) de l'application Edara ERP
 * Ce script gère l'animation de la barre de chargement et envoie un message
 * au processus principal lorsque l'écran de démarrage est terminé
 */

document.addEventListener('DOMContentLoaded', () => {
  // La barre de chargement est animée via CSS
  console.log('Écran de démarrage initialisé');

  // Après 5 secondes, envoyer un message au processus principal pour indiquer
  // que l'écran de démarrage est terminé
  setTimeout(() => {
    console.log('Écran de démarrage terminé');
    // Envoyer un message au processus principal via l'API Electron
    if (window.electronAPI) {
      window.electronAPI.sendAuthData({ action: 'splash-finished' });
    }
  }, 5000);
});
