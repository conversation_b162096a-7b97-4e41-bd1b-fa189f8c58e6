/**
 * Script pour gérer l'animation de chargement post-authentification
 * Ce script est chargé dans custom-login.html et gère la transition
 * entre le formulaire de connexion et l'interface Odoo
 */

// Fonction pour démarrer l'animation de chargement
function startLoadingAnimation() {
  console.log('Démarrage de l\'animation de chargement');
  
  // Récupérer les éléments du DOM
  const loadingAnimation = document.getElementById('loading-animation');
  const loadingLogo = document.getElementById('loading-logo');
  const loadingText = document.getElementById('loading-text');
  const progressBar = document.getElementById('progress-bar');
  const progressFill = document.getElementById('progress-fill');
  
  // Afficher l'animation de chargement
  if (loadingAnimation) {
    loadingAnimation.classList.add('visible');
  }
  
  // Séquence d'animation
  setTimeout(() => {
    // Faire grandir le logo
    if (loadingLogo) {
      loadingLogo.classList.add('grow');
    }
    
    // Afficher le texte avec un délai
    setTimeout(() => {
      if (loadingText) {
        loadingText.classList.add('visible');
      }
      
      // Afficher la barre de progression avec un délai
      setTimeout(() => {
        if (progressBar) {
          progressBar.classList.add('visible');
        }
        
        // Initialiser la progression à 10%
        updateProgressBar(10);
        
        // Simuler une progression
        simulateProgress();
      }, 200);
    }, 300);
  }, 300);
  
  // Fonction pour mettre à jour la barre de progression
  function updateProgressBar(percent) {
    if (progressFill) {
      progressFill.style.width = `${percent}%`;
    }
  }
  
  // Fonction pour simuler une progression
  function simulateProgress() {
    let progress = 10;
    const totalDuration = 5000; // 5 secondes au total
    const steps = 18; // Nombre d'étapes
    const interval = totalDuration / steps;
    
    const progressInterval = setInterval(() => {
      progress += 5;
      updateProgressBar(progress);
      
      // Mettre à jour le texte en fonction de la progression
      if (loadingText) {
        if (progress < 30) {
          loadingText.textContent = 'Préparation de l\'interface...';
        } else if (progress < 60) {
          loadingText.textContent = 'Chargement des données...';
        } else if (progress < 90) {
          loadingText.textContent = 'Finalisation...';
        }
      }
      
      // Quand on atteint 95%, ajouter l'animation de pulsation au logo
      if (progress >= 95) {
        clearInterval(progressInterval);
        
        // Mettre à jour le texte
        if (loadingText) {
          loadingText.textContent = 'Prêt !';
        }
        
        // Ajouter l'animation de pulsation au logo
        if (loadingLogo) {
          loadingLogo.classList.add('pulse');
        }
        
        // Remplir complètement la barre de progression
        updateProgressBar(100);
        
        // Attendre un court instant avant de charger l'interface Odoo
        setTimeout(() => {
          // Notifier le processus principal que l'animation est terminée
          if (window.electronAPI) {
            window.electronAPI.sendAuthData({
              action: 'loading-animation-complete'
            });
          }
        }, 500);
      }
    }, interval);
  }
}

// Exposer la fonction pour qu'elle puisse être appelée depuis custom-login.js
window.startLoadingAnimation = startLoadingAnimation;
