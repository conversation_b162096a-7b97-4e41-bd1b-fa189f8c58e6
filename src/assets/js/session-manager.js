/**
 * Script d'injection pour gérer la session Odoo côté client
 * Ce script est injecté dans la page web Odoo pour gérer la session
 */

(function() {
    console.log('[Edara ERP] Script de gestion de session injecté');

    // Fonction pour vérifier si la session est valide
    function checkSession() {
        console.log('[Edara ERP] Vérification de la session Odoo...');
        
        // Vérifier si nous sommes sur la page de connexion
        if (window.location.href.includes('/web/login')) {
            console.log('[Edara ERP] Détection de la page de connexion, session probablement expirée');
            
            // Notifier l'application Electron que la session a expiré
            if (window.electron && window.electron.ipcRenderer) {
                window.electron.ipcRenderer.send('session-expired');
            }
            
            return false;
        }
        
        // Vérifier si l'objet odoo est disponible (signe que la session est valide)
        if (typeof odoo !== 'undefined') {
            console.log('[Edara ERP] Session Odoo valide, objet odoo disponible');
            return true;
        }
        
        console.log('[Edara ERP] État de session indéterminé');
        return false;
    }

    // Fonction pour extraire le cookie de session
    function getSessionCookie() {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.startsWith('session_id=')) {
                const sessionId = cookie.substring('session_id='.length);
                console.log('[Edara ERP] Cookie session_id trouvé:', sessionId.substring(0, 8) + '...');
                
                // Notifier l'application Electron du cookie de session
                if (window.electron && window.electron.ipcRenderer) {
                    window.electron.ipcRenderer.send('store-session-cookie', sessionId);
                }
                
                return sessionId;
            }
        }
        
        console.log('[Edara ERP] Cookie session_id non trouvé');
        return null;
    }

    // Fonction pour intercepter les requêtes de déconnexion
    function interceptLogout() {
        console.log('[Edara ERP] Configuration de l\'interception de déconnexion');
        
        // Intercepter les clics sur les boutons de déconnexion
        document.addEventListener('click', function(event) {
            // Vérifier si l'élément cliqué ou un de ses parents est un lien de déconnexion
            let target = event.target;
            while (target && target !== document) {
                // Vérifier si c'est un lien de déconnexion
                if (target.tagName === 'A' && 
                    (target.href.includes('/web/session/logout') || 
                     target.href.includes('logout=1') || 
                     target.href.includes('action=logout'))) {
                    
                    console.log('[Edara ERP] Clic sur un lien de déconnexion intercepté:', target.href);
                    event.preventDefault();
                    event.stopPropagation();
                    
                    // Notifier l'application Electron de la déconnexion
                    if (window.electron && window.electron.ipcRenderer) {
                        window.electron.ipcRenderer.send('logout-requested', target.href);
                    }
                    
                    return false;
                }
                
                target = target.parentNode;
            }
        }, true);
        
        // Intercepter les requêtes fetch pour détecter les déconnexions
        const originalFetch = window.fetch;
        window.fetch = function(url, options) {
            if (typeof url === 'string' && 
                (url.includes('/web/session/logout') || 
                 url.includes('logout=1') || 
                 url.includes('action=logout'))) {
                
                console.log('[Edara ERP] Requête fetch de déconnexion interceptée:', url);
                
                // Notifier l'application Electron de la déconnexion
                if (window.electron && window.electron.ipcRenderer) {
                    window.electron.ipcRenderer.send('logout-requested', url);
                }
                
                // Retourner une promesse résolue pour éviter l'erreur
                return Promise.resolve(new Response(JSON.stringify({ result: true }), {
                    status: 200,
                    headers: { 'Content-Type': 'application/json' }
                }));
            }
            
            return originalFetch.apply(this, arguments);
        };
        
        // Intercepter les requêtes XMLHttpRequest pour détecter les déconnexions
        const originalOpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
            if (typeof url === 'string' && 
                (url.includes('/web/session/logout') || 
                 url.includes('logout=1') || 
                 url.includes('action=logout'))) {
                
                console.log('[Edara ERP] Requête XMLHttpRequest de déconnexion interceptée:', url);
                
                // Notifier l'application Electron de la déconnexion
                if (window.electron && window.electron.ipcRenderer) {
                    window.electron.ipcRenderer.send('logout-requested', url);
                }
                
                // Modifier l'URL pour éviter la déconnexion
                url = '/web';
            }
            
            return originalOpen.call(this, method, url, async, user, password);
        };
    }

    // Fonction pour injecter le cookie de session si nécessaire
    function injectSessionIfNeeded() {
        // Vérifier si le cookie de session est présent
        const sessionId = getSessionCookie();
        
        if (!sessionId) {
            console.log('[Edara ERP] Cookie de session non trouvé, tentative de récupération depuis l\'URL');
            
            // Essayer de récupérer le session_id depuis l'URL
            const urlParams = new URLSearchParams(window.location.search);
            const urlSessionId = urlParams.get('session_id');
            
            if (urlSessionId) {
                console.log('[Edara ERP] Session ID trouvé dans l\'URL:', urlSessionId.substring(0, 8) + '...');
                
                // Définir le cookie de session manuellement
                document.cookie = `session_id=${urlSessionId}; path=/; domain=${window.location.hostname}`;
                console.log('[Edara ERP] Cookie de session défini manuellement');
                
                // Notifier l'application Electron du cookie de session
                if (window.electron && window.electron.ipcRenderer) {
                    window.electron.ipcRenderer.send('store-session-cookie', urlSessionId);
                }
            }
        }
    }

    // Fonction principale d'initialisation
    function init() {
        console.log('[Edara ERP] Initialisation du gestionnaire de session');
        
        // Injecter le cookie de session si nécessaire
        injectSessionIfNeeded();
        
        // Vérifier la session
        const isSessionValid = checkSession();
        
        // Intercepter les déconnexions
        interceptLogout();
        
        // Si la session n'est pas valide, notifier l'application Electron
        if (!isSessionValid && window.electron && window.electron.ipcRenderer) {
            console.log('[Edara ERP] Notification de session invalide à l\'application Electron');
            window.electron.ipcRenderer.send('session-invalid');
        }
        
        // Vérifier périodiquement la session
        setInterval(function() {
            const isStillValid = checkSession();
            
            // Si la session n'est plus valide, notifier l'application Electron
            if (!isStillValid && window.electron && window.electron.ipcRenderer) {
                console.log('[Edara ERP] Notification de session expirée à l\'application Electron');
                window.electron.ipcRenderer.send('session-expired');
            }
        }, 30000); // Vérifier toutes les 30 secondes
    }

    // Attendre que le DOM soit chargé
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();
