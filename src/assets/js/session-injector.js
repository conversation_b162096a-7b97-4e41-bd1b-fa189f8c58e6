/**
 * <PERSON>ript d'injection de session pour Odoo
 * Ce script est injecté dans la page web pour vérifier et manipuler les cookies de session
 */

(function() {
  console.log('[Edara ERP] Session Injector: Démarrage de la vérification des cookies');
  
  // Fonction pour vérifier les cookies
  function checkCookies() {
    const cookies = document.cookie.split(';').map(cookie => cookie.trim());
    console.log('[Edara ERP] Session Injector: Cookies disponibles dans document.cookie:', cookies);
    
    // Vérifier si le cookie session_id est présent
    const sessionCookie = cookies.find(cookie => cookie.startsWith('session_id='));
    
    if (sessionCookie) {
      const sessionId = sessionCookie.split('=')[1];
      console.log(`[Edara ERP] Session Injector: Cookie session_id trouvé: ${sessionId.substring(0, 8)}...`);
      
      // Signaler au processus principal que le cookie est présent
      if (window.electron && window.electron.ipcRenderer) {
        window.electron.ipcRenderer.send('session-cookie-found', sessionId);
      }
      
      return true;
    } else {
      console.warn('[Edara ERP] Session Injector: Cookie session_id non trouvé dans document.cookie');
      
      // Signaler au processus principal que le cookie est absent
      if (window.electron && window.electron.ipcRenderer) {
        window.electron.ipcRenderer.send('session-cookie-missing');
      }
      
      return false;
    }
  }
  
  // Fonction pour vérifier l'état de la session Odoo
  function checkOdooSession() {
    console.log('[Edara ERP] Session Injector: Vérification de l\'état de la session Odoo');
    
    // Vérifier si nous sommes sur la page de connexion
    const loginForm = document.querySelector('form.oe_login_form');
    if (loginForm) {
      console.warn('[Edara ERP] Session Injector: Page de connexion détectée, la session n\'est pas valide');
      
      // Signaler au processus principal que nous sommes sur la page de connexion
      if (window.electron && window.electron.ipcRenderer) {
        window.electron.ipcRenderer.send('odoo-login-page-detected');
      }
      
      return false;
    }
    
    // Vérifier si nous sommes sur la page principale d'Odoo
    const odooApp = document.querySelector('.o_web_client');
    if (odooApp) {
      console.log('[Edara ERP] Session Injector: Interface Odoo détectée, la session est valide');
      
      // Signaler au processus principal que nous sommes sur l'interface Odoo
      if (window.electron && window.electron.ipcRenderer) {
        window.electron.ipcRenderer.send('odoo-interface-detected');
      }
      
      return true;
    }
    
    console.warn('[Edara ERP] Session Injector: État de session indéterminé');
    return null;
  }
  
  // Fonction pour injecter un cookie de session manuellement
  function injectSessionCookie(sessionId) {
    if (!sessionId) {
      console.error('[Edara ERP] Session Injector: Impossible d\'injecter le cookie, sessionId non fourni');
      return false;
    }
    
    try {
      console.log(`[Edara ERP] Session Injector: Tentative d'injection du cookie session_id: ${sessionId.substring(0, 8)}...`);
      
      // Définir le cookie avec path=/ et une date d'expiration de 24 heures
      const expirationDate = new Date();
      expirationDate.setTime(expirationDate.getTime() + (24 * 60 * 60 * 1000));
      
      document.cookie = `session_id=${sessionId}; path=/; expires=${expirationDate.toUTCString()}`;
      
      // Vérifier si le cookie a été défini
      const cookies = document.cookie.split(';').map(cookie => cookie.trim());
      const sessionCookie = cookies.find(cookie => cookie.startsWith('session_id='));
      
      if (sessionCookie) {
        console.log('[Edara ERP] Session Injector: Cookie injecté avec succès');
        return true;
      } else {
        console.error('[Edara ERP] Session Injector: Échec de l\'injection du cookie');
        return false;
      }
    } catch (error) {
      console.error('[Edara ERP] Session Injector: Erreur lors de l\'injection du cookie:', error);
      return false;
    }
  }
  
  // Fonction pour récupérer le sessionId depuis l'URL si présent
  function getSessionIdFromUrl() {
    try {
      const urlParams = new URLSearchParams(window.location.search);
      const sessionId = urlParams.get('session_id');
      
      if (sessionId) {
        console.log(`[Edara ERP] Session Injector: session_id trouvé dans l'URL: ${sessionId.substring(0, 8)}...`);
        return sessionId;
      }
    } catch (error) {
      console.error('[Edara ERP] Session Injector: Erreur lors de la récupération du sessionId depuis l\'URL:', error);
    }
    
    return null;
  }
  
  // Fonction principale
  function main() {
    console.log('[Edara ERP] Session Injector: Démarrage du script d\'injection de session');
    
    // Vérifier les cookies actuels
    const hasCookie = checkCookies();
    
    // Vérifier l'état de la session Odoo
    const sessionValid = checkOdooSession();
    
    // Si nous sommes sur la page de connexion et qu'un sessionId est disponible dans l'URL
    const sessionIdFromUrl = getSessionIdFromUrl();
    if (!hasCookie && sessionIdFromUrl) {
      console.log('[Edara ERP] Session Injector: Tentative d\'injection du cookie depuis l\'URL');
      injectSessionCookie(sessionIdFromUrl);
      
      // Recharger la page après l'injection du cookie
      setTimeout(() => {
        console.log('[Edara ERP] Session Injector: Rechargement de la page après injection du cookie');
        window.location.href = window.location.origin + window.location.pathname;
      }, 500);
    }
    
    // Écouter les messages du processus principal
    if (window.electron && window.electron.ipcRenderer) {
      window.electron.ipcRenderer.on('inject-session-cookie', (sessionId) => {
        console.log('[Edara ERP] Session Injector: Demande d\'injection de cookie reçue du processus principal');
        injectSessionCookie(sessionId);
      });
    }
    
    // Envoyer les résultats au processus principal
    if (window.electron && window.electron.ipcRenderer) {
      window.electron.ipcRenderer.send('session-injector-result', {
        hasCookie,
        sessionValid,
        url: window.location.href
      });
    }
  }
  
  // Exécuter le script après le chargement complet de la page
  if (document.readyState === 'complete') {
    main();
  } else {
    window.addEventListener('load', main);
  }
})();
