/**
 * Script pour remplacer le comportement du bouton de déconnexion d'Odoo
 * Ce script est injecté dans la page Odoo pour intercepter et remplacer le lien de déconnexion
 */

(function() {
  console.log('[Edara ERP] Script de remplacement du bouton de déconnexion chargé');
  
  // Fonction pour envoyer un message à l'application Electron
  function notifyElectron(action, data) {
    console.log('[Edara ERP] Envoi d\'un message à l\'application Electron:', action);
    
    // Essayer d'utiliser l'API Electron si disponible
    if (window.electronAPI && typeof window.electronAPI.logout === 'function') {
      window.electronAPI.logout();
      return true;
    }
    
    // Sinon, utiliser postMessage
    window.postMessage({
      type: 'edara-action',
      action: action,
      data: data || {}
    }, '*');
    
    return true;
  }
  
  // Fonction pour remplacer le lien de déconnexion
  function replaceLogoutLink() {
    console.log('[Edara ERP] Recherche des liens de déconnexion...');
    
    // Sélectionner tous les liens de déconnexion possibles
    const logoutSelectors = [
      'a[data-menu="logout"]',
      'a.o_logout',
      'a[href*="/web/session/logout"]',
      'li.o_user_menu_item a[href="#"]',
      '.dropdown-item:contains("Déconnexion")',
      '.dropdown-item:contains("Log out")',
      '.dropdown-item:contains("Sign out")'
    ];
    
    // Fonction pour vérifier si un élément est un lien de déconnexion
    function isLogoutLink(element) {
      if (!element) return false;
      
      // Vérifier le texte
      const text = element.textContent.toLowerCase();
      if (text.includes('déconnexion') || text.includes('logout') || text.includes('sign out')) {
        return true;
      }
      
      // Vérifier les attributs
      if (element.getAttribute('data-menu') === 'logout') return true;
      if (element.classList.contains('o_logout')) return true;
      
      // Vérifier le href
      const href = element.getAttribute('href');
      if (href && (href.includes('/web/session/logout') || href === '#' || href === '')) {
        return true;
      }
      
      return false;
    }
    
    // Fonction pour remplacer un lien
    function overrideLink(link) {
      if (!link || link.dataset.edaraOverridden) return;
      
      console.log('[Edara ERP] Remplacement du lien de déconnexion:', link);
      
      // Marquer le lien comme remplacé
      link.dataset.edaraOverridden = 'true';
      
      // Sauvegarder l'ancien onclick
      const oldOnClick = link.onclick;
      
      // Remplacer le onclick
      link.onclick = function(event) {
        console.log('[Edara ERP] Clic sur le bouton de déconnexion intercepté');
        event.preventDefault();
        event.stopPropagation();
        
        // Notifier l'application Electron
        notifyElectron('logout');
        
        return false;
      };
      
      // Remplacer le href
      if (link.getAttribute('href')) {
        link.setAttribute('data-original-href', link.getAttribute('href'));
        link.setAttribute('href', 'javascript:void(0)');
      }
      
      console.log('[Edara ERP] Lien de déconnexion remplacé avec succès');
    }
    
    // Rechercher et remplacer tous les liens de déconnexion
    for (const selector of logoutSelectors) {
      try {
        const links = document.querySelectorAll(selector);
        console.log(`[Edara ERP] Sélecteur "${selector}" a trouvé ${links.length} éléments`);
        
        links.forEach(link => {
          if (isLogoutLink(link)) {
            overrideLink(link);
          }
        });
      } catch (error) {
        console.error(`[Edara ERP] Erreur lors de la recherche avec le sélecteur "${selector}":`, error);
      }
    }
    
    // Rechercher tous les liens et vérifier s'ils sont des liens de déconnexion
    try {
      const allLinks = document.querySelectorAll('a');
      console.log(`[Edara ERP] Vérification de tous les liens (${allLinks.length} trouvés)...`);
      
      allLinks.forEach(link => {
        if (isLogoutLink(link)) {
          overrideLink(link);
        }
      });
    } catch (error) {
      console.error('[Edara ERP] Erreur lors de la vérification de tous les liens:', error);
    }
  }
  
  // Fonction pour observer les changements dans le DOM
  function setupMutationObserver() {
    console.log('[Edara ERP] Configuration de l\'observateur de mutations');
    
    // Créer un observateur de mutations
    const observer = new MutationObserver(function(mutations) {
      let shouldCheck = false;
      
      // Vérifier si les mutations contiennent des éléments intéressants
      for (const mutation of mutations) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          for (const node of mutation.addedNodes) {
            if (node.nodeType === Node.ELEMENT_NODE) {
              // Si un élément a été ajouté, vérifier s'il contient des liens
              if (node.tagName === 'A' || node.querySelector('a')) {
                shouldCheck = true;
                break;
              }
              
              // Vérifier si c'est un menu déroulant
              if (node.classList && (
                  node.classList.contains('dropdown-menu') || 
                  node.classList.contains('o_user_menu') ||
                  node.querySelector('.dropdown-menu, .o_user_menu')
              )) {
                shouldCheck = true;
                break;
              }
            }
          }
        }
        
        if (shouldCheck) break;
      }
      
      // Si des éléments intéressants ont été ajoutés, rechercher les liens de déconnexion
      if (shouldCheck) {
        console.log('[Edara ERP] Changements détectés dans le DOM, recherche de nouveaux liens de déconnexion');
        setTimeout(replaceLogoutLink, 100); // Petit délai pour s'assurer que le DOM est stable
      }
    });
    
    // Observer tout le document
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
    
    console.log('[Edara ERP] Observateur de mutations configuré');
    
    // Rechercher les liens de déconnexion immédiatement
    replaceLogoutLink();
    
    // Et aussi après un court délai pour s'assurer que le DOM est complètement chargé
    setTimeout(replaceLogoutLink, 1000);
    setTimeout(replaceLogoutLink, 3000);
  }
  
  // Fonction pour remplacer la fonction de déconnexion native d'Odoo
  function overrideOdooLogout() {
    console.log('[Edara ERP] Tentative de remplacement de la fonction de déconnexion native d\'Odoo');
    
    try {
      // Essayer de trouver l'objet session d'Odoo
      if (window.odoo && window.odoo.session) {
        console.log('[Edara ERP] Objet session d\'Odoo trouvé, remplacement de la fonction logout');
        
        // Sauvegarder l'ancienne fonction
        const originalLogout = window.odoo.session.logout;
        
        // Remplacer la fonction
        window.odoo.session.logout = function() {
          console.log('[Edara ERP] Fonction de déconnexion native d\'Odoo interceptée');
          
          // Notifier l'application Electron
          return notifyElectron('logout');
        };
        
        console.log('[Edara ERP] Fonction de déconnexion native d\'Odoo remplacée avec succès');
      } else {
        console.log('[Edara ERP] Objet session d\'Odoo non trouvé');
      }
    } catch (error) {
      console.error('[Edara ERP] Erreur lors du remplacement de la fonction de déconnexion native d\'Odoo:', error);
    }
  }
  
  // Exécuter les fonctions lorsque le DOM est chargé
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      setupMutationObserver();
      overrideOdooLogout();
    });
  } else {
    setupMutationObserver();
    overrideOdooLogout();
  }
  
  // Écouter les messages de l'application Electron
  window.addEventListener('message', function(event) {
    if (event.data && event.data.type === 'edara-command') {
      console.log('[Edara ERP] Message reçu de l\'application Electron:', event.data);
      
      if (event.data.action === 'check-logout-links') {
        replaceLogoutLink();
      }
    }
  });
  
  // Exposer une fonction pour la déconnexion manuelle
  window.edaraLogout = function() {
    console.log('[Edara ERP] Déconnexion manuelle déclenchée');
    notifyElectron('logout');
  };
  
  console.log('[Edara ERP] Script de remplacement du bouton de déconnexion initialisé');
})();
