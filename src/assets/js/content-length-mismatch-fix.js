/**
 * Fix avancé pour les erreurs ERR_CONTENT_LENGTH_MISMATCH dans Odoo
 * Ce script résout les problèmes de chargement des assets qui causent l'écran blanc
 */
(function() {
  'use strict';
  
  console.log('[Edara ERP] Content-Length Mismatch Fix: Initialisation avancée');

  // Configuration
  const CONFIG = {
    retryAttempts: 3,
    retryDelay: 1000,
    reloadDelay: 2000,
    forceReloadDelay: 5000,
    debug: true
  };

  // Assets critiques à surveiller
  const CRITICAL_ASSETS = [
    'web.assets_backend.js',
    'web.assets_common.js',
    'web.assets_backend.css',
    'web.assets_common.css',
    'load_menus',
    'web/webclient'
  ];

  // Compteurs pour le suivi
  let failedAssets = new Set();
  let retryCount = 0;
  let isFixing = false;

  /**
   * Ajoute un timestamp unique pour éviter le cache
   */
  function addCacheBuster(url) {
    try {
      const urlObj = new URL(url, window.location.origin);
      urlObj.searchParams.set('_cb', Date.now() + Math.random().toString(36).substr(2, 9));
      return urlObj.toString();
    } catch (error) {
      console.error('[Edara ERP] Erreur lors de l\'ajout du cache-buster:', error);
      return url + (url.includes('?') ? '&' : '?') + '_cb=' + Date.now();
    }
  }

  /**
   * Recharge un asset JavaScript avec gestion d'erreur robuste
   */
  async function reloadJavaScriptAsset(originalUrl) {
    const url = addCacheBuster(originalUrl);
    console.log(`[Edara ERP] Rechargement JS: ${url}`);

    try {
      // Méthode 1: Fetch avec blob
      const response = await fetch(url, {
        method: 'GET',
        cache: 'no-cache',
        credentials: 'same-origin',
        headers: {
          'Accept': 'application/javascript, text/javascript, */*',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const jsContent = await response.text();
      
      // Créer et injecter le script
      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.textContent = jsContent;
      script.setAttribute('data-reloaded', 'true');
      script.setAttribute('data-original-src', originalUrl);
      
      // Ajouter au head
      document.head.appendChild(script);
      
      console.log(`[Edara ERP] ✅ JS rechargé avec succès: ${originalUrl}`);
      return true;

    } catch (error) {
      console.error(`[Edara ERP] ❌ Échec rechargement JS: ${originalUrl}`, error);
      
      // Méthode 2: XMLHttpRequest en fallback
      return reloadWithXHR(originalUrl);
    }
  }

  /**
   * Recharge un asset CSS avec gestion d'erreur robuste
   */
  async function reloadCSSAsset(originalUrl) {
    const url = addCacheBuster(originalUrl);
    console.log(`[Edara ERP] Rechargement CSS: ${url}`);

    try {
      const response = await fetch(url, {
        method: 'GET',
        cache: 'no-cache',
        credentials: 'same-origin',
        headers: {
          'Accept': 'text/css, */*',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const cssContent = await response.text();
      
      // Créer et injecter le style
      const style = document.createElement('style');
      style.type = 'text/css';
      style.textContent = cssContent;
      style.setAttribute('data-reloaded', 'true');
      style.setAttribute('data-original-href', originalUrl);
      
      // Ajouter au head
      document.head.appendChild(style);
      
      console.log(`[Edara ERP] ✅ CSS rechargé avec succès: ${originalUrl}`);
      return true;

    } catch (error) {
      console.error(`[Edara ERP] ❌ Échec rechargement CSS: ${originalUrl}`, error);
      return false;
    }
  }

  /**
   * Fallback avec XMLHttpRequest
   */
  function reloadWithXHR(url) {
    return new Promise((resolve) => {
      const xhr = new XMLHttpRequest();
      const cacheBustedUrl = addCacheBuster(url);
      
      xhr.open('GET', cacheBustedUrl, true);
      xhr.setRequestHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      xhr.setRequestHeader('Pragma', 'no-cache');
      
      xhr.onload = function() {
        if (xhr.status >= 200 && xhr.status < 300) {
          const script = document.createElement('script');
          script.type = 'text/javascript';
          script.textContent = xhr.responseText;
          script.setAttribute('data-reloaded-xhr', 'true');
          document.head.appendChild(script);
          
          console.log(`[Edara ERP] ✅ JS rechargé via XHR: ${url}`);
          resolve(true);
        } else {
          console.error(`[Edara ERP] ❌ XHR échec: ${url} - Status: ${xhr.status}`);
          resolve(false);
        }
      };
      
      xhr.onerror = function() {
        console.error(`[Edara ERP] ❌ XHR erreur réseau: ${url}`);
        resolve(false);
      };
      
      xhr.send();
    });
  }

  /**
   * Détecte si un asset est critique
   */
  function isCriticalAsset(url) {
    return CRITICAL_ASSETS.some(asset => url.includes(asset));
  }

  /**
   * Intercepte les erreurs de chargement
   */
  function setupErrorInterception() {
    console.log('[Edara ERP] Configuration de l\'interception d\'erreurs');

    // Intercepter les erreurs d'éléments
    window.addEventListener('error', async function(event) {
      if (!event.target || isFixing) return;

      const element = event.target;
      const url = element.src || element.href;
      
      if (!url || !isCriticalAsset(url)) return;

      console.log(`[Edara ERP] 🔍 Erreur détectée sur asset critique: ${url}`);
      
      // Marquer comme échoué
      failedAssets.add(url);
      
      // Empêcher la propagation
      event.preventDefault();
      event.stopPropagation();
      
      // Recharger l'asset
      if (element.tagName === 'SCRIPT') {
        await reloadJavaScriptAsset(url);
      } else if (element.tagName === 'LINK' && element.rel === 'stylesheet') {
        await reloadCSSAsset(url);
      }
      
    }, true);

    // Intercepter les erreurs de fetch/XHR
    const originalFetch = window.fetch;
    window.fetch = async function(...args) {
      try {
        const response = await originalFetch.apply(this, args);
        
        // Vérifier si c'est un asset critique qui a échoué
        const url = args[0];
        if (typeof url === 'string' && isCriticalAsset(url) && !response.ok) {
          console.log(`[Edara ERP] 🔍 Fetch échoué pour asset critique: ${url}`);
          failedAssets.add(url);
          
          // Essayer de recharger
          if (url.includes('.js')) {
            setTimeout(() => reloadJavaScriptAsset(url), 100);
          } else if (url.includes('.css')) {
            setTimeout(() => reloadCSSAsset(url), 100);
          }
        }
        
        return response;
      } catch (error) {
        const url = args[0];
        if (typeof url === 'string' && isCriticalAsset(url)) {
          console.log(`[Edara ERP] 🔍 Fetch exception pour asset critique: ${url}`);
          failedAssets.add(url);
        }
        throw error;
      }
    };
  }

  /**
   * Vérifie si Odoo est correctement chargé
   */
  function checkOdooLoaded() {
    const checks = [
      () => typeof window.odoo !== 'undefined',
      () => typeof window.$ !== 'undefined' || typeof window.jQuery !== 'undefined',
      () => document.querySelector('.o_web_client') !== null,
      () => document.querySelector('.o_main_navbar') !== null
    ];

    const passedChecks = checks.filter(check => {
      try {
        return check();
      } catch (e) {
        return false;
      }
    }).length;

    console.log(`[Edara ERP] Vérification Odoo: ${passedChecks}/${checks.length} checks passés`);
    return passedChecks >= 2; // Au moins 2 checks doivent passer
  }

  /**
   * Force le rechargement de la page si nécessaire
   */
  function forceReloadIfNeeded() {
    setTimeout(() => {
      if (!checkOdooLoaded() && failedAssets.size > 0) {
        console.log('[Edara ERP] 🔄 Odoo non chargé correctement, rechargement forcé...');
        
        // Notifier l'application Electron
        if (window.electronAPI && window.electronAPI.sendAuthData) {
          window.electronAPI.sendAuthData({
            action: 'force-reload-needed',
            failedAssets: Array.from(failedAssets)
          });
        }
        
        // Recharger la page
        window.location.reload();
      }
    }, CONFIG.forceReloadDelay);
  }

  /**
   * Tente de recharger les menus Odoo
   */
  function attemptMenuReload() {
    setTimeout(() => {
      if (typeof window.odoo !== 'undefined' && window.odoo.reloadMenus) {
        console.log('[Edara ERP] 🔄 Tentative de rechargement des menus Odoo');
        try {
          window.odoo.reloadMenus();
        } catch (error) {
          console.error('[Edara ERP] Erreur lors du rechargement des menus:', error);
        }
      }
    }, CONFIG.reloadDelay);
  }

  /**
   * Surveille les changements DOM pour détecter les problèmes
   */
  function setupDOMObserver() {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Vérifier les nouveaux scripts et liens
            const scripts = node.tagName === 'SCRIPT' ? [node] : node.querySelectorAll ? node.querySelectorAll('script') : [];
            const links = node.tagName === 'LINK' ? [node] : node.querySelectorAll ? node.querySelectorAll('link[rel="stylesheet"]') : [];
            
            [...scripts, ...links].forEach(element => {
              const url = element.src || element.href;
              if (url && isCriticalAsset(url)) {
                console.log(`[Edara ERP] 👀 Nouvel asset critique détecté: ${url}`);
                
                // Ajouter un gestionnaire d'erreur
                element.addEventListener('error', () => {
                  console.log(`[Edara ERP] ❌ Erreur sur nouvel asset: ${url}`);
                  failedAssets.add(url);
                });
              }
            });
          }
        });
      });
    });

    observer.observe(document, {
      childList: true,
      subtree: true
    });

    console.log('[Edara ERP] Observer DOM configuré');
  }

  /**
   * Initialisation principale
   */
  function init() {
    console.log('[Edara ERP] 🚀 Initialisation du fix Content-Length Mismatch');

    // Configurer l'interception d'erreurs
    setupErrorInterception();

    // Configurer l'observer DOM
    setupDOMObserver();

    // Programmer les vérifications
    attemptMenuReload();
    forceReloadIfNeeded();

    // Notifier l'application Electron
    if (window.electronAPI && window.electronAPI.sendAuthData) {
      window.electronAPI.sendAuthData({
        action: 'content-length-fix-initialized',
        timestamp: Date.now()
      });
    }

    console.log('[Edara ERP] ✅ Fix Content-Length Mismatch initialisé');
  }

  // Démarrer dès que possible
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

  // Exposer des fonctions utiles pour le débogage
  window.EdaraContentLengthFix = {
    getFailedAssets: () => Array.from(failedAssets),
    checkOdooLoaded,
    reloadJavaScriptAsset,
    reloadCSSAsset,
    forceReload: () => window.location.reload()
  };

})();