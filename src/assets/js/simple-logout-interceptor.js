/**
 * Script amélioré pour intercepter les déconnexions d'Odoo
 * Ce script utilise plusieurs méthodes pour détecter et intercepter les déconnexions:
 * 1. MutationObserver pour détecter les liens de déconnexion ajoutés dynamiquement
 * 2. Écouteur de soumission de formulaire au niveau du document (capture phase)
 * 3. Écouteur de clic au niveau du document (capture phase)
 * Tous ces mécanismes appellent window.edaraAPI.logout() pour signaler la déconnexion au processus principal
 */

(function() {
  console.log('[Edara ERP] Intercepteur de déconnexion amélioré chargé');

  // Fonction pour vérifier si un élément est lié à une déconnexion
  function isLogoutElement(element) {
    if (!element) return false;

    // Vérifier si c'est un lien de déconnexion
    if (element.tagName === 'A') {
      // Vérifier les attributs
      if (element.getAttribute('data-menu') === 'logout') return true;
      if (element.classList.contains('o_logout')) return true;

      // Vérifier le href
      const href = element.getAttribute('href');
      if (href && href.includes('/web/session/logout')) return true;

      // Vérifier le texte
      const text = element.textContent.toLowerCase();
      if (text.includes('déconnexion') || text.includes('logout') || text.includes('sign out')) {
        return true;
      }
    }

    // Vérifier si c'est un formulaire de déconnexion
    if (element.tagName === 'FORM') {
      const action = element.getAttribute('action');
      if (action && (action.includes('/web/session/logout') || action.includes('/web/login'))) {
        return true;
      }
    }

    return false;
  }

  // Fonction pour notifier l'application Electron d'une déconnexion
  function notifyLogout(event, source) {
    console.log(`[Edara ERP] Déconnexion interceptée (source: ${source})`);

    // Empêcher l'action par défaut
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    // Notifier l'application Electron via l'API exposée
    if (window.edaraAPI && typeof window.edaraAPI.logout === 'function') {
      console.log('[Edara ERP] Appel de window.edaraAPI.logout()');
      window.edaraAPI.logout();
    } else {
      console.error('[Edara ERP] API de déconnexion non disponible');
    }

    return false;
  }

  // 1. Configurer le MutationObserver pour détecter les liens de déconnexion ajoutés dynamiquement
  function setupMutationObserver() {
    console.log('[Edara ERP] Configuration de l\'observateur de mutations');

    const observer = new MutationObserver(function() {
      // Rechercher les liens de déconnexion
      const logoutLinks = document.querySelectorAll('a[data-menu="logout"], a.o_logout, a[href*="/web/session/logout"]');

      if (logoutLinks.length > 0) {
        console.log('[Edara ERP] Liens de déconnexion détectés:', logoutLinks.length);

        // Ajouter un gestionnaire d'événements à chaque lien de déconnexion
        logoutLinks.forEach(function(link) {
          if (!link.dataset.edaraIntercepted) {
            link.dataset.edaraIntercepted = 'true';

            link.addEventListener('click', function(event) {
              return notifyLogout(event, 'lien-mutation');
            });

            console.log('[Edara ERP] Gestionnaire d\'événements ajouté au lien de déconnexion:', link.textContent);
          }
        });
      }
    });

    // Observer tout le document pour les changements
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // Rechercher les liens de déconnexion déjà présents dans le DOM
    const existingLogoutLinks = document.querySelectorAll('a[data-menu="logout"], a.o_logout, a[href*="/web/session/logout"]');
    if (existingLogoutLinks.length > 0) {
      console.log('[Edara ERP] Liens de déconnexion existants détectés:', existingLogoutLinks.length);
      existingLogoutLinks.forEach(function(link) {
        if (!link.dataset.edaraIntercepted) {
          link.dataset.edaraIntercepted = 'true';

          link.addEventListener('click', function(event) {
            return notifyLogout(event, 'lien-existant');
          });

          console.log('[Edara ERP] Gestionnaire d\'événements ajouté au lien de déconnexion existant:', link.textContent);
        }
      });
    }

    console.log('[Edara ERP] Observateur de mutations configuré');
  }

  // 2. Configurer l'écouteur de soumission de formulaire au niveau du document
  function setupFormSubmitListener() {
    console.log('[Edara ERP] Configuration de l\'écouteur de soumission de formulaire');

    document.addEventListener('submit', function(event) {
      const form = event.target;

      // Vérifier si c'est un formulaire de déconnexion
      if (form && form.tagName === 'FORM') {
        const action = form.getAttribute('action');

        if (action && (action.includes('/web/session/logout') || action.includes('/web/login'))) {
          console.log('[Edara ERP] Soumission de formulaire de déconnexion interceptée:', action);
          return notifyLogout(event, 'formulaire');
        }
      }
    }, true); // true = phase de capture pour intercepter avant la propagation

    console.log('[Edara ERP] Écouteur de soumission de formulaire configuré');
  }

  // 3. Configurer l'écouteur de clic au niveau du document
  function setupDocumentClickListener() {
    console.log('[Edara ERP] Configuration de l\'écouteur de clic au niveau du document');

    document.addEventListener('click', function(event) {
      // Vérifier si l'élément cliqué ou l'un de ses parents est un élément de déconnexion
      let target = event.target;

      // Remonter jusqu'à 5 niveaux de parents pour trouver un élément de déconnexion
      for (let i = 0; i < 5 && target; i++) {
        if (isLogoutElement(target)) {
          console.log('[Edara ERP] Clic sur élément de déconnexion intercepté:', target);
          return notifyLogout(event, 'clic-document');
        }
        target = target.parentElement;
      }
    }, true); // true = phase de capture pour intercepter avant la propagation

    console.log('[Edara ERP] Écouteur de clic au niveau du document configuré');
  }

  // Fonction principale pour configurer tous les intercepteurs
  function setupAllInterceptors() {
    setupMutationObserver();
    setupFormSubmitListener();
    setupDocumentClickListener();

    // Essayer également d'intercepter la fonction de déconnexion native d'Odoo si elle existe
    try {
      if (window.odoo && window.odoo.session && typeof window.odoo.session.logout === 'function') {
        console.log('[Edara ERP] Remplacement de la fonction de déconnexion native d\'Odoo');

        // Sauvegarder l'ancienne fonction
        const originalLogout = window.odoo.session.logout;

        // Remplacer la fonction
        window.odoo.session.logout = function() {
          console.log('[Edara ERP] Fonction de déconnexion native d\'Odoo interceptée');
          return notifyLogout(null, 'fonction-native');
        };
      }
    } catch (error) {
      console.error('[Edara ERP] Erreur lors du remplacement de la fonction native:', error);
    }
  }

  // Exécuter la fonction d'interception lorsque le DOM est chargé
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupAllInterceptors);
  } else {
    setupAllInterceptors();
  }

  console.log('[Edara ERP] Intercepteur de déconnexion amélioré initialisé');
})();
