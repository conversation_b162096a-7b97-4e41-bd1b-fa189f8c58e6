/**
 * Script pour corriger les problèmes de Content-Length mismatch dans les assets Odoo
 * Ce script est injecté dans la page pour intercepter et corriger les problèmes de chargement
 * des assets JavaScript et CSS comme web.assets_backend.js et web.assets_backend.css
 */
(function() {
  console.log('[Edara ERP] Content-Length Fix: Initialisation');

  // Liste des assets problématiques à surveiller
  const problematicAssets = [
    'web.assets_backend.js',
    'web.assets_common.js',
    'web.assets_backend.css',
    'web.assets_common.css',
    'load_menus',
    'web/webclient',
    'web/dataset',
    'web/static',
    'web/binary',
    'web/image',
    'web/session',
    'web_editor',
    'web/assets'
  ];

  // Fonction pour ajouter un paramètre de cache-busting à une URL
  function addCacheBuster(url) {
    try {
      const urlObj = new URL(url);
      urlObj.searchParams.set('nocache', Date.now());
      return urlObj.toString();
    } catch (error) {
      console.error(`[Edara ERP] Content-Length Fix: Erreur lors de l'ajout du cache-buster à l'URL ${url}:`, error);
      return url;
    }
  }

  // Fonction pour recharger un asset JavaScript avec fetch et blob
  function reloadAssetWithFetch(url) {
    console.log(`[Edara ERP] Content-Length Fix: Tentative de rechargement de l'asset avec fetch: ${url}`);

    // Ajouter un paramètre de cache-busting
    const cacheBustedUrl = addCacheBuster(url);

    // Utiliser fetch avec l'option no-cors pour éviter les problèmes CORS
    return fetch(cacheBustedUrl, {
      method: 'GET',
      mode: 'cors',
      cache: 'no-cache',
      credentials: 'same-origin',
      redirect: 'follow',
      referrerPolicy: 'no-referrer'
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }
      // Utiliser response.blob() pour éviter les problèmes de content-length
      return response.blob();
    })
    .then(blob => {
      // Créer une URL pour le blob
      const blobUrl = URL.createObjectURL(blob);

      // Créer un nouvel élément script
      const script = document.createElement('script');
      script.src = blobUrl;
      script.async = false; // Ensure synchronous-like behavior for dependent inline scripts

      // Gestionnaires d'événements pour le chargement et les erreurs
      script.onload = function() {
        console.log(`[Edara ERP] Content-Length Fix: Asset rechargé avec succès: ${url}`);
        // Libérer l'URL du blob après le chargement
        URL.revokeObjectURL(blobUrl);
      };

      script.onerror = function(error) {
        console.error(`[Edara ERP] Content-Length Fix: Erreur lors du rechargement de l'asset: ${url}`, error);
        // Libérer l'URL du blob en cas d'erreur
        URL.revokeObjectURL(blobUrl);
      };

      // Ajouter le script au document
      document.head.appendChild(script);

      return script;
    })
    .catch(error => {
      console.error(`[Edara ERP] Content-Length Fix: Erreur lors du rechargement de l'asset avec fetch: ${url}`, error);
      // En cas d'erreur, essayer une autre méthode de rechargement
      return reloadAssetWithXHR(url);
    });
  }

  // Fonction pour recharger un asset JavaScript avec XMLHttpRequest
  function reloadAssetWithXHR(url) {
    console.log(`[Edara ERP] Content-Length Fix: Tentative de rechargement de l'asset avec XHR: ${url}`);

    return new Promise((resolve, reject) => {
      // Ajouter un paramètre de cache-busting
      const cacheBustedUrl = addCacheBuster(url);

      // Créer une nouvelle requête XHR
      const xhr = new XMLHttpRequest();

      // Configurer la requête pour ignorer le content-length
      xhr.open('GET', cacheBustedUrl, true);
      xhr.responseType = 'text';

      // Gestionnaire d'événements pour le chargement
      xhr.onload = function() {
        if (xhr.status >= 200 && xhr.status < 300) {
          // Créer un nouvel élément script
          const script = document.createElement('script');
          script.textContent = xhr.responseText;

          // Ajouter le script au document
          document.head.appendChild(script);

          console.log(`[Edara ERP] Content-Length Fix: Asset rechargé avec succès via XHR: ${url}`);
          resolve(script);
        } else {
          reject(new Error(`Erreur HTTP: ${xhr.status}`));
        }
      };

      // Gestionnaire d'événements pour les erreurs
      xhr.onerror = function(error) {
        console.error(`[Edara ERP] Content-Length Fix: Erreur lors du rechargement de l'asset avec XHR: ${url}`, error);
        reject(error);
      };

      // Envoyer la requête
      xhr.send();
    });
  }

  // Fonction pour recharger un fichier CSS avec fetch et blob
  function reloadCSSWithFetch(url) {
    console.log(`[Edara ERP] Content-Length Fix: Tentative de rechargement du CSS avec fetch: ${url}`);

    // Ajouter un paramètre de cache-busting
    const cacheBustedUrl = addCacheBuster(url);

    // Utiliser fetch avec l'option no-cors pour éviter les problèmes CORS
    return fetch(cacheBustedUrl, {
      method: 'GET',
      mode: 'cors',
      cache: 'no-cache',
      credentials: 'same-origin',
      redirect: 'follow',
      referrerPolicy: 'no-referrer'
    })
    .then(response => {
      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }
      // Utiliser response.text() pour les fichiers CSS
      return response.text();
    })
    .then(cssText => {
      // Créer un nouvel élément style
      const style = document.createElement('style');
      style.textContent = cssText;

      // Ajouter le style au document
      document.head.appendChild(style);

      console.log(`[Edara ERP] Content-Length Fix: CSS rechargé avec succès: ${url}`);
      return style;
    })
    .catch(error => {
      console.error(`[Edara ERP] Content-Length Fix: Erreur lors du rechargement du CSS: ${url}`, error);
    });
  }

  // Fonction pour intercepter les erreurs de chargement d'assets
  function interceptAssetErrors() {
    console.log('[Edara ERP] Content-Length Fix: Configuration de l\'interception des erreurs d\'assets');

    // Intercepter les erreurs globales
    window.addEventListener('error', function(event) {
      // Vérifier si l'erreur concerne un asset problématique
      if (event.target) {
        const src = event.target.src || event.target.href || '';

        // Vérifier si l'URL correspond à un asset problématique
        const isProblematicAsset = problematicAssets.some(asset => src.includes(asset));

        if (isProblematicAsset) {
          console.log(`[Edara ERP] Content-Length Fix: Erreur détectée lors du chargement de l'asset: ${src}`);

          // Empêcher la propagation de l'erreur
          event.preventDefault();

          // Recharger l'asset en fonction de son type
          if (event.target.tagName === 'SCRIPT') {
            reloadAssetWithFetch(src);
          } else if (event.target.tagName === 'LINK' && event.target.rel === 'stylesheet') {
            reloadCSSWithFetch(src);
          }
        }
      }
    }, true);

    // Ajouter un correctif pour les menus qui échouent à charger
    setTimeout(() => {
      // Vérifier si odoo est défini
      if (typeof window.odoo !== 'undefined' && typeof window.odoo.reloadMenus === 'function') {
        console.log('[Edara ERP] Content-Length Fix: Tentative de rechargement des menus Odoo');
        try {
          window.odoo.reloadMenus();
        } catch (error) {
          console.error('[Edara ERP] Content-Length Fix: Erreur lors du rechargement des menus Odoo', error);
        }
      }
    }, 5000); // Attendre 5 secondes pour s'assurer que la page est chargée
  }

  // Fonction pour précharger les assets connus pour poser problème
  function preloadProblematicAssets() {
    console.log('[Edara ERP] Content-Length Fix: Préchargement des assets problématiques');

    // Attendre que le DOM soit chargé
    setTimeout(() => {
      // Trouver tous les scripts et liens CSS dans la page
      const scripts = Array.from(document.getElementsByTagName('script'));
      const links = Array.from(document.getElementsByTagName('link')).filter(link => link.rel === 'stylesheet');

      // Parcourir tous les scripts et liens CSS
      [...scripts, ...links].forEach(element => {
        const src = element.src || element.href || '';

        // Vérifier si l'URL correspond à un asset problématique
        const isProblematicAsset = problematicAssets.some(asset => src.includes(asset));

        if (isProblematicAsset) {
          console.log(`[Edara ERP] Content-Length Fix: Asset problématique détecté: ${src}`);

          // Recharger l'asset en fonction de son type
          if (element.tagName === 'SCRIPT') {
            reloadAssetWithFetch(src);
          } else if (element.tagName === 'LINK' && element.rel === 'stylesheet') {
            reloadCSSWithFetch(src);
          }
        }
      });

      // Essayer de recharger les menus Odoo
      if (typeof window.odoo !== 'undefined' && typeof window.odoo.reloadMenus === 'function') {
        console.log('[Edara ERP] Content-Length Fix: Rechargement des menus Odoo');
        try {
          window.odoo.reloadMenus();
        } catch (error) {
          console.error('[Edara ERP] Content-Length Fix: Erreur lors du rechargement des menus Odoo', error);
        }
      }
    }, 2000); // Attendre 2 secondes pour s'assurer que la page est chargée
  }

  // Fonction principale
  function init() {
    console.log('[Edara ERP] Content-Length Fix: Initialisation');

    // Intercepter les erreurs de chargement d'assets
    interceptAssetErrors();

    // Précharger les assets problématiques
    preloadProblematicAssets();

    // Notifier l'application Electron que le script est initialisé
    if (window.electronAPI && typeof window.electronAPI.sendAuthData === 'function') {
      window.electronAPI.sendAuthData({
        action: 'content-length-fix-initialized'
      });
    }
  }

  // Exécuter la fonction principale
  init();
})();
