/**
 * Système Intelligent de Récupération de Contenu
 * Remplace les scripts de correction existants par une approche intelligente et moins disruptive
 */

(function() {
  'use strict';
  
  console.log('[Edara ERP] 🧠 Intelligent Content Recovery: Initialisation');

  // Configuration avancée
  const CONFIG = {
    // Stratégies de récupération
    enableSelectiveReload: true,        // Rechargement sélectif uniquement
    enableSmartRetry: true,             // Retry intelligent avec backoff
    enablePreemptiveLoading: true,      // Chargement préemptif
    enableHealthMonitoring: true,       // Surveillance de santé continue
    
    // Timeouts et délais optimisés
    retryDelay: 500,                    // 500ms entre tentatives
    maxRetries: 3,                      // 3 tentatives max par asset
    healthCheckInterval: 30000,         // 30s entre vérifications santé
    preloadDelay: 2000,                 // 2s avant préchargement
    
    // Seuils de détection
    errorThreshold: 3,                  // 3 erreurs avant action
    responseTimeThreshold: 5000,        // 5s max par asset
    memoryThreshold: 100 * 1024 * 1024, // 100MB max mémoire
    
    // Debug et logging
    debug: true,
    logLevel: 'info'
  };

  // État global du système
  const STATE = {
    isRecovering: false,
    recoveryAttempts: new Map(),        // URL -> tentatives
    assetHealth: new Map(),             // URL -> santé
    errorHistory: [],                   // Historique des erreurs
    performanceMetrics: {
      totalAssets: 0,
      successfulLoads: 0,
      failedLoads: 0,
      averageLoadTime: 0,
      memoryUsage: 0
    },
    criticalAssets: new Set(),          // Assets critiques identifiés
    preloadedAssets: new Map()          // Assets préchargés
  };

  // Patterns d'assets critiques avec priorités
  const CRITICAL_ASSETS = {
    'web.assets_backend.js': { priority: 1, essential: true },
    'web.assets_common.js': { priority: 2, essential: true },
    'web.assets_backend.css': { priority: 3, essential: true },
    'web.assets_common.css': { priority: 4, essential: true },
    'load_menus': { priority: 5, essential: true },
    'web/webclient': { priority: 6, essential: false }
  };

  /**
   * Analyseur intelligent d'erreurs
   */
  class IntelligentErrorAnalyzer {
    constructor() {
      this.patterns = new Map();
      this.solutions = new Map();
      this.initializePatterns();
    }

    initializePatterns() {
      // Patterns d'erreur et leurs solutions
      this.patterns.set(/ERR_CONTENT_LENGTH_MISMATCH/, {
        cause: 'compression-mismatch',
        solution: 'selective-reload',
        confidence: 0.9
      });

      this.patterns.set(/ERR_EMPTY_RESPONSE/, {
        cause: 'server-timeout',
        solution: 'delayed-retry',
        confidence: 0.8
      });

      this.patterns.set(/Failed to load resource/, {
        cause: 'network-error',
        solution: 'smart-retry',
        confidence: 0.7
      });

      this.patterns.set(/net::ERR_NETWORK_CHANGED/, {
        cause: 'network-change',
        solution: 'full-recovery',
        confidence: 0.95
      });
    }

    analyzeError(error, url, context = {}) {
      const analysis = {
        url,
        error: error.toString(),
        timestamp: Date.now(),
        cause: 'unknown',
        solution: 'default-retry',
        confidence: 0.5,
        priority: this.getAssetPriority(url),
        context
      };

      // Analyser avec les patterns
      for (const [pattern, info] of this.patterns) {
        if (pattern.test(error.toString())) {
          analysis.cause = info.cause;
          analysis.solution = info.solution;
          analysis.confidence = info.confidence;
          break;
        }
      }

      // Analyser le contexte
      if (context.responseTime > CONFIG.responseTimeThreshold) {
        analysis.cause = 'slow-response';
        analysis.solution = 'optimized-retry';
      }

      if (context.memoryUsage > CONFIG.memoryThreshold) {
        analysis.cause = 'memory-pressure';
        analysis.solution = 'memory-optimized-reload';
      }

      return analysis;
    }

    getAssetPriority(url) {
      for (const [pattern, info] of Object.entries(CRITICAL_ASSETS)) {
        if (url.includes(pattern)) {
          return info.priority;
        }
      }
      return 10; // Priorité basse par défaut
    }
  }

  /**
   * Gestionnaire de récupération sélective
   */
  class SelectiveRecoveryManager {
    constructor() {
      this.analyzer = new IntelligentErrorAnalyzer();
      this.recoveryQueue = [];
      this.isProcessing = false;
    }

    async handleError(error, element, context = {}) {
      const url = element.src || element.href || element.url;
      if (!url) return false;

      // Analyser l'erreur
      const analysis = this.analyzer.analyzeError(error, url, context);
      
      console.log(`[Edara ERP] 🔍 Analyse erreur: ${analysis.cause} (confiance: ${analysis.confidence})`);

      // Ajouter à l'historique
      STATE.errorHistory.push(analysis);
      if (STATE.errorHistory.length > 100) {
        STATE.errorHistory = STATE.errorHistory.slice(-100);
      }

      // Déterminer la stratégie de récupération
      const strategy = this.selectRecoveryStrategy(analysis);
      
      // Exécuter la récupération
      return await this.executeRecovery(strategy, element, analysis);
    }

    selectRecoveryStrategy(analysis) {
      const strategies = {
        'selective-reload': this.selectiveAssetReload.bind(this),
        'delayed-retry': this.delayedRetry.bind(this),
        'smart-retry': this.smartRetry.bind(this),
        'optimized-retry': this.optimizedRetry.bind(this),
        'memory-optimized-reload': this.memoryOptimizedReload.bind(this),
        'full-recovery': this.fullRecovery.bind(this),
        'default-retry': this.defaultRetry.bind(this)
      };

      return strategies[analysis.solution] || strategies['default-retry'];
    }

    async executeRecovery(strategy, element, analysis) {
      try {
        console.log(`[Edara ERP] 🔧 Exécution stratégie: ${analysis.solution}`);
        
        const result = await strategy(element, analysis);
        
        if (result.success) {
          console.log(`[Edara ERP] ✅ Récupération réussie: ${analysis.url}`);
          this.updateSuccessMetrics(analysis);
        } else {
          console.log(`[Edara ERP] ❌ Récupération échouée: ${analysis.url}`);
          this.updateFailureMetrics(analysis);
        }

        return result;
      } catch (error) {
        console.error(`[Edara ERP] ❌ Erreur lors de la récupération:`, error);
        return { success: false, error: error.message };
      }
    }

    // Stratégies de récupération spécialisées

    async selectiveAssetReload(element, analysis) {
      const url = analysis.url;
      
      // Vérifier si déjà en cours de récupération
      if (STATE.recoveryAttempts.has(url)) {
        const attempts = STATE.recoveryAttempts.get(url);
        if (attempts >= CONFIG.maxRetries) {
          return { success: false, reason: 'max-retries-exceeded' };
        }
      }

      // Incrémenter les tentatives
      STATE.recoveryAttempts.set(url, (STATE.recoveryAttempts.get(url) || 0) + 1);

      // Créer un nouvel élément optimisé
      const newElement = await this.createOptimizedElement(element, analysis);
      
      if (newElement) {
        // Remplacer l'ancien élément
        element.parentNode.replaceChild(newElement, element);
        
        // Attendre le chargement
        return await this.waitForElementLoad(newElement);
      }

      return { success: false, reason: 'element-creation-failed' };
    }

    async delayedRetry(element, analysis) {
      // Attendre avant de réessayer
      await this.delay(CONFIG.retryDelay * (STATE.recoveryAttempts.get(analysis.url) || 1));
      
      return await this.selectiveAssetReload(element, analysis);
    }

    async smartRetry(element, analysis) {
      // Retry avec backoff exponentiel
      const attempts = STATE.recoveryAttempts.get(analysis.url) || 0;
      const delay = CONFIG.retryDelay * Math.pow(2, attempts);
      
      await this.delay(Math.min(delay, 10000)); // Max 10s
      
      return await this.selectiveAssetReload(element, analysis);
    }

    async optimizedRetry(element, analysis) {
      // Optimiser l'URL avant retry
      const optimizedUrl = this.optimizeAssetUrl(analysis.url);
      
      if (optimizedUrl !== analysis.url) {
        element.src = optimizedUrl;
        element.href = optimizedUrl;
        
        return await this.waitForElementLoad(element);
      }
      
      return await this.smartRetry(element, analysis);
    }

    async memoryOptimizedReload(element, analysis) {
      // Nettoyer la mémoire avant rechargement
      await this.performMemoryCleanup();
      
      return await this.selectiveAssetReload(element, analysis);
    }

    async fullRecovery(element, analysis) {
      // Récupération complète en dernier recours
      console.log('[Edara ERP] 🔄 Récupération complète nécessaire');
      
      // Nettoyer l'état
      this.resetRecoveryState();
      
      // Recharger tous les assets critiques
      return await this.reloadCriticalAssets();
    }

    async defaultRetry(element, analysis) {
      return await this.selectiveAssetReload(element, analysis);
    }

    // Méthodes utilitaires

    async createOptimizedElement(originalElement, analysis) {
      const tagName = originalElement.tagName.toLowerCase();
      const url = analysis.url;
      
      let newElement;
      
      if (tagName === 'script') {
        newElement = document.createElement('script');
        newElement.type = 'text/javascript';
        newElement.async = false;
        newElement.defer = false;
        
        // Optimiser l'URL
        newElement.src = this.optimizeAssetUrl(url);
        
        // Ajouter des headers optimisés via fetch puis injection
        try {
          const response = await fetch(newElement.src, {
            headers: {
              'Accept-Encoding': 'identity',
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            }
          });
          
          if (response.ok) {
            const content = await response.text();
            newElement.textContent = content;
            newElement.removeAttribute('src'); // Utiliser le contenu inline
          }
        } catch (fetchError) {
          console.warn(`[Edara ERP] ⚠️ Fetch échoué, utilisation src: ${fetchError.message}`);
        }
        
      } else if (tagName === 'link' && originalElement.rel === 'stylesheet') {
        newElement = document.createElement('link');
        newElement.rel = 'stylesheet';
        newElement.type = 'text/css';
        newElement.href = this.optimizeAssetUrl(url);
      }
      
      if (newElement) {
        // Copier les attributs importants
        ['id', 'class', 'data-asset-id'].forEach(attr => {
          if (originalElement.hasAttribute(attr)) {
            newElement.setAttribute(attr, originalElement.getAttribute(attr));
          }
        });
        
        // Ajouter un identifiant de récupération
        newElement.setAttribute('data-recovery-attempt', STATE.recoveryAttempts.get(url) || 1);
        newElement.setAttribute('data-recovery-timestamp', Date.now());
      }
      
      return newElement;
    }

    optimizeAssetUrl(url) {
      const urlObj = new URL(url, window.location.origin);
      
      // Ajouter des paramètres d'optimisation
      urlObj.searchParams.set('no-gzip', '1');
      urlObj.searchParams.set('encoding', 'identity');
      urlObj.searchParams.set('cache-bust', Date.now().toString(36));
      
      // Forcer HTTPS si disponible
      if (window.location.protocol === 'https:' && urlObj.protocol === 'http:') {
        urlObj.protocol = 'https:';
      }
      
      return urlObj.toString();
    }

    async waitForElementLoad(element, timeout = 10000) {
      return new Promise((resolve) => {
        let resolved = false;
        
        const cleanup = (success, reason = '') => {
          if (!resolved) {
            resolved = true;
            element.removeEventListener('load', onLoad);
            element.removeEventListener('error', onError);
            clearTimeout(timeoutId);
            resolve({ success, reason });
          }
        };
        
        const onLoad = () => cleanup(true, 'loaded');
        const onError = (error) => cleanup(false, error.message || 'load-error');
        
        element.addEventListener('load', onLoad);
        element.addEventListener('error', onError);
        
        const timeoutId = setTimeout(() => {
          cleanup(false, 'timeout');
        }, timeout);
        
        // Si l'élément est déjà chargé
        if (element.tagName === 'SCRIPT' && element.readyState === 'complete') {
          cleanup(true, 'already-loaded');
        }
      });
    }

    async performMemoryCleanup() {
      // Nettoyer les assets non utilisés
      const unusedAssets = document.querySelectorAll('[data-recovery-attempt]');
      unusedAssets.forEach(asset => {
        const timestamp = parseInt(asset.getAttribute('data-recovery-timestamp'));
        if (Date.now() - timestamp > 300000) { // 5 minutes
          asset.remove();
        }
      });
      
      // Forcer garbage collection si disponible
      if (window.gc) {
        window.gc();
      }
      
      // Nettoyer le cache des assets
      STATE.preloadedAssets.clear();
      
      console.log('[Edara ERP] 🧹 Nettoyage mémoire effectué');
    }

    async reloadCriticalAssets() {
      const criticalElements = document.querySelectorAll('script[src*="web.assets"], link[href*="web.assets"]');
      const reloadPromises = [];
      
      criticalElements.forEach(element => {
        const url = element.src || element.href;
        if (this.isCriticalAsset(url)) {
          reloadPromises.push(this.selectiveAssetReload(element, {
            url,
            cause: 'full-recovery',
            solution: 'selective-reload',
            priority: this.analyzer.getAssetPriority(url)
          }));
        }
      });
      
      const results = await Promise.allSettled(reloadPromises);
      const successCount = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
      
      return {
        success: successCount > 0,
        successCount,
        totalCount: results.length
      };
    }

    isCriticalAsset(url) {
      return Object.keys(CRITICAL_ASSETS).some(pattern => url.includes(pattern));
    }

    resetRecoveryState() {
      STATE.recoveryAttempts.clear();
      STATE.errorHistory = [];
      STATE.isRecovering = false;
    }

    updateSuccessMetrics(analysis) {
      STATE.performanceMetrics.successfulLoads++;
      STATE.assetHealth.set(analysis.url, {
        status: 'healthy',
        lastSuccess: Date.now(),
        successCount: (STATE.assetHealth.get(analysis.url)?.successCount || 0) + 1
      });
    }

    updateFailureMetrics(analysis) {
      STATE.performanceMetrics.failedLoads++;
      STATE.assetHealth.set(analysis.url, {
        status: 'unhealthy',
        lastFailure: Date.now(),
        failureCount: (STATE.assetHealth.get(analysis.url)?.failureCount || 0) + 1
      });
    }

    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    }
  }

  /**
   * Gestionnaire de surveillance de santé
   */
  class HealthMonitor {
    constructor() {
      this.isMonitoring = false;
      this.healthCheckInterval = null;
    }

    start() {
      if (this.isMonitoring) return;
      
      this.isMonitoring = true;
      this.healthCheckInterval = setInterval(() => {
        this.performHealthCheck();
      }, CONFIG.healthCheckInterval);
      
      console.log('[Edara ERP] 💓 Surveillance de santé démarrée');
    }

    stop() {
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
        this.healthCheckInterval = null;
      }
      this.isMonitoring = false;
    }

    performHealthCheck() {
      const metrics = this.calculateMetrics();
      
      if (CONFIG.debug) {
        console.log(`[Edara ERP] 📊 Métriques santé:`, metrics);
      }
      
      // Vérifier les seuils critiques
      if (metrics.errorRate > 0.1) { // >10% d'erreurs
        console.warn(`[Edara ERP] ⚠️ Taux d'erreur élevé: ${(metrics.errorRate * 100).toFixed(1)}%`);
        this.triggerPreventiveMaintenance();
      }
      
      if (metrics.averageLoadTime > CONFIG.responseTimeThreshold) {
        console.warn(`[Edara ERP] ⚠️ Temps de chargement élevé: ${metrics.averageLoadTime}ms`);
      }
      
      // Émettre un événement pour l'application Electron
      if (window.electronAPI) {
        window.electronAPI.sendAuthData({
          action: 'health-metrics',
          metrics,
          timestamp: Date.now()
        });
      }
    }

    calculateMetrics() {
      const total = STATE.performanceMetrics.successfulLoads + STATE.performanceMetrics.failedLoads;
      
      return {
        totalAssets: total,
        successRate: total > 0 ? STATE.performanceMetrics.successfulLoads / total : 1,
        errorRate: total > 0 ? STATE.performanceMetrics.failedLoads / total : 0,
        averageLoadTime: STATE.performanceMetrics.averageLoadTime,
        recentErrors: STATE.errorHistory.filter(e => Date.now() - e.timestamp < 3600000).length,
        healthyAssets: Array.from(STATE.assetHealth.values()).filter(h => h.status === 'healthy').length,
        unhealthyAssets: Array.from(STATE.assetHealth.values()).filter(h => h.status === 'unhealthy').length
      };
    }

    triggerPreventiveMaintenance() {
      console.log('[Edara ERP] 🔧 Déclenchement maintenance préventive');
      
      // Nettoyer les assets problématiques
      const unhealthyAssets = Array.from(STATE.assetHealth.entries())
        .filter(([url, health]) => health.status === 'unhealthy')
        .map(([url]) => url);
      
      unhealthyAssets.forEach(url => {
        const elements = document.querySelectorAll(`[src="${url}"], [href="${url}"]`);
        elements.forEach(element => {
          recoveryManager.handleError(new Error('Preventive maintenance'), element, {
            cause: 'preventive-maintenance'
          });
        });
      });
    }
  }

  // Initialisation du système
  const recoveryManager = new SelectiveRecoveryManager();
  const healthMonitor = new HealthMonitor();

  /**
   * Installation des gestionnaires d'événements
   */
  function installEventHandlers() {
    // Intercepter les erreurs d'éléments
    window.addEventListener('error', async (event) => {
      if (event.target && (event.target.src || event.target.href)) {
        event.preventDefault();
        event.stopPropagation();
        
        const context = {
          responseTime: performance.now(),
          memoryUsage: performance.memory?.usedJSHeapSize || 0
        };
        
        await recoveryManager.handleError(event.error || new Error('Load failed'), event.target, context);
      }
    }, true);

    // Intercepter les erreurs de console
    const originalConsoleError = console.error;
    console.error = function(...args) {
      const message = args.join(' ');
      
      if (message.includes('ERR_CONTENT_LENGTH_MISMATCH') || 
          message.includes('ERR_EMPTY_RESPONSE') ||
          message.includes('Failed to load resource')) {
        
        // Analyser l'erreur depuis le message
        const urlMatch = message.match(/https?:\/\/[^\s]+/);
        if (urlMatch) {
          const url = urlMatch[0];
          const element = document.querySelector(`[src="${url}"], [href="${url}"]`);
          
          if (element) {
            recoveryManager.handleError(new Error(message), element, {
              source: 'console-error'
            });
          }
        }
      }
      
      return originalConsoleError.apply(console, args);
    };

    console.log('[Edara ERP] 🎯 Gestionnaires d\'événements installés');
  }

  /**
   * Initialisation principale
   */
  function initialize() {
    console.log('[Edara ERP] 🚀 Initialisation Intelligent Content Recovery');
    
    // Installer les gestionnaires
    installEventHandlers();
    
    // Démarrer la surveillance
    if (CONFIG.enableHealthMonitoring) {
      healthMonitor.start();
    }
    
    // Exposer l'API globale
    window.EdaraIntelligentRecovery = {
      getStats: () => ({
        config: CONFIG,
        state: STATE,
        metrics: healthMonitor.calculateMetrics()
      }),
      forceRecovery: (url) => {
        const element = document.querySelector(`[src="${url}"], [href="${url}"]`);
        if (element) {
          return recoveryManager.handleError(new Error('Manual recovery'), element);
        }
      },
      resetState: () => recoveryManager.resetRecoveryState(),
      toggleDebug: () => CONFIG.debug = !CONFIG.debug
    };
    
    // Notifier l'application Electron
    if (window.electronAPI) {
      window.electronAPI.sendAuthData({
        action: 'intelligent-recovery-initialized',
        timestamp: Date.now()
      });
    }
    
    console.log('[Edara ERP] ✅ Intelligent Content Recovery initialisé');
  }

  // Démarrer dès que possible
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initialize);
  } else {
    initialize();
  }

})();
