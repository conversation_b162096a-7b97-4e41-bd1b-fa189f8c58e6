/**
 * Thème principal de l'application Edara ERP
 * Ce fichier contient les styles globaux utilisés dans toute l'application
 */

:root {
  --primary-color: #124559;
  --primary-dark: #206f8c;
  --secondary-color: #2ecc71;
  --secondary-dark: #27ae60;
  --text-color: #ffffff;
  --text-light: #cccccc;
  --background-color: #121212;
  --card-background: #1e1e1e;
  --border-color: #333333;
  --error-color: #e74c3c;
  --success-color: #2ecc71;
  --warning-color: #f39c12;
  --info-color: #3498db;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: var(--text-color);
  background-color: var(--background-color);
}

h1, h2, h3, h4, h5, h6 {
  margin-bottom: 1rem;
  font-weight: 600;
}

h1 {
  font-size: 2rem;
}

h2 {
  font-size: 1.75rem;
}

h3 {
  font-size: 1.5rem;
}

p {
  margin-bottom: 1rem;
}

a {
  color: var(--primary-color);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

button, .btn {
  display: inline-block;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  font-weight: 500;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  border: none;
  border-radius: 4px;
  transition: background-color 0.3s, color 0.3s;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: var(--secondary-dark);
}

.btn-logout {
  background-color: transparent;
  color: var(--text-light);
  border: 1px solid var(--border-color);
}

.btn-logout:hover {
  background-color: var(--border-color);
}

input, select, textarea {
  width: 100%;
  padding: 0.5rem;
  font-size: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  margin-bottom: 1rem;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-group {
  margin-bottom: 1rem;
}

/* Layout de l'application */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: var(--card-background);
  border-bottom: 1px solid var(--border-color);
}

.header-logo {
  display: flex;
  align-items: center;
}

.small-logo {
  height: 30px;
  margin-right: 0.5rem;
}

.app-title {
  font-size: 1.25rem;
  font-weight: 600;
}

.app-content {
  display: flex;
  flex: 1;
}

.sidebar {
  width: 250px;
  background-color: var(--card-background);
  border-right: 1px solid var(--border-color);
  padding: 1rem 0;
}

.menu {
  list-style: none;
}

.menu-item {
  padding: 0.5rem 1.5rem;
}

.menu-item a {
  color: var(--text-color);
  text-decoration: none;
  display: block;
}

.menu-item:hover {
  background-color: var(--background-color);
}

.menu-item.active {
  background-color: var(--primary-color);
}

.menu-item.active a {
  color: white;
}

.main-content {
  flex: 1;
  padding: 2rem;
}

/* Widgets du tableau de bord */
.dashboard-widgets {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.widget {
  background-color: var(--card-background);
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.widget h3 {
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

/* Utilitaires */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.mt-1 {
  margin-top: 0.5rem;
}

.mt-2 {
  margin-top: 1rem;
}

.mb-1 {
  margin-bottom: 0.5rem;
}

.mb-2 {
  margin-bottom: 1rem;
}
