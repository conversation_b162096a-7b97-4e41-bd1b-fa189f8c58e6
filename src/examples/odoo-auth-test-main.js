/**
 * Script principal pour tester l'authentification Odoo
 * Ce script crée une fenêtre Electron avec un formulaire de connexion
 * et gère l'authentification via l'API Odoo
 */

const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const log = require('electron-log');
const { authenticateAndRedirectToOdoo } = require('../main/odoo-auth');

// Configuration de electron-log
log.transports.file.level = 'info';
log.transports.console.level = 'debug';
log.transports.console.format = '[{level}] {text}';

// Garder une référence globale de l'objet window pour éviter que la fenêtre
// ne soit fermée automatiquement quand l'objet JavaScript est récupéré par le ramasse-miettes.
let mainWindow = null;
let odooWindow = null;

/**
 * Crée la fenêtre principale de l'application
 */
function createWindow() {
  // Créer la fenêtre de navigateur.
  mainWindow = new BrowserWindow({
    width: 600,
    height: 700,
    backgroundColor: '#181818',
    webPreferences: {
      nodeIntegration: true, // Activer l'intégration de Node.js pour le test
      contextIsolation: false, // Désactiver l'isolation du contexte pour le test
      enableRemoteModule: true // Activer le module remote pour le test
    }
  });

  // Charger le fichier HTML du formulaire de connexion
  mainWindow.loadFile(path.join(__dirname, 'odoo-auth-test.html'));

  // Ouvrir les outils de développement.
  mainWindow.webContents.openDevTools();

  // Émis lorsque la fenêtre est fermée.
  mainWindow.on('closed', () => {
    // Dé-référencer l'objet window, généralement vous stockeriez les fenêtres
    // dans un tableau si votre application supporte le multi-fenêtre. C'est le moment
    // où vous devriez supprimer l'élément correspondant.
    mainWindow = null;
  });
}

// Cette méthode sera appelée quand Electron aura fini
// de s'initialiser et sera prêt à créer des fenêtres de navigateur.
// Certaines APIs peuvent être utilisées uniquement après cet événement.
app.whenReady().then(createWindow);

// Quitter quand toutes les fenêtres sont fermées.
app.on('window-all-closed', () => {
  // Sur macOS, il est commun pour une application et leur barre de menu
  // de rester active tant que l'utilisateur ne quitte pas explicitement avec Cmd + Q
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // Sur macOS, il est commun de re-créer une fenêtre de l'application quand
  // l'icône du dock est cliquée et qu'il n'y a pas d'autres fenêtres ouvertes.
  if (mainWindow === null) {
    createWindow();
  }
});

// Écouter les demandes d'authentification depuis le renderer process
ipcMain.on('authenticate-odoo', async (event, credentials) => {
  log.info(`Demande d'authentification reçue pour l'utilisateur ${credentials.username} sur ${credentials.serverUrl}`);

  try {
    // Appeler la fonction d'authentification
    const authResult = await authenticateAndRedirectToOdoo(
      credentials.username,
      credentials.password,
      credentials.serverUrl,
      credentials.dbName
    );

    // Envoyer le résultat au renderer process
    event.reply('authenticate-odoo-response', authResult);
  } catch (error) {
    log.error('Erreur lors de l\'authentification:', error);

    // Envoyer l'erreur au renderer process
    event.reply('authenticate-odoo-response', {
      success: false,
      error: error.message || 'Erreur inconnue lors de l\'authentification'
    });
  }
});

// Écouter les demandes de chargement de l'interface Odoo
ipcMain.on('load-odoo-interface', async (event, authData) => {
  log.info('Demande de chargement de l\'interface Odoo reçue');

  try {
    // Créer une fenêtre pour l'interface Odoo
    odooWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      show: false,
      backgroundColor: '#181818',
      webPreferences: {
        contextIsolation: false,
        nodeIntegration: false,
        webSecurity: false
      }
    });

    // Extraire le domaine de base de l'URL Odoo
    const odooUrl = new URL(authData.odoo_url);
    const odooDomain = `${odooUrl.protocol}//${odooUrl.hostname}${odooUrl.port ? `:${odooUrl.port}` : ''}`;

    // Définir le cookie de session dans la fenêtre Odoo
    const { session } = require('electron');
    // Importer la fonction existante pour définir le cookie de session
    const { setOdooSessionCookie } = require('../main/odoo-window');

    // Définir le cookie de session
    let cookieSet = false;

    try {
      log.info('Définition du cookie de session...');

      // Supprimer d'abord tous les cookies existants pour éviter les conflits
      await session.defaultSession.clearStorageData({ storages: ['cookies'] });

      // Définir le cookie avec des paramètres minimaux pour éviter les problèmes
      const cookie = {
        url: odooDomain,
        name: 'session_id',
        value: authData.sessionId
      };

      await session.defaultSession.cookies.set(cookie);

      // Vérifier que le cookie a bien été défini
      const cookies = await session.defaultSession.cookies.get({ url: odooDomain });
      const sessionCookie = cookies.find(c => c.name === 'session_id');

      if (sessionCookie) {
        log.info(`Cookie défini avec succès: ${sessionCookie.value.substring(0, 8)}...`);
        cookieSet = true;
      } else {
        log.error('Échec de la définition du cookie');

        // Essayer une autre approche avec plus de paramètres
        log.info('Tentative avec plus de paramètres...');

        const cookieWithParams = {
          url: odooDomain,
          name: 'session_id',
          value: authData.sessionId,
          path: '/',
          httpOnly: false,
          secure: false
        };

        await session.defaultSession.cookies.set(cookieWithParams);

        // Vérifier à nouveau
        const cookiesRetry = await session.defaultSession.cookies.get({ url: odooDomain });
        const sessionCookieRetry = cookiesRetry.find(c => c.name === 'session_id');

        if (sessionCookieRetry) {
          log.info(`Cookie défini avec succès après nouvelle tentative: ${sessionCookieRetry.value.substring(0, 8)}...`);
          cookieSet = true;
        } else {
          log.error('Échec de la définition du cookie après nouvelle tentative');
        }
      }
    } catch (cookieError) {
      log.error(`Erreur lors de la définition du cookie: ${cookieError.message}`);
    }

    if (cookieSet) {
      log.info('Cookie de session défini avec succès dans la fenêtre Odoo');

      // Charger l'URL de l'interface Odoo
      await odooWindow.loadURL(authData.odoo_url);
      log.info('Interface Odoo chargée avec succès');

      // Afficher la fenêtre
      odooWindow.show();
    } else {
      log.error('Échec de la définition du cookie de session dans la fenêtre Odoo');

      // Afficher un message d'erreur
      odooWindow.loadURL('data:text/html,<html><body style="background-color: #181818; color: white; font-family: Arial; display: flex; justify-content: center; align-items: center; height: 100vh;"><h2>Erreur</h2><p>Impossible de définir le cookie de session</p></body></html>');
      odooWindow.show();
    }
  } catch (error) {
    log.error('Erreur lors du chargement de l\'interface Odoo:', error);

    // Si la fenêtre a été créée, afficher un message d'erreur
    if (odooWindow) {
      odooWindow.loadURL(`data:text/html,<html><body style="background-color: #181818; color: white; font-family: Arial; display: flex; justify-content: center; align-items: center; height: 100vh;"><h2>Erreur</h2><p>${error.message || 'Erreur inconnue'}</p></body></html>`);
      odooWindow.show();
    }
  }
});

// Dans ce fichier, vous pouvez inclure le reste du code spécifique au processus principal de votre application
// Vous pouvez également le mettre dans des fichiers séparés et les inclure ici.
