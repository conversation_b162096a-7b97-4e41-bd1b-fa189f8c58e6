<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test d'authentification Odoo</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #181818;
      color: #ffffff;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
      padding: 0;
    }
    
    .login-container {
      background-color: #272B30;
      border-radius: 8px;
      padding: 30px;
      width: 400px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    }
    
    h1 {
      text-align: center;
      margin-bottom: 30px;
      color: #ffffff;
    }
    
    .form-group {
      margin-bottom: 20px;
    }
    
    label {
      display: block;
      margin-bottom: 8px;
      font-weight: bold;
    }
    
    input {
      width: 100%;
      padding: 10px;
      border-radius: 8px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      background-color: rgba(255, 255, 255, 0.05);
      color: #ffffff;
      box-sizing: border-box;
    }
    
    input:focus {
      outline: none;
      border-color: #121111;
      box-shadow: 0 0 0 2px rgba(18, 17, 17, 0.3);
    }
    
    button {
      width: 100%;
      padding: 12px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
      font-weight: bold;
    }
    
    button:hover {
      background-color: #0069d9;
    }
    
    .error-message {
      color: #ff6b6b;
      margin-top: 20px;
      text-align: center;
      display: none;
    }
    
    .success-message {
      color: #51cf66;
      margin-top: 20px;
      text-align: center;
      display: none;
    }
  </style>
</head>
<body>
  <div class="login-container">
    <h1>Test d'authentification Odoo</h1>
    
    <form id="login-form">
      <div class="form-group">
        <label for="server-url">URL du serveur</label>
        <input type="text" id="server-url" placeholder="http://localhost:8069" value="http://localhost:8069" required>
      </div>
      
      <div class="form-group">
        <label for="db-name">Base de données</label>
        <input type="text" id="db-name" placeholder="ligne-digitale" value="ligne-digitale" required>
      </div>
      
      <div class="form-group">
        <label for="username">Nom d'utilisateur</label>
        <input type="text" id="username" placeholder="admin" required>
      </div>
      
      <div class="form-group">
        <label for="password">Mot de passe</label>
        <input type="password" id="password" required>
      </div>
      
      <button type="submit">Se connecter</button>
    </form>
    
    <div id="error-message" class="error-message"></div>
    <div id="success-message" class="success-message"></div>
  </div>
  
  <script>
    // Ce script sera exécuté dans le contexte du renderer process
    // Il nécessite que nodeIntegration soit activé
    
    // Vérifier si nous sommes dans un environnement Electron
    if (typeof window.require === 'function') {
      const { ipcRenderer } = require('electron');
      
      document.getElementById('login-form').addEventListener('submit', async (event) => {
        event.preventDefault();
        
        // Récupérer les valeurs du formulaire
        const serverUrl = document.getElementById('server-url').value;
        const dbName = document.getElementById('db-name').value;
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        
        // Masquer les messages précédents
        document.getElementById('error-message').style.display = 'none';
        document.getElementById('success-message').style.display = 'none';
        
        // Désactiver le bouton pendant l'authentification
        const submitButton = event.target.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        submitButton.textContent = 'Connexion en cours...';
        
        try {
          // Envoyer les informations d'authentification au processus principal
          ipcRenderer.send('authenticate-odoo', {
            username,
            password,
            serverUrl,
            dbName
          });
          
          // Écouter la réponse du processus principal
          ipcRenderer.once('authenticate-odoo-response', (event, response) => {
            // Réactiver le bouton
            submitButton.disabled = false;
            submitButton.textContent = 'Se connecter';
            
            if (response.success) {
              // Afficher un message de succès
              const successMessage = document.getElementById('success-message');
              successMessage.textContent = `Authentification réussie! Utilisateur: ${response.name}, UID: ${response.userId}`;
              successMessage.style.display = 'block';
              
              // Rediriger vers l'interface Odoo après un court délai
              setTimeout(() => {
                ipcRenderer.send('load-odoo-interface', response);
              }, 1500);
            } else {
              // Afficher un message d'erreur
              const errorMessage = document.getElementById('error-message');
              errorMessage.textContent = `Erreur: ${response.error}`;
              errorMessage.style.display = 'block';
            }
          });
        } catch (error) {
          // Réactiver le bouton
          submitButton.disabled = false;
          submitButton.textContent = 'Se connecter';
          
          // Afficher un message d'erreur
          const errorMessage = document.getElementById('error-message');
          errorMessage.textContent = `Erreur: ${error.message}`;
          errorMessage.style.display = 'block';
        }
      });
    } else {
      // Afficher un message d'erreur si nous ne sommes pas dans Electron
      document.getElementById('error-message').textContent = 'Cette page doit être exécutée dans Electron avec nodeIntegration activé.';
      document.getElementById('error-message').style.display = 'block';
      document.querySelector('button[type="submit"]').disabled = true;
    }
  </script>
</body>
</html>
