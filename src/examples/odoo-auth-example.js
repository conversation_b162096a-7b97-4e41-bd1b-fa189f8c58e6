/**
 * Exemple d'utilisation de la fonction authenticateAndRedirectToOdoo
 * Ce fichier montre comment utiliser la fonction pour authentifier un utilisateur
 * et rediriger vers l'interface principale d'Odoo
 */

// Importer les modules nécessaires
const { BrowserWindow } = require('electron');
const { authenticateAndRedirectToOdoo } = require('../main/odoo-auth');
const log = require('electron-log');

/**
 * Fonction d'exemple pour authentifier un utilisateur et charger l'interface Odoo
 * @param {string} username - Nom d'utilisateur ou email
 * @param {string} password - Mot de passe
 * @param {string} serverUrl - URL du serveur Odoo (ex: http://localhost:8069)
 * @param {string} dbName - Nom de la base de données Odoo (par défaut: 'ligne-digitale')
 * @returns {Promise<void>}
 */
async function loginAndLoadOdooInterface(username, password, serverUrl, dbName = 'ligne-digitale') {
  try {
    log.info('Début du processus d\'authentification et de redirection');

    // Créer une fenêtre pour l'interface Odoo
    const window = new BrowserWindow({
      width: 1200,
      height: 800,
      show: false,
      backgroundColor: '#181818',
      frame: true,
      titleBarStyle: 'default',
      webPreferences: {
        contextIsolation: false,
        nodeIntegration: false,
        webSecurity: false
      }
    });

    // Afficher un message de chargement
    window.loadURL('data:text/html,<html><body style="background-color: #181818; color: white; font-family: Arial; display: flex; justify-content: center; align-items: center; height: 100vh;"><h2>Connexion en cours...</h2></body></html>');
    window.show();

    // Appeler la fonction d'authentification
    const authResult = await authenticateAndRedirectToOdoo(
      username,
      password,
      serverUrl,
      dbName,
      window
    );

    // Vérifier le résultat de l'authentification
    if (authResult.success) {
      log.info('Authentification réussie, interface Odoo chargée');
      log.info(`Utilisateur: ${authResult.name}, UID: ${authResult.userId}`);
      
      // La fenêtre a déjà été chargée avec l'interface Odoo par la fonction authenticateAndRedirectToOdoo
      return authResult;
    } else {
      log.error(`Échec de l'authentification: ${authResult.error}`);
      
      // Afficher un message d'erreur dans la fenêtre
      window.loadURL(`data:text/html,<html><body style="background-color: #181818; color: white; font-family: Arial; display: flex; justify-content: center; align-items: center; height: 100vh;"><h2>Erreur de connexion</h2><p>${authResult.error}</p></body></html>`);
      
      // Retourner le résultat de l'authentification
      return authResult;
    }
  } catch (error) {
    log.error('Erreur lors du processus d\'authentification et de redirection:', error);
    throw error;
  }
}

// Exemple d'utilisation
// loginAndLoadOdooInterface('admin', 'admin', 'http://localhost:8069', 'ligne-digitale')
//   .then(result => {
//     console.log('Résultat:', result);
//   })
//   .catch(error => {
//     console.error('Erreur:', error);
//   });

module.exports = {
  loginAndLoadOdooInterface
};
