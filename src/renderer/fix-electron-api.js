/**
 * <PERSON>ript pour corriger le problème de l'objet window.electron qui devient undefined
 * Ce script est injecté dans la page web pour s'assurer que l'objet window.electron reste disponible
 */

(function() {
  console.log('Démarrage du script de correction pour window.electron');

  // Sauvegarder une référence à l'objet window.electron original
  let savedElectron = null;

  // Fonction pour sauvegarder l'objet electron s'il existe
  function saveElectronObject() {
    if (window.electron && window.electron.ipcRenderer) {
      console.log('Sauvegarde de l\'objet window.electron');

      try {
        // Créer une copie des méthodes importantes
        savedElectron = {
          ipcRenderer: {}
        };

        // Copier les méthodes si elles existent
        if (typeof window.electron.ipcRenderer.invoke === 'function') {
          savedElectron.ipcRenderer.invoke = function() {
            return window.electron.ipcRenderer.invoke.apply(window.electron.ipcRenderer, arguments);
          };
        }

        if (typeof window.electron.ipcRenderer.send === 'function') {
          savedElectron.ipcRenderer.send = function() {
            return window.electron.ipcRenderer.send.apply(window.electron.ipcRenderer, arguments);
          };
        }

        if (typeof window.electron.ipcRenderer.on === 'function') {
          savedElectron.ipcRenderer.on = function() {
            return window.electron.ipcRenderer.on.apply(window.electron.ipcRenderer, arguments);
          };
        }

        console.log('Objet window.electron sauvegardé avec succès');
        return true;
      } catch (error) {
        console.error('Erreur lors de la sauvegarde de window.electron:', error);
        return false;
      }
    }
    return false;
  }

  // Fonction pour restaurer l'objet electron s'il a disparu
  function restoreElectronObject() {
    if (!window.electron && savedElectron) {
      console.log('Restauration de l\'objet window.electron');
      try {
        window.electron = savedElectron;
        console.log('Objet window.electron restauré avec succès');
        return true;
      } catch (error) {
        console.error('Erreur lors de la restauration de window.electron:', error);
        return false;
      }
    }
    return false;
  }

  // Sauvegarder l'objet electron initial s'il existe
  saveElectronObject();

  // Vérifier périodiquement si window.electron existe toujours
  setInterval(function() {
    if (!window.electron && savedElectron) {
      console.log('window.electron a disparu, tentative de restauration');
      restoreElectronObject();
    } else if (window.electron && !savedElectron) {
      console.log('window.electron est disponible mais non sauvegardé, sauvegarde en cours');
      saveElectronObject();
    }
  }, 1000);

  // Surveiller les modifications de window.electron
  if (window.electron) {
    console.log('window.electron est déjà disponible, surveillance des modifications');
  } else {
    console.log('window.electron n\'est pas encore disponible, attente de sa création');

    // Vérifier toutes les 500ms si window.electron a été créé
    let checkCount = 0;
    const maxChecks = 20; // 10 secondes maximum

    const checkInterval = setInterval(function() {
      checkCount++;

      if (window.electron) {
        console.log('window.electron a été créé, sauvegarde en cours');
        saveElectronObject();
        clearInterval(checkInterval);
      } else if (checkCount >= maxChecks) {
        console.log('window.electron n\'a pas été créé après plusieurs tentatives');
        clearInterval(checkInterval);
      }
    }, 500);
  }
})();
