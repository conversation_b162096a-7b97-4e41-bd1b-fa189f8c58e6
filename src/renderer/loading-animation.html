<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Edara - Chargement</title>
  <style>
    /* Variables CSS pour le thème clair */
    :root {
      --background-color: #f5f5f5;
      --text-color: #333333;
      --progress-bar-background: #e0e0e0;
      --progress-bar-fill: #585765;
      --logo-shadow: rgba(0, 0, 0, 0.1);
    }

    /* Variables CSS pour le thème sombre */
    .dark-theme {
      --background-color: #2d3436;
      --text-color: #f5f6fa;
      --progress-bar-background: #3d4548;
      --progress-bar-fill: #585765;
      --logo-shadow: rgba(0, 0, 0, 0.3);
    }

    /* Appliquer le thème sombre si le système est en mode sombre */
    @media (prefers-color-scheme: dark) {
      body {
        --background-color: #2d3436;
        --text-color: #f5f6fa;
        --progress-bar-background: #3d4548;
        --progress-bar-fill: #585765;
        --logo-shadow: rgba(0, 0, 0, 0.3);
      }
    }

    html, body {
      margin: 0;
      padding: 0;
      height: 100%;
      width: 100%;
      overflow: hidden;
      font-family: 'Arial', sans-serif;
      background-color: var(--background-color);
      color: var(--text-color);
      transition: background-color 0.3s ease;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100%;
      width: 100%;
    }

    .logo-container {
      position: relative;
      margin-bottom: 30px;
      transition: transform 0.5s ease;
    }

    .logo {
      width: 120px;
      height: 120px;
      object-fit: contain;
      filter: drop-shadow(0 4px 8px var(--logo-shadow));
      transition: all 0.5s ease;
    }

    .logo.initial {
      transform: scale(0.9);
    }

    .logo.grow {
      transform: scale(1);
    }

    .app-name {
      font-size: 24px;
      font-weight: 600;
      margin: 15px 0;
      opacity: 0;
      transform: translateY(10px);
      transition: opacity 0.5s ease, transform 0.5s ease;
    }

    .app-name.visible {
      opacity: 1;
      transform: translateY(0);
    }

    .loading-text {
      font-size: 16px;
      margin-bottom: 20px;
      opacity: 0;
      transform: translateY(10px);
      transition: opacity 0.5s ease, transform 0.5s ease;
    }

    .loading-text.visible {
      opacity: 1;
      transform: translateY(0);
    }

    .progress-container {
      width: 250px;
      margin-top: 10px;
    }

    .progress-bar {
      width: 100%;
      height: 4px;
      background-color: var(--progress-bar-background);
      border-radius: 2px;
      overflow: hidden;
      opacity: 0;
      transform: scaleX(0.9);
      transition: opacity 0.5s ease, transform 0.5s ease;
    }

    .progress-bar.visible {
      opacity: 1;
      transform: scaleX(1);
    }

    .progress-fill {
      height: 100%;
      width: 0%;
      background-color: var(--progress-bar-fill);
      border-radius: 2px;
      transition: width 0.3s ease-out;
    }

    /* Animation de pulsation pour le logo */
    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }

    .pulse {
      animation: pulse 2s infinite ease-in-out;
    }
  </style>
</head>
<body>
  <div class="loading-container">
    <div class="logo-container">
      <img src="../assets/images/icon.png" alt="Edara Logo" class="logo initial" id="logo">
    </div>
    <div class="app-name" id="appName">Edara Space</div>
    <div class="loading-text" id="loadingText">Préparation de l'interface...</div>
    <div class="progress-container">
      <div class="progress-bar" id="progressBar">
        <div class="progress-fill" id="progressFill"></div>
      </div>
    </div>
  </div>

  <script src="../renderer/loading-animation.js"></script>
</body>
</html>
