/**
 * <PERSON><PERSON>t d'aide pour la page de connexion
 * Ce script est injecté dans la page de connexion pour faciliter l'authentification
 */

(function() {
  console.log('Script d\'aide pour la page de connexion chargé');
  
  // Fonction pour corriger les problèmes de contenu mixte
  function fixMixedContent() {
    // Trouver tous les liens et formulaires qui utilisent HTTP
    const links = document.querySelectorAll('a[href^="http:"]');
    const forms = document.querySelectorAll('form[action^="http:"]');
    
    // Convertir les liens HTTP en HTTPS
    links.forEach(link => {
      const href = link.getAttribute('href');
      if (href && href.startsWith('http:')) {
        const newHref = href.replace('http:', 'https:');
        console.log(`Conversion du lien de ${href} à ${newHref}`);
        link.setAttribute('href', newHref);
      }
    });
    
    // Convertir les actions de formulaire HTTP en HTTPS
    forms.forEach(form => {
      const action = form.getAttribute('action');
      if (action && action.startsWith('http:')) {
        const newAction = action.replace('http:', 'https:');
        console.log(`Conversion de l'action du formulaire de ${action} à ${newAction}`);
        form.setAttribute('action', newAction);
      }
    });
    
    // Intercepter les requêtes XMLHttpRequest pour convertir HTTP en HTTPS
    const originalOpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
      let newUrl = url;
      if (typeof url === 'string' && url.startsWith('http:')) {
        newUrl = url.replace('http:', 'https:');
        console.log(`Conversion de la requête XHR de ${url} à ${newUrl}`);
      }
      return originalOpen.call(this, method, newUrl, async, user, password);
    };
    
    // Intercepter les requêtes fetch pour convertir HTTP en HTTPS
    const originalFetch = window.fetch;
    window.fetch = function(input, init) {
      if (typeof input === 'string' && input.startsWith('http:')) {
        const newInput = input.replace('http:', 'https:');
        console.log(`Conversion de la requête fetch de ${input} à ${newInput}`);
        return originalFetch.call(this, newInput, init);
      }
      return originalFetch.call(this, input, init);
    };
    
    console.log('Correction des problèmes de contenu mixte terminée');
  }
  
  // Fonction pour améliorer la gestion des erreurs
  function enhanceErrorHandling() {
    // Intercepter les erreurs non gérées
    window.addEventListener('error', function(event) {
      console.log('Erreur détectée:', event.message);
      
      // Si l'erreur concerne une connexion, afficher un message plus convivial
      if (event.message.includes('network') || 
          event.message.includes('connection') || 
          event.message.includes('connexion') ||
          event.message.includes('Mixed Content')) {
        console.log('Erreur de connexion détectée, affichage d\'un message convivial');
        
        // Trouver l'élément d'erreur dans la page
        const errorElement = document.querySelector('.o_error_message, .alert-danger, .o_notification_content');
        if (errorElement) {
          errorElement.textContent = 'Problème de connexion détecté. Veuillez vérifier votre connexion Internet et réessayer.';
        }
        
        event.preventDefault();
      }
    });
    
    console.log('Amélioration de la gestion des erreurs terminée');
  }
  
  // Exécuter les fonctions d'amélioration
  try {
    fixMixedContent();
    enhanceErrorHandling();
    console.log('Script d\'aide pour la page de connexion exécuté avec succès');
  } catch (error) {
    console.error('Erreur lors de l\'exécution du script d\'aide pour la page de connexion:', error);
  }
})();
