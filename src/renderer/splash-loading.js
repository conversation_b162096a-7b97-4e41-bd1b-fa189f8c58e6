/**
 * Script pour l'écran de chargement de l'application Edara ERP
 * Ce script gère l'animation de la barre de chargement, la vérification de la connexion
 * au serveur Odoo et la transition vers l'interface principale.
 */

// Éléments DOM
const progressBar = document.getElementById('progressBar');
const loadingText = document.getElementById('loadingText');
const connectionTitle = document.getElementById('connectionTitle');
const retryButton = document.getElementById('retryButton');
const mainContainer = document.getElementById('mainContainer');

// Messages de chargement
const loadingMessages = [
  'Initialisation de l\'application',
  'Vérification de la connexion au serveur',
  'Chargement des paramètres système'
];

// État actuel
let currentMessageIndex = 0;
let progress = 0;
let isConnecting = true;
let checkingServer = false;

// Configuration
const SERVER_URL = 'http://localhost:8069';
const CONNECTION_TIMEOUT = 5000; // 5 secondes

// Fonction pour mettre à jour la barre de progression
function updateProgressBar(value) {
  if (progressBar) {
    progressBar.style.width = `${value}%`;
  }
}

// Fonction pour mettre à jour le message de chargement
function updateLoadingMessage(index) {
  if (loadingText) {
    loadingText.textContent = loadingMessages[index];
    loadingText.classList.add('animate-dots');
  }
}

// Fonction pour vérifier la connexion au serveur Odoo
async function checkServerConnection() {
  if (checkingServer) return;
  checkingServer = true;

  console.log('Vérification de la connexion au serveur:', SERVER_URL);

  try {
    // Tentative de connexion au serveur Odoo
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), CONNECTION_TIMEOUT);

    const response = await fetch(`${SERVER_URL}/web/webclient/version_info`, {
      method: 'GET',
      headers: { 'Accept': 'application/json' },
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      console.log('Connexion au serveur établie avec succès');
      connectionSuccessful();
    } else {
      console.error('Erreur de connexion au serveur:', response.status);
      connectionFailed();
    }
  } catch (error) {
    console.error('Erreur lors de la vérification de la connexion:', error.message);
    connectionFailed();
  } finally {
    checkingServer = false;
  }
}

// Fonction appelée lorsque la connexion est établie
function connectionSuccessful() {
  // Mettre à jour le message
  if (loadingText) {
    loadingText.textContent = 'Connexion établie avec succès.';
    loadingText.classList.remove('animate-dots');
  }

  // Mettre à jour la barre de progression
  updateProgressBar(100);

  // Après un court délai, lancer l'application principale
  setTimeout(() => {
    console.log('Chargement terminé, notification au processus principal');

    // Vérifier si nous sommes dans un contexte post-connexion
    const urlParams = new URLSearchParams(window.location.search);
    const isPostLogin = urlParams.get('context') === 'post-login';

    if (window.electronAPI) {
      // Utiliser le canal approprié selon le contexte
      if (isPostLogin) {
        if (window.electronAPI.notifyLoadingScreenDone) {
          console.log('Envoi du message loading-screen-done avec données');
          window.electronAPI.notifyLoadingScreenDone({
            timestamp: new Date().toISOString(),
            status: 'complete',
            context: 'post-login'
          });
        } else {
          console.log('Envoi du message loading-screen-done via fallback');
          window.electronAPI.sendAuthData({ action: 'loading-screen-done' });
        }
      } else {
        console.log('Envoi du message splash-finished');
        window.electronAPI.sendAuthData({ action: 'splash-finished' });
      }
    } else {
      console.warn('API Electron non disponible');
    }
  }, 1000);
}

// Fonction appelée lorsque la connexion échoue
function connectionFailed() {
  console.error('Échec de la connexion au serveur');

  // Mettre à jour l'interface pour afficher l'erreur
  if (mainContainer) mainContainer.classList.add('error-state');
  if (connectionTitle) connectionTitle.textContent = 'Problème de connexion détecté !';

  if (loadingText) {
    loadingText.textContent = 'Impossible de joindre le serveur.\nVeuillez réessayer';
    loadingText.classList.remove('animate-dots');
    loadingText.style.whiteSpace = 'pre-line';
  }

  // Afficher le bouton Réessayer
  if (retryButton) retryButton.style.display = 'block';

  // Arrêter l'animation de la barre de progression
  isConnecting = false;
}

// Fonction pour redémarrer le processus de connexion
function retryConnection() {
  console.log('Tentative de reconnexion au serveur');

  // Réinitialiser l'interface
  if (mainContainer) mainContainer.classList.remove('error-state');
  if (connectionTitle) connectionTitle.textContent = 'Connexion en cours';
  if (retryButton) retryButton.style.display = 'none';

  // Réinitialiser les variables
  currentMessageIndex = 0;
  progress = 0;
  isConnecting = true;

  // Redémarrer l'animation
  updateLoadingMessage(currentMessageIndex);
  updateProgressBar(progress);

  // Redémarrer le processus
  startConnectionProcess();
}

// Fonction principale pour démarrer le processus de connexion
function startConnectionProcess() {
  console.log('Démarrage du processus de connexion');

  // Mettre à jour la barre de progression progressivement
  const progressInterval = setInterval(() => {
    if (!isConnecting) {
      clearInterval(progressInterval);
      return;
    }

    progress += 1;
    updateProgressBar(progress);

    // Changer de message à certains pourcentages
    if (progress === 30 && currentMessageIndex < 1) {
      currentMessageIndex = 1;
      updateLoadingMessage(currentMessageIndex);
    } else if (progress === 60 && currentMessageIndex < 2) {
      currentMessageIndex = 2;
      updateLoadingMessage(currentMessageIndex);
    } else if (progress === 80) {
      // Vérifier la connexion au serveur
      checkServerConnection();
    }

    // Arrêter à 90% et attendre la vérification de connexion
    if (progress >= 90) {
      clearInterval(progressInterval);
    }
  }, 50);
}

// Initialisation au chargement de la page
document.addEventListener('DOMContentLoaded', () => {
  console.log('Écran de chargement initialisé');

  // Initialiser la barre de progression à 0%
  updateProgressBar(0);

  // Ajouter un écouteur d'événement pour le bouton Réessayer
  if (retryButton) {
    retryButton.addEventListener('click', retryConnection);
  }

  // Appliquer le thème sombre si le système est en mode sombre
  if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
    document.body.classList.add('dark-theme');
    console.log('Thème sombre appliqué en fonction des préférences du système');
  }

  // Démarrer le processus de connexion
  startConnectionProcess();

  // Exposer la fonction de redémarrage pour l'API Electron
  if (window.electronAPI) {
    window.electronAPI.onRetryConnection = retryConnection;
  }
});
