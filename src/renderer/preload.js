/**
 * Script de préchargement pour l'application Edara ERP
 * Ce fichier est chargé avant le processus de rendu et permet une communication
 * sécurisée entre le processus principal et le processus de rendu
 * en utilisant contextBridge pour exposer des API spécifiques.
 */

const { contextBridge, ipcRenderer } = require('electron');

// Variable pour stocker l'URL du serveur (accessible uniquement dans le contexte du preload)
let lastServerUrl = null;

// Fonction de journalisation avec horodatage
function logWithTimestamp(message, ...args) {
  console.log(`[${new Date().toISOString()}] ${message}`, ...args);
}

// Supprimer les avertissements liés aux source maps manquants
const originalConsoleError = console.error;
console.error = function(...args) {
  // Filtrer les erreurs liées aux source maps
  const errorMessage = args.join(' ');
  if (errorMessage.includes('Could not load content for') &&
      (errorMessage.includes('.map') || errorMessage.includes('source map'))) {
    // Ignorer les erreurs liées aux source maps
    return;
  }
  // Laisser passer les autres erreurs
  return originalConsoleError.apply(console, args);
};

// Exposer des API sécurisées au processus de rendu via contextBridge
contextBridge.exposeInMainWorld('electronAPI', {
  // API pour authentifier un utilisateur via l'API /web/session/authenticate et rediriger vers /web
  authenticateAndRedirect: (username, password, serverUrl, dbName = 'ligne-digitale') => {
    logWithTimestamp(`Authentification via API /web/session/authenticate pour l'utilisateur ${username} sur ${serverUrl}`);
    return ipcRenderer.invoke('authenticate-and-redirect', { username, password, serverUrl, dbName });
  },

  // API pour envoyer des données d'authentification au processus principal
  sendAuthData: (data) => {
    logWithTimestamp('Envoi des données d\'authentification au processus principal');
    ipcRenderer.send('auth-data', data);
  },

  // API pour supprimer un cookie d'un domaine spécifique (avec réponse)
  removeCookie: (url, name) => {
    logWithTimestamp(`Suppression du cookie ${name} pour ${url}`);
    return ipcRenderer.invoke('remove-cookie', { url, name });
  },

  // API pour sélectionner un dossier de sauvegarde
  selectBackupFolder: () => {
    logWithTimestamp('Sélection d\'un dossier de sauvegarde');
    return ipcRenderer.invoke('select-backup-folder');
  },

  // API pour enregistrer le chemin de sauvegarde
  saveBackupPath: (backupPath) => {
    logWithTimestamp('Enregistrement du chemin de sauvegarde:', backupPath);
    return ipcRenderer.invoke('save-backup-path', backupPath);
  },

  // API pour écouter l'événement load-odoo-interface
  onLoadOdooInterface: (callback) => {
    logWithTimestamp('Enregistrement du callback pour l\'événement load-odoo-interface');
    const unsubscribe = (listener) => {
      ipcRenderer.removeListener('load-odoo-interface', listener);
    };

    const listener = (_, data) => callback(data);
    ipcRenderer.on('load-odoo-interface', listener);

    // Retourner une fonction pour se désabonner
    return () => unsubscribe(listener);
  },

  // API pour se déconnecter (via le main process)
  logout: (server) => {
    logWithTimestamp('Demande de déconnexion envoyée au processus principal');
    try {
      // Utiliser send au lieu de invoke pour ne pas attendre de réponse
      // car la fenêtre sera rechargée immédiatement
      ipcRenderer.send('auth-data', {
        action: 'logout',
        server: server || lastServerUrl || window.location.origin
      });
      return { success: true };
    } catch (error) {
      console.error('Erreur lors de la déconnexion via IPC:', error);
      return {
        success: false,
        error: error.message || 'Erreur de communication avec le processus principal',
        errorType: 'IPC_ERROR'
      };
    }
  },

  // API pour écouter les changements de thème
  onThemeChanged: (callback) => {
    logWithTimestamp('Enregistrement du callback pour les changements de thème');
    const listener = (_, theme) => callback(theme);
    ipcRenderer.on('theme-changed', listener);
    return () => ipcRenderer.removeListener('theme-changed', listener);
  },

  // API pour signaler que l'écran de chargement est terminé
  sendLoadingScreenDone: (data) => {
    logWithTimestamp('Envoi du signal de fin de chargement au processus principal', data || '');
    ipcRenderer.send('loading-screen-done', data);
  },

  // API pour signaler que l'écran de démarrage est prêt
  splashReady: () => {
    logWithTimestamp('Envoi du signal de préparation de l\'écran de démarrage au processus principal');
    ipcRenderer.send('splash-ready');
  },

  // API pour obtenir des informations sur les serveurs disponibles
  getServerInfo: () => {
    logWithTimestamp('Récupération des informations sur les serveurs disponibles');
    return ipcRenderer.invoke('check-server-connection');
  },

  // API pour récupérer la dernière adresse IP locale valide
  getLastValidIp: () => {
    logWithTimestamp('Récupération de la dernière adresse IP locale valide');
    return ipcRenderer.invoke('get-last-valid-ip');
  },

  // API pour signaler que la connexion au serveur est réussie
  connectionSuccessful: (serverUrl) => {
    logWithTimestamp('Connexion au serveur réussie, envoi du signal au processus principal');
    ipcRenderer.send('connection-successful', { serverUrl: serverUrl || '' });
  },

  // API pour écouter les événements de déconnexion
  onLogoutComplete: (callback) => {
    logWithTimestamp('Enregistrement du callback pour l\'événement de déconnexion');
    const listener = (event) => {
      logWithTimestamp('Événement de déconnexion reçu');
      callback(event);
    };
    ipcRenderer.on('logout-complete', listener);
    return () => ipcRenderer.removeListener('logout-complete', listener);
  },

  // API pour écouter les erreurs d'authentification
  onAuthError: (callback) => {
    logWithTimestamp('Enregistrement du callback pour les erreurs d\'authentification');
    const listener = (_, message) => callback(message);
    ipcRenderer.on('auth-error', listener);
    return () => ipcRenderer.removeListener('auth-error', listener);
  }
});

// Exposer une API pour la page web Odoo
contextBridge.exposeInMainWorld('edaraAPI', {
  // API pour se déconnecter
  logout: () => {
    logWithTimestamp('Déconnexion demandée par la page web via window.edaraAPI.logout()');

    // Déterminer l'URL du serveur
    const serverUrl = lastServerUrl || window.location.origin;
    logWithTimestamp(`Envoi de la demande de déconnexion au processus principal avec serverUrl: ${serverUrl}`);

    // Envoyer la demande de déconnexion au processus principal
    ipcRenderer.send('auth-data', {
      action: 'logout',
      server: serverUrl
    });

    return { success: true, message: 'Demande de déconnexion envoyée au processus principal' };
  },

  // API pour créer une fenêtre Odoo avec un cookie de session injecté
  createOdooWindow: (sessionId, serverUrl = 'http://localhost:8069', path = '/web') => {
    const sessionIdPreview = sessionId ? sessionId.substring(0, 8) + '...' : 'undefined';
    logWithTimestamp(`Création d'une fenêtre Odoo avec session injectée: ${sessionIdPreview}`);

    return ipcRenderer.invoke('create-odoo-window', {
      sessionId,
      serverUrl,
      path
    });
  },

  // API pour récupérer le cookie session_id actuel
  getSessionId: () => {
    logWithTimestamp('Récupération du cookie session_id');

    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === 'session_id') {
        const valuePreview = value ? value.substring(0, 8) + '...' : 'undefined';
        logWithTimestamp(`Cookie session_id trouvé: ${valuePreview}`);
        return value;
      }
    }

    logWithTimestamp('Cookie session_id non trouvé');
    return null;
  }
});

// Exposer une API de débogage pour vérifier que le preload est chargé
contextBridge.exposeInMainWorld('preloadDebug', {
  check: () => {
    logWithTimestamp('Le script preload.js est chargé avec contextIsolation: true');
    return 'Preload loaded successfully with contextIsolation enabled';
  },
  info: {
    loaded: true,
    timestamp: new Date().toISOString(),
    contextIsolation: true,
    apis: [
      'authenticateAndRedirect', 'sendAuthData', 'removeCookie', 'onLoadOdooInterface',
      'logout', 'onThemeChanged', 'sendLoadingScreenDone', 'splashReady', 'getServerInfo',
      'getLastValidIp', 'onLogoutComplete', 'onAuthError'
    ]
  }
});

// Événement DOMContentLoaded pour ajouter l'indicateur de preload
window.addEventListener('DOMContentLoaded', () => {
  logWithTimestamp('Événement DOMContentLoaded déclenché dans preload.js');

  // Ajouter un élément au DOM pour confirmer que le preload est chargé
  setTimeout(() => {
    try {
      const preloadIndicator = document.createElement('div');
      preloadIndicator.id = 'preload-indicator';
      preloadIndicator.style.display = 'none';
      preloadIndicator.dataset.loaded = 'true';
      preloadIndicator.dataset.timestamp = new Date().toISOString();
      preloadIndicator.dataset.contextIsolation = 'true';

      if (document.body) {
        document.body.appendChild(preloadIndicator);
        logWithTimestamp('Indicateur de preload ajouté au DOM');
      } else {
        console.warn('document.body non disponible, impossible d\'ajouter l\'indicateur de preload');
      }
    } catch (error) {
      console.error('Erreur lors de l\'ajout de l\'indicateur de preload:', error);
    }
  }, 1000);
});
