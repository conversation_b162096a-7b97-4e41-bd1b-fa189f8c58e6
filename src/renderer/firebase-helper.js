/**
 * Helper script for Firebase integration
 * This script helps load Firebase scripts in the Electron context
 */

// Function to load a script dynamically
function loadScript(url) {
  return new Promise((resolve, reject) => {
    console.log(`Attempting to load script: ${url}`);
    const script = document.createElement('script');
    script.src = url;
    script.onload = () => {
      console.log(`Successfully loaded script: ${url}`);
      resolve();
    };
    script.onerror = (error) => {
      console.error(`Failed to load script: ${url}`, error);
      reject(error);
    };
    document.head.appendChild(script);
  });
}

// Function to initialize Firebase
async function initializeFirebase() {
  try {
    console.log('Initializing Firebase helper');
    
    // Check if Firebase is already loaded
    if (window.firebase) {
      console.log('Firebase is already loaded');
      return;
    }
    
    // Load Firebase scripts
    await loadScript('https://www.gstatic.com/firebasejs/8.4.3/firebase-app.js');
    await loadScript('https://www.gstatic.com/firebasejs/8.4.3/firebase-messaging.js');
    
    console.log('Firebase scripts loaded successfully');
    
    // Initialize Firebase if needed
    // This would be where you'd put your Firebase configuration
    
  } catch (error) {
    console.error('Error initializing Firebase:', error);
  }
}

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('Firebase helper: DOM content loaded');
  initializeFirebase();
});

// Export functions for use in other scripts
window.firebaseHelper = {
  loadScript,
  initializeFirebase
};

console.log('Firebase helper script loaded');
