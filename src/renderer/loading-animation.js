/**
 * Script pour l'animation de chargement post-authentification
 * Ce script gère l'animation du logo, l'affichage progressif des éléments
 * et la barre de progression en fonction des messages du processus principal
 */

document.addEventListener('DOMContentLoaded', () => {
  console.log('Animation de chargement initialisée');

  // Récupérer les éléments du DOM
  const logo = document.getElementById('logo');
  const appName = document.getElementById('appName');
  const loadingText = document.getElementById('loadingText');
  const progressBar = document.getElementById('progressBar');
  const progressFill = document.getElementById('progressFill');

  // Appliquer le thème sombre si le système est en mode sombre
  if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
    document.body.classList.add('dark-theme');
    console.log('Thème sombre appliqué en fonction des préférences du système');
  }

  // Fonction pour mettre à jour la barre de progression
  const updateProgress = (percent) => {
    if (progressFill) {
      progressFill.style.width = `${percent}%`;
    }
  };

  // Fonction pour afficher un message de chargement
  const updateLoadingText = (message) => {
    if (loadingText) {
      loadingText.textContent = message;
    }
  };

  // Fonction pour signaler que le chargement est terminé
  const notifyLoadingComplete = () => {
    console.log('Animation de chargement terminée, notification au processus principal');
    
    // Ajouter une animation de pulsation finale au logo
    if (logo) {
      logo.classList.add('pulse');
    }
    
    // Mettre à jour le texte de chargement
    updateLoadingText('Chargement terminé !');
    
    // Remplir complètement la barre de progression
    updateProgress(100);
    
    // Attendre un court instant avant de signaler que le chargement est terminé
    setTimeout(() => {
      if (window.electronAPI) {
        if (window.electronAPI.notifyLoadingScreenDone) {
          window.electronAPI.notifyLoadingScreenDone({
            timestamp: new Date().toISOString(),
            status: 'complete'
          });
        } else if (window.electronAPI.sendLoadingScreenDone) {
          window.electronAPI.sendLoadingScreenDone();
        } else {
          window.electronAPI.sendAuthData({ action: 'loading-screen-done' });
        }
      }
    }, 800); // Attendre 800ms pour que l'utilisateur voie l'animation complète
  };

  // Séquence d'animation initiale
  setTimeout(() => {
    // Faire grandir le logo
    if (logo) {
      logo.classList.remove('initial');
      logo.classList.add('grow');
    }
    
    // Afficher le nom de l'application avec un délai
    setTimeout(() => {
      if (appName) {
        appName.classList.add('visible');
      }
      
      // Afficher le texte de chargement avec un délai
      setTimeout(() => {
        if (loadingText) {
          loadingText.classList.add('visible');
        }
        
        // Afficher la barre de progression avec un délai
        setTimeout(() => {
          if (progressBar) {
            progressBar.classList.add('visible');
          }
          
          // Initialiser la barre de progression à 10%
          updateProgress(10);
          
          // Commencer à écouter les messages du processus principal
          setupMessageListeners();
        }, 200);
      }, 200);
    }, 300);
  }, 300);

  // Configurer les écouteurs de messages du processus principal
  const setupMessageListeners = () => {
    if (window.electronAPI && window.electronAPI.onLoadOdooInterface) {
      console.log('Configuration des écouteurs de messages pour la progression du chargement');
      
      window.electronAPI.onLoadOdooInterface((data) => {
        console.log('Message reçu du processus principal:', data);
        
        if (data.action === 'loading-progress') {
          // Mettre à jour la barre de progression
          if (data.percent !== undefined) {
            updateProgress(data.percent);
          }
          
          // Mettre à jour le texte de chargement si fourni
          if (data.message) {
            updateLoadingText(data.message);
          }
        } 
        else if (data.action === 'loading-complete') {
          // Signaler que le chargement est terminé
          notifyLoadingComplete();
        }
      });
    } else {
      console.warn('API Electron non disponible ou onLoadOdooInterface non défini');
      
      // Simuler une progression si l'API n'est pas disponible
      let simulatedProgress = 10;
      
      const simulateProgress = () => {
        if (simulatedProgress < 90) {
          simulatedProgress += 5;
          updateProgress(simulatedProgress);
          setTimeout(simulateProgress, 300);
        } else {
          // Simuler la fin du chargement après avoir atteint 90%
          setTimeout(() => {
            notifyLoadingComplete();
          }, 1000);
        }
      };
      
      // Démarrer la simulation de progression
      setTimeout(simulateProgress, 1000);
    }
  };
});
