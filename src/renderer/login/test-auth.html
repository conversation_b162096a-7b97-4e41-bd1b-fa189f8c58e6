<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test de l'API d'authentification Odoo</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
      color: #333;
    }
    
    h1 {
      color: #2c3e50;
      margin-bottom: 20px;
    }
    
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: #fff;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .section {
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid #eee;
    }
    
    .section:last-child {
      border-bottom: none;
    }
    
    h2 {
      color: #3498db;
      margin-top: 0;
    }
    
    .form-group {
      margin-bottom: 15px;
    }
    
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    
    input[type="text"],
    input[type="password"],
    textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
      font-family: inherit;
      font-size: 14px;
    }
    
    textarea {
      min-height: 100px;
      font-family: monospace;
    }
    
    button {
      background-color: #3498db;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s;
    }
    
    button:hover {
      background-color: #2980b9;
    }
    
    button:disabled {
      background-color: #95a5a6;
      cursor: not-allowed;
    }
    
    .success {
      background-color: #d4edda;
      color: #155724;
      padding: 15px;
      border-radius: 4px;
      margin-top: 15px;
    }
    
    .error {
      background-color: #f8d7da;
      color: #721c24;
      padding: 15px;
      border-radius: 4px;
      margin-top: 15px;
    }
    
    pre {
      background-color: #f8f9fa;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
      font-size: 13px;
    }
    
    .event {
      background-color: #e8f4f8;
      padding: 10px;
      border-radius: 4px;
      margin-bottom: 10px;
    }
    
    .event pre {
      margin: 5px 0 0 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Test de l'API d'authentification Odoo</h1>
    
    <div class="section">
      <h2>Authentification</h2>
      <div class="form-group">
        <label for="username">Nom d'utilisateur</label>
        <input type="text" id="username" placeholder="admin">
      </div>
      <div class="form-group">
        <label for="password">Mot de passe</label>
        <input type="password" id="password" placeholder="admin">
      </div>
      <div class="form-group">
        <label for="server">URL du serveur</label>
        <input type="text" id="server" placeholder="http://localhost:8069">
      </div>
      <div class="form-group">
        <label for="dbName">Base de données (optionnel)</label>
        <input type="text" id="dbName" placeholder="ligne-digitale">
      </div>
      <button id="loginBtn">Se connecter</button>
      <button id="validateBtn">Valider la session</button>
      <button id="logoutBtn" disabled>Se déconnecter</button>
    </div>
    
    <div class="section">
      <h2>Appel à l'API Odoo</h2>
      <div class="form-group">
        <label for="endpoint">Endpoint</label>
        <input type="text" id="endpoint" placeholder="/web/dataset/call_kw">
      </div>
      <div class="form-group">
        <label for="params">Paramètres (JSON)</label>
        <textarea id="params" placeholder='{"model": "res.users", "method": "search_read", "args": [[]], "kwargs": {"fields": ["name", "login"]}}'></textarea>
      </div>
      <button id="callApiBtn">Appeler l'API</button>
    </div>
    
    <div class="section">
      <h2>Résultat</h2>
      <div id="result">
        <p>Aucun résultat pour le moment.</p>
      </div>
    </div>
    
    <div class="section">
      <h2>Événements de session</h2>
      <div id="events">
        <p>Aucun événement pour le moment.</p>
      </div>
    </div>
  </div>
  
  <script src="test-auth-api.js"></script>
</body>
</html>
