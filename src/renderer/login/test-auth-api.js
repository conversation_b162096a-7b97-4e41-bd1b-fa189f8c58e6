/**
 * Script de test pour l'API d'authentification Odoo
 * Ce script permet de tester les nouvelles fonctions d'authentification exposées par le processus principal
 */

// Fonction pour tester l'authentification
async function testAuthentication() {
  console.log('Test de l\'authentification...');
  
  // Récupérer les valeurs des champs
  const username = document.getElementById('username').value;
  const password = document.getElementById('password').value;
  const server = document.getElementById('server').value;
  const dbName = document.getElementById('dbName').value || 'ligne-digitale';
  
  // Afficher les informations de test
  console.log(`Tentative d'authentification pour l'utilisateur ${username} sur ${server}`);
  console.log(`Base de données: ${dbName}`);
  
  try {
    // Appeler l'API d'authentification
    const result = await window.electronAPI.authenticate(username, password, server, dbName);
    
    // Afficher le résultat
    console.log('Résultat de l\'authentification:', result);
    
    if (result.success) {
      // Authentification réussie
      document.getElementById('result').innerHTML = `
        <div class="success">
          <h3>Authentification réussie</h3>
          <p><strong>Utilisateur:</strong> ${result.name} (${result.username})</p>
          <p><strong>UID:</strong> ${result.uid}</p>
          <p><strong>Session ID:</strong> ${result.session_id ? result.session_id.substring(0, 8) + '...' : 'Non disponible'}</p>
        </div>
      `;
      
      // Activer le bouton de déconnexion
      document.getElementById('logoutBtn').disabled = false;
    } else {
      // Authentification échouée
      document.getElementById('result').innerHTML = `
        <div class="error">
          <h3>Échec de l'authentification</h3>
          <p><strong>Erreur:</strong> ${result.error}</p>
          <p><strong>Type d'erreur:</strong> ${result.errorType}</p>
        </div>
      `;
    }
  } catch (error) {
    // Erreur lors de l'appel à l'API
    console.error('Erreur lors de l\'appel à l\'API d\'authentification:', error);
    
    document.getElementById('result').innerHTML = `
      <div class="error">
        <h3>Erreur lors de l'appel à l'API</h3>
        <p><strong>Message:</strong> ${error.message || 'Erreur inconnue'}</p>
      </div>
    `;
  }
}

// Fonction pour tester la validation de session
async function testValidateSession() {
  console.log('Test de la validation de session...');
  
  try {
    // Appeler l'API de validation de session
    const result = await window.electronAPI.validateSession();
    
    // Afficher le résultat
    console.log('Résultat de la validation de session:', result);
    
    if (result.valid) {
      // Session valide
      document.getElementById('result').innerHTML = `
        <div class="success">
          <h3>Session valide</h3>
          <p><strong>Utilisateur:</strong> ${result.name} (${result.username})</p>
          <p><strong>UID:</strong> ${result.uid}</p>
        </div>
      `;
      
      // Activer le bouton de déconnexion
      document.getElementById('logoutBtn').disabled = false;
    } else {
      // Session invalide
      document.getElementById('result').innerHTML = `
        <div class="error">
          <h3>Session invalide</h3>
          <p><strong>Erreur:</strong> ${result.error}</p>
          <p><strong>Type d'erreur:</strong> ${result.errorType}</p>
        </div>
      `;
      
      // Désactiver le bouton de déconnexion
      document.getElementById('logoutBtn').disabled = true;
    }
  } catch (error) {
    // Erreur lors de l'appel à l'API
    console.error('Erreur lors de l\'appel à l\'API de validation de session:', error);
    
    document.getElementById('result').innerHTML = `
      <div class="error">
        <h3>Erreur lors de l'appel à l'API</h3>
        <p><strong>Message:</strong> ${error.message || 'Erreur inconnue'}</p>
      </div>
    `;
  }
}

// Fonction pour tester la déconnexion
async function testLogout() {
  console.log('Test de la déconnexion...');
  
  try {
    // Appeler l'API de déconnexion
    const result = await window.electronAPI.logout();
    
    // Afficher le résultat
    console.log('Résultat de la déconnexion:', result);
    
    if (result.success) {
      // Déconnexion réussie
      document.getElementById('result').innerHTML = `
        <div class="success">
          <h3>Déconnexion réussie</h3>
          <p><strong>Message:</strong> ${result.message}</p>
          ${result.warning ? `<p><strong>Avertissement:</strong> ${result.warning}</p>` : ''}
        </div>
      `;
      
      // Désactiver le bouton de déconnexion
      document.getElementById('logoutBtn').disabled = true;
    } else {
      // Déconnexion échouée
      document.getElementById('result').innerHTML = `
        <div class="error">
          <h3>Échec de la déconnexion</h3>
          <p><strong>Erreur:</strong> ${result.error}</p>
          <p><strong>Type d'erreur:</strong> ${result.errorType}</p>
        </div>
      `;
    }
  } catch (error) {
    // Erreur lors de l'appel à l'API
    console.error('Erreur lors de l\'appel à l\'API de déconnexion:', error);
    
    document.getElementById('result').innerHTML = `
      <div class="error">
        <h3>Erreur lors de l'appel à l'API</h3>
        <p><strong>Message:</strong> ${error.message || 'Erreur inconnue'}</p>
      </div>
    `;
  }
}

// Fonction pour tester un appel à l'API Odoo
async function testCallApi() {
  console.log('Test d\'appel à l\'API Odoo...');
  
  // Récupérer les valeurs des champs
  const endpoint = document.getElementById('endpoint').value;
  const paramsStr = document.getElementById('params').value;
  
  // Valider l'endpoint
  if (!endpoint) {
    document.getElementById('result').innerHTML = `
      <div class="error">
        <h3>Erreur</h3>
        <p>Veuillez spécifier un endpoint</p>
      </div>
    `;
    return;
  }
  
  // Parser les paramètres JSON
  let params;
  try {
    params = paramsStr ? JSON.parse(paramsStr) : {};
  } catch (error) {
    document.getElementById('result').innerHTML = `
      <div class="error">
        <h3>Erreur de parsing JSON</h3>
        <p>${error.message}</p>
      </div>
    `;
    return;
  }
  
  // Afficher les informations de test
  console.log(`Appel à l'API Odoo: ${endpoint}`);
  console.log('Paramètres:', params);
  
  try {
    // Appeler l'API Odoo
    const result = await window.electronAPI.callOdooApi(endpoint, params);
    
    // Afficher le résultat
    console.log('Résultat de l\'appel à l\'API:', result);
    
    if (result.success) {
      // Appel réussi
      document.getElementById('result').innerHTML = `
        <div class="success">
          <h3>Appel à l'API réussi</h3>
          <pre>${JSON.stringify(result.result, null, 2)}</pre>
        </div>
      `;
    } else {
      // Appel échoué
      document.getElementById('result').innerHTML = `
        <div class="error">
          <h3>Échec de l'appel à l'API</h3>
          <p><strong>Erreur:</strong> ${result.error}</p>
          <p><strong>Type d'erreur:</strong> ${result.errorType}</p>
        </div>
      `;
      
      // Si la session est expirée, désactiver le bouton de déconnexion
      if (result.errorType === 'SESSION_EXPIRED') {
        document.getElementById('logoutBtn').disabled = true;
      }
    }
  } catch (error) {
    // Erreur lors de l'appel à l'API
    console.error('Erreur lors de l\'appel à l\'API Odoo:', error);
    
    document.getElementById('result').innerHTML = `
      <div class="error">
        <h3>Erreur lors de l'appel à l'API</h3>
        <p><strong>Message:</strong> ${error.message || 'Erreur inconnue'}</p>
      </div>
    `;
  }
}

// Initialiser les écouteurs d'événements lorsque le DOM est chargé
document.addEventListener('DOMContentLoaded', () => {
  // Écouteur pour le bouton d'authentification
  document.getElementById('loginBtn').addEventListener('click', testAuthentication);
  
  // Écouteur pour le bouton de validation de session
  document.getElementById('validateBtn').addEventListener('click', testValidateSession);
  
  // Écouteur pour le bouton de déconnexion
  document.getElementById('logoutBtn').addEventListener('click', testLogout);
  
  // Écouteur pour le bouton d'appel à l'API
  document.getElementById('callApiBtn').addEventListener('click', testCallApi);
  
  // Vérifier s'il existe une session au chargement
  testValidateSession();
  
  // Écouter les événements de session
  window.electronAPI.onSessionEvent((event) => {
    console.log('Événement de session reçu:', event);
    
    // Afficher l'événement
    document.getElementById('events').innerHTML += `
      <div class="event">
        <p><strong>Type:</strong> ${event.type}</p>
        <pre>${JSON.stringify(event.data || {}, null, 2)}</pre>
      </div>
    `;
    
    // Traiter l'événement
    switch (event.type) {
      case 'session-updated':
      case 'session-validated':
        // Activer le bouton de déconnexion
        document.getElementById('logoutBtn').disabled = false;
        break;
      case 'session-invalid':
      case 'session-expired':
      case 'logged-out':
        // Désactiver le bouton de déconnexion
        document.getElementById('logoutBtn').disabled = true;
        break;
    }
  });
});
