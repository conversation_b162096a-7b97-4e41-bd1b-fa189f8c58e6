<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exemple d'authentification Odoo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #272B30;
            color: #fff;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
        }
        .container {
            background-color: #1F1E1E;
            border-radius: 8px;
            padding: 30px;
            width: 400px;
            max-width: 90%;
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            background-color: rgba(255, 255, 255, 0.05);
            color: #fff;
            box-sizing: border-box;
        }
        input:focus {
            outline: none;
            border-color: #121111;
        }
        button {
            width: 100%;
            padding: 12px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0069d9;
        }
        .error-message {
            color: #ff6b6b;
            margin-top: 20px;
            text-align: center;
        }
        .success-message {
            color: #51cf66;
            margin-top: 20px;
            text-align: center;
        }
        .loading {
            text-align: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Authentification Odoo</h1>
        <form id="loginForm">
            <div class="form-group">
                <label for="serverUrl">URL du serveur</label>
                <input type="text" id="serverUrl" name="serverUrl" placeholder="http://localhost:8069" required>
            </div>
            <div class="form-group">
                <label for="dbName">Base de données</label>
                <input type="text" id="dbName" name="dbName" value="ligne-digitale" required>
            </div>
            <div class="form-group">
                <label for="username">Nom d'utilisateur</label>
                <input type="text" id="username" name="username" placeholder="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label for="password">Mot de passe</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit">Se connecter</button>
        </form>
        <div id="errorMessage" class="error-message" style="display: none;"></div>
        <div id="successMessage" class="success-message" style="display: none;"></div>
        <div id="loadingMessage" class="loading" style="display: none;">Authentification en cours...</div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const loginForm = document.getElementById('loginForm');
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');
            const loadingMessage = document.getElementById('loadingMessage');

            // Vérifier si l'API Electron est disponible
            if (!window.electronAPI || !window.electronAPI.authenticateAndRedirect) {
                errorMessage.textContent = "L'API d'authentification n'est pas disponible. Vérifiez que le script preload.js est correctement chargé.";
                errorMessage.style.display = 'block';
                loginForm.style.display = 'none';
                return;
            }

            loginForm.addEventListener('submit', async (event) => {
                event.preventDefault();
                
                // Récupérer les valeurs du formulaire
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                const serverUrl = document.getElementById('serverUrl').value;
                const dbName = document.getElementById('dbName').value;
                
                // Masquer les messages précédents
                errorMessage.style.display = 'none';
                successMessage.style.display = 'none';
                
                // Afficher le message de chargement
                loadingMessage.style.display = 'block';
                
                try {
                    // Appeler la fonction d'authentification
                    console.log(`Tentative d'authentification pour l'utilisateur ${username} sur ${serverUrl}`);
                    const result = await window.electronAPI.authenticateAndRedirect(username, password, serverUrl, dbName);
                    
                    // Masquer le message de chargement
                    loadingMessage.style.display = 'none';
                    
                    if (result.success) {
                        // Authentification réussie
                        successMessage.textContent = `Authentification réussie pour ${result.name} (ID: ${result.userId})`;
                        successMessage.style.display = 'block';
                        
                        // Afficher des informations supplémentaires
                        console.log('Résultat de l\'authentification:', result);
                        
                        // La redirection vers l'interface Odoo est gérée par le processus principal
                    } else {
                        // Authentification échouée
                        errorMessage.textContent = result.error || 'Erreur d\'authentification inconnue';
                        errorMessage.style.display = 'block';
                        
                        // Afficher des informations supplémentaires
                        console.error('Erreur d\'authentification:', result);
                    }
                } catch (error) {
                    // Erreur lors de l'appel à la fonction d'authentification
                    loadingMessage.style.display = 'none';
                    errorMessage.textContent = `Erreur: ${error.message || 'Erreur inconnue'}`;
                    errorMessage.style.display = 'block';
                    console.error('Exception lors de l\'authentification:', error);
                }
            });
        });
    </script>
</body>
</html>
