<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <!-- La CSP est gérée par le main process, pas besoin de la définir ici -->
  <title>Edara ERP - Connexion en cours</title>
  <style>
    html, body {
      margin: 0;
      padding: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      font-family: Arial, sans-serif;
      background-color: #f5f5f5;
      color: #333;
    }

    .loading-container {
      text-align: center;
      padding: 20px;
      border-radius: 8px;
      background-color: white;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      border-top: 4px solid #3498db;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    h2 {
      color: #333;
      margin-bottom: 10px;
    }

    p {
      color: #666;
    }
  </style>
  <!-- Ajouter le script d'aide Firebase -->
  <script src="firebase-helper.js"></script>
</head>
<body>
  <div class="loading-container">
    <div class="spinner"></div>
    <h2>Connexion en cours...</h2>
  </div>
</body>
</html>
