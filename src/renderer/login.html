<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edara ERP - Connexion</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        /* Variables CSS pour le thème sombre (par défaut) */
        :root {
          --background-color: #181818;
          --text-color: #F8F9FA;
          --secondary-text-color: #DEE2E6;
          --button-background: #124559;
          --button-hover: #206f8c;
          --form-background: #1F1E1E;
          --input-background: rgba(255, 255, 255, 0.05);
          --input-border: rgba(255, 255, 255, 0.3);
          --input-focus: #124559;
          --input-text: #F8F9FA;
          --input-placeholder: rgba(255, 255, 255, 0.4);
          --error-color: #e74c3c;
          --success-color: #2ecc71;
          --separator-color: rgba(255, 255, 255, 0.1);
          --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
          --toggle-icon-color: #DEE2E6;
          --dropdown-background: #1F1E1E;
          --dropdown-text: #FFFFFF;
        }

        /* Variables CSS pour le thème clair */
        @media (prefers-color-scheme: light) {
          :root {
            --background-color: #F5F5F5;
            --text-color: #212529;
            --secondary-text-color: #6C757D;
            --button-background: #124559;
            --button-hover: #206f8c;
            --form-background: #FFFFFF;
            --input-background: #F8F9FA;
            --input-border: rgba(0, 0, 0, 0.2);
            --input-focus: #124559;
            --input-text: #212529;
            --input-placeholder: rgba(0, 0, 0, 0.4);
            --error-color: #e74c3c;
            --success-color: #2ecc71;
            --separator-color: rgba(0, 0, 0, 0.1);
            --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            --toggle-icon-color: #6C757D;
            --dropdown-background: #FFFFFF;
            --dropdown-text: #212529;
          }
        }

        /* Classes pour le basculement manuel du thème */
        .light-theme {
          --background-color: #F5F5F5;
          --text-color: #212529;
          --secondary-text-color: #6C757D;
          --button-background: #124559;
          --button-hover: #206f8c;
          --form-background: #FFFFFF;
          --input-background: #F8F9FA;
          --input-border: rgba(0, 0, 0, 0.2);
          --input-focus: #124559;
          --input-text: #212529;
          --input-placeholder: rgba(0, 0, 0, 0.4);
          --error-color: #e74c3c;
          --success-color: #2ecc71;
          --separator-color: rgba(0, 0, 0, 0.1);
          --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
          --toggle-icon-color: #6C757D;
          --dropdown-background: #FFFFFF;
          --dropdown-text: #212529;
        }

        .dark-theme {
          --background-color: #181818;
          --text-color: #F8F9FA;
          --secondary-text-color: #DEE2E6;
          --button-background: #124559;
          --button-hover: #206f8c;
          --form-background: #1F1E1E;
          --input-background: rgba(255, 255, 255, 0.05);
          --input-border: rgba(255, 255, 255, 0.3);
          --input-focus: #124559;
          --input-text: #F8F9FA;
          --input-placeholder: rgba(255, 255, 255, 0.4);
          --error-color: #e74c3c;
          --success-color: #2ecc71;
          --separator-color: rgba(255, 255, 255, 0.1);
          --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
          --toggle-icon-color: #DEE2E6;
          --dropdown-background: #1F1E1E;
          --dropdown-text: #FFFFFF;
        }

        /* Styles pour le bouton de basculement de thème */
        .theme-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            background: none;
            border: none;
            color: var(--text-color);
            font-size: 20px;
            cursor: pointer;
            z-index: 100;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }
        .theme-toggle:hover {
            opacity: 1;
        }

        /* Réinitialisation des styles */
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body, html {
          width: 100%;
          height: 100%;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
          background-color: var(--background-color);
          color: var(--text-color);
          font-size: 14px;
          line-height: 1.5;
          overflow: hidden;
          -webkit-app-region: no-drag; /* S'assurer que le corps n'est pas draggable pour permettre la barre native */
        }

        /* Conteneur principal */
        .edara-container {
          padding: 0;
          width: 100%;
          height: 100vh;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: var(--background-color);
          gap: 80px; /* Espacement horizontal entre les éléments */
          transition: background-color 0.3s ease;
        }

        /* Section gauche (formulaire) */
        .left-section {
          flex: 0 0 380px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          padding: 30px;
          position: relative;
          background-color: var(--form-background);
          border-radius: 7px;
          box-shadow: var(--box-shadow);
          transition: background-color 0.3s ease, box-shadow 0.3s ease;
        }

        /* Section droite (illustration) */
        .right-section {
          flex: 0 0 400px;
          display: flex;
          justify-content: center;
          align-items: center;
          overflow: hidden;
          position: relative;
        }

        /* Conteneur du formulaire */
        .login-form-container {
          width: 100%;
          max-width: 320px;
          display: flex;
          flex-direction: column;
        }

        /* Logo */
        .logo-container {
          display: flex;
          justify-content: center;
          margin-bottom: 15px;
          margin-top: 5px;
        }

        #edara-login-logo {
          width: 90px;
          height: 90px;
          object-fit: contain;
          filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
          transition: all 0.3s ease;
        }

        /* Séparateur */
        .separator {
          width: 100%;
          height: 1px;
          background-color: var(--separator-color);
          margin-bottom: 20px;
        }

        /* Titre */
        .login-title {
          font-size: 20px;
          font-weight: 500;
          margin-bottom: 25px;
          text-align: center;
          color: var(--text-color);
        }

        /* Formulaire */
        .edara-login-form {
          width: 100%;
        }

        /* Groupes de formulaire */
        .form-group {
          margin-bottom: 16px;
        }

        .form-group label {
          display: block;
          font-size: 13px;
          margin-bottom: 5px;
          color: var(--text-color);
          font-weight: 400;
        }

        /* Champs de saisie */
        .form-control {
          width: 100%;
          padding: 10px 12px;
          background-color: var(--input-background);
          border: 1px solid var(--input-border);
          border-radius: 6px;
          color: var(--input-text);
          font-size: 14px;
          height: 38px;
          transition: all 0.3s ease;
        }

        .form-control::placeholder {
          color: var(--input-placeholder);
          font-size: 13px;
        }

        .form-control:focus {
          outline: none;
          background-color: var(--input-background);
          border: 1px solid var(--button-background);
          box-shadow: 0 0 0 2px rgba(18, 69, 89, 0.25);
        }

        /* Select spécifique */
        select.form-control {
          appearance: none;
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%23ffffff' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
          background-repeat: no-repeat;
          background-position: right 12px center;
          padding-right: 30px;
        }

        [data-theme="light"] select.form-control {
          background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%23333333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
        }

        /* Conteneur de mot de passe */
        .password-container {
          position: relative;
        }

        .password-container .form-control {
          padding-right: 40px;
        }

        .toggle-password {
          position: absolute;
          right: 10px;
          top: 50%;
          transform: translateY(-50%);
          background: none;
          border: none;
          color: var(--secondary-text-color);
          cursor: pointer;
          font-size: 15px;
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0.8;
          transition: opacity 0.2s ease;
          padding: 0;
          width: 24px;
          height: 24px;
        }

        .toggle-password:focus {
          outline: none;
        }

        .toggle-password:hover {
          opacity: 1;
        }

        /* Bouton de connexion */
        .login-button {
          width: 100%;
          padding: 10px 15px;
          background-color: var(--button-background);
          color: #ffffff;
          border: none;
          border-radius: 4px;
          font-size: 15px;
          font-weight: 500;
          cursor: pointer;
          height: 40px;
          margin-top: 20px;
          transition: background-color 0.2s ease, transform 0.1s ease;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
        }

        .login-button:hover {
          background-color: var(--button-hover);
        }

        .login-button:active {
          transform: translateY(1px);
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        /* Conteneur d'illustration */
        .illustration-container {
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
        }

        .login-illustration {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
          opacity: 1;
        }

        /* Styles spécifiques pour le groupe d'instance */
        .instance-group label {
          font-weight: 500;
        }

        .instance-select {
          font-weight: 500;
          position: relative;
          z-index: 10;
          top: 0;
          left: 0;
          transform: translateY(2px); /* Déplace le select légèrement vers le bas */
        }

        .instance-option {
          font-weight: 500;
          background-color: var(--dropdown-background);
          padding: 8px 12px;
          color: var(--dropdown-text);
        }

        /* Style pour le select au focus */
        select.form-control:focus {
          outline: none;
          border-color: var(--button-background);
          box-shadow: 0 0 0 2px rgba(18, 69, 89, 0.25);
          z-index: 20;
        }

        /* Ajustement de la position du menu déroulant */
        select.form-control {
          margin-bottom: 2px; /* Crée un petit espace sous le select */
        }

        select.form-control option {
          background-color: var(--dropdown-background);
          color: var(--dropdown-text);
          padding: 10px;
          margin: 5px;
          position: relative;
          top: 2px; /* Ajustez cette valeur pour déplacer les options vers le bas */
          left: 10px; /* Ajustez cette valeur pour déplacer les options vers la droite */
        }

        /* Message d'erreur */
        .error-message {
          color: var(--error-color);
          font-size: 13px;
          margin-top: 5px;
          margin-bottom: 10px;
          text-align: center;
          display: none;
        }

        /* Responsive design */
        @media (max-width: 992px) {
          .edara-login-container {
            width: 95%;
            max-width: 800px;
          }

          .left-section {
            flex: 0 0 340px;
          }
        }

        @media (max-width: 768px) {
          .edara-content-wrapper {
            flex-direction: column;
            height: auto;
          }

          .left-section {
            flex: none;
            width: 100%;
            order: 2;
            padding: 30px 20px;
          }

          .right-section {
            flex: none;
            width: 100%;
            order: 1;
            height: 200px;
          }

          .login-form-container {
            padding: 0;
          }

          .illustration-container {
            padding: 20px;
          }

          .login-illustration {
            max-height: 160px;
          }
        }

        @media (max-width: 480px) {
          .edara-login-container {
            width: 100%;
            height: 100%;
          }

          .edara-content-wrapper {
            border-radius: 0;
            box-shadow: none;
          }

          .left-section {
            padding: 20px;
          }

          .right-section {
            height: 150px;
          }

          .login-form-container {
            max-width: 100%;
          }

          #edara-login-logo {
            width: 70px;
            height: 70px;
          }

          .login-title {
            font-size: 20px;
            margin-bottom: 20px;
          }

          .form-group {
            margin-bottom: 15px;
          }

          .form-control, .login-button {
            height: 38px;
          }
        }
    </style>
</head>
<body>
    <!-- Bouton de basculement de thème -->
    <button id="theme-toggle" class="theme-toggle">
        <i class="fas fa-moon"></i>
    </button>

    <div class="edara-container">
        <!-- Section gauche : formulaire -->
        <div class="left-section">
            <!-- Cadre secondaire contenant le formulaire -->
            <div class="login-form-container">
                <!-- Logo -->
                <div class="logo-container">
                    <img id="edara-login-logo" src="../assets/images/logo-edara-claire.png" alt="Edara Logo">
                </div>

                <!-- Séparateur -->
                <div class="separator"></div>

                <!-- Titre -->
                <h1 class="login-title">Connexion</h1>

                <!-- Message d'erreur -->
                <div id="error-message" class="error-message">
                    Adresse email ou mot de passe incorrect
                </div>

                <!-- Formulaire -->
                <form class="edara-login-form" id="login-form">
                    <!-- Instance -->
                    <div class="form-group instance-group">
                        <label for="instance">Instance</label>
                        <select id="instance" class="form-control instance-select">
                            <option value="local" class="instance-option">Connexion Local</option>
                            <option value="distance" selected class="instance-option">Connexion Distance</option>
                        </select>
                    </div>

                    <!-- Email -->
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="text" id="email" class="form-control" placeholder="Nom d'utilisateur ou email">
                    </div>

                    <!-- Mot de passe -->
                    <div class="form-group">
                        <label for="password">Mot de passe</label>
                        <div class="password-container">
                            <input type="password" id="password" class="form-control" placeholder="Votre mot de passe">
                            <button type="button" id="toggle-password" class="toggle-password">
                                <i class="fas fa-eye-slash"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Bouton de connexion -->
                    <button type="submit" class="login-button">Se connecter</button>
                </form>
            </div>
        </div>

        <!-- Section droite : illustration -->
        <div class="right-section">
            <div class="illustration-container">
                <img src="../assets/images/edara_illustration.svg" alt="Edara Illustration" class="login-illustration">
            </div>
        </div>
    </div>

    <script>
        // Gestionnaire d'erreurs global pour capturer les erreurs dans la page de connexion
        window.onerror = function(message, source, lineno, colno, error) {
            console.error('ERREUR CAPTURÉE DANS LA PAGE DE CONNEXION:');
            console.error('Message:', message);
            console.error('Source:', source);
            console.error('Ligne:', lineno, 'Colonne:', colno);
            console.error('Stack trace:', error ? error.stack : 'Pas de stack trace disponible');

            // Envoyer l'erreur au processus principal
            if (window.electronAPI && typeof window.electronAPI.sendAuthData === 'function') {
                window.electronAPI.sendAuthData({
                    action: 'report-error',
                    error: {
                        message: message,
                        source: source,
                        lineno: lineno,
                        colno: colno,
                        stack: error ? error.stack : 'Pas de stack trace disponible'
                    }
                });
            }

            // Afficher l'erreur dans l'interface utilisateur
            const errorElement = document.getElementById('error-message');
            if (errorElement) {
                errorElement.textContent = 'Une erreur est survenue: ' + message;
                errorElement.style.display = 'block';
            }

            return true; // Empêcher l'affichage de l'erreur dans la console du navigateur
        };

        // Gestionnaire de rejets de promesses non gérés
        window.addEventListener('unhandledrejection', function(event) {
            console.error('PROMESSE REJETÉE NON GÉRÉE DANS LA PAGE DE CONNEXION:');
            console.error('Raison:', event.reason);

            // Envoyer l'erreur au processus principal
            if (window.electronAPI && typeof window.electronAPI.sendAuthData === 'function') {
                window.electronAPI.sendAuthData({
                    action: 'report-promise-rejection',
                    error: {
                        reason: event.reason ? event.reason.toString() : 'Raison inconnue',
                        stack: event.reason && event.reason.stack ? event.reason.stack : 'Pas de stack trace disponible'
                    }
                });
            }

            // Afficher l'erreur dans l'interface utilisateur
            const errorElement = document.getElementById('error-message');
            if (errorElement) {
                errorElement.textContent = 'Une erreur est survenue: ' + (event.reason ? event.reason.toString() : 'Erreur inconnue');
                errorElement.style.display = 'block';
            }

            event.preventDefault(); // Empêcher l'affichage de l'erreur dans la console du navigateur
        });

        document.addEventListener('DOMContentLoaded', function() {
            // Éléments DOM
            const togglePassword = document.getElementById('toggle-password');
            const passwordInput = document.getElementById('password');
            const loginForm = document.getElementById('login-form');
            const errorMessage = document.getElementById('error-message');
            const instanceSelect = document.getElementById('instance');
            const emailInput = document.getElementById('email');

            // Afficher/masquer le mot de passe
            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);

                // Changer l'icône
                this.querySelector('i').classList.toggle('fa-eye');
                this.querySelector('i').classList.toggle('fa-eye-slash');
            });

            // Variables pour la gestion du thème
            const themeToggleBtn = document.getElementById('theme-toggle');
            let userTheme = localStorage.getItem('theme');
            let systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';

            // Fonction pour définir le thème
            function setTheme(theme) {
                if (theme === 'dark') {
                    document.documentElement.classList.add('dark-theme');
                    document.documentElement.classList.remove('light-theme');
                    themeToggleBtn.innerHTML = '<i class="fas fa-sun"></i>';
                } else {
                    document.documentElement.classList.add('light-theme');
                    document.documentElement.classList.remove('dark-theme');
                    themeToggleBtn.innerHTML = '<i class="fas fa-moon"></i>';
                }

                // Mettre à jour le logo en fonction du thème
                const logoElement = document.getElementById('edara-login-logo');
                if (logoElement) {
                    logoElement.src = theme === 'dark'
                        ? '../assets/images/logo-edara-claire.png'
                        : '../assets/images/logo-edara-noire.png';
                }

                // Sauvegarder le thème choisi par l'utilisateur
                localStorage.setItem('theme', theme);
            }

            // Fonction pour détecter le thème du système
            function detectColorScheme() {
                const isDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
                systemTheme = isDarkMode ? 'dark' : 'light';

                // Appliquer le thème choisi par l'utilisateur ou le thème du système
                if (userTheme) {
                    setTheme(userTheme);
                } else {
                    setTheme(systemTheme);
                }
            }

            // Basculer le thème manuellement
            themeToggleBtn.addEventListener('click', function() {
                userTheme = document.documentElement.classList.contains('dark-theme') ? 'light' : 'dark';
                setTheme(userTheme);
            });

            // Détecter le thème initial
            detectColorScheme();

            // Écouter les changements de thème du système
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
                if (!userTheme) {
                    detectColorScheme();
                }
            });

            // Variables pour stocker les informations sur les serveurs
            let localServerUrl = null;
            let remoteServerUrl = null;

            // Fonction pour mettre à jour l'URL du serveur en fonction du type de connexion sélectionné
            function updateServerUrl() {
                const selectedConnectionType = instanceSelect.value;

                if (selectedConnectionType === 'local') {
                    // Pour la connexion locale, toujours utiliser l'URL locale, même si elle n'est pas disponible
                    // Cela permet à l'utilisateur de forcer une connexion locale même si la détection automatique a échoué
                    if (localServerUrl) {
                        console.log(`Utilisation de l'URL du serveur local: ${localServerUrl}`);
                        return localServerUrl;
                    } else {
                        // Valeur par défaut pour la connexion locale
                        console.log('URL du serveur local non disponible, utilisation de la valeur par défaut localhost');
                        return 'http://localhost:8069';
                    }
                } else if (selectedConnectionType === 'distance') {
                    if (remoteServerUrl) {
                        console.log(`Utilisation de l'URL du serveur distant: ${remoteServerUrl}`);
                        return remoteServerUrl;
                    } else {
                        // Valeur par défaut pour la connexion distante
                        console.log('URL du serveur distant non disponible, utilisation de la valeur par défaut');
                        return 'https://edara.ligne-digitale.com';
                    }
                } else {
                    console.warn(`Type de connexion non reconnu: ${selectedConnectionType}`);
                    // Valeur par défaut en cas de type de connexion non reconnu
                    return 'https://edara.ligne-digitale.com';
                }
            }

            // Fonction pour détecter automatiquement les serveurs disponibles
            async function detectServers() {
                try {
                    console.log('Détection automatique des serveurs disponibles...');

                    // Afficher un message de chargement
                    errorMessage.textContent = 'Détection des serveurs disponibles...';
                    errorMessage.style.color = '#3498db'; // Bleu pour indiquer le chargement
                    errorMessage.style.display = 'block';

                    // Vérifier si l'API getServerInfo est disponible
                    if (window.electronAPI && typeof window.electronAPI.getServerInfo === 'function') {
                        try {
                            const serverInfo = await window.electronAPI.getServerInfo();
                            console.log('Informations sur les serveurs:', serverInfo);

                            // Masquer le message de chargement
                            errorMessage.style.display = 'none';

                            if (serverInfo && serverInfo.success) {
                                // Stocker les URLs des serveurs
                                if (serverInfo.localServer && serverInfo.localServer.available) {
                                    localServerUrl = serverInfo.localServer.url;
                                    console.log(`Serveur local disponible: ${localServerUrl}`);
                                } else {
                                    console.log('Aucun serveur local disponible détecté automatiquement');
                                    // Essayer de récupérer la dernière IP valide
                                    const hasValidIp = await fetchLastValidIp();

                                    // Si aucune IP valide n'a été trouvée, utiliser localhost
                                    if (!hasValidIp) {
                                        // Définir quand même une URL locale par défaut pour permettre à l'utilisateur de forcer la connexion locale
                                        localServerUrl = 'http://localhost:8069';
                                        console.log(`Aucune IP valide trouvée, URL du serveur local définie par défaut: ${localServerUrl}`);
                                    }
                                }

                                if (serverInfo.remoteServer) {
                                    remoteServerUrl = serverInfo.remoteServer.url;
                                    console.log(`Serveur distant: ${remoteServerUrl}`);
                                } else {
                                    // Définir une URL distante par défaut
                                    remoteServerUrl = 'https://edara.ligne-digitale.com';
                                    console.log(`Utilisation de l'URL distante par défaut: ${remoteServerUrl}`);
                                }

                                // Sélectionner automatiquement le type de connexion en fonction du meilleur serveur détecté
                                if (serverInfo.connectionType) {
                                    instanceSelect.value = serverInfo.connectionType;
                                    console.log(`Type de connexion sélectionné automatiquement: ${serverInfo.connectionType}`);
                                }
                            } else {
                                console.warn('Échec de la détection des serveurs:', serverInfo ? serverInfo.error : 'Réponse invalide');
                                // En cas d'échec, utiliser des valeurs par défaut mais ne pas afficher d'erreur
                                setDefaultServerValues();
                            }
                        } catch (apiError) {
                            console.error('Erreur lors de l\'appel à getServerInfo:', apiError);
                            // En cas d'erreur, utiliser des valeurs par défaut
                            setDefaultServerValues();
                        }
                    } else {
                        console.warn('API getServerInfo non disponible, utilisation des valeurs par défaut');
                        // Si l'API n'est pas disponible, utiliser des valeurs par défaut
                        setDefaultServerValues();
                    }
                } catch (error) {
                    console.error('Erreur lors de la détection des serveurs:', error);
                    // En cas d'erreur, utiliser des valeurs par défaut
                    setDefaultServerValues();
                } finally {
                    // S'assurer que le message de chargement est masqué dans tous les cas
                    errorMessage.style.display = 'none';
                }
            }

            // Fonction pour définir des valeurs par défaut pour les serveurs
            async function setDefaultServerValues() {
                console.log('Utilisation des valeurs par défaut pour les serveurs');

                // Masquer le message d'erreur
                errorMessage.style.display = 'none';

                // Si localServerUrl n'est pas défini, essayer de récupérer la dernière IP valide
                if (!localServerUrl) {
                    // Essayer de récupérer la dernière IP valide
                    const hasValidIp = await fetchLastValidIp();

                    // Si aucune IP valide n'a été trouvée, utiliser localhost
                    if (!hasValidIp) {
                        localServerUrl = 'http://localhost:8069';
                        console.log(`Aucune IP valide trouvée, URL du serveur local définie par défaut: ${localServerUrl}`);
                    }
                } else {
                    console.log(`Conservation de l'URL du serveur local existante: ${localServerUrl}`);
                }

                // Conserver l'URL distante existante si elle est définie, sinon utiliser la valeur par défaut
                if (!remoteServerUrl) {
                    remoteServerUrl = 'https://edara.ligne-digitale.com';
                    console.log(`URL du serveur distant définie par défaut: ${remoteServerUrl}`);
                } else {
                    console.log(`Conservation de l'URL du serveur distant existante: ${remoteServerUrl}`);
                }

                // Par défaut, sélectionner la connexion distante
                instanceSelect.value = 'distance';

                console.log('Valeurs définies: local=' + localServerUrl + ', distant=' + remoteServerUrl);
            }

            // Fonction pour récupérer la dernière adresse IP locale valide
            async function fetchLastValidIp() {
                try {
                    console.log('Récupération de la dernière adresse IP locale valide...');

                    if (window.electronAPI && typeof window.electronAPI.getLastValidIp === 'function') {
                        const result = await window.electronAPI.getLastValidIp();
                        console.log('Résultat de la récupération de la dernière IP valide:', result);

                        if (result && result.success && result.url) {
                            localServerUrl = result.url;
                            console.log(`Dernière adresse IP locale valide récupérée: ${localServerUrl}`);
                            return true;
                        } else {
                            console.log('Aucune adresse IP locale valide trouvée');
                            return false;
                        }
                    } else {
                        console.warn('API getLastValidIp non disponible');
                        return false;
                    }
                } catch (error) {
                    console.error('Erreur lors de la récupération de la dernière adresse IP locale valide:', error);
                    return false;
                }
            }

            // Détecter automatiquement les serveurs au chargement de la page
            detectServers();

            // Écouter les changements du champ "Instance"
            instanceSelect.addEventListener('change', function() {
                console.log(`Type de connexion sélectionné: ${this.value}`);
                // Vous pouvez ajouter ici une logique supplémentaire si nécessaire
            });

            // Écouter les messages de déconnexion envoyés par le processus principal
            if (window.electronAPI && typeof window.electronAPI.onLogoutComplete === 'function') {
                const unsubscribe = window.electronAPI.onLogoutComplete(async (event) => {
                    console.log('Message de déconnexion reçu du processus principal');
                    // Ne pas réinitialiser localServerUrl, mais récupérer la dernière IP valide
                    remoteServerUrl = null;
                    // Récupérer la dernière IP valide si disponible
                    await fetchLastValidIp();
                    // Utiliser les valeurs par défaut
                    setDefaultServerValues();
                });
                // Stocker la fonction de désabonnement pour un nettoyage ultérieur si nécessaire
                window.unsubscribeLogoutComplete = unsubscribe;
            }

            // Fonction pour créer un contrôleur d'abandon avec timeout
            function createTimeoutController(timeoutMs) {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

                return {
                    signal: controller.signal,
                    clear: () => clearTimeout(timeoutId)
                };
            }

            // Fonction pour vérifier la disponibilité d'un serveur
            async function checkServerAvailability(url) {
                try {
                    console.log(`Vérification de la disponibilité du serveur: ${url}`);

                    // Afficher un message de chargement
                    errorMessage.textContent = 'Vérification de la connexion au serveur...';
                    errorMessage.style.color = '#3498db'; // Bleu pour indiquer le chargement
                    errorMessage.style.display = 'block';

                    // Essayer d'abord avec l'URL de base
                    try {
                        const controller1 = createTimeoutController(5000); // 5 secondes de timeout

                        const baseResponse = await fetch(`${url}/web`, {
                            method: 'GET',
                            headers: {
                                'Accept': 'text/html',
                                'Cache-Control': 'no-cache'
                            },
                            signal: controller1.signal
                        });

                        // Nettoyer le timeout
                        controller1.clear();

                        // Si le serveur répond, même avec un code d'erreur, c'est probablement un serveur Odoo
                        if (baseResponse.status >= 200 && baseResponse.status < 500) {
                            console.log(`Serveur ${url} disponible, code de statut: ${baseResponse.status}`);

                            // Masquer le message de chargement
                            errorMessage.style.display = 'none';

                            return true;
                        }
                    } catch (baseError) {
                        console.warn(`Erreur lors de la vérification de base de ${url}:`, baseError);
                        // Continuer avec la vérification suivante
                    }

                    // Essayer ensuite avec l'URL de login
                    try {
                        const controller2 = createTimeoutController(5000); // 5 secondes de timeout

                        const loginResponse = await fetch(`${url}/web/login`, {
                            method: 'GET',
                            headers: {
                                'Accept': 'text/html',
                                'Cache-Control': 'no-cache'
                            },
                            signal: controller2.signal
                        });

                        // Nettoyer le timeout
                        controller2.clear();

                        // Si le serveur répond, même avec un code d'erreur, c'est probablement un serveur Odoo
                        if (loginResponse.status >= 200 && loginResponse.status < 500) {
                            console.log(`Serveur ${url} disponible (page de login), code de statut: ${loginResponse.status}`);

                            // Masquer le message de chargement
                            errorMessage.style.display = 'none';

                            return true;
                        }
                    } catch (loginError) {
                        console.warn(`Erreur lors de la vérification de login de ${url}:`, loginError);
                    }

                    // Masquer le message de chargement
                    errorMessage.style.display = 'none';

                    console.warn(`Serveur ${url} non disponible après toutes les vérifications`);
                    return false;
                } catch (error) {
                    console.error(`Erreur lors de la vérification du serveur ${url}:`, error);

                    // Masquer le message de chargement
                    errorMessage.style.display = 'none';

                    return false;
                }
            }

            // Gérer la soumission du formulaire
            loginForm.addEventListener('submit', async function(event) {
                event.preventDefault();

                // Récupérer les valeurs du formulaire
                const instance = instanceSelect.value;
                const email = emailInput.value;
                const password = passwordInput.value;

                // Vérifier que les champs sont remplis
                if (!email || !password) {
                    errorMessage.textContent = 'Veuillez remplir tous les champs';
                    errorMessage.style.color = 'var(--error-color)';
                    errorMessage.style.display = 'block';
                    return;
                }

                // Obtenir l'URL du serveur en fonction du type de connexion sélectionné
                const serverUrl = updateServerUrl();
                if (!serverUrl) {
                    errorMessage.textContent = 'Impossible de déterminer l\'URL du serveur';
                    errorMessage.style.color = 'var(--error-color)';
                    errorMessage.style.display = 'block';
                    return;
                }

                // Si l'utilisateur a sélectionné la connexion locale, vérifier explicitement la disponibilité du serveur
                if (instance === 'local') {
                    const isLocalServerAvailable = await checkServerAvailability(serverUrl);

                    if (!isLocalServerAvailable) {
                        errorMessage.textContent = 'Le serveur local n\'est pas disponible. Vérifiez que le service Odoo est bien démarré sur le port 8069.';
                        errorMessage.style.color = 'var(--error-color)';
                        errorMessage.style.display = 'block';
                        return;
                    }
                }

                // Masquer le message d'erreur
                errorMessage.style.display = 'none';

                // Envoyer les données d'authentification au processus principal
                if (window.electronAPI && typeof window.electronAPI.sendAuthData === 'function') {
                    window.electronAPI.sendAuthData({
                        instance: instance,
                        serverUrl: serverUrl,
                        login: email,
                        password: password
                    });
                } else {
                    console.error('API Electron sendAuthData non disponible');
                    errorMessage.textContent = 'Erreur de communication avec l\'application';
                    errorMessage.style.color = 'var(--error-color)';
                    errorMessage.style.display = 'block';
                }
            });

            // Écouter les messages d'erreur d'authentification
            if (window.electronAPI && typeof window.electronAPI.onAuthError === 'function') {
                const unsubscribe = window.electronAPI.onAuthError((message) => {
                    errorMessage.textContent = message || 'Adresse email ou mot de passe incorrect';
                    errorMessage.style.display = 'block';
                });
                // Stocker la fonction de désabonnement pour un nettoyage ultérieur si nécessaire
                window.unsubscribeAuthError = unsubscribe;
            }
        });
    </script>
</body>
</html>
