<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test de Session Odoo</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background-color: #212226;
      color: #f0f0f0;
      margin: 0;
      padding: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100vh;
    }
    
    .container {
      background-color: #2a2a30;
      border-radius: 8px;
      padding: 30px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      width: 100%;
      max-width: 600px;
    }
    
    h1 {
      color: #ffffff;
      margin-top: 0;
      text-align: center;
      margin-bottom: 30px;
    }
    
    .form-group {
      margin-bottom: 20px;
    }
    
    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
    }
    
    input[type="text"] {
      width: 100%;
      padding: 12px;
      border: 1px solid #3a3a40;
      border-radius: 4px;
      background-color: rgba(255, 255, 255, 0.05);
      color: #f0f0f0;
      font-size: 16px;
      box-sizing: border-box;
    }
    
    input[type="text"]:focus {
      outline: none;
      border-color: #4a90e2;
      background-color: rgba(255, 255, 255, 0.1);
    }
    
    button {
      background-color: #4a90e2;
      color: white;
      border: none;
      padding: 12px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 500;
      width: 100%;
      transition: background-color 0.2s;
    }
    
    button:hover {
      background-color: #3a80d2;
    }
    
    button:active {
      background-color: #2a70c2;
    }
    
    .status {
      margin-top: 20px;
      padding: 15px;
      border-radius: 4px;
      font-family: monospace;
      white-space: pre-wrap;
      word-break: break-all;
      max-height: 200px;
      overflow-y: auto;
    }
    
    .status.success {
      background-color: rgba(40, 167, 69, 0.2);
      border: 1px solid #28a745;
    }
    
    .status.error {
      background-color: rgba(220, 53, 69, 0.2);
      border: 1px solid #dc3545;
    }
    
    .status.info {
      background-color: rgba(23, 162, 184, 0.2);
      border: 1px solid #17a2b8;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Test de Session Odoo</h1>
    
    <div class="form-group">
      <label for="server-url">URL du serveur Odoo:</label>
      <input type="text" id="server-url" value="http://localhost:8069" placeholder="http://localhost:8069">
    </div>
    
    <div class="form-group">
      <label for="session-id">ID de session Odoo:</label>
      <input type="text" id="session-id" placeholder="Coller l'ID de session ici">
    </div>
    
    <button id="create-window">Créer une fenêtre avec session injectée</button>
    
    <div id="status" class="status info">
      Entrez l'URL du serveur Odoo et l'ID de session, puis cliquez sur le bouton pour créer une fenêtre avec la session injectée.
    </div>
  </div>
  
  <script>
    // Fonction pour mettre à jour le statut
    function updateStatus(message, type = 'info') {
      const statusElement = document.getElementById('status');
      statusElement.textContent = message;
      statusElement.className = `status ${type}`;
    }
    
    // Gestionnaire de clic pour le bouton
    document.getElementById('create-window').addEventListener('click', async () => {
      const serverUrl = document.getElementById('server-url').value.trim();
      const sessionId = document.getElementById('session-id').value.trim();
      
      // Vérifier que les champs sont remplis
      if (!serverUrl) {
        updateStatus('Erreur: L\'URL du serveur est requise', 'error');
        return;
      }
      
      if (!sessionId) {
        updateStatus('Erreur: L\'ID de session est requis', 'error');
        return;
      }
      
      try {
        updateStatus(`Création d'une fenêtre Odoo avec session injectée...\nServeur: ${serverUrl}\nSession ID: ${sessionId.substring(0, 8)}...`, 'info');
        
        // Appeler la fonction du processus principal via IPC
        const result = await window.electron.ipcRenderer.invoke('create-odoo-window', {
          sessionId,
          serverUrl
        });
        
        if (result.success) {
          updateStatus(`Fenêtre Odoo créée avec succès!\nServeur: ${serverUrl}\nSession ID: ${sessionId.substring(0, 8)}...`, 'success');
        } else {
          updateStatus(`Erreur lors de la création de la fenêtre Odoo: ${result.error}`, 'error');
        }
      } catch (error) {
        updateStatus(`Erreur: ${error.message}`, 'error');
      }
    });
    
    // Fonction pour récupérer l'ID de session depuis les cookies
    function getSessionIdFromCookies() {
      const cookies = document.cookie.split(';');
      for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'session_id') {
          return value;
        }
      }
      return null;
    }
    
    // Au chargement de la page, vérifier si un ID de session est disponible dans les cookies
    window.addEventListener('DOMContentLoaded', () => {
      const sessionId = getSessionIdFromCookies();
      if (sessionId) {
        document.getElementById('session-id').value = sessionId;
        updateStatus(`ID de session trouvé dans les cookies: ${sessionId.substring(0, 8)}...`, 'info');
      }
    });
  </script>
</body>
</html>
