/**
 * Cache d'IP intelligent avec TTL dynamique et validation proactive
 * Gère la persistance et la validation des adresses IP de serveurs Odoo
 */

const fs = require('fs').promises;
const path = require('path');
const { app } = require('electron');
const log = require('electron-log');
const { EventEmitter } = require('events');

class IntelligentIpCache extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.config = {
      cacheFile: options.cacheFile || path.join(app.getPath('userData'), 'odoo_servers_cache.json'),
      defaultTtl: options.defaultTtl || 24 * 60 * 60 * 1000, // 24 heures
      minTtl: options.minTtl || 5 * 60 * 1000,              // 5 minutes
      maxTtl: options.maxTtl || 7 * 24 * 60 * 60 * 1000,    // 7 jours
      revalidateThreshold: options.revalidateThreshold || 0.5, // 50% du TTL
      maxEntries: options.maxEntries || 100,
      validationInterval: options.validationInterval || 10 * 60 * 1000, // 10 minutes
      ...options
    };
    
    // Cache en mémoire avec métadonnées enrichies
    this.cache = new Map();
    
    // Métriques de performance
    this.metrics = {
      hits: 0,
      misses: 0,
      validations: 0,
      invalidations: 0,
      saves: 0,
      loads: 0
    };
    
    // État de validation
    this.validationState = {
      isValidating: false,
      lastValidation: 0,
      validationQueue: new Set()
    };
    
    this.initialize();
  }

  /**
   * Initialise le cache
   */
  async initialize() {
    try {
      await this.loadFromDisk();
      this.startPeriodicValidation();
      log.info('Cache d\'IP intelligent initialisé');
    } catch (error) {
      log.error(`Erreur lors de l'initialisation du cache: ${error.message}`);
    }
  }

  /**
   * Récupère une IP avec validation intelligente
   */
  async getValidIp(criteria = {}) {
    const {
      preferLocal = true,
      maxAge = this.config.defaultTtl,
      requireValidation = false
    } = criteria;
    
    // 1. Chercher dans le cache
    const cachedEntries = this.findMatchingEntries(criteria);
    
    if (cachedEntries.length === 0) {
      this.metrics.misses++;
      return null;
    }
    
    // 2. Filtrer par âge et validité
    const validEntries = cachedEntries.filter(entry => {
      const age = Date.now() - entry.timestamp;
      return age < maxAge && (!requireValidation || entry.validated);
    });
    
    if (validEntries.length === 0) {
      this.metrics.misses++;
      return null;
    }
    
    // 3. Sélectionner la meilleure IP
    const bestEntry = this.selectBestEntry(validEntries, { preferLocal });
    
    // 4. Validation proactive si nécessaire
    if (this.shouldRevalidate(bestEntry)) {
      this.scheduleValidation(bestEntry);
    }
    
    // 5. Mettre à jour les métriques d'utilisation
    this.updateUsageMetrics(bestEntry);
    
    this.metrics.hits++;
    log.debug(`IP récupérée du cache: ${bestEntry.ip} (âge: ${this.getEntryAge(bestEntry)}ms)`);
    
    return {
      ip: bestEntry.ip,
      url: `http://${bestEntry.ip}:8069`,
      confidence: this.calculateConfidence(bestEntry),
      metadata: bestEntry
    };
  }

  /**
   * Sauvegarde une IP avec métadonnées enrichies
   */
  async saveIp(ip, metadata = {}) {
    try {
      const entry = {
        ip,
        timestamp: Date.now(),
        ttl: this.calculateDynamicTtl(metadata),
        validated: metadata.validated || false,
        lastValidation: metadata.validated ? Date.now() : 0,
        responseTime: metadata.responseTime || null,
        reliability: metadata.reliability || 0.5,
        usageCount: 0,
        lastUsed: Date.now(),
        source: metadata.source || 'unknown',
        networkInfo: this.extractNetworkInfo(ip),
        validationHistory: [],
        ...metadata
      };
      
      // Ajouter au cache
      this.cache.set(ip, entry);
      
      // Sauvegarder sur disque
      await this.saveToDisk();
      
      this.metrics.saves++;
      log.info(`IP sauvegardée: ${ip} (TTL: ${entry.ttl}ms, fiabilité: ${entry.reliability})`);
      
      this.emit('ip-saved', { ip, entry });
      
      return entry;
    } catch (error) {
      log.error(`Erreur lors de la sauvegarde de l'IP ${ip}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Calcule un TTL dynamique basé sur la fiabilité et l'historique
   */
  calculateDynamicTtl(metadata) {
    let baseTtl = this.config.defaultTtl;
    
    // Facteur de fiabilité (0.0 à 1.0)
    const reliability = metadata.reliability || 0.5;
    const reliabilityFactor = 0.5 + (reliability * 1.5); // 0.5x à 2.0x
    
    // Facteur de temps de réponse
    let responseTimeFactor = 1.0;
    if (metadata.responseTime) {
      if (metadata.responseTime < 200) {
        responseTimeFactor = 1.5; // Serveur rapide, TTL plus long
      } else if (metadata.responseTime > 1000) {
        responseTimeFactor = 0.7; // Serveur lent, TTL plus court
      }
    }
    
    // Facteur de source
    const sourceFactors = {
      'network-scan': 1.0,
      'user-input': 1.2,
      'last-valid': 1.5,
      'preference': 1.3
    };
    const sourceFactor = sourceFactors[metadata.source] || 1.0;
    
    // Calculer le TTL final
    let finalTtl = baseTtl * reliabilityFactor * responseTimeFactor * sourceFactor;
    
    // Appliquer les limites
    finalTtl = Math.max(this.config.minTtl, Math.min(this.config.maxTtl, finalTtl));
    
    return Math.round(finalTtl);
  }

  /**
   * Trouve les entrées correspondant aux critères
   */
  findMatchingEntries(criteria) {
    const entries = Array.from(this.cache.values());
    
    return entries.filter(entry => {
      // Filtrer par réseau si spécifié
      if (criteria.network) {
        const entryNetwork = entry.networkInfo?.network;
        if (entryNetwork !== criteria.network) {
          return false;
        }
      }
      
      // Filtrer par type (local/remote)
      if (criteria.type) {
        const isLocal = this.isLocalIp(entry.ip);
        if ((criteria.type === 'local' && !isLocal) || 
            (criteria.type === 'remote' && isLocal)) {
          return false;
        }
      }
      
      // Filtrer par fiabilité minimale
      if (criteria.minReliability && entry.reliability < criteria.minReliability) {
        return false;
      }
      
      return true;
    });
  }

  /**
   * Sélectionne la meilleure entrée selon les critères
   */
  selectBestEntry(entries, options = {}) {
    const { preferLocal = true } = options;
    
    // Calculer un score pour chaque entrée
    const scoredEntries = entries.map(entry => ({
      ...entry,
      score: this.calculateEntryScore(entry, { preferLocal })
    }));
    
    // Trier par score décroissant
    scoredEntries.sort((a, b) => b.score - a.score);
    
    return scoredEntries[0];
  }

  /**
   * Calcule le score d'une entrée
   */
  calculateEntryScore(entry, options = {}) {
    let score = 0;
    
    // Score de base sur la fiabilité
    score += entry.reliability * 1000;
    
    // Bonus pour les serveurs locaux si préféré
    if (options.preferLocal && this.isLocalIp(entry.ip)) {
      score += 500;
    }
    
    // Bonus pour les temps de réponse rapides
    if (entry.responseTime) {
      score += Math.max(0, 500 - entry.responseTime);
    }
    
    // Bonus pour l'utilisation récente
    const daysSinceLastUse = (Date.now() - entry.lastUsed) / (24 * 60 * 60 * 1000);
    score += Math.max(0, 200 - (daysSinceLastUse * 50));
    
    // Bonus pour la validation récente
    if (entry.validated && entry.lastValidation) {
      const hoursSinceValidation = (Date.now() - entry.lastValidation) / (60 * 60 * 1000);
      score += Math.max(0, 300 - (hoursSinceValidation * 10));
    }
    
    // Malus pour l'âge
    const age = Date.now() - entry.timestamp;
    const ageHours = age / (60 * 60 * 1000);
    score -= ageHours * 5;
    
    return Math.max(0, score);
  }

  /**
   * Détermine si une entrée doit être revalidée
   */
  shouldRevalidate(entry) {
    const age = Date.now() - entry.timestamp;
    const revalidateAge = entry.ttl * this.config.revalidateThreshold;
    
    return age > revalidateAge && !this.validationState.validationQueue.has(entry.ip);
  }

  /**
   * Programme une validation en arrière-plan
   */
  scheduleValidation(entry) {
    if (this.validationState.validationQueue.has(entry.ip)) {
      return; // Déjà en file
    }
    
    this.validationState.validationQueue.add(entry.ip);
    
    // Validation asynchrone
    setImmediate(async () => {
      try {
        await this.validateEntry(entry);
      } catch (error) {
        log.warn(`Erreur lors de la validation de ${entry.ip}: ${error.message}`);
      } finally {
        this.validationState.validationQueue.delete(entry.ip);
      }
    });
  }

  /**
   * Valide une entrée de cache
   */
  async validateEntry(entry) {
    try {
      log.debug(`Validation de l'entrée: ${entry.ip}`);
      
      const startTime = Date.now();
      const isValid = await this.pingServer(entry.ip);
      const responseTime = Date.now() - startTime;
      
      // Mettre à jour l'entrée
      entry.lastValidation = Date.now();
      entry.validated = isValid;
      entry.responseTime = responseTime;
      
      // Mettre à jour l'historique de validation
      entry.validationHistory = entry.validationHistory || [];
      entry.validationHistory.push({
        timestamp: Date.now(),
        valid: isValid,
        responseTime
      });
      
      // Garder seulement les 10 dernières validations
      if (entry.validationHistory.length > 10) {
        entry.validationHistory = entry.validationHistory.slice(-10);
      }
      
      // Recalculer la fiabilité
      entry.reliability = this.calculateReliability(entry);
      
      // Recalculer le TTL
      entry.ttl = this.calculateDynamicTtl(entry);
      
      this.metrics.validations++;
      
      if (isValid) {
        log.debug(`Validation réussie pour ${entry.ip} (${responseTime}ms)`);
        this.emit('ip-validated', { ip: entry.ip, entry });
      } else {
        log.debug(`Validation échouée pour ${entry.ip}`);
        this.emit('ip-invalidated', { ip: entry.ip, entry });
        
        // Réduire drastiquement le TTL pour les IPs invalides
        entry.ttl = this.config.minTtl;
        this.metrics.invalidations++;
      }
      
      // Sauvegarder les changements
      await this.saveToDisk();
      
    } catch (error) {
      log.error(`Erreur lors de la validation de ${entry.ip}: ${error.message}`);
    }
  }

  /**
   * Ping rapide d'un serveur
   */
  async pingServer(ip) {
    try {
      const net = require('net');
      
      return new Promise((resolve) => {
        const socket = new net.Socket();
        let resolved = false;
        
        const cleanup = (result) => {
          if (!resolved) {
            resolved = true;
            socket.destroy();
            resolve(result);
          }
        };
        
        socket.setTimeout(2000);
        socket.on('connect', () => cleanup(true));
        socket.on('timeout', () => cleanup(false));
        socket.on('error', () => cleanup(false));
        
        try {
          socket.connect(8069, ip);
        } catch (error) {
          cleanup(false);
        }
        
        setTimeout(() => cleanup(false), 2500);
      });
    } catch (error) {
      return false;
    }
  }

  /**
   * Calcule la fiabilité basée sur l'historique
   */
  calculateReliability(entry) {
    if (!entry.validationHistory || entry.validationHistory.length === 0) {
      return entry.reliability || 0.5;
    }
    
    const history = entry.validationHistory;
    const recentHistory = history.slice(-5); // 5 dernières validations
    
    // Taux de succès récent
    const successRate = recentHistory.filter(h => h.valid).length / recentHistory.length;
    
    // Facteur de temps (validations récentes comptent plus)
    const now = Date.now();
    const weightedSuccess = recentHistory.reduce((sum, h, index) => {
      const age = now - h.timestamp;
      const weight = Math.max(0.1, 1 - (age / (24 * 60 * 60 * 1000))); // Poids basé sur l'âge
      return sum + (h.valid ? weight : 0);
    }, 0);
    
    const totalWeight = recentHistory.reduce((sum, h) => {
      const age = now - h.timestamp;
      const weight = Math.max(0.1, 1 - (age / (24 * 60 * 60 * 1000)));
      return sum + weight;
    }, 0);
    
    const weightedReliability = totalWeight > 0 ? weightedSuccess / totalWeight : successRate;
    
    // Lisser avec l'ancienne fiabilité
    const oldReliability = entry.reliability || 0.5;
    return (oldReliability * 0.3) + (weightedReliability * 0.7);
  }

  /**
   * Met à jour les métriques d'utilisation
   */
  updateUsageMetrics(entry) {
    entry.usageCount = (entry.usageCount || 0) + 1;
    entry.lastUsed = Date.now();
  }

  /**
   * Extrait les informations réseau d'une IP
   */
  extractNetworkInfo(ip) {
    const parts = ip.split('.');
    if (parts.length !== 4) return null;
    
    return {
      network: parts.slice(0, 3).join('.'),
      host: parseInt(parts[3]),
      isPrivate: this.isPrivateIp(ip),
      isLocal: this.isLocalIp(ip)
    };
  }

  /**
   * Vérifie si une IP est privée
   */
  isPrivateIp(ip) {
    const parts = ip.split('.').map(Number);
    
    return (
      (parts[0] === 10) ||
      (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31) ||
      (parts[0] === 192 && parts[1] === 168)
    );
  }

  /**
   * Vérifie si une IP est locale
   */
  isLocalIp(ip) {
    return ip === '127.0.0.1' || ip === 'localhost' || this.isPrivateIp(ip);
  }

  /**
   * Calcule la confiance dans une entrée
   */
  calculateConfidence(entry) {
    let confidence = entry.reliability || 0.5;
    
    // Bonus pour validation récente
    if (entry.validated && entry.lastValidation) {
      const hoursSinceValidation = (Date.now() - entry.lastValidation) / (60 * 60 * 1000);
      if (hoursSinceValidation < 1) {
        confidence += 0.2;
      } else if (hoursSinceValidation < 6) {
        confidence += 0.1;
      }
    }
    
    // Malus pour l'âge
    const age = this.getEntryAge(entry);
    const ageHours = age / (60 * 60 * 1000);
    if (ageHours > 24) {
      confidence -= 0.1;
    }
    
    return Math.max(0, Math.min(1, confidence));
  }

  /**
   * Obtient l'âge d'une entrée
   */
  getEntryAge(entry) {
    return Date.now() - entry.timestamp;
  }

  /**
   * Démarre la validation périodique
   */
  startPeriodicValidation() {
    setInterval(async () => {
      if (this.validationState.isValidating) return;
      
      this.validationState.isValidating = true;
      
      try {
        await this.performPeriodicValidation();
      } catch (error) {
        log.error(`Erreur lors de la validation périodique: ${error.message}`);
      } finally {
        this.validationState.isValidating = false;
        this.validationState.lastValidation = Date.now();
      }
    }, this.config.validationInterval);
  }

  /**
   * Effectue une validation périodique
   */
  async performPeriodicValidation() {
    const entries = Array.from(this.cache.values());
    
    // Sélectionner les entrées à valider
    const toValidate = entries.filter(entry => {
      const age = Date.now() - (entry.lastValidation || 0);
      return age > this.config.validationInterval;
    });
    
    if (toValidate.length === 0) return;
    
    log.debug(`Validation périodique de ${toValidate.length} entrée(s)`);
    
    // Valider en parallèle (max 5 à la fois)
    const maxConcurrent = 5;
    for (let i = 0; i < toValidate.length; i += maxConcurrent) {
      const chunk = toValidate.slice(i, i + maxConcurrent);
      
      await Promise.allSettled(
        chunk.map(entry => this.validateEntry(entry))
      );
      
      // Petit délai entre les chunks
      if (i + maxConcurrent < toValidate.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  }

  /**
   * Charge le cache depuis le disque
   */
  async loadFromDisk() {
    try {
      const data = await fs.readFile(this.config.cacheFile, 'utf8');
      const parsed = JSON.parse(data);
      
      // Reconstruire le cache
      this.cache.clear();
      
      if (parsed.entries) {
        for (const [ip, entry] of Object.entries(parsed.entries)) {
          // Vérifier si l'entrée n'est pas expirée
          const age = Date.now() - entry.timestamp;
          if (age < entry.ttl) {
            this.cache.set(ip, entry);
          }
        }
      }
      
      if (parsed.metrics) {
        this.metrics = { ...this.metrics, ...parsed.metrics };
      }
      
      this.metrics.loads++;
      log.info(`Cache chargé: ${this.cache.size} entrée(s)`);
      
    } catch (error) {
      if (error.code !== 'ENOENT') {
        log.warn(`Erreur lors du chargement du cache: ${error.message}`);
      }
    }
  }

  /**
   * Sauvegarde le cache sur disque
   */
  async saveToDisk() {
    try {
      const data = {
        version: '1.0',
        timestamp: Date.now(),
        entries: Object.fromEntries(this.cache),
        metrics: this.metrics
      };
      
      // Créer le répertoire si nécessaire
      const dir = path.dirname(this.config.cacheFile);
      await fs.mkdir(dir, { recursive: true });
      
      // Sauvegarder atomiquement
      const tempFile = this.config.cacheFile + '.tmp';
      await fs.writeFile(tempFile, JSON.stringify(data, null, 2));
      await fs.rename(tempFile, this.config.cacheFile);
      
      log.debug(`Cache sauvegardé: ${this.cache.size} entrée(s)`);
      
    } catch (error) {
      log.error(`Erreur lors de la sauvegarde du cache: ${error.message}`);
    }
  }

  /**
   * Nettoie les entrées expirées
   */
  cleanup() {
    const now = Date.now();
    let cleaned = 0;
    
    for (const [ip, entry] of this.cache.entries()) {
      const age = now - entry.timestamp;
      if (age > entry.ttl) {
        this.cache.delete(ip);
        cleaned++;
      }
    }
    
    if (cleaned > 0) {
      log.debug(`Cache nettoyé: ${cleaned} entrée(s) expirée(s) supprimée(s)`);
    }
    
    return cleaned;
  }

  /**
   * Obtient les statistiques du cache
   */
  getStats() {
    return {
      ...this.metrics,
      cacheSize: this.cache.size,
      validationQueue: this.validationState.validationQueue.size,
      lastValidation: this.validationState.lastValidation
    };
  }

  /**
   * Réinitialise le cache
   */
  async reset() {
    this.cache.clear();
    this.validationState.validationQueue.clear();
    
    try {
      await fs.unlink(this.config.cacheFile);
    } catch (error) {
      // Ignorer si le fichier n'existe pas
    }
    
    log.info('Cache d\'IP réinitialisé');
  }
}

module.exports = IntelligentIpCache;
