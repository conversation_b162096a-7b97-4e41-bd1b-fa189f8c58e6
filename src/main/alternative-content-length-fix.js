/**
 * Solution Alternative pour ERR_CONTENT_LENGTH_MISMATCH
 * Approche différente utilisant les webPreferences et session personnalisée
 */

const { session, BrowserWindow } = require('electron');
const log = require('electron-log');

class AlternativeContentLengthFix {
  constructor() {
    this.customSession = null;
    this.isActive = false;
  }

  /**
   * Crée une session personnalisée avec configuration optimisée
   */
  async createCustomSession() {
    log.info('🔧 [Alternative] Création d\'une session personnalisée...');

    try {
      // Créer une session avec partition unique
      const partitionName = `edara-local-fix-${Date.now()}`;
      this.customSession = session.fromPartition(partitionName, {
        cache: false // Désactiver le cache pour éviter les problèmes
      });

      // Nettoyer complètement la session
      await this.customSession.clearCache();
      await this.customSession.clearStorageData();
      await this.customSession.clearAuthCache();

      // Configurer les intercepteurs sur la session personnalisée
      this.setupCustomSessionInterceptors();

      log.info('✅ [Alternative] Session personnalisée créée avec succès');
      return this.customSession;

    } catch (error) {
      log.error(`❌ [Alternative] Erreur lors de la création de session: ${error.message}`);
      throw error;
    }
  }

  /**
   * Configure les intercepteurs sur la session personnalisée
   */
  setupCustomSessionInterceptors() {
    log.info('🔧 [Alternative] Configuration des intercepteurs sur session personnalisée...');

    // Intercepteur pour modifier les requêtes
    this.customSession.webRequest.onBeforeSendHeaders([], (details, callback) => {
      if (this.isLocalOdooAsset(details.url)) {
        log.info(`🔧 [Alternative] Modification requête: ${details.url}`);

        const modifiedHeaders = { ...details.requestHeaders };
        
        // Forcer l'absence de compression
        modifiedHeaders['Accept-Encoding'] = ['identity'];
        modifiedHeaders['Cache-Control'] = ['no-cache'];
        modifiedHeaders['Pragma'] = ['no-cache'];
        
        // Headers spéciaux pour Electron
        modifiedHeaders['User-Agent'] = ['Edara-ERP-Alternative/1.0 (Electron)'];
        modifiedHeaders['X-Requested-With'] = ['Electron-Alternative'];

        callback({
          cancel: false,
          requestHeaders: modifiedHeaders
        });
      } else {
        callback({ cancel: false });
      }
    });

    // Intercepteur pour modifier les réponses
    this.customSession.webRequest.onHeadersReceived([], (details, callback) => {
      if (this.isLocalOdooAsset(details.url)) {
        log.info(`🔧 [Alternative] Modification réponse: ${details.url}`);

        const modifiedHeaders = { ...details.responseHeaders };

        // Supprimer TOUS les headers problématiques
        delete modifiedHeaders['content-length'];
        delete modifiedHeaders['Content-Length'];
        delete modifiedHeaders['content-encoding'];
        delete modifiedHeaders['Content-Encoding'];
        delete modifiedHeaders['transfer-encoding'];
        delete modifiedHeaders['Transfer-Encoding'];

        // Forcer des headers de cache appropriés
        modifiedHeaders['Cache-Control'] = ['no-cache, no-store, must-revalidate'];
        modifiedHeaders['Pragma'] = ['no-cache'];
        modifiedHeaders['Expires'] = ['0'];

        // Header de tracking
        modifiedHeaders['X-Edara-Alternative-Fixed'] = ['true'];

        log.info(`✅ [Alternative] Headers modifiés pour: ${details.url}`);

        callback({
          cancel: false,
          responseHeaders: modifiedHeaders
        });
      } else {
        callback({ cancel: false });
      }
    });

    // Intercepteur d'erreurs
    this.customSession.webRequest.onErrorOccurred([], (details) => {
      if (this.isLocalOdooAsset(details.url)) {
        log.error(`❌ [Alternative] Erreur détectée: ${details.url} - ${details.error}`);
        
        if (details.error.includes('CONTENT_LENGTH_MISMATCH')) {
          log.error(`🚨 [Alternative] ERREUR CONTENT-LENGTH MISMATCH ENCORE PRÉSENTE !`);
        }
      }
    });

    log.info('✅ [Alternative] Intercepteurs configurés sur session personnalisée');
  }

  /**
   * Crée une BrowserWindow optimisée avec la session personnalisée
   */
  createOptimizedWindow(options = {}) {
    log.info('🪟 [Alternative] Création de fenêtre optimisée...');

    if (!this.customSession) {
      throw new Error('Session personnalisée non créée');
    }

    const defaultOptions = {
      width: 1200,
      height: 800,
      show: false,
      webPreferences: {
        // Utiliser la session personnalisée
        session: this.customSession,
        
        // Configuration de sécurité optimisée
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true,
        
        // Optimisations spécifiques
        backgroundThrottling: false,
        offscreen: false,
        
        // Désactiver certaines fonctionnalités qui peuvent causer des problèmes
        allowRunningInsecureContent: false,
        experimentalFeatures: false,
        
        // Configuration réseau
        partition: this.customSession.partition
      }
    };

    const finalOptions = { ...defaultOptions, ...options };
    const window = new BrowserWindow(finalOptions);

    // Configurer les événements de la fenêtre
    this.setupWindowEvents(window);

    log.info('✅ [Alternative] Fenêtre optimisée créée');
    return window;
  }

  /**
   * Configure les événements de la fenêtre
   */
  setupWindowEvents(window) {
    window.webContents.on('did-finish-load', () => {
      log.info('✅ [Alternative] Page chargée avec succès');
    });

    window.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
      log.error(`❌ [Alternative] Échec de chargement: ${errorCode} - ${errorDescription}`);
      
      if (errorDescription.includes('CONTENT_LENGTH_MISMATCH')) {
        log.error(`🚨 [Alternative] ERREUR CONTENT-LENGTH DANS did-fail-load !`);
      }
    });

    window.webContents.on('console-message', (event, level, message) => {
      if (message.includes('ERR_CONTENT_LENGTH_MISMATCH')) {
        log.error(`🚨 [Alternative] ERREUR CONSOLE CONTENT-LENGTH: ${message}`);
      }
    });

    // Injecter un script de diagnostic dans la page
    window.webContents.on('dom-ready', () => {
      this.injectDiagnosticScript(window);
    });
  }

  /**
   * Injecte un script de diagnostic dans la page
   */
  async injectDiagnosticScript(window) {
    try {
      await window.webContents.executeJavaScript(`
        // Script de diagnostic côté client
        console.log('🔧 [Alternative] Script de diagnostic injecté');
        
        // Intercepter les erreurs XMLHttpRequest
        const originalXHR = window.XMLHttpRequest;
        window.XMLHttpRequest = function() {
          const xhr = new originalXHR();
          const originalOpen = xhr.open;
          const originalSend = xhr.send;
          
          xhr.open = function(method, url, ...args) {
            console.log('🔧 [Alternative] XHR ouvert:', method, url);
            return originalOpen.apply(this, [method, url, ...args]);
          };
          
          xhr.send = function(...args) {
            console.log('🔧 [Alternative] XHR envoyé');
            
            xhr.addEventListener('error', function(e) {
              console.error('🚨 [Alternative] XHR Error:', e);
              if (e.type === 'error' && e.target.readyState === 4) {
                console.error('🚨 [Alternative] Possible Content-Length Mismatch dans XHR');
              }
            });
            
            xhr.addEventListener('load', function() {
              console.log('✅ [Alternative] XHR chargé avec succès');
            });
            
            return originalSend.apply(this, args);
          };
          
          return xhr;
        };
        
        // Intercepter les erreurs fetch
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
          console.log('🔧 [Alternative] Fetch:', url);
          
          // Forcer les headers optimisés
          const optimizedOptions = {
            ...options,
            headers: {
              ...options.headers,
              'Accept-Encoding': 'identity',
              'Cache-Control': 'no-cache'
            }
          };
          
          return originalFetch(url, optimizedOptions)
            .then(response => {
              console.log('✅ [Alternative] Fetch réussi:', url, response.status);
              return response;
            })
            .catch(error => {
              console.error('🚨 [Alternative] Fetch Error:', url, error);
              throw error;
            });
        };
        
        console.log('✅ [Alternative] Script de diagnostic configuré');
      `);
      
      log.info('✅ [Alternative] Script de diagnostic injecté avec succès');
    } catch (error) {
      log.error(`❌ [Alternative] Erreur lors de l'injection du script: ${error.message}`);
    }
  }

  /**
   * Vérifie si une URL est un asset Odoo local
   */
  isLocalOdooAsset(url) {
    return url.includes('**************:8069') && (
      url.includes('web.assets_backend.js') ||
      url.includes('web.assets_common.js') ||
      url.includes('web.assets_backend.css') ||
      url.includes('web.assets_common.css') ||
      url.includes('load_menus') ||
      url.includes('web/webclient')
    );
  }

  /**
   * Active la solution alternative
   */
  async activate() {
    if (this.isActive) {
      log.info('🔧 [Alternative] Solution déjà active');
      return this.customSession;
    }

    log.info('🔧 [Alternative] Activation de la solution alternative...');

    try {
      // Créer la session personnalisée
      await this.createCustomSession();
      
      this.isActive = true;
      log.info('✅ [Alternative] Solution alternative activée avec succès');
      
      return this.customSession;
    } catch (error) {
      log.error(`❌ [Alternative] Erreur lors de l'activation: ${error.message}`);
      throw error;
    }
  }

  /**
   * Obtient la session personnalisée
   */
  getCustomSession() {
    return this.customSession;
  }

  /**
   * Obtient les statistiques
   */
  getStats() {
    return {
      isActive: this.isActive,
      hasCustomSession: !!this.customSession,
      sessionPartition: this.customSession ? this.customSession.partition : null
    };
  }

  /**
   * Nettoie les ressources
   */
  cleanup() {
    if (this.customSession) {
      this.customSession.clearCache().catch(() => {});
      this.customSession = null;
    }
    
    this.isActive = false;
    log.info('🧹 [Alternative] Ressources nettoyées');
  }
}

// Instance singleton
let alternativeFixInstance = null;

/**
 * Obtient l'instance singleton
 */
function getAlternativeFix() {
  if (!alternativeFixInstance) {
    alternativeFixInstance = new AlternativeContentLengthFix();
  }
  return alternativeFixInstance;
}

/**
 * Active la solution alternative
 */
async function activateAlternativeFix() {
  const fix = getAlternativeFix();
  return await fix.activate();
}

/**
 * Crée une fenêtre optimisée avec la solution alternative
 */
async function createAlternativeWindow(options = {}) {
  const fix = getAlternativeFix();
  
  if (!fix.isActive) {
    await fix.activate();
  }
  
  return fix.createOptimizedWindow(options);
}

module.exports = {
  AlternativeContentLengthFix,
  getAlternativeFix,
  activateAlternativeFix,
  createAlternativeWindow
};
