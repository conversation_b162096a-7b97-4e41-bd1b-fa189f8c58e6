/**
 * Exemple d'utilisation du module odoo-window
 * Ce fichier montre comment créer une fenêtre Odoo avec un cookie de session injecté
 */

const { app, ipcMain } = require('electron');
const log = require('electron-log');
const { createOdooWindow } = require('./odoo-window');

// Configuration de electron-log
log.transports.file.level = 'info';
log.transports.console.level = 'debug';
log.transports.console.format = '[{level}] {text}';

// Variable pour stocker la fenêtre Odoo
let odooWindow = null;

// Fonction pour créer une fenêtre Odoo avec un cookie de session injecté
async function createOdooSessionWindow(sessionId, serverUrl = 'http://localhost:8069') {
  try {
    log.info(`Création d'une fenêtre Odoo avec session injectée: ${sessionId.substring(0, 8)}...`);
    
    // Créer la fenêtre Odoo avec le cookie de session injecté
    odooWindow = await createOdooWindow({
      sessionId,
      serverUrl,
      path: '/web',
      windowOptions: {
        width: 1280,
        height: 800,
        title: 'Edara ERP - Session Odoo',
        show: true
      }
    });
    
    // Gérer la fermeture de la fenêtre
    odooWindow.on('closed', () => {
      log.info('Fenêtre Odoo fermée');
      odooWindow = null;
    });
    
    return odooWindow;
  } catch (error) {
    log.error(`Erreur lors de la création de la fenêtre Odoo: ${error.message}`);
    log.error(error.stack);
    throw error;
  }
}

// Quand l'application est prête, créer la fenêtre Odoo
app.whenReady().then(() => {
  log.info('Application prête, attente des paramètres de session...');
  
  // Gestionnaire pour créer une fenêtre Odoo avec un cookie de session injecté
  ipcMain.handle('create-odoo-window', async (event, { sessionId, serverUrl }) => {
    try {
      log.info(`Demande de création d'une fenêtre Odoo avec session injectée: ${sessionId.substring(0, 8)}...`);
      
      // Créer la fenêtre Odoo
      const window = await createOdooSessionWindow(sessionId, serverUrl);
      
      return { success: true };
    } catch (error) {
      log.error(`Erreur lors de la création de la fenêtre Odoo: ${error.message}`);
      return { success: false, error: error.message };
    }
  });
});

// Gérer la fermeture de l'application
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (odooWindow === null) {
    log.info('Activation de l\'application, mais aucune fenêtre n\'existe');
  }
});

// Exemple d'utilisation directe (décommenter pour tester)
/*
app.whenReady().then(async () => {
  // Remplacer par un vrai ID de session Odoo
  const sessionId = 'votre_session_id_odoo';
  const serverUrl = 'http://localhost:8069';
  
  try {
    await createOdooSessionWindow(sessionId, serverUrl);
  } catch (error) {
    log.error(`Erreur lors de la création de la fenêtre Odoo: ${error.message}`);
  }
});
*/

module.exports = {
  createOdooSessionWindow
};
