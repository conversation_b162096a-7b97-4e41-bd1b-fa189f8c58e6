/**
 * Correction DIRECTE et IMMÉDIATE pour ERR_CONTENT_LENGTH_MISMATCH
 * Solution qui s'applique AVANT toute autre chose
 */

const { session } = require('electron');
const log = require('electron-log');

class DirectContentLengthFix {
  constructor() {
    this.isApplied = false;
    this.fixedCount = 0;
  }

  /**
   * Applique la correction IMMÉDIATEMENT
   */
  applyImmediateFix() {
    if (this.isApplied) {
      log.info('🔧 [DirectFix] Correction déjà appliquée');
      return;
    }

    log.info('🔧 [DirectFix] ========================================');
    log.info('🔧 [DirectFix] APPLICATION DE LA CORRECTION DIRECTE');
    log.info('🔧 [DirectFix] ========================================');

    try {
      // ÉTAPE 1: Nettoyer COMPLÈTEMENT la session par défaut
      this.clearDefaultSession();

      // ÉTAPE 2: Installer les intercepteurs DIRECTEMENT
      this.installDirectInterceptors();

      this.isApplied = true;
      log.info('✅ [DirectFix] CORRECTION DIRECTE APPLIQUÉE AVEC SUCCÈS');

    } catch (error) {
      log.error(`❌ [DirectFix] Erreur lors de l'application: ${error.message}`);
    }
  }

  /**
   * Nettoie complètement la session par défaut
   */
  async clearDefaultSession() {
    log.info('🧹 [DirectFix] Nettoyage COMPLET de la session par défaut...');

    try {
      const defaultSession = session.defaultSession;

      // Nettoyer TOUT
      await defaultSession.clearCache();
      await defaultSession.clearStorageData();
      await defaultSession.clearAuthCache();

      log.info('✅ [DirectFix] Session par défaut nettoyée');
    } catch (error) {
      log.error(`❌ [DirectFix] Erreur nettoyage session: ${error.message}`);
    }
  }

  /**
   * Installe les intercepteurs DIRECTEMENT sur la session par défaut
   */
  installDirectInterceptors() {
    log.info('🔧 [DirectFix] Installation des intercepteurs DIRECTS...');

    const defaultSession = session.defaultSession;

    // INTERCEPTEUR DIRECT POUR LES REQUÊTES
    defaultSession.webRequest.onBeforeSendHeaders([], (details, callback) => {
      // Vérifier si c'est une requête vers les assets problématiques
      if (this.isProblematicAsset(details.url)) {
        log.info(`🔧 [DirectFix] INTERCEPTION REQUÊTE: ${details.url}`);

        const headers = { ...details.requestHeaders };

        // FORCER les headers optimisés
        headers['Accept-Encoding'] = ['identity'];
        headers['Cache-Control'] = ['no-cache, no-store, must-revalidate'];
        headers['Pragma'] = ['no-cache'];
        headers['X-Edara-Direct-Fix'] = ['true'];

        log.info(`🔧 [DirectFix] ✅ Headers requête optimisés pour: ${details.url}`);

        callback({
          cancel: false,
          requestHeaders: headers
        });
      } else {
        callback({ cancel: false });
      }
    });

    // INTERCEPTEUR DIRECT POUR LES RÉPONSES - LE PLUS IMPORTANT
    defaultSession.webRequest.onHeadersReceived([], (details, callback) => {
      // Vérifier si c'est une réponse des assets problématiques
      if (this.isProblematicAsset(details.url)) {
        log.info(`🔧 [DirectFix] INTERCEPTION RÉPONSE: ${details.url}`);
        log.info(`🔧 [DirectFix] Status: ${details.statusCode}`);

        const headers = { ...details.responseHeaders };

        // DIAGNOSTIC: Afficher les headers originaux
        const originalContentLength = headers['content-length'] || headers['Content-Length'];
        const originalContentEncoding = headers['content-encoding'] || headers['Content-Encoding'];

        if (originalContentLength) {
          log.info(`🔧 [DirectFix] Content-Length original: ${originalContentLength[0]}`);
        }
        if (originalContentEncoding) {
          log.info(`🔧 [DirectFix] Content-Encoding original: ${originalContentEncoding[0]}`);
        }

        // CORRECTION AGRESSIVE: SUPPRIMER TOUS LES HEADERS PROBLÉMATIQUES
        delete headers['content-length'];
        delete headers['Content-Length'];
        delete headers['content-encoding'];
        delete headers['Content-Encoding'];
        delete headers['transfer-encoding'];
        delete headers['Transfer-Encoding'];

        // FORCER les headers de cache
        headers['Cache-Control'] = ['no-cache, no-store, must-revalidate'];
        headers['Pragma'] = ['no-cache'];
        headers['Expires'] = ['0'];

        // Header de tracking
        headers['X-Edara-Direct-Fixed'] = ['true'];
        headers['X-Edara-Fix-Time'] = [Date.now().toString()];

        this.fixedCount++;
        log.info(`🔧 [DirectFix] ✅ RÉPONSE CORRIGÉE (${this.fixedCount}): ${details.url}`);

        callback({
          cancel: false,
          responseHeaders: headers
        });
      } else {
        callback({ cancel: false });
      }
    });

    // INTERCEPTEUR DIRECT POUR LES ERREURS
    defaultSession.webRequest.onErrorOccurred([], (details) => {
      if (this.isProblematicAsset(details.url)) {
        log.error(`🚨 [DirectFix] ERREUR DÉTECTÉE: ${details.url}`);
        log.error(`🚨 [DirectFix] Type: ${details.error}`);

        if (details.error.includes('CONTENT_LENGTH_MISMATCH')) {
          log.error(`🚨 [DirectFix] ❌ ERREUR CONTENT-LENGTH MISMATCH ENCORE PRÉSENTE !`);
          log.error(`🚨 [DirectFix] ❌ URL: ${details.url}`);
          log.error(`🚨 [DirectFix] ❌ La correction n'a pas fonctionné pour cette requête !`);
        }
      }
    });

    log.info('✅ [DirectFix] Intercepteurs DIRECTS installés avec succès');
  }

  /**
   * Vérifie si une URL est un asset problématique
   */
  isProblematicAsset(url) {
    // Vérifier si c'est une URL locale
    const isLocal = url.includes('**************:8069') || 
                   url.includes('localhost:8069') || 
                   url.includes('127.0.0.1:8069');

    // Vérifier si c'est un asset problématique
    const isAsset = url.includes('web.assets_backend.js') ||
                   url.includes('web.assets_common.js') ||
                   url.includes('web.assets_backend.css') ||
                   url.includes('web.assets_common.css') ||
                   url.includes('load_menus') ||
                   url.includes('web/webclient');

    return isLocal && isAsset;
  }

  /**
   * Obtient les statistiques
   */
  getStats() {
    return {
      isApplied: this.isApplied,
      fixedCount: this.fixedCount
    };
  }
}

// Instance globale
let directFixInstance = null;

/**
 * Obtient l'instance de la correction directe
 */
function getDirectFix() {
  if (!directFixInstance) {
    directFixInstance = new DirectContentLengthFix();
  }
  return directFixInstance;
}

/**
 * Applique la correction directe IMMÉDIATEMENT
 */
function applyDirectContentLengthFix() {
  const fix = getDirectFix();
  fix.applyImmediateFix();
  return fix;
}

/**
 * Fonction d'initialisation à appeler AU TOUT DÉBUT de main.js
 */
function initializeDirectFix() {
  log.info('🚀 [DirectFix] INITIALISATION DE LA CORRECTION DIRECTE...');
  
  try {
    const fix = applyDirectContentLengthFix();
    log.info('✅ [DirectFix] CORRECTION DIRECTE INITIALISÉE AVEC SUCCÈS');
    return fix;
  } catch (error) {
    log.error(`❌ [DirectFix] Erreur lors de l'initialisation: ${error.message}`);
    throw error;
  }
}

module.exports = {
  DirectContentLengthFix,
  getDirectFix,
  applyDirectContentLengthFix,
  initializeDirectFix
};
