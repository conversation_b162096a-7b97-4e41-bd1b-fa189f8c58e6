/**
 * Module de stockage sécurisé pour Edara ERP
 * Ce module gère le stockage sécurisé des informations sensibles comme les tokens de session
 */

const { app } = require('electron');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');
const log = require('electron-log');

// Chemin vers le fichier de stockage
const userDataPath = app.getPath('userData');
const storagePath = path.join(userDataPath, 'session-data.enc');

// Clé de chiffrement dérivée du nom de la machine et de l'ID de l'application
// Cette approche n'est pas parfaitement sécurisée mais offre une protection de base
// Pour une sécurité renforcée, il faudrait utiliser un système de gestion de clés plus robuste
let encryptionKey = null;

/**
 * Initialise la clé de chiffrement
 * @returns {Buffer} - La clé de chiffrement
 */
function initEncryptionKey() {
  if (encryptionKey) return encryptionKey;
  
  try {
    // Utiliser le nom de la machine et l'ID de l'application comme base pour la clé
    const os = require('os');
    const machineName = os.hostname();
    const appId = app.getPath('userData').split(path.sep).pop();
    
    // Dériver une clé de 32 octets (256 bits) à partir de ces informations
    const keyMaterial = `${machineName}-${appId}-edara-erp-session-key`;
    encryptionKey = crypto.createHash('sha256').update(keyMaterial).digest();
    
    log.info('Clé de chiffrement initialisée avec succès');
    return encryptionKey;
  } catch (error) {
    log.error(`Erreur lors de l'initialisation de la clé de chiffrement: ${error.message}`);
    // Utiliser une clé par défaut en cas d'erreur (moins sécurisé)
    return crypto.createHash('sha256').update('edara-erp-default-key').digest();
  }
}

/**
 * Chiffre les données
 * @param {Object} data - Les données à chiffrer
 * @returns {string} - Les données chiffrées en format hexadécimal
 */
function encrypt(data) {
  try {
    // Initialiser la clé si nécessaire
    const key = initEncryptionKey();
    
    // Générer un vecteur d'initialisation aléatoire
    const iv = crypto.randomBytes(16);
    
    // Créer un chiffreur
    const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
    
    // Chiffrer les données
    const jsonData = JSON.stringify(data);
    let encrypted = cipher.update(jsonData, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    // Combiner le vecteur d'initialisation et les données chiffrées
    return iv.toString('hex') + ':' + encrypted;
  } catch (error) {
    log.error(`Erreur lors du chiffrement des données: ${error.message}`);
    return null;
  }
}

/**
 * Déchiffre les données
 * @param {string} encryptedData - Les données chiffrées en format hexadécimal
 * @returns {Object|null} - Les données déchiffrées ou null en cas d'erreur
 */
function decrypt(encryptedData) {
  try {
    // Initialiser la clé si nécessaire
    const key = initEncryptionKey();
    
    // Séparer le vecteur d'initialisation et les données chiffrées
    const parts = encryptedData.split(':');
    if (parts.length !== 2) {
      throw new Error('Format de données chiffrées invalide');
    }
    
    const iv = Buffer.from(parts[0], 'hex');
    const encrypted = parts[1];
    
    // Créer un déchiffreur
    const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
    
    // Déchiffrer les données
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    // Parser les données JSON
    return JSON.parse(decrypted);
  } catch (error) {
    log.error(`Erreur lors du déchiffrement des données: ${error.message}`);
    return null;
  }
}

/**
 * Sauvegarde les données de session
 * @param {Object} sessionData - Les données de session à sauvegarder
 * @returns {boolean} - true si la sauvegarde a réussi, false sinon
 */
function saveSessionData(sessionData) {
  try {
    // Créer le répertoire s'il n'existe pas
    const directory = path.dirname(storagePath);
    if (!fs.existsSync(directory)) {
      fs.mkdirSync(directory, { recursive: true });
    }
    
    // Ajouter un timestamp pour suivre quand la session a été sauvegardée
    const dataToSave = {
      ...sessionData,
      timestamp: Date.now()
    };
    
    // Chiffrer les données
    const encryptedData = encrypt(dataToSave);
    if (!encryptedData) {
      return false;
    }
    
    // Sauvegarder les données chiffrées
    fs.writeFileSync(storagePath, encryptedData, 'utf8');
    log.info('Données de session sauvegardées avec succès');
    return true;
  } catch (error) {
    log.error(`Erreur lors de la sauvegarde des données de session: ${error.message}`);
    return false;
  }
}

/**
 * Charge les données de session
 * @returns {Object|null} - Les données de session ou null si aucune donnée n'est disponible
 */
function loadSessionData() {
  try {
    // Vérifier si le fichier existe
    if (!fs.existsSync(storagePath)) {
      log.info('Aucune donnée de session trouvée');
      return null;
    }
    
    // Lire les données chiffrées
    const encryptedData = fs.readFileSync(storagePath, 'utf8');
    
    // Déchiffrer les données
    const sessionData = decrypt(encryptedData);
    if (!sessionData) {
      log.warn('Impossible de déchiffrer les données de session');
      return null;
    }
    
    log.info('Données de session chargées avec succès');
    return sessionData;
  } catch (error) {
    log.error(`Erreur lors du chargement des données de session: ${error.message}`);
    return null;
  }
}

/**
 * Supprime les données de session
 * @returns {boolean} - true si la suppression a réussi, false sinon
 */
function clearSessionData() {
  try {
    // Vérifier si le fichier existe
    if (!fs.existsSync(storagePath)) {
      log.info('Aucune donnée de session à supprimer');
      return true;
    }
    
    // Supprimer le fichier
    fs.unlinkSync(storagePath);
    log.info('Données de session supprimées avec succès');
    return true;
  } catch (error) {
    log.error(`Erreur lors de la suppression des données de session: ${error.message}`);
    return false;
  }
}

// Exporter les fonctions
module.exports = {
  saveSessionData,
  loadSessionData,
  clearSessionData
};
