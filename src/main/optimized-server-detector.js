/**
 * Détecteur de serveur Odoo optimisé - Architecture finale
 * Intègre tous les mécanismes de découverte et stratégies de cache optimisées
 */

const log = require('electron-log');
const { EventEmitter } = require('events');
const EnhancedServerDetector = require('./enhanced-server-detector');
const NetworkScanner = require('./network-scanner');
const IntelligentIpCache = require('./intelligent-ip-cache');

class OptimizedServerDetector extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.config = {
      // Timeouts optimisés
      fastScanTimeout: 300,      // 300ms pour scan rapide
      normalScanTimeout: 800,    // 800ms pour scan normal
      remoteScanTimeout: 3000,   // 3s pour serveurs distants
      
      // Stratégies de découverte
      enableMDNS: false,         // mDNS/Bonjour (future)
      enableNetworkScan: true,   // Balayage réseau
      enableCacheFirst: true,    // Cache en priorité
      enableAdaptive: true,      // Timeouts adaptatifs
      
      // Limites de performance
      maxConcurrentScans: 15,
      maxNetworkRange: 20,
      maxCacheAge: 30 * 60 * 1000, // 30 minutes
      
      // Fallbacks
      fallbackToRemote: true,
      fallbackToLocalhost: true,
      
      ...options
    };
    
    // Composants spécialisés
    this.enhancedDetector = new EnhancedServerDetector();
    this.networkScanner = new NetworkScanner({
      maxConcurrent: this.config.maxConcurrentScans,
      portTimeout: this.config.fastScanTimeout,
      httpTimeout: this.config.normalScanTimeout,
      adaptiveTimeout: this.config.enableAdaptive
    });
    this.ipCache = new IntelligentIpCache({
      defaultTtl: this.config.maxCacheAge * 2,
      revalidateThreshold: 0.3
    });
    
    // État de détection
    this.detectionState = {
      isDetecting: false,
      lastDetection: 0,
      preferredServer: null,
      detectionHistory: []
    };
    
    // Métriques globales
    this.metrics = {
      totalDetections: 0,
      successfulDetections: 0,
      averageDetectionTime: 0,
      cacheHitRate: 0,
      networkScanSuccess: 0
    };
    
    this.setupEventHandlers();
    log.info('Détecteur de serveur optimisé initialisé');
  }

  /**
   * Configuration des gestionnaires d'événements
   */
  setupEventHandlers() {
    // Événements du cache
    this.ipCache.on('ip-validated', (data) => {
      this.emit('server-validated', data);
    });
    
    this.ipCache.on('ip-invalidated', (data) => {
      this.emit('server-invalidated', data);
      // Invalider le serveur préféré si nécessaire
      if (this.detectionState.preferredServer?.ip === data.ip) {
        this.detectionState.preferredServer = null;
      }
    });
    
    // Événements du détecteur amélioré
    this.enhancedDetector.on('discovery-completed', (data) => {
      this.updateMetrics(data);
    });
  }

  /**
   * Détection principale avec stratégie multi-niveaux
   */
  async detectServer(options = {}) {
    if (this.detectionState.isDetecting) {
      log.warn('Détection déjà en cours, utilisation du résultat en cache');
      return this.detectionState.preferredServer || this.getFallbackServer();
    }
    
    const startTime = Date.now();
    this.detectionState.isDetecting = true;
    
    try {
      log.info('🔍 Démarrage de la détection optimisée de serveur...');
      
      // Stratégie en cascade pour performance optimale
      let server = null;
      
      // Niveau 1: Cache intelligent (le plus rapide)
      if (this.config.enableCacheFirst) {
        server = await this.detectFromCache(options);
        if (server) {
          log.info(`✅ Serveur trouvé en cache: ${server.url}`);
          return this.finalizeDetection(server, startTime, 'cache');
        }
      }
      
      // Niveau 2: Serveurs connus/préférés (rapide)
      server = await this.detectKnownServers(options);
      if (server) {
        log.info(`✅ Serveur connu trouvé: ${server.url}`);
        return this.finalizeDetection(server, startTime, 'known');
      }
      
      // Niveau 3: Balayage réseau intelligent (moyen)
      if (this.config.enableNetworkScan) {
        server = await this.detectViaNetworkScan(options);
        if (server) {
          log.info(`✅ Serveur trouvé par balayage: ${server.url}`);
          return this.finalizeDetection(server, startTime, 'network-scan');
        }
      }
      
      // Niveau 4: Serveur distant (lent mais fiable)
      if (this.config.fallbackToRemote) {
        server = await this.detectRemoteServer(options);
        if (server) {
          log.info(`✅ Serveur distant trouvé: ${server.url}`);
          return this.finalizeDetection(server, startTime, 'remote');
        }
      }
      
      // Niveau 5: Fallback localhost (dernier recours)
      if (this.config.fallbackToLocalhost) {
        server = this.getFallbackServer();
        log.warn(`⚠️ Utilisation du serveur de fallback: ${server.url}`);
        return this.finalizeDetection(server, startTime, 'fallback');
      }
      
      throw new Error('Aucun serveur Odoo disponible');
      
    } catch (error) {
      log.error(`❌ Erreur lors de la détection: ${error.message}`);
      
      // Fallback d'urgence
      const fallbackServer = this.getFallbackServer();
      return this.finalizeDetection(fallbackServer, startTime, 'error-fallback');
      
    } finally {
      this.detectionState.isDetecting = false;
    }
  }

  /**
   * Détection depuis le cache intelligent
   */
  async detectFromCache(options = {}) {
    try {
      const cached = await this.ipCache.getValidIp({
        preferLocal: true,
        maxAge: this.config.maxCacheAge,
        requireValidation: false
      });
      
      if (cached && cached.confidence > 0.7) {
        // Validation rapide en arrière-plan
        this.validateServerInBackground(cached);
        
        return {
          url: cached.url,
          ip: cached.ip,
          type: 'local',
          method: 'cache',
          confidence: cached.confidence,
          responseTime: cached.metadata.responseTime || null
        };
      }
      
      return null;
    } catch (error) {
      log.warn(`Erreur lors de la détection en cache: ${error.message}`);
      return null;
    }
  }

  /**
   * Détection des serveurs connus
   */
  async detectKnownServers(options = {}) {
    try {
      // 1. Serveur préféré
      if (this.detectionState.preferredServer) {
        const isValid = await this.quickValidateServer(this.detectionState.preferredServer.url);
        if (isValid) {
          return this.detectionState.preferredServer;
        } else {
          this.detectionState.preferredServer = null;
        }
      }
      
      // 2. Dernière IP valide
      const lastValidIp = this.getLastValidIp();
      if (lastValidIp) {
        const url = `http://${lastValidIp}:8069`;
        const isValid = await this.quickValidateServer(url);
        if (isValid) {
          return {
            url,
            ip: lastValidIp,
            type: 'local',
            method: 'last-valid',
            responseTime: null
          };
        }
      }
      
      // 3. Localhost standard
      const localhostValid = await this.quickValidateServer('http://localhost:8069');
      if (localhostValid) {
        return {
          url: 'http://localhost:8069',
          ip: '127.0.0.1',
          type: 'local',
          method: 'localhost',
          responseTime: null
        };
      }
      
      return null;
    } catch (error) {
      log.warn(`Erreur lors de la détection des serveurs connus: ${error.message}`);
      return null;
    }
  }

  /**
   * Détection via balayage réseau
   */
  async detectViaNetworkScan(options = {}) {
    try {
      const localIp = this.getLocalIpAddress();
      if (!localIp) {
        log.warn('Aucune IP locale trouvée pour le balayage');
        return null;
      }
      
      log.info(`🌐 Balayage réseau depuis ${localIp}...`);
      
      const servers = await this.networkScanner.scanNetwork(localIp, {
        maxIps: this.config.maxNetworkRange,
        fullScan: false,
        localRange: 10
      });
      
      if (servers.length > 0) {
        // Sélectionner le meilleur serveur
        const bestServer = servers[0]; // Déjà trié par priorité
        
        // Sauvegarder dans le cache
        await this.ipCache.saveIp(bestServer.ip, {
          responseTime: bestServer.responseTime,
          reliability: 0.8,
          source: 'network-scan',
          validated: true
        });
        
        return bestServer;
      }
      
      return null;
    } catch (error) {
      log.error(`Erreur lors du balayage réseau: ${error.message}`);
      return null;
    }
  }

  /**
   * Détection du serveur distant
   */
  async detectRemoteServer(options = {}) {
    try {
      const remoteUrl = this.getRemoteServerUrl();
      log.info(`🌍 Vérification du serveur distant: ${remoteUrl}`);
      
      const isValid = await this.quickValidateServer(remoteUrl, this.config.remoteScanTimeout);
      
      if (isValid) {
        return {
          url: remoteUrl,
          type: 'remote',
          method: 'remote-check',
          responseTime: null
        };
      }
      
      return null;
    } catch (error) {
      log.warn(`Erreur lors de la vérification du serveur distant: ${error.message}`);
      return null;
    }
  }

  /**
   * Validation rapide d'un serveur
   */
  async quickValidateServer(url, timeout = this.config.normalScanTimeout) {
    try {
      const axios = require('axios');
      
      const response = await axios.get(`${url}/web/database/selector`, {
        timeout,
        validateStatus: status => status >= 200 && status < 500,
        headers: {
          'User-Agent': 'Edara-Detector/2.0',
          'Accept': 'text/html,application/json',
          'Cache-Control': 'no-cache'
        }
      });
      
      return response.status < 400 || response.status === 401 || response.status === 403;
    } catch (error) {
      return error.response?.status === 401 || error.response?.status === 403;
    }
  }

  /**
   * Validation en arrière-plan
   */
  async validateServerInBackground(server) {
    try {
      const isValid = await this.quickValidateServer(server.url);
      
      if (isValid) {
        // Mettre à jour le cache avec validation
        await this.ipCache.saveIp(server.ip, {
          ...server.metadata,
          validated: true,
          lastValidation: Date.now()
        });
      } else {
        // Invalider le serveur
        this.emit('server-invalidated', { ip: server.ip, url: server.url });
      }
    } catch (error) {
      log.debug(`Erreur lors de la validation en arrière-plan: ${error.message}`);
    }
  }

  /**
   * Finalise la détection et met à jour l'état
   */
  finalizeDetection(server, startTime, method) {
    const detectionTime = Date.now() - startTime;
    
    // Mettre à jour l'état
    this.detectionState.preferredServer = server;
    this.detectionState.lastDetection = Date.now();
    
    // Ajouter à l'historique
    this.detectionState.detectionHistory.push({
      timestamp: Date.now(),
      server,
      method,
      detectionTime
    });
    
    // Garder seulement les 10 dernières détections
    if (this.detectionState.detectionHistory.length > 10) {
      this.detectionState.detectionHistory = this.detectionState.detectionHistory.slice(-10);
    }
    
    // Mettre à jour les métriques
    this.metrics.totalDetections++;
    if (server && !server.fallback) {
      this.metrics.successfulDetections++;
    }
    
    this.metrics.averageDetectionTime = 
      (this.metrics.averageDetectionTime * (this.metrics.totalDetections - 1) + detectionTime) / 
      this.metrics.totalDetections;
    
    // Sauvegarder l'IP si c'est un serveur local
    if (server.type === 'local' && server.ip && server.ip !== '127.0.0.1') {
      this.saveLastValidIp(server.ip);
    }
    
    log.info(`🎯 Détection terminée en ${detectionTime}ms via ${method}: ${server.url}`);
    
    this.emit('server-detected', {
      server,
      method,
      detectionTime,
      metrics: this.getMetrics()
    });
    
    return server;
  }

  /**
   * Met à jour les métriques globales
   */
  updateMetrics(data) {
    if (data.metrics) {
      // Calculer le taux de succès du cache
      const cacheStats = this.ipCache.getStats();
      this.metrics.cacheHitRate = cacheStats.hits / (cacheStats.hits + cacheStats.misses) || 0;
      
      // Calculer le taux de succès du balayage réseau
      const scanStats = this.networkScanner.getStats();
      this.metrics.networkScanSuccess = scanStats.successRate || 0;
    }
  }

  /**
   * Obtient un serveur de fallback
   */
  getFallbackServer() {
    const remoteUrl = this.getRemoteServerUrl();
    
    return {
      url: remoteUrl,
      type: 'remote',
      method: 'fallback',
      fallback: true,
      responseTime: null
    };
  }

  /**
   * Réinitialise la détection et force un nouveau scan
   */
  async resetAndRediscover() {
    log.info('🔄 Réinitialisation et nouvelle découverte...');
    
    // Réinitialiser l'état
    this.detectionState.preferredServer = null;
    this.detectionState.detectionHistory = [];
    
    // Réinitialiser le cache
    await this.ipCache.reset();
    
    // Nouvelle détection
    return await this.detectServer({ useCache: false, forceScan: true });
  }

  /**
   * Obtient les métriques complètes
   */
  getMetrics() {
    return {
      ...this.metrics,
      cache: this.ipCache.getStats(),
      scanner: this.networkScanner.getStats(),
      detection: this.detectionState
    };
  }

  /**
   * Obtient les informations de diagnostic
   */
  getDiagnostics() {
    return {
      config: this.config,
      metrics: this.getMetrics(),
      state: this.detectionState,
      components: {
        enhancedDetector: this.enhancedDetector.getStats(),
        networkScanner: this.networkScanner.getStats(),
        ipCache: this.ipCache.getStats()
      }
    };
  }

  // Méthodes utilitaires
  getLocalIpAddress() {
    try {
      const os = require('os');
      const interfaces = os.networkInterfaces();
      
      for (const ifaceName in interfaces) {
        const iface = interfaces[ifaceName];
        for (const alias of iface) {
          if (alias.family === 'IPv4' && !alias.internal) {
            return alias.address;
          }
        }
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  getRemoteServerUrl() {
    try {
      const userPreferences = require('./user-preferences');
      const workspaceName = userPreferences.getWorkspaceName();
      
      if (workspaceName && workspaceName !== 'edara') {
        return `https://${workspaceName}.ligne-digitale.com`;
      }
      return 'https://edara.ligne-digitale.com';
    } catch (error) {
      return 'https://edara.ligne-digitale.com';
    }
  }

  getLastValidIp() {
    try {
      const lastValidIp = require('./last-valid-ip');
      return lastValidIp.getLastValidIp();
    } catch (error) {
      return null;
    }
  }

  saveLastValidIp(ip) {
    try {
      const lastValidIp = require('./last-valid-ip');
      lastValidIp.saveLastValidIp(ip);
    } catch (error) {
      log.warn(`Impossible de sauvegarder l'IP: ${error.message}`);
    }
  }

  /**
   * Nettoie les ressources
   */
  cleanup() {
    this.enhancedDetector.cleanup();
    this.ipCache.removeAllListeners();
    this.removeAllListeners();
    
    log.info('Détecteur de serveur optimisé nettoyé');
  }
}

module.exports = OptimizedServerDetector;
