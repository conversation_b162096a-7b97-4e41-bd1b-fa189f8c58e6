/**
 * Gestionnaire de Récupération de Session pour Edara ERP
 * Gère les scénarios de déconnexion, expiration et reconnexion automatique
 */

const log = require('electron-log');
const EventEmitter = require('events');

class SessionRecoveryManager extends EventEmitter {
  constructor(sessionManager) {
    super();
    this.sessionManager = sessionManager;
    
    // Configuration de récupération
    this.config = {
      maxRetryAttempts: 3,
      retryDelay: 2000, // 2 secondes
      exponentialBackoff: true,
      maxRetryDelay: 30000, // 30 secondes max
      gracePeriod: 60000, // 1 minute de grâce avant déconnexion
      autoReconnect: true
    };
    
    // État de récupération
    this.recoveryState = {
      isRecovering: false,
      retryCount: 0,
      lastFailure: null,
      recoveryStartTime: null
    };
    
    // Stockage des dernières credentials valides (chiffré)
    this.lastValidCredentials = null;
    
    this.setupEventListeners();
  }

  /**
   * Configure les écouteurs d'événements pour la récupération automatique
   */
  setupEventListeners() {
    // Écouter les événements de session du gestionnaire principal
    this.sessionManager.on('session-expired', this.handleSessionExpired.bind(this));
    this.sessionManager.on('session-invalid', this.handleSessionInvalid.bind(this));
    this.sessionManager.on('authentication-failed', this.handleAuthenticationFailed.bind(this));
    this.sessionManager.on('connection-lost', this.handleConnectionLost.bind(this));
    
    // Écouter les événements réseau
    this.setupNetworkListeners();
  }

  /**
   * Configure la surveillance réseau pour détecter les reconnexions
   */
  setupNetworkListeners() {
    const { net } = require('electron');
    
    // Surveiller l'état de la connexion réseau
    if (net.isOnline()) {
      this.onNetworkOnline();
    }
    
    // Note: Electron ne fournit pas d'événements réseau natifs
    // Utiliser une vérification périodique comme alternative
    this.networkCheckInterval = setInterval(() => {
      if (net.isOnline() && this.recoveryState.isRecovering) {
        this.attemptRecovery();
      }
    }, 5000);
  }

  /**
   * Gère l'expiration de session avec tentative de renouvellement
   */
  async handleSessionExpired(event) {
    log.info('Gestion de l\'expiration de session');
    
    // Vérifier si nous sommes dans la période de grâce
    const timeSinceLastActivity = Date.now() - this.sessionManager.sessionState.lastActivity;
    
    if (timeSinceLastActivity < this.config.gracePeriod) {
      log.info('Tentative de renouvellement de session dans la période de grâce');
      
      try {
        // Essayer de renouveler la session existante
        const renewed = await this.renewSession();
        if (renewed) {
          log.info('Session renouvelée avec succès');
          this.emit('session-renewed');
          return;
        }
      } catch (error) {
        log.warn(`Échec du renouvellement de session: ${error.message}`);
      }
    }
    
    // Si le renouvellement échoue, tenter une reconnexion complète
    if (this.config.autoReconnect && this.lastValidCredentials) {
      await this.startRecoveryProcess('session-expired');
    } else {
      this.emit('session-recovery-required', { reason: 'session-expired' });
    }
  }

  /**
   * Gère les sessions invalides
   */
  async handleSessionInvalid(event) {
    log.info('Gestion de session invalide');
    
    if (this.config.autoReconnect && this.lastValidCredentials) {
      await this.startRecoveryProcess('session-invalid');
    } else {
      this.emit('session-recovery-required', { reason: 'session-invalid' });
    }
  }

  /**
   * Gère les échecs d'authentification
   */
  async handleAuthenticationFailed(event) {
    log.warn(`Échec d'authentification: ${event.error}`);
    
    // Ne pas tenter de récupération automatique en cas d'échec d'authentification
    // (probablement un problème de credentials)
    this.recoveryState.isRecovering = false;
    this.emit('authentication-failed-final', event);
  }

  /**
   * Gère la perte de connexion réseau
   */
  async handleConnectionLost(event) {
    log.info('Perte de connexion détectée');
    
    // Attendre le retour de la connexion pour tenter une récupération
    this.recoveryState.isRecovering = true;
    this.recoveryState.lastFailure = 'connection-lost';
    
    this.emit('connection-lost', { timestamp: Date.now() });
  }

  /**
   * Démarre le processus de récupération
   */
  async startRecoveryProcess(reason) {
    if (this.recoveryState.isRecovering) {
      log.info('Processus de récupération déjà en cours');
      return;
    }
    
    log.info(`Démarrage du processus de récupération pour: ${reason}`);
    
    this.recoveryState = {
      isRecovering: true,
      retryCount: 0,
      lastFailure: reason,
      recoveryStartTime: Date.now()
    };
    
    this.emit('recovery-started', { reason });
    
    await this.attemptRecovery();
  }

  /**
   * Tente une récupération de session
   */
  async attemptRecovery() {
    if (!this.recoveryState.isRecovering || !this.lastValidCredentials) {
      return;
    }
    
    if (this.recoveryState.retryCount >= this.config.maxRetryAttempts) {
      log.error('Nombre maximum de tentatives de récupération atteint');
      await this.failRecovery('max-retries-exceeded');
      return;
    }
    
    this.recoveryState.retryCount++;
    
    log.info(`Tentative de récupération ${this.recoveryState.retryCount}/${this.config.maxRetryAttempts}`);
    
    try {
      // Décrypter les credentials
      const credentials = await this.decryptCredentials(this.lastValidCredentials);
      
      // Tenter la reconnexion
      const result = await this.sessionManager.authenticate(credentials);
      
      if (result.success) {
        log.info('Récupération de session réussie');
        await this.completeRecovery();
      } else {
        throw new Error(result.error || 'Authentification échouée');
      }
    } catch (error) {
      log.warn(`Tentative de récupération ${this.recoveryState.retryCount} échouée: ${error.message}`);
      
      // Calculer le délai avant la prochaine tentative
      const delay = this.calculateRetryDelay();
      
      log.info(`Prochaine tentative dans ${delay}ms`);
      
      setTimeout(() => {
        this.attemptRecovery();
      }, delay);
    }
  }

  /**
   * Tente de renouveler la session existante
   */
  async renewSession() {
    try {
      // Utiliser l'API Odoo pour renouveler la session
      const axios = require('axios');
      
      const renewUrl = `${this.sessionManager.sessionState.serverUrl}/web/session/change_password`;
      const response = await axios.post(renewUrl, {
        jsonrpc: '2.0',
        method: 'call',
        params: {},
        id: Date.now().toString()
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Cookie': `session_id=${this.sessionManager.sessionState.sessionId}`
        },
        timeout: 10000
      });
      
      // Si la requête réussit, la session est encore valide
      if (response.status === 200) {
        // Prolonger l'expiration
        this.sessionManager.sessionState.expiresAt = Date.now() + this.sessionManager.config.sessionTimeout;
        this.sessionManager.sessionState.lastActivity = Date.now();
        
        await this.sessionManager.persistSession();
        return true;
      }
      
      return false;
    } catch (error) {
      log.warn(`Échec du renouvellement de session: ${error.message}`);
      return false;
    }
  }

  /**
   * Complète le processus de récupération
   */
  async completeRecovery() {
    const recoveryDuration = Date.now() - this.recoveryState.recoveryStartTime;
    
    log.info(`Récupération de session complétée en ${recoveryDuration}ms après ${this.recoveryState.retryCount} tentative(s)`);
    
    this.recoveryState = {
      isRecovering: false,
      retryCount: 0,
      lastFailure: null,
      recoveryStartTime: null
    };
    
    this.emit('recovery-completed', {
      duration: recoveryDuration,
      attempts: this.recoveryState.retryCount
    });
  }

  /**
   * Marque la récupération comme échouée
   */
  async failRecovery(reason) {
    const recoveryDuration = Date.now() - this.recoveryState.recoveryStartTime;
    
    log.error(`Récupération de session échouée après ${recoveryDuration}ms et ${this.recoveryState.retryCount} tentative(s): ${reason}`);
    
    this.recoveryState.isRecovering = false;
    
    this.emit('recovery-failed', {
      reason,
      duration: recoveryDuration,
      attempts: this.recoveryState.retryCount
    });
    
    // Nettoyer la session et rediriger vers la connexion
    await this.sessionManager.clearSession();
  }

  /**
   * Calcule le délai avant la prochaine tentative (avec backoff exponentiel)
   */
  calculateRetryDelay() {
    if (!this.config.exponentialBackoff) {
      return this.config.retryDelay;
    }
    
    const baseDelay = this.config.retryDelay;
    const exponentialDelay = baseDelay * Math.pow(2, this.recoveryState.retryCount - 1);
    
    return Math.min(exponentialDelay, this.config.maxRetryDelay);
  }

  /**
   * Stocke les credentials pour la récupération automatique
   */
  async storeCredentialsForRecovery(credentials) {
    try {
      // Chiffrer et stocker les credentials
      this.lastValidCredentials = await this.encryptCredentials(credentials);
      log.info('Credentials stockés pour récupération automatique');
    } catch (error) {
      log.error(`Erreur lors du stockage des credentials: ${error.message}`);
    }
  }

  /**
   * Chiffre les credentials
   */
  async encryptCredentials(credentials) {
    const crypto = require('crypto');
    const key = crypto.randomBytes(32);
    const iv = crypto.randomBytes(16);
    
    const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
    let encrypted = cipher.update(JSON.stringify(credentials), 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    return {
      encrypted,
      key: key.toString('hex'),
      iv: iv.toString('hex')
    };
  }

  /**
   * Décrypte les credentials
   */
  async decryptCredentials(encryptedData) {
    const crypto = require('crypto');
    const key = Buffer.from(encryptedData.key, 'hex');
    const iv = Buffer.from(encryptedData.iv, 'hex');
    
    const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return JSON.parse(decrypted);
  }

  /**
   * Gestionnaire pour le retour de la connexion réseau
   */
  onNetworkOnline() {
    if (this.recoveryState.isRecovering && this.recoveryState.lastFailure === 'connection-lost') {
      log.info('Connexion réseau restaurée, tentative de récupération');
      setTimeout(() => {
        this.attemptRecovery();
      }, 1000); // Petit délai pour s'assurer que la connexion est stable
    }
  }

  /**
   * Nettoie les ressources
   */
  cleanup() {
    if (this.networkCheckInterval) {
      clearInterval(this.networkCheckInterval);
    }
    
    this.removeAllListeners();
  }

  // Getters pour l'état de récupération
  get isRecovering() { return this.recoveryState.isRecovering; }
  get retryCount() { return this.recoveryState.retryCount; }
  get lastFailure() { return this.recoveryState.lastFailure; }
}

module.exports = SessionRecoveryManager;
