/**
 * Module d'authentification Odoo pour l'application Edara ERP
 * Ce module gère l'authentification avec un serveur Odoo
 * Version pour le main process
 */

// Importer les bibliothèques nécessaires
const xmlrpc = require('xmlrpc');
const electron = require('electron');
const log = require('electron-log');
const axios = require('axios');
const { URL } = require('url');
const { session } = require('electron');

// Importer la fonction pour définir le cookie de session
// Utiliser la fonction existante pour éviter les conflits de noms
const { setOdooSessionCookie } = require('./odoo-window');

// Configuration de electron-log
log.transports.file.level = 'info';
log.transports.console.level = 'debug';
log.transports.console.format = '[{level}] {text}';

/**
 * Authentifie un utilisateur auprès d'un serveur Odoo en utilisant l'API XML-RPC
 * @param {string} username - L'identifiant (nom d'utilisateur ou email) de l'utilisateur
 * @param {string} password - Le mot de passe de l'utilisateur
 * @param {string} server - L'URL du serveur Odoo (ex: http://localhost:8069 ou https://edara.ligne-digitale.com)
 * @param {number} [timeout=30000] - Délai d'expiration de la requête en millisecondes (30 secondes par défaut)
 * @param {string} [dbName='ligne-digitale'] - Le nom de la base de données Odoo
 * @returns {Promise<Object>} - Un objet contenant le résultat de l'authentification
 */
async function authenticateWithOdoo(username, password, server, timeout = 30000, dbName = 'ligne-digitale') {
  // Horodatage du début de l'authentification pour mesurer le temps d'exécution
  const startTime = Date.now();
  log.info(`Début de l'authentification pour l'utilisateur ${username}`);
  log.info(`Paramètres d'authentification: serveur=${server}, base de données=${dbName}, timeout=${timeout}ms`);

  try {
    // Vérifier que les paramètres requis sont fournis
    if (!username || !password || !server || !dbName) {
      const missingParams = [];
      if (!username) missingParams.push('nom d\'utilisateur');
      if (!password) missingParams.push('mot de passe');
      if (!server) missingParams.push('URL du serveur');
      if (!dbName) missingParams.push('nom de la base de données');

      const errorMsg = `Paramètres manquants: ${missingParams.join(', ')}`;
      log.error(errorMsg);
      return {
        success: false,
        error: errorMsg,
        errorType: 'MISSING_PARAMS'
      };
    }

    // Vérifier que l'URL du serveur est valide
    let serverUrl;
    try {
      serverUrl = new URL(server);
    } catch (urlError) {
      const errorMsg = `URL du serveur invalide: ${server}`;
      log.error(errorMsg, urlError);
      return {
        success: false,
        error: errorMsg,
        errorType: 'INVALID_URL'
      };
    }

    log.info(`Tentative d'authentification avec Odoo via XML-RPC: ${server}`);

    // Extraire le protocole et l'hôte de l'URL du serveur
    const protocol = serverUrl.protocol.includes('https') ? 'https' : 'http';
    const host = serverUrl.hostname;
    const port = serverUrl.port || (protocol === 'https' ? 443 : 80);

    log.info(`Connexion à ${protocol}://${host}:${port}/xmlrpc/2/common`);

    // Créer un client XML-RPC avec options de timeout
    const clientOptions = {
      host,
      port,
      path: '/xmlrpc/2/common',
      headers: {
        'User-Agent': 'Edara-ERP-Client/1.0',
        'Connection': 'keep-alive'
      }
    };

    const client = protocol === 'https'
      ? xmlrpc.createSecureClient(clientOptions)
      : xmlrpc.createClient(clientOptions);

    // Préparer les paramètres pour l'authentification
    // L'API XML-RPC d'Odoo attend: (db, username, password, {})
    const params = [dbName, username, password, {}];

    // Ajouter un log pour afficher les paramètres de la requête (sans le mot de passe)
    log.info(`Paramètres de la requête XML-RPC:`, JSON.stringify([
      params[0], // db
      params[1], // username
      '********', // password masqué
      params[3]  // {}
    ]));

    // Créer une promesse pour gérer l'appel XML-RPC avec timeout
    return new Promise((resolve, reject) => {
      // Créer un timer pour le timeout
      const timeoutId = setTimeout(() => {
        const errorMsg = `Délai d'attente dépassé (${timeout}ms) lors de la connexion au serveur Odoo`;
        log.error(errorMsg);
        resolve({
          success: false,
          error: errorMsg,
          errorType: 'TIMEOUT'
        });
      }, timeout);

      // Appeler la méthode 'authenticate' via XML-RPC
      client.methodCall('authenticate', params, (error, value) => {
        // Annuler le timer de timeout
        clearTimeout(timeoutId);

        // Calculer le temps d'exécution
        const executionTime = Date.now() - startTime;
        log.info(`Temps d'exécution de la requête: ${executionTime}ms`);

        if (error) {
          // Analyser l'erreur pour fournir un message plus précis
          let errorMsg = 'Erreur de connexion au serveur Odoo';
          let errorType = 'CONNECTION_ERROR';

          if (error.code === 'ECONNREFUSED') {
            errorMsg = `Connexion refusée par le serveur: ${server}`;
            errorType = 'CONNECTION_REFUSED';
          } else if (error.code === 'ENOTFOUND') {
            errorMsg = `Serveur introuvable: ${server}`;
            errorType = 'SERVER_NOT_FOUND';
          } else if (error.code === 'ETIMEDOUT') {
            errorMsg = `Délai d'attente dépassé lors de la connexion au serveur: ${server}`;
            errorType = 'CONNECTION_TIMEOUT';
          } else if (error.message && error.message.includes('certificate')) {
            errorMsg = `Erreur de certificat SSL: ${error.message}`;
            errorType = 'SSL_ERROR';
          }

          log.error(`${errorType}: ${errorMsg}`, error);
          resolve({
            success: false,
            error: errorMsg,
            errorType: errorType,
            details: error.message
          });
          return;
        }

        log.info(`Réponse de l'authentification XML-RPC:`, value);

        // Si la valeur retournée est false ou 0, l'authentification a échoué
        if (value === false || value === 0) {
          log.error(`Échec de l'authentification: identifiants incorrects pour l'utilisateur ${username}`);
          resolve({
            success: false,
            error: 'Identifiants incorrects',
            errorType: 'INVALID_CREDENTIALS'
          });
          return;
        }

        // Si la valeur est un nombre positif, c'est l'ID de l'utilisateur (uid)
        if (typeof value === 'number' && value > 0) {
          log.info(`Authentification réussie pour l'utilisateur ${username}. UID:`, value);

          // Calculer le temps d'exécution total
          const executionTime = Date.now() - startTime;
          log.info(`Temps d'exécution total de l'authentification: ${executionTime}ms`);

          // Retourner directement l'objet avec les informations de base
          resolve({
            success: true,
            userId: value,
            name: username,
            server: server,
            dbName: dbName
          });
          return;
        }

        // Si nous arrivons ici, c'est que la réponse n'est pas dans un format attendu
        log.error(`Format de réponse inattendu:`, value);
        resolve({
          success: false,
          error: 'Format de réponse inattendu',
          errorType: 'UNEXPECTED_RESPONSE',
          details: JSON.stringify(value)
        });
      });
    });
  } catch (error) {
    // Calculer le temps d'exécution même en cas d'erreur
    const executionTime = Date.now() - startTime;

    // Analyser l'erreur pour fournir un message plus précis
    let errorMsg = 'Erreur lors de l\'authentification avec Odoo';
    let errorType = 'UNKNOWN_ERROR';

    if (error instanceof TypeError) {
      errorMsg = `Erreur de type: ${error.message}`;
      errorType = 'TYPE_ERROR';
    } else if (error instanceof ReferenceError) {
      errorMsg = `Erreur de référence: ${error.message}`;
      errorType = 'REFERENCE_ERROR';
    } else if (error instanceof SyntaxError) {
      errorMsg = `Erreur de syntaxe: ${error.message}`;
      errorType = 'SYNTAX_ERROR';
    } else if (error.name === 'NetworkError') {
      errorMsg = `Erreur réseau: ${error.message}`;
      errorType = 'NETWORK_ERROR';
    }

    log.error(`${errorType}: ${errorMsg}`, error);
    log.error(`Stack trace:`, error.stack);

    return {
      success: false,
      error: errorMsg,
      errorType: errorType,
      details: error.message,
      stack: error.stack
    };
  } finally {
    const executionTime = Date.now() - startTime;
    log.info(`Tentative d'authentification terminée (succès ou échec) en ${executionTime}ms.`);
  }
}

/**
 * Vérifie si un token d'authentification est valide en utilisant l'API standard d'Odoo
 * @param {string} token - Le token d'authentification à vérifier (UID)
 * @param {string} server - L'URL du serveur Odoo
 * @param {string} [dbName='ligne-digitale'] - Le nom de la base de données Odoo
 * @returns {Promise<Object>} - Un objet contenant le résultat de la validation
 */
async function verifyOdooToken(token, server, dbName = 'ligne-digitale') {
  const startTime = Date.now();
  log.info(`Début de la vérification du token pour l'utilisateur avec UID: ${token}`);

  try {
    if (!token || !server) {
      log.error(`Token ou serveur manquant pour la vérification`);
      return { valid: false, error: 'Token ou serveur manquant', errorType: 'MISSING_PARAMS' };
    }

    // Vérifier que l'URL du serveur est valide
    let serverUrl;
    try {
      serverUrl = new URL(server);
    } catch (urlError) {
      const errorMsg = `URL du serveur invalide: ${server}`;
      log.error(errorMsg, urlError);
      return {
        valid: false,
        error: errorMsg,
        errorType: 'INVALID_URL'
      };
    }

    log.info(`Vérification du token Odoo (UID: ${token}) via API standard`);

    // Extraire le protocole et l'hôte de l'URL du serveur
    const protocol = serverUrl.protocol.includes('https') ? 'https' : 'http';
    const host = serverUrl.hostname;
    const port = serverUrl.port || (protocol === 'https' ? 443 : 80);

    // Utiliser l'API XML-RPC pour vérifier si la session est valide
    // Nous allons essayer d'appeler une méthode simple qui nécessite une authentification
    log.info(`Connexion à ${protocol}://${host}:${port}/xmlrpc/2/object pour vérifier le token`);

    // Créer un client XML-RPC pour l'endpoint object
    const clientOptions = {
      host,
      port,
      path: '/xmlrpc/2/object',
      headers: {
        'User-Agent': 'Edara-ERP-Client/1.0',
        'Connection': 'keep-alive'
      }
    };

    const client = protocol === 'https'
      ? xmlrpc.createSecureClient(clientOptions)
      : xmlrpc.createClient(clientOptions);

    // Préparer les paramètres pour la vérification
    // L'API XML-RPC d'Odoo attend: (db, uid, password, model, method, args)
    // Nous utilisons 'execute_kw' pour appeler 'res.users', 'search_count', [[['id', '=', uid]]]
    const params = [
      dbName,             // db
      parseInt(token),    // uid (convertir en nombre)
      'dummy_password',   // password (non utilisé car nous vérifions juste si l'UID est valide)
      'res.users',        // model
      'search_count',     // method
      [[['id', '=', parseInt(token)]]]  // args (rechercher l'utilisateur avec cet UID)
    ];

    // Ajouter un log pour afficher les paramètres de la requête (sans le mot de passe)
    log.info(`Paramètres de la requête XML-RPC pour vérification:`, JSON.stringify([
      params[0], // db
      params[1], // uid
      '********', // password masqué
      params[3], // model
      params[4], // method
      params[5]  // args
    ]));

    // Créer une promesse pour gérer l'appel XML-RPC
    return new Promise((resolve, reject) => {
      // Créer un timer pour le timeout (10 secondes)
      const timeoutId = setTimeout(() => {
        const errorMsg = `Délai d'attente dépassé (10000ms) lors de la vérification du token`;
        log.error(errorMsg);
        resolve({
          valid: false,
          error: errorMsg,
          errorType: 'TIMEOUT'
        });
      }, 10000);

      // Appeler la méthode 'execute_kw' via XML-RPC
      client.methodCall('execute_kw', params, (error, value) => {
        // Annuler le timer de timeout
        clearTimeout(timeoutId);

        // Calculer le temps d'exécution
        const executionTime = Date.now() - startTime;
        log.info(`Temps d'exécution de la vérification: ${executionTime}ms`);

        if (error) {
          // Si nous obtenons une erreur d'authentification, cela signifie que l'UID est invalide
          if (error.message && (error.message.includes('AccessDenied') || error.message.includes('Access denied'))) {
            log.error(`Token invalide: accès refusé pour l'UID ${token}`);
            resolve({
              valid: false,
              error: 'Token invalide ou expiré',
              errorType: 'INVALID_TOKEN'
            });
            return;
          }

          // Pour les autres erreurs, c'est probablement un problème de connexion
          log.error(`Erreur lors de la vérification du token:`, error);
          resolve({
            valid: false,
            error: error.message || 'Erreur de connexion au serveur Odoo',
            errorType: 'CONNECTION_ERROR'
          });
          return;
        }

        log.info(`Réponse de la vérification du token:`, value);

        // Si la valeur retournée est 1, cela signifie que l'utilisateur existe
        if (value === 1) {
          log.info(`Token valide pour l'UID ${token}`);
          resolve({
            valid: true,
            userId: parseInt(token),
            name: null // Nous n'avons pas le nom complet via cette API simple
          });
        } else {
          // Si la valeur est 0, l'utilisateur n'existe pas
          log.error(`Token invalide: aucun utilisateur trouvé avec l'UID ${token}`);
          resolve({
            valid: false,
            error: 'Token invalide: utilisateur introuvable',
            errorType: 'USER_NOT_FOUND'
          });
        }
      });
    });
  } catch (error) {
    // Calculer le temps d'exécution même en cas d'erreur
    const executionTime = Date.now() - startTime;

    log.error(`Erreur lors de la vérification du token Odoo:`, error);
    return {
      valid: false,
      error: error.message || 'Erreur lors de la vérification du token',
      errorType: 'UNKNOWN_ERROR',
      executionTime
    };
  } finally {
    const executionTime = Date.now() - startTime;
    log.info(`Vérification du token terminée en ${executionTime}ms`);
  }
}

/**
 * Déconnecte un utilisateur du serveur Odoo
 * @param {string} token - L'UID de l'utilisateur à déconnecter
 * @param {string} server - L'URL du serveur Odoo
 * @returns {Promise<Object>} - Un objet indiquant si la déconnexion a réussi
 */
async function logoutFromOdoo(token, server) {
  const startTime = Date.now();
  log.info(`Début de la déconnexion pour l'utilisateur avec UID: ${token}`);

  try {
    if (!token || !server) {
      log.error(`Token ou serveur manquant pour la déconnexion`);
      return { success: false, error: 'Token ou serveur manquant', errorType: 'MISSING_PARAMS' };
    }

    log.info(`Déconnexion du serveur Odoo: ${server}`);

    // Pour Odoo, la déconnexion consiste simplement à supprimer le cookie session_id
    // Cette opération sera gérée par le processus principal

    const executionTime = Date.now() - startTime;
    log.info(`Déconnexion réussie en ${executionTime}ms`);

    return {
      success: true,
      message: 'Déconnexion réussie',
      executionTime
    };
  } catch (error) {
    const executionTime = Date.now() - startTime;
    log.error(`Erreur lors de la déconnexion du serveur Odoo:`, error);

    return {
      success: false,
      error: error.message || 'Erreur lors de la déconnexion',
      errorType: 'UNKNOWN_ERROR',
      executionTime
    };
  } finally {
    const executionTime = Date.now() - startTime;
    log.info(`Tentative de déconnexion terminée en ${executionTime}ms`);
  }
}

/**
 * Établit une session web Odoo en utilisant directement l'API de session d'Odoo
 * @param {string} username - L'identifiant (nom d'utilisateur ou email) de l'utilisateur
 * @param {string} password - Le mot de passe de l'utilisateur
 * @param {string} server - L'URL du serveur Odoo
 * @param {string} [dbName='ligne-digitale'] - Le nom de la base de données Odoo
 * @returns {Promise<Object>} - Un objet contenant le résultat de l'authentification
 */
async function establishDirectSession(username, password, server, dbName = 'ligne-digitale') {
  const axios = require('axios');
  const startTime = Date.now();
  log.info(`Début de l'établissement direct de session pour l'utilisateur ${username}`);

  try {
    // Vérifier que les paramètres requis sont fournis
    if (!username || !password || !server) {
      const missingParams = [];
      if (!username) missingParams.push('nom d\'utilisateur');
      if (!password) missingParams.push('mot de passe');
      if (!server) missingParams.push('URL du serveur');

      const errorMsg = `Paramètres manquants: ${missingParams.join(', ')}`;
      log.error(errorMsg);
      return {
        success: false,
        error: errorMsg,
        errorType: 'MISSING_PARAMS'
      };
    }

    // S'assurer que l'URL du serveur est valide
    let serverUrl;
    try {
      serverUrl = new URL(server);
    } catch (urlError) {
      const errorMsg = `URL du serveur invalide: ${server}`;
      log.error(errorMsg, urlError);
      return {
        success: false,
        error: errorMsg,
        errorType: 'INVALID_URL'
      };
    }

    // Construire l'URL de l'API de session Odoo
    const sessionUrl = `${server}/web/session/authenticate`;
    log.info(`Tentative d'authentification directe avec l'API de session Odoo: ${sessionUrl}`);

    // Préparer les données pour la requête
    const data = {
      jsonrpc: '2.0',
      method: 'call',
      params: {
        db: dbName,
        login: username,
        password: password,
        context: {}
      },
      id: Date.now().toString()
    };

    // Effectuer la requête POST
    const response = await axios.post(sessionUrl, data, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      withCredentials: true,
      timeout: 30000
    });

    // Vérifier la réponse
    if (response.status === 200 && response.data && response.data.result) {
      const result = response.data.result;

      // Vérifier si l'authentification a réussi
      if (result.uid) {
        log.info(`Authentification directe réussie pour l'utilisateur ${username}. UID: ${result.uid}`);

        // Extraire le cookie de session
        const cookies = response.headers['set-cookie'];
        let sessionId = null;

        if (cookies && cookies.length > 0) {
          for (const cookie of cookies) {
            if (cookie.includes('session_id=')) {
              sessionId = cookie.split('session_id=')[1].split(';')[0];
              break;
            }
          }
        }

        if (sessionId) {
          log.info(`Cookie de session obtenu: ${sessionId.substring(0, 8)}...`);
        } else {
          log.warn('Aucun cookie de session trouvé dans la réponse');
        }

        // Calculer le temps d'exécution
        const executionTime = Date.now() - startTime;
        log.info(`Temps d'exécution total de l'authentification directe: ${executionTime}ms`);

        return {
          success: true,
          userId: result.uid,
          name: result.name || username,
          sessionId: sessionId,
          server: server,
          dbName: dbName,
          company_id: result.company_id,
          user_context: result.user_context,
          executionTime
        };
      } else {
        log.error(`Échec de l'authentification directe: aucun UID retourné`);
        return {
          success: false,
          error: 'Identifiants incorrects',
          errorType: 'INVALID_CREDENTIALS'
        };
      }
    } else if (response.data && response.data.error) {
      // Erreur retournée par l'API Odoo
      const error = response.data.error;
      log.error(`Erreur retournée par l'API Odoo: ${error.message || JSON.stringify(error)}`);
      return {
        success: false,
        error: error.message || 'Erreur d\'authentification',
        errorType: 'ODOO_API_ERROR',
        details: error
      };
    } else {
      // Réponse inattendue
      log.error(`Réponse inattendue de l'API Odoo: ${JSON.stringify(response.data)}`);
      return {
        success: false,
        error: 'Réponse inattendue du serveur',
        errorType: 'UNEXPECTED_RESPONSE',
        details: response.data
      };
    }
  } catch (error) {
    // Calculer le temps d'exécution même en cas d'erreur
    const executionTime = Date.now() - startTime;

    // Analyser l'erreur pour fournir un message plus précis
    let errorMsg = 'Erreur lors de l\'authentification directe avec Odoo';
    let errorType = 'UNKNOWN_ERROR';

    if (error.response) {
      // La requête a été effectuée et le serveur a répondu avec un code d'état hors de la plage 2xx
      errorMsg = `Erreur HTTP ${error.response.status}: ${error.response.statusText}`;
      errorType = 'HTTP_ERROR';
      log.error(`${errorType}: ${errorMsg}`, error.response.data);
      return {
        success: false,
        error: errorMsg,
        errorType: errorType,
        status: error.response.status,
        details: error.response.data,
        executionTime
      };
    } else if (error.request) {
      // La requête a été effectuée mais aucune réponse n'a été reçue
      errorMsg = 'Aucune réponse reçue du serveur';
      errorType = 'NO_RESPONSE';
      log.error(`${errorType}: ${errorMsg}`, error.request);
      return {
        success: false,
        error: errorMsg,
        errorType: errorType,
        details: error.message,
        executionTime
      };
    } else {
      // Une erreur s'est produite lors de la configuration de la requête
      errorMsg = `Erreur lors de la configuration de la requête: ${error.message}`;
      errorType = 'REQUEST_SETUP_ERROR';
      log.error(`${errorType}: ${errorMsg}`, error);
      return {
        success: false,
        error: errorMsg,
        errorType: errorType,
        details: error.message,
        executionTime
      };
    }
  } finally {
    const executionTime = Date.now() - startTime;
    log.info(`Tentative d'authentification directe terminée en ${executionTime}ms`);
  }
}

/**
 * Authentifie un utilisateur via l'API Odoo /web/session/authenticate et redirige vers /web
 * Cette fonction :
 * 1. Effectue une requête POST vers /web/session/authenticate avec axios
 * 2. Extrait le cookie session_id depuis la réponse
 * 3. Ajoute ce cookie à session.defaultSession.cookies
 * 4. Charge la page principale d'Odoo (/web) dans la BrowserWindow
 * 5. Affiche les erreurs si une étape échoue
 *
 * @param {string} username - Nom d'utilisateur ou email
 * @param {string} password - Mot de passe
 * @param {string} serverUrl - URL du serveur Odoo (ex: http://localhost:8069)
 * @param {string} dbName - Nom de la base de données Odoo (par défaut: 'ligne-digitale')
 * @param {BrowserWindow} window - Fenêtre Electron dans laquelle charger l'interface Odoo
 * @returns {Promise<Object>} - Résultat de l'authentification
 */
async function authenticateAndRedirectToOdoo(username, password, serverUrl, dbName = 'ligne-digitale', window = null) {
  log.info(`Authentification via API /web/session/authenticate pour l'utilisateur ${username} sur ${serverUrl}`);

  try {
    // Vérifier que les paramètres requis sont fournis
    if (!username || !password || !serverUrl) {
      const missingParams = [];
      if (!username) missingParams.push('nom d\'utilisateur');
      if (!password) missingParams.push('mot de passe');
      if (!serverUrl) missingParams.push('URL du serveur');

      const errorMsg = `Paramètres manquants: ${missingParams.join(', ')}`;
      log.error(errorMsg);
      return {
        success: false,
        error: errorMsg,
        errorType: 'MISSING_PARAMS'
      };
    }

    // S'assurer que l'URL du serveur est valide
    let serverUrlObj;
    try {
      serverUrlObj = new URL(serverUrl);
    } catch (urlError) {
      const errorMsg = `URL du serveur invalide: ${serverUrl}`;
      log.error(errorMsg, urlError);
      return {
        success: false,
        error: errorMsg,
        errorType: 'INVALID_URL'
      };
    }

    // Construire l'URL de l'API d'authentification Odoo
    const authUrl = `${serverUrl}/web/session/authenticate`;
    log.info(`URL d'authentification: ${authUrl}`);

    // Préparer les données pour la requête
    const data = {
      jsonrpc: '2.0',
      method: 'call',
      params: {
        db: dbName,
        login: username,
        password: password,
        context: {}
      },
      id: Date.now().toString()
    };

    log.info(`Envoi de la requête d'authentification pour l'utilisateur ${username} sur la base de données ${dbName}`);

    // Effectuer la requête POST
    const response = await axios.post(authUrl, data, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      withCredentials: true,
      timeout: 30000
    });

    // Vérifier la réponse
    if (response.status === 200 && response.data && response.data.result) {
      const result = response.data.result;

      // Vérifier si l'authentification a réussi
      if (result.uid) {
        log.info(`Authentification réussie pour l'utilisateur ${username}. UID: ${result.uid}`);

        // Extraire le cookie de session
        const cookies = response.headers['set-cookie'];
        let sessionId = null;

        if (cookies && cookies.length > 0) {
          for (const cookie of cookies) {
            if (cookie.includes('session_id=')) {
              sessionId = cookie.split('session_id=')[1].split(';')[0];
              break;
            }
          }
        }

        if (sessionId) {
          log.info(`Cookie de session obtenu: ${sessionId.substring(0, 8)}...`);

          // Extraire le domaine de base de l'URL Odoo
          const odooUrl = new URL(serverUrl);
          const odooDomain = `${odooUrl.protocol}//${odooUrl.hostname}${odooUrl.port ? `:${odooUrl.port}` : ''}`;

          // Définir le cookie de session dans Electron
          let cookieSet = false;

          try {
            log.info('Définition du cookie de session...');

            // Supprimer d'abord tous les cookies existants pour éviter les conflits
            await session.defaultSession.clearStorageData({ storages: ['cookies'] });

            // Définir le cookie avec des paramètres minimaux pour éviter les problèmes
            const cookie = {
              url: odooDomain,
              name: 'session_id',
              value: sessionId
            };

            await session.defaultSession.cookies.set(cookie);

            // Vérifier que le cookie a bien été défini
            const cookies = await session.defaultSession.cookies.get({ url: odooDomain });
            const sessionCookie = cookies.find(c => c.name === 'session_id');

            if (sessionCookie) {
              log.info(`Cookie défini avec succès: ${sessionCookie.value.substring(0, 8)}...`);
              cookieSet = true;
            } else {
              log.error('Échec de la définition du cookie');

              // Essayer une autre approche avec plus de paramètres
              log.info('Tentative avec plus de paramètres...');

              const cookieWithParams = {
                url: odooDomain,
                name: 'session_id',
                value: sessionId,
                path: '/',
                httpOnly: false,
                secure: false
              };

              await session.defaultSession.cookies.set(cookieWithParams);

              // Vérifier à nouveau
              const cookiesRetry = await session.defaultSession.cookies.get({ url: odooDomain });
              const sessionCookieRetry = cookiesRetry.find(c => c.name === 'session_id');

              if (sessionCookieRetry) {
                log.info(`Cookie défini avec succès après nouvelle tentative: ${sessionCookieRetry.value.substring(0, 8)}...`);
                cookieSet = true;
              } else {
                log.error('Échec de la définition du cookie après nouvelle tentative');
              }
            }
          } catch (cookieError) {
            log.error(`Erreur lors de la définition du cookie: ${cookieError.message}`);
          }

          if (cookieSet) {
            log.info('Cookie de session défini avec succès dans Electron');

            // Si une fenêtre est fournie, charger l'interface Odoo
            if (window && !window.isDestroyed()) {
              log.info('Chargement de l\'interface Odoo dans la fenêtre fournie');

              // Construire l'URL de l'interface Odoo avec une URL simple
              const timestamp = Date.now(); // Ajouter un timestamp pour éviter le cache
              const odooWebUrl = `${odooDomain}/web?nocache=${timestamp}`;

              // Ajouter un délai avant de charger l'URL pour s'assurer que les cookies sont correctement définis
              log.info('Attente de 500ms avant de charger l\'interface Odoo...');
              await new Promise(resolve => setTimeout(resolve, 500));

              // Charger l'URL dans la fenêtre
              await window.loadURL(odooWebUrl);
              log.info('Interface Odoo chargée avec succès');
            }

            // Retourner les informations d'authentification
            return {
              success: true,
              userId: result.uid,
              name: result.name || username,
              sessionId: sessionId,
              odoo_url: `${odooDomain}/web?nocache=${Date.now()}`,
              dbName: dbName,
              company_id: result.company_id,
              user_context: result.user_context
            };
          } else {
            log.error('Échec de la définition du cookie de session dans Electron');
            return {
              success: false,
              error: 'Échec de la définition du cookie de session',
              errorType: 'COOKIE_SET_FAILED'
            };
          }
        } else {
          log.error('Aucun cookie de session trouvé dans la réponse');
          return {
            success: false,
            error: 'Aucun cookie de session trouvé dans la réponse',
            errorType: 'NO_SESSION_COOKIE'
          };
        }
      } else {
        log.error(`Échec de l'authentification: aucun UID retourné`);
        return {
          success: false,
          error: 'Identifiants incorrects',
          errorType: 'INVALID_CREDENTIALS'
        };
      }
    } else if (response.data && response.data.error) {
      // Erreur retournée par l'API Odoo
      const error = response.data.error;
      log.error(`Erreur retournée par l'API Odoo: ${error.message || JSON.stringify(error)}`);
      return {
        success: false,
        error: error.message || 'Erreur d\'authentification',
        errorType: 'ODOO_API_ERROR',
        details: error
      };
    } else {
      // Réponse inattendue
      log.error(`Réponse inattendue de l'API Odoo: ${JSON.stringify(response.data)}`);
      return {
        success: false,
        error: 'Réponse inattendue du serveur',
        errorType: 'UNEXPECTED_RESPONSE',
        details: response.data
      };
    }
  } catch (error) {
    // Analyser l'erreur pour fournir un message plus précis
    let errorMsg = 'Erreur lors de l\'authentification avec Odoo';
    let errorType = 'UNKNOWN_ERROR';

    if (error.response) {
      // La requête a été effectuée et le serveur a répondu avec un code d'état hors de la plage 2xx
      errorMsg = `Erreur HTTP ${error.response.status}: ${error.response.statusText}`;
      errorType = 'HTTP_ERROR';
      log.error(`${errorType}: ${errorMsg}`, error.response.data);
      return {
        success: false,
        error: errorMsg,
        errorType: errorType,
        status: error.response.status,
        details: error.response.data
      };
    } else if (error.request) {
      // La requête a été effectuée mais aucune réponse n'a été reçue
      errorMsg = 'Aucune réponse reçue du serveur';
      errorType = 'NO_RESPONSE';
      log.error(`${errorType}: ${errorMsg}`, error.request);
      return {
        success: false,
        error: errorMsg,
        errorType: errorType,
        details: error.message
      };
    } else {
      // Une erreur s'est produite lors de la configuration de la requête
      errorMsg = `Erreur lors de la configuration de la requête: ${error.message}`;
      errorType = 'REQUEST_SETUP_ERROR';
      log.error(`${errorType}: ${errorMsg}`, error);
      return {
        success: false,
        error: errorMsg,
        errorType: errorType,
        details: error.message
      };
    }
  }
}

// Exporter les fonctions pour qu'elles soient accessibles depuis d'autres modules
module.exports = {
  authenticateWithOdoo,
  verifyOdooToken,
  logoutFromOdoo,
  establishDirectSession,
  authenticateAndRedirectToOdoo
};
