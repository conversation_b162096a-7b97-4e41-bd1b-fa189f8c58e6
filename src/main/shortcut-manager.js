/**
 * Module de gestion des raccourcis pour Edara ERP
 * Ce module gère la création de raccourcis sur le bureau
 */

const { app } = require('electron');
const path = require('path');
const fs = require('fs');
const log = require('electron-log');

/**
 * Crée un raccourci sur le bureau pour macOS
 * @returns {Promise<boolean>} - true si la création a réussi, false sinon
 */
async function createMacOSDesktopShortcut() {
  try {
    log.info('Création d\'un raccourci sur le bureau pour macOS');
    
    // Chemin de l'application
    const appPath = app.getPath('exe');
    log.info(`Chemin de l'application: ${appPath}`);
    
    // Chemin du bureau
    const desktopPath = app.getPath('desktop');
    log.info(`Chemin du bureau: ${desktopPath}`);
    
    // Nom de l'application
    const appName = app.getName();
    log.info(`Nom de l'application: ${appName}`);
    
    // Chemin du raccourci
    const shortcutPath = path.join(desktopPath, `${appName}.app`);
    log.info(`Chemin du raccourci: ${shortcutPath}`);
    
    // Vérifier si le raccourci existe déjà
    if (fs.existsSync(shortcutPath)) {
      log.info('Le raccourci existe déjà, suppression...');
      fs.rmSync(shortcutPath, { recursive: true, force: true });
    }
    
    // Créer un lien symbolique vers l'application
    // Pour macOS, nous utilisons un lien symbolique car les .app sont des répertoires
    const { exec } = require('child_process');
    
    return new Promise((resolve, reject) => {
      // Extraire le chemin de l'application .app
      // Sur macOS, app.getPath('exe') renvoie le chemin vers l'exécutable à l'intérieur du bundle .app
      // Nous devons remonter au bundle .app lui-même
      const appBundle = appPath.substring(0, appPath.indexOf('.app') + 4);
      log.info(`Bundle de l'application: ${appBundle}`);
      
      // Créer le lien symbolique
      exec(`ln -s "${appBundle}" "${shortcutPath}"`, (error, stdout, stderr) => {
        if (error) {
          log.error(`Erreur lors de la création du raccourci: ${error.message}`);
          log.error(`stderr: ${stderr}`);
          resolve(false);
          return;
        }
        
        log.info(`Raccourci créé avec succès: ${shortcutPath}`);
        resolve(true);
      });
    });
  } catch (error) {
    log.error(`Erreur lors de la création du raccourci: ${error.message}`);
    return false;
  }
}

/**
 * Crée un raccourci sur le bureau pour Windows
 * @returns {Promise<boolean>} - true si la création a réussi, false sinon
 */
async function createWindowsDesktopShortcut() {
  try {
    log.info('Création d\'un raccourci sur le bureau pour Windows');
    
    // Sur Windows, nous utilisons le module windows-shortcuts
    const windowsShortcuts = require('windows-shortcuts');
    
    // Chemin de l'application
    const appPath = app.getPath('exe');
    log.info(`Chemin de l'application: ${appPath}`);
    
    // Chemin du bureau
    const desktopPath = app.getPath('desktop');
    log.info(`Chemin du bureau: ${desktopPath}`);
    
    // Nom de l'application
    const appName = app.getName();
    log.info(`Nom de l'application: ${appName}`);
    
    // Chemin du raccourci
    const shortcutPath = path.join(desktopPath, `${appName}.lnk`);
    log.info(`Chemin du raccourci: ${shortcutPath}`);
    
    return new Promise((resolve, reject) => {
      windowsShortcuts.create(shortcutPath, {
        target: appPath,
        desc: `Raccourci vers ${appName}`,
        icon: appPath,
        windowMode: 'normal'
      }, (error) => {
        if (error) {
          log.error(`Erreur lors de la création du raccourci: ${error.message}`);
          resolve(false);
          return;
        }
        
        log.info(`Raccourci créé avec succès: ${shortcutPath}`);
        resolve(true);
      });
    });
  } catch (error) {
    log.error(`Erreur lors de la création du raccourci: ${error.message}`);
    return false;
  }
}

/**
 * Crée un raccourci sur le bureau pour Linux
 * @returns {Promise<boolean>} - true si la création a réussi, false sinon
 */
async function createLinuxDesktopShortcut() {
  try {
    log.info('Création d\'un raccourci sur le bureau pour Linux');
    
    // Chemin de l'application
    const appPath = app.getPath('exe');
    log.info(`Chemin de l'application: ${appPath}`);
    
    // Chemin du bureau
    const desktopPath = app.getPath('desktop');
    log.info(`Chemin du bureau: ${desktopPath}`);
    
    // Nom de l'application
    const appName = app.getName();
    log.info(`Nom de l'application: ${appName}`);
    
    // Chemin du raccourci
    const shortcutPath = path.join(desktopPath, `${appName}.desktop`);
    log.info(`Chemin du raccourci: ${shortcutPath}`);
    
    // Contenu du fichier .desktop
    const desktopFileContent = `[Desktop Entry]
Type=Application
Name=${appName}
Exec="${appPath}"
Icon=${appPath}
Terminal=false
Categories=Office;
`;
    
    // Écrire le fichier .desktop
    fs.writeFileSync(shortcutPath, desktopFileContent, 'utf8');
    
    // Rendre le fichier exécutable
    fs.chmodSync(shortcutPath, '755');
    
    log.info(`Raccourci créé avec succès: ${shortcutPath}`);
    return true;
  } catch (error) {
    log.error(`Erreur lors de la création du raccourci: ${error.message}`);
    return false;
  }
}

/**
 * Crée un raccourci sur le bureau en fonction du système d'exploitation
 * @returns {Promise<boolean>} - true si la création a réussi, false sinon
 */
async function createDesktopShortcut() {
  const platform = process.platform;
  
  if (platform === 'darwin') {
    return createMacOSDesktopShortcut();
  } else if (platform === 'win32') {
    return createWindowsDesktopShortcut();
  } else if (platform === 'linux') {
    return createLinuxDesktopShortcut();
  } else {
    log.error(`Système d'exploitation non pris en charge: ${platform}`);
    return false;
  }
}

// Exporter les fonctions
module.exports = {
  createDesktopShortcut
};
