/**
 * Gestionnaire de Diagnostic Avancé pour Electron
 * Analyse et résout les problèmes spécifiques à Electron avec Odoo local
 */

const { session, webContents } = require('electron');
const log = require('electron-log');
const { EventEmitter } = require('events');

class ElectronDiagnosticManager extends EventEmitter {
  constructor() {
    super();
    
    this.config = {
      // Diagnostic en temps réel
      enableRealTimeDiagnostic: true,
      enableNetworkAnalysis: true,
      enableSecurityAnalysis: true,
      enablePerformanceAnalysis: true,
      
      // Seuils de détection
      responseTimeThreshold: 5000,    // 5s
      errorRateThreshold: 0.1,        // 10%
      memoryThreshold: 200 * 1024 * 1024, // 200MB
      
      // Intervalles de surveillance
      diagnosticInterval: 30000,      // 30s
      networkSampleInterval: 5000,    // 5s
      
      // Debug
      debug: true
    };
    
    this.diagnosticData = {
      networkRequests: [],
      securityEvents: [],
      performanceMetrics: [],
      errorHistory: [],
      contentLengthIssues: []
    };
    
    this.realTimeMetrics = {
      requestCount: 0,
      errorCount: 0,
      averageResponseTime: 0,
      memoryUsage: 0,
      lastUpdate: Date.now()
    };
    
    this.diagnosticInterval = null;
  }

  /**
   * Démarre le diagnostic en temps réel
   */
  startRealTimeDiagnostic() {
    if (this.diagnosticInterval) {
      return; // Déjà démarré
    }
    
    log.info('🔍 [Diagnostic] Démarrage du diagnostic en temps réel...');
    
    // Installer les intercepteurs de diagnostic
    this.installDiagnosticInterceptors();
    
    // Démarrer la surveillance périodique
    this.diagnosticInterval = setInterval(() => {
      this.performPeriodicDiagnostic();
    }, this.config.diagnosticInterval);
    
    log.info('✅ [Diagnostic] Diagnostic en temps réel démarré');
  }

  /**
   * Installe les intercepteurs pour le diagnostic
   */
  installDiagnosticInterceptors() {
    const defaultSession = session.defaultSession;
    
    // Intercepteur de requêtes pour analyse réseau
    defaultSession.webRequest.onBeforeRequest((details, callback) => {
      if (this.config.enableNetworkAnalysis) {
        this.analyzeRequest(details);
      }
      callback({});
    });
    
    // Intercepteur de réponses pour analyse des erreurs
    defaultSession.webRequest.onHeadersReceived((details, callback) => {
      if (this.config.enableNetworkAnalysis) {
        this.analyzeResponse(details);
      }
      callback({});
    });
    
    // Intercepteur d'erreurs réseau
    defaultSession.webRequest.onErrorOccurred((details) => {
      this.analyzeNetworkError(details);
    });
    
    // Intercepteur de sécurité
    defaultSession.webRequest.onBeforeSendHeaders((details, callback) => {
      if (this.config.enableSecurityAnalysis) {
        this.analyzeSecurityHeaders(details);
      }
      callback({});
    });
  }

  /**
   * Analyse une requête sortante
   */
  analyzeRequest(details) {
    const requestData = {
      id: details.id,
      url: details.url,
      method: details.method,
      timestamp: Date.now(),
      headers: details.requestHeaders,
      isOdooRequest: this.isOdooRequest(details.url),
      isLocalRequest: this.isLocalRequest(details.url)
    };
    
    this.diagnosticData.networkRequests.push(requestData);
    this.realTimeMetrics.requestCount++;
    
    // Garder seulement les 1000 dernières requêtes
    if (this.diagnosticData.networkRequests.length > 1000) {
      this.diagnosticData.networkRequests = this.diagnosticData.networkRequests.slice(-1000);
    }
    
    if (this.config.debug && requestData.isOdooRequest) {
      log.debug(`🔍 [Diagnostic] Requête Odoo: ${details.method} ${details.url}`);
    }
  }

  /**
   * Analyse une réponse entrante
   */
  analyzeResponse(details) {
    const responseData = {
      id: details.id,
      url: details.url,
      statusCode: details.statusCode,
      timestamp: Date.now(),
      headers: details.responseHeaders,
      isOdooRequest: this.isOdooRequest(details.url),
      contentLengthIssue: this.detectContentLengthIssue(details)
    };
    
    // Calculer le temps de réponse
    const request = this.diagnosticData.networkRequests.find(r => r.id === details.id);
    if (request) {
      responseData.responseTime = responseData.timestamp - request.timestamp;
      
      // Mettre à jour les métriques en temps réel
      this.updateResponseTimeMetrics(responseData.responseTime);
    }
    
    // Détecter les problèmes de Content-Length
    if (responseData.contentLengthIssue) {
      this.diagnosticData.contentLengthIssues.push({
        url: details.url,
        issue: responseData.contentLengthIssue,
        timestamp: Date.now(),
        headers: details.responseHeaders
      });
      
      log.warn(`⚠️ [Diagnostic] Problème Content-Length détecté: ${details.url}`);
      this.emit('content-length-issue', responseData);
    }
    
    if (this.config.debug && responseData.isOdooRequest) {
      log.debug(`🔍 [Diagnostic] Réponse Odoo: ${details.statusCode} ${details.url} (${responseData.responseTime}ms)`);
    }
  }

  /**
   * Analyse une erreur réseau
   */
  analyzeNetworkError(details) {
    const errorData = {
      url: details.url,
      error: details.error,
      timestamp: Date.now(),
      isOdooRequest: this.isOdooRequest(details.url),
      isLocalRequest: this.isLocalRequest(details.url),
      isContentLengthError: details.error.includes('CONTENT_LENGTH_MISMATCH')
    };
    
    this.diagnosticData.errorHistory.push(errorData);
    this.realTimeMetrics.errorCount++;
    
    // Garder seulement les 500 dernières erreurs
    if (this.diagnosticData.errorHistory.length > 500) {
      this.diagnosticData.errorHistory = this.diagnosticData.errorHistory.slice(-500);
    }
    
    if (errorData.isContentLengthError) {
      log.error(`❌ [Diagnostic] Erreur Content-Length: ${details.url} - ${details.error}`);
      this.emit('content-length-error', errorData);
    }
    
    if (this.config.debug) {
      log.debug(`🔍 [Diagnostic] Erreur réseau: ${details.error} pour ${details.url}`);
    }
  }

  /**
   * Analyse les headers de sécurité
   */
  analyzeSecurityHeaders(details) {
    const securityIssues = [];
    const headers = details.requestHeaders;
    
    // Vérifier les headers de sécurité problématiques
    if (headers['User-Agent'] && headers['User-Agent'][0].includes('Electron')) {
      securityIssues.push('User-Agent révèle Electron');
    }
    
    if (!headers['Accept-Encoding'] || headers['Accept-Encoding'][0] !== 'identity') {
      securityIssues.push('Accept-Encoding non optimisé pour mode local');
    }
    
    if (securityIssues.length > 0) {
      this.diagnosticData.securityEvents.push({
        url: details.url,
        issues: securityIssues,
        timestamp: Date.now(),
        headers: headers
      });
    }
  }

  /**
   * Détecte les problèmes de Content-Length
   */
  detectContentLengthIssue(details) {
    const headers = details.responseHeaders;
    
    if (!headers) return null;
    
    const contentLength = headers['content-length'] || headers['Content-Length'];
    const contentEncoding = headers['content-encoding'] || headers['Content-Encoding'];
    const transferEncoding = headers['transfer-encoding'] || headers['Transfer-Encoding'];
    
    // Problème 1: Content-Length avec compression
    if (contentLength && contentEncoding && contentEncoding[0] !== 'identity') {
      return {
        type: 'compression-with-content-length',
        description: 'Content-Length présent avec compression',
        contentLength: contentLength[0],
        contentEncoding: contentEncoding[0]
      };
    }
    
    // Problème 2: Content-Length avec Transfer-Encoding chunked
    if (contentLength && transferEncoding && transferEncoding[0] === 'chunked') {
      return {
        type: 'content-length-with-chunked',
        description: 'Content-Length présent avec Transfer-Encoding chunked',
        contentLength: contentLength[0]
      };
    }
    
    // Problème 3: Content-Length invalide
    if (contentLength && (isNaN(parseInt(contentLength[0])) || parseInt(contentLength[0]) < 0)) {
      return {
        type: 'invalid-content-length',
        description: 'Content-Length invalide',
        contentLength: contentLength[0]
      };
    }
    
    return null;
  }

  /**
   * Met à jour les métriques de temps de réponse
   */
  updateResponseTimeMetrics(responseTime) {
    const currentAvg = this.realTimeMetrics.averageResponseTime;
    const count = this.realTimeMetrics.requestCount;
    
    this.realTimeMetrics.averageResponseTime = 
      (currentAvg * (count - 1) + responseTime) / count;
  }

  /**
   * Effectue un diagnostic périodique
   */
  async performPeriodicDiagnostic() {
    try {
      const diagnosticResult = {
        timestamp: Date.now(),
        network: this.analyzeNetworkHealth(),
        security: this.analyzeSecurityHealth(),
        performance: this.analyzePerformanceHealth(),
        contentLength: this.analyzeContentLengthHealth(),
        recommendations: []
      };
      
      // Générer des recommandations
      diagnosticResult.recommendations = this.generateRecommendations(diagnosticResult);
      
      // Mettre à jour les métriques
      this.diagnosticData.performanceMetrics.push(diagnosticResult);
      
      // Garder seulement les 100 derniers diagnostics
      if (this.diagnosticData.performanceMetrics.length > 100) {
        this.diagnosticData.performanceMetrics = this.diagnosticData.performanceMetrics.slice(-100);
      }
      
      // Émettre les alertes si nécessaire
      this.checkForAlerts(diagnosticResult);
      
      if (this.config.debug) {
        log.debug(`🔍 [Diagnostic] Diagnostic périodique terminé`);
      }
      
    } catch (error) {
      log.error(`❌ [Diagnostic] Erreur lors du diagnostic périodique: ${error.message}`);
    }
  }

  /**
   * Analyse la santé du réseau
   */
  analyzeNetworkHealth() {
    const recentRequests = this.getRecentRequests(60000); // Dernière minute
    const recentErrors = this.getRecentErrors(60000);
    
    const totalRequests = recentRequests.length;
    const totalErrors = recentErrors.length;
    const errorRate = totalRequests > 0 ? totalErrors / totalRequests : 0;
    
    const odooRequests = recentRequests.filter(r => r.isOdooRequest);
    const localRequests = recentRequests.filter(r => r.isLocalRequest);
    
    return {
      totalRequests,
      totalErrors,
      errorRate,
      odooRequests: odooRequests.length,
      localRequests: localRequests.length,
      averageResponseTime: this.realTimeMetrics.averageResponseTime,
      health: errorRate < this.config.errorRateThreshold ? 'good' : 'poor'
    };
  }

  /**
   * Analyse la santé de la sécurité
   */
  analyzeSecurityHealth() {
    const recentSecurityEvents = this.diagnosticData.securityEvents.filter(
      event => Date.now() - event.timestamp < 300000 // 5 minutes
    );
    
    return {
      recentEvents: recentSecurityEvents.length,
      issues: recentSecurityEvents.map(event => event.issues).flat(),
      health: recentSecurityEvents.length === 0 ? 'good' : 'warning'
    };
  }

  /**
   * Analyse la santé des performances
   */
  analyzePerformanceHealth() {
    const memoryUsage = process.memoryUsage();
    this.realTimeMetrics.memoryUsage = memoryUsage.heapUsed;
    
    const slowRequests = this.getRecentRequests(300000).filter(
      r => r.responseTime && r.responseTime > this.config.responseTimeThreshold
    );
    
    return {
      memoryUsage: memoryUsage.heapUsed,
      memoryHealth: memoryUsage.heapUsed < this.config.memoryThreshold ? 'good' : 'warning',
      slowRequests: slowRequests.length,
      performanceHealth: slowRequests.length === 0 ? 'good' : 'warning'
    };
  }

  /**
   * Analyse la santé des Content-Length
   */
  analyzeContentLengthHealth() {
    const recentIssues = this.diagnosticData.contentLengthIssues.filter(
      issue => Date.now() - issue.timestamp < 300000 // 5 minutes
    );
    
    const issueTypes = recentIssues.reduce((acc, issue) => {
      const type = issue.issue.type;
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});
    
    return {
      recentIssues: recentIssues.length,
      issueTypes,
      health: recentIssues.length === 0 ? 'good' : 'critical'
    };
  }

  /**
   * Génère des recommandations basées sur le diagnostic
   */
  generateRecommendations(diagnosticResult) {
    const recommendations = [];
    
    // Recommandations réseau
    if (diagnosticResult.network.errorRate > this.config.errorRateThreshold) {
      recommendations.push({
        type: 'network',
        priority: 'high',
        message: 'Taux d\'erreur réseau élevé détecté',
        action: 'Vérifier la connectivité et les timeouts'
      });
    }
    
    // Recommandations Content-Length
    if (diagnosticResult.contentLength.recentIssues > 0) {
      recommendations.push({
        type: 'content-length',
        priority: 'critical',
        message: 'Problèmes Content-Length détectés',
        action: 'Activer le gestionnaire de correction Content-Length'
      });
    }
    
    // Recommandations performance
    if (diagnosticResult.performance.memoryHealth === 'warning') {
      recommendations.push({
        type: 'performance',
        priority: 'medium',
        message: 'Utilisation mémoire élevée',
        action: 'Effectuer un nettoyage du cache'
      });
    }
    
    // Recommandations sécurité
    if (diagnosticResult.security.recentEvents > 0) {
      recommendations.push({
        type: 'security',
        priority: 'medium',
        message: 'Problèmes de sécurité détectés',
        action: 'Réviser la configuration des headers'
      });
    }
    
    return recommendations;
  }

  /**
   * Vérifie et émet les alertes nécessaires
   */
  checkForAlerts(diagnosticResult) {
    // Alerte critique pour Content-Length
    if (diagnosticResult.contentLength.health === 'critical') {
      this.emit('critical-alert', {
        type: 'content-length',
        message: 'Problèmes critiques de Content-Length détectés',
        data: diagnosticResult.contentLength
      });
    }
    
    // Alerte réseau
    if (diagnosticResult.network.health === 'poor') {
      this.emit('network-alert', {
        type: 'network',
        message: 'Santé réseau dégradée',
        data: diagnosticResult.network
      });
    }
  }

  /**
   * Obtient les requêtes récentes
   */
  getRecentRequests(timeWindow) {
    const cutoff = Date.now() - timeWindow;
    return this.diagnosticData.networkRequests.filter(r => r.timestamp > cutoff);
  }

  /**
   * Obtient les erreurs récentes
   */
  getRecentErrors(timeWindow) {
    const cutoff = Date.now() - timeWindow;
    return this.diagnosticData.errorHistory.filter(e => e.timestamp > cutoff);
  }

  /**
   * Vérifie si une URL est une requête Odoo
   */
  isOdooRequest(url) {
    return url.includes('/web/') || 
           url.includes('/odoo/') ||
           url.includes(':8069') ||
           url.includes('load_menus') ||
           url.includes('assets');
  }

  /**
   * Vérifie si une URL est une requête locale
   */
  isLocalRequest(url) {
    return url.includes('localhost') ||
           url.includes('127.0.0.1') ||
           url.includes('192.168.') ||
           url.includes('10.') ||
           url.includes('172.');
  }

  /**
   * Génère un rapport de diagnostic complet
   */
  generateDiagnosticReport() {
    const now = Date.now();
    
    return {
      timestamp: now,
      summary: {
        totalRequests: this.realTimeMetrics.requestCount,
        totalErrors: this.realTimeMetrics.errorCount,
        averageResponseTime: this.realTimeMetrics.averageResponseTime,
        memoryUsage: this.realTimeMetrics.memoryUsage
      },
      network: this.analyzeNetworkHealth(),
      security: this.analyzeSecurityHealth(),
      performance: this.analyzePerformanceHealth(),
      contentLength: this.analyzeContentLengthHealth(),
      recentIssues: {
        contentLengthIssues: this.diagnosticData.contentLengthIssues.slice(-10),
        networkErrors: this.diagnosticData.errorHistory.slice(-10),
        securityEvents: this.diagnosticData.securityEvents.slice(-10)
      },
      recommendations: this.generateRecommendations({
        network: this.analyzeNetworkHealth(),
        security: this.analyzeSecurityHealth(),
        performance: this.analyzePerformanceHealth(),
        contentLength: this.analyzeContentLengthHealth()
      })
    };
  }

  /**
   * Arrête le diagnostic
   */
  stopDiagnostic() {
    if (this.diagnosticInterval) {
      clearInterval(this.diagnosticInterval);
      this.diagnosticInterval = null;
    }
    
    log.info('🔍 [Diagnostic] Diagnostic arrêté');
  }

  /**
   * Nettoie les ressources
   */
  cleanup() {
    this.stopDiagnostic();
    this.removeAllListeners();
    
    // Nettoyer les données
    this.diagnosticData = {
      networkRequests: [],
      securityEvents: [],
      performanceMetrics: [],
      errorHistory: [],
      contentLengthIssues: []
    };
    
    log.info('🧹 [Diagnostic] Gestionnaire nettoyé');
  }
}

module.exports = ElectronDiagnosticManager;
