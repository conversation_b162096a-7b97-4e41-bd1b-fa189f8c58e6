/**
 * Module de gestion des préférences utilisateur pour Edara ERP
 * Ce module gère le stockage et la récupération des préférences utilisateur
 */

const { app } = require('electron');
const path = require('path');
const fs = require('fs');
const log = require('electron-log');

// Chemin vers le fichier de préférences
const userDataPath = app.getPath('userData');
const preferencesPath = path.join(userDataPath, 'preferences.json');

// Préférences par défaut
const defaultPreferences = {
  firstLaunch: true,
  backupPath: path.join(app.getPath('documents'), 'Edara Workspace', 'Backups'),
  serverUrl: 'https://edara.ligne-digitale.com', // Utiliser HTTPS par défaut
  theme: 'dark',
  language: 'fr',
  workspaceName: 'edara' // Nom par défaut de l'espace de travail
};

/**
 * Charge les préférences utilisateur depuis le fichier
 * @returns {Object} Les préférences utilisateur
 */
function loadPreferences() {
  try {
    // Vérifier si le fichier existe
    if (fs.existsSync(preferencesPath)) {
      const data = fs.readFileSync(preferencesPath, 'utf8');
      log.info(`Contenu brut du fichier de préférences: ${data}`);

      // Vérifier si le fichier est vide
      if (!data || data.trim() === '') {
        log.warn('Fichier de préférences vide, utilisation des valeurs par défaut');
        return { ...defaultPreferences };
      }

      try {
        const preferences = JSON.parse(data);
        log.info('Préférences utilisateur chargées avec succès');
        log.info(`Préférences chargées: firstLaunch = ${preferences.firstLaunch}`);
        return { ...defaultPreferences, ...preferences };
      } catch (parseError) {
        log.error(`Erreur lors du parsing JSON des préférences: ${parseError.message}`);
        log.info('Suppression du fichier de préférences corrompu et utilisation des valeurs par défaut');

        // Renommer le fichier corrompu pour le conserver
        try {
          const backupPath = `${preferencesPath}.corrupted`;
          fs.renameSync(preferencesPath, backupPath);
          log.info(`Fichier corrompu renommé en ${backupPath}`);
        } catch (renameError) {
          log.error(`Erreur lors du renommage du fichier corrompu: ${renameError.message}`);
        }

        return { ...defaultPreferences };
      }
    } else {
      log.info('Fichier de préférences non trouvé, utilisation des valeurs par défaut');
      return { ...defaultPreferences };
    }
  } catch (error) {
    log.error(`Erreur lors du chargement des préférences: ${error.message}`);
    return { ...defaultPreferences };
  }
}

/**
 * Sauvegarde les préférences utilisateur dans le fichier
 * @param {Object} preferences - Les préférences à sauvegarder
 * @returns {boolean} - true si la sauvegarde a réussi, false sinon
 */
function savePreferences(preferences) {
  try {
    // Créer le répertoire s'il n'existe pas
    const directory = path.dirname(preferencesPath);
    if (!fs.existsSync(directory)) {
      fs.mkdirSync(directory, { recursive: true });
    }

    // Fusionner avec les préférences par défaut pour s'assurer que toutes les propriétés sont présentes
    const mergedPreferences = { ...defaultPreferences, ...preferences };

    // Sauvegarder les préférences
    fs.writeFileSync(preferencesPath, JSON.stringify(mergedPreferences, null, 2), 'utf8');
    log.info('Préférences utilisateur sauvegardées avec succès');
    return true;
  } catch (error) {
    log.error(`Erreur lors de la sauvegarde des préférences: ${error.message}`);
    return false;
  }
}

/**
 * Vérifie si c'est le premier lancement de l'application
 * @returns {boolean} - true si c'est le premier lancement, false sinon
 */
function isFirstLaunch() {
  const preferences = loadPreferences();
  log.info(`Vérification du premier lancement - Chemin des préférences: ${preferencesPath}`);
  log.info(`Vérification du premier lancement - Valeur actuelle: ${preferences.firstLaunch}`);

  // Vérifier si le fichier de préférences existe
  const preferencesFileExists = fs.existsSync(preferencesPath);
  log.info(`Fichier de préférences existe: ${preferencesFileExists}`);

  // Si le fichier n'existe pas et que l'application est packagée, c'est le premier lancement
  if (!preferencesFileExists && app.isPackaged) {
    log.info(`Premier lancement détecté: fichier de préférences inexistant et application packagée`);
    return true;
  }

  // Sinon, utiliser la valeur stockée dans les préférences
  return preferences.firstLaunch === true;
}

/**
 * Marque l'application comme ayant déjà été lancée
 * @returns {boolean} - true si la mise à jour a réussi, false sinon
 */
function setNotFirstLaunch() {
  const preferences = loadPreferences();
  preferences.firstLaunch = false;
  log.info('Marquage de l\'application comme ayant déjà été lancée');

  // Assurons-nous que le fichier de préférences est bien créé et sauvegardé
  const result = savePreferences(preferences);

  // Vérifier que le fichier a bien été créé
  const fileExists = fs.existsSync(preferencesPath);
  log.info(`Fichier de préférences après sauvegarde: ${fileExists ? 'Existe' : 'N\'existe pas'}`);

  if (fileExists) {
    try {
      // Vérifier le contenu du fichier
      const savedData = JSON.parse(fs.readFileSync(preferencesPath, 'utf8'));
      log.info(`Contenu du fichier de préférences: firstLaunch = ${savedData.firstLaunch}`);
    } catch (error) {
      log.error(`Erreur lors de la lecture du fichier de préférences: ${error.message}`);
    }
  }

  return result;
}

/**
 * Définit le chemin de sauvegarde
 * @param {string} path - Le chemin de sauvegarde
 * @returns {boolean} - true si la mise à jour a réussi, false sinon
 */
function setBackupPath(path) {
  const preferences = loadPreferences();
  preferences.backupPath = path;
  return savePreferences(preferences);
}

/**
 * Récupère le chemin de sauvegarde
 * @returns {string} - Le chemin de sauvegarde
 */
function getBackupPath() {
  const preferences = loadPreferences();
  return preferences.backupPath;
}

/**
 * Définit l'URL du serveur
 * @param {string} url - L'URL du serveur
 * @returns {boolean} - true si la mise à jour a réussi, false sinon
 */
function setServerUrl(url) {
  const preferences = loadPreferences();
  preferences.serverUrl = url;
  return savePreferences(preferences);
}

/**
 * Récupère l'URL du serveur
 * @returns {string} - L'URL du serveur
 */
function getServerUrl() {
  const preferences = loadPreferences();
  return preferences.serverUrl;
}

/**
 * Définit le nom de l'espace de travail
 * @param {string} name - Le nom de l'espace de travail
 * @returns {boolean} - true si la mise à jour a réussi, false sinon
 */
function setWorkspaceName(name) {
  const preferences = loadPreferences();
  preferences.workspaceName = name;
  return savePreferences(preferences);
}

/**
 * Récupère le nom de l'espace de travail
 * @returns {string} - Le nom de l'espace de travail
 */
function getWorkspaceName() {
  const preferences = loadPreferences();
  return preferences.workspaceName;
}

// Exporter les fonctions
module.exports = {
  loadPreferences,
  savePreferences,
  isFirstLaunch,
  setNotFirstLaunch,
  setBackupPath,
  getBackupPath,
  setServerUrl,
  getServerUrl,
  setWorkspaceName,
  getWorkspaceName
};
