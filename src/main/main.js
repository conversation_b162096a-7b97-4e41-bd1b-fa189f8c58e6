/**
 * Processus principal de l'application Edara ERP
 * Ce fichier est le point d'entrée de l'application Electron
 */

// Désactiver les avertissements de sécurité en mode développement
// Ces avertissements ne s'affichent pas dans l'application packagée
process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = 'true';

const { app, BrowserWindow, ipcMain, session, dialog } = require('electron');
const path = require('path');
const log = require('electron-log');
const axios = require('axios');
const { URL } = require('url');

// Configuration de electron-log
log.transports.file.level = 'info';
log.transports.console.level = 'debug';
log.transports.console.format = '[{level}] {text}';

// Configurer un fichier de log spécifique pour les erreurs
log.transports.file.fileName = 'edara-error-logs.txt';
log.transports.file.maxSize = 10 * 1024 * 1024; // 10 MB

// Gestionnaire d'erreurs non capturées dans le processus principal
process.on('uncaughtException', (error) => {
  log.error('ERREUR NON CAPTURÉE DANS LE PROCESSUS PRINCIPAL:', error);
  log.error('Stack trace:', error.stack);
});

// Gestionnaire de rejets de promesses non gérés
process.on('unhandledRejection', (reason, promise) => {
  log.error('PROMESSE REJETÉE NON GÉRÉE DANS LE PROCESSUS PRINCIPAL:', reason);
  if (reason && reason.stack) {
    log.error('Stack trace:', reason.stack);
  }
});

// Gestionnaires pour les erreurs provenant du processus de rendu
ipcMain.on('renderer-error', (event, errorInfo) => {
  log.error('=== ERREUR CAPTURÉE DANS LE PROCESSUS DE RENDU ===');
  log.error('Message:', errorInfo.message);
  log.error('Source:', errorInfo.source);
  log.error('Ligne:', errorInfo.lineno, 'Colonne:', errorInfo.colno);
  log.error('Stack trace:', errorInfo.error);
});

ipcMain.on('renderer-promise-rejection', (event, errorInfo) => {
  log.error('=== PROMESSE REJETÉE NON GÉRÉE DANS LE PROCESSUS DE RENDU ===');
  log.error('Message:', errorInfo.message);
  log.error('Stack trace:', errorInfo.stack);
});

// Gestionnaires pour les erreurs provenant de la page de connexion
ipcMain.on('login-page-error', (event, errorInfo) => {
  log.error('=== ERREUR CAPTURÉE DANS LA PAGE DE CONNEXION ===');
  log.error('Message:', errorInfo.message);
  log.error('Source:', errorInfo.source);
  log.error('Ligne:', errorInfo.lineno, 'Colonne:', errorInfo.colno);
  log.error('Stack trace:', errorInfo.stack);
});

ipcMain.on('login-page-promise-rejection', (event, errorInfo) => {
  log.error('=== PROMESSE REJETÉE NON GÉRÉE DANS LA PAGE DE CONNEXION ===');
  log.error('Raison:', errorInfo.reason);
  log.error('Stack trace:', errorInfo.stack);
});

// Importer le module d'authentification Odoo
const { authenticateWithOdoo, verifyOdooToken, logoutFromOdoo, establishDirectSession, authenticateAndRedirectToOdoo } = require('./odoo-auth');

// Importer le module odoo-window pour la fonction setOdooSessionCookie
const { setOdooSessionCookie } = require('./odoo-window');

// Importer le nouveau module de gestion de session Odoo
const odooSessionManager = require('./odoo-session-manager');

// Importer le module de gestion des préférences utilisateur
const userPreferences = require('./user-preferences');

// Importer le module de gestion des raccourcis
const shortcutManager = require('./shortcut-manager');

// Importer le module de gestion des sauvegardes
const backupManager = require('./backup-manager');

// Log pour confirmer que les modules ont été importés
log.info('Modules importés dans le main process');

// Désactiver l'accélération matérielle pour éviter les erreurs graphiques
app.disableHardwareAcceleration();

// Garde une référence globale des objets window pour éviter qu'ils ne soient fermés par le garbage collector
let singleWindow = null; // Fenêtre unique pour toute l'application
let mainWindow = null; // Fenêtre principale (pour compatibilité avec le code existant)
let loginWindow = null; // Fenêtre de connexion (pour compatibilité avec le code existant)
let splashWindow = null; // Fenêtre de démarrage (pour compatibilité avec le code existant)
let installerWindow = null;

// Variables pour suivre l'état actuel de l'application
let currentAppState = 'login'; // États possibles: 'login', 'splash', 'main'

// Variable globale pour stocker l'URL du serveur Odoo
let lastServerUrl = null;

// Variable globale pour stocker le cookie session_id
let storedSessionId = null;

// Variable globale pour stocker le timestamp de la dernière navigation vers /web
let lastNavigationToWeb = 0;

// Variables pour l'assistant d'installation
let createShortcutOnFinish = true;
let currentInstallerStep = 0;
const installerSteps = [
  'pages/welcome.html',
  'pages/terms.html',
  'pages/product-key.html',
  'pages/backup.html',
  'pages/complete.html'
];

/**
 * Crée la fenêtre unique de l'application qui sera utilisée pour toutes les étapes
 * @returns {BrowserWindow} - La fenêtre créée
 */
function createSingleWindow() {
  log.info('Création de la fenêtre unique de l\'application');

  // Si la fenêtre existe déjà, la retourner
  if (singleWindow && !singleWindow.isDestroyed()) {
    log.info('La fenêtre unique existe déjà, retour de la fenêtre existante');
    return singleWindow;
  }

  // Créer la fenêtre avec les dimensions de l'interface principale
  singleWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    show: false,
    backgroundColor: '#181818', // Couleur de fond sombre
    frame: true, // Utiliser la barre native de macOS
    titleBarStyle: 'default', // Utiliser la barre de titre standard de macOS
    webPreferences: {
      preload: path.join(__dirname, '../renderer/preload.js'),
      // Configuration sécurisée avec contextIsolation activée
      contextIsolation: true, // Activer l'isolation du contexte pour améliorer la sécurité
      nodeIntegration: false, // Désactiver l'intégration de Node.js dans le processus de rendu
      offscreen: false,
      session: session.defaultSession,
      webSecurity: false, // ⚠️ Désactive CSP et autres protections : à revoir pour la prod
      // Désactiver les avertissements de source map dans les DevTools
      devTools: {
        preferences: {
          'hideNetworkMessages': true,
          'hideSourceMapFailures': true
        }
      }
    }
  });

  // Activer les DevTools si la fonction est disponible
  if (typeof enableDevTools === 'function') {
    enableDevTools(singleWindow);
  }

  // Intercepte l'événement de fermeture pour gérer le comportement sur macOS
  singleWindow.on('close', (event) => {
    if (process.platform === 'darwin') {
      // Sur macOS, quand l'utilisateur clique sur le bouton rouge de fermeture
      log.info('Bouton rouge de fermeture cliqué, fermeture complète de l\'application');
      process.exit(0); // Force la fermeture complète de tous les processus Node.js
      return;
    }
  });

  // Nettoie la référence à la fenêtre quand elle est fermée
  singleWindow.on('closed', () => {
    singleWindow = null;
  });

  // Intercepter les navigations pour gérer la déconnexion
  singleWindow.webContents.on('will-navigate', (event, url) => {
    // Vérifier si la fenêtre existe toujours
    if (!singleWindow || singleWindow.isDestroyed()) {
      log.warn('NAVIGATION: Fenêtre non disponible lors de la tentative de navigation');
      return;
    }

    log.info(`NAVIGATION: Tentative de navigation détectée vers: ${url}`);
    log.info(`NAVIGATION: URL actuelle: ${singleWindow.webContents.getURL()}`);

    // Vérifier si l'URL correspond à une page de connexion ou de déconnexion Odoo
    try {
      const urlObj = new URL(url);

      // Détection précise des pages de connexion et de déconnexion
      const isLoginPage = urlObj.pathname === '/web/login' || urlObj.pathname.endsWith('/web/login');
      const isLogoutPage = urlObj.pathname === '/web/session/logout' || urlObj.pathname.endsWith('/web/session/logout');
      const hasLogoutParam = urlObj.search.includes('logout=1') || urlObj.search.includes('action=logout');

      // Log détaillé pour le débogage
      log.info(`NAVIGATION: Analyse de l'URL - pathname: ${urlObj.pathname}, search: ${urlObj.search}`);
      log.info(`NAVIGATION: Détection - isLoginPage: ${isLoginPage}, isLogoutPage: ${isLogoutPage}, hasLogoutParam: ${hasLogoutParam}`);

      if (isLogoutPage || hasLogoutParam) {
        log.info(`NAVIGATION: Déconnexion détectée (will-navigate): ${url}`);

        // IMPORTANT: Empêcher la navigation vers la page de déconnexion Odoo
        event.preventDefault();
        log.info('NAVIGATION: Navigation empêchée avec event.preventDefault()');

        // Appeler handleOdooLogout avec l'URL complète
        log.info(`NAVIGATION: Appel de handleOdooLogout avec l'URL: ${url}`);
        handleOdooLogout(url);

        return false; // Assurer que la navigation est bien arrêtée
      } else {
        log.info(`NAVIGATION: Navigation autorisée vers: ${url}`);
      }
    } catch (error) {
      log.error(`NAVIGATION: Erreur lors de l'analyse de l'URL dans will-navigate: ${error.message}`);

      // Même en cas d'erreur d'analyse, si l'URL contient des mots-clés de déconnexion, intercepter
      if (url.includes('/web/session/logout') || url.includes('logout=')) {
        log.info(`NAVIGATION: Déconnexion détectée par mots-clés après erreur d'analyse: ${url}`);

        // IMPORTANT: Empêcher la navigation
        event.preventDefault();
        log.info('NAVIGATION: Navigation empêchée avec event.preventDefault() (après erreur)');

        // Appeler handleOdooLogout avec l'URL brute
        log.info(`NAVIGATION: Appel de handleOdooLogout avec l'URL brute: ${url}`);
        handleOdooLogout(url);

        return false; // Assurer que la navigation est bien arrêtée
      } else {
        log.info(`NAVIGATION: Navigation autorisée malgré l'erreur d'analyse: ${url}`);
      }
    }
  });

  return singleWindow;
}

/**
 * Charge l'écran de démarrage (splash screen) dans une fenêtre dédiée
 */
function loadSplashScreen() {
  log.info('Chargement de l\'écran de démarrage dans une fenêtre dédiée');

  // Créer une fenêtre de démarrage dédiée avec les dimensions spécifiées
  createSplashWindow();

  // Mettre à jour l'état de l'application
  currentAppState = 'splash';
}

/**
 * Crée ou réutilise une fenêtre pour l'écran de connexion
 * @param {string} serverUrl - L'URL du serveur Odoo
 * @returns {Promise<BrowserWindow>} - La fenêtre de connexion
 */
async function createLoginWindow(serverUrl) {
  log.info('Création ou réutilisation de la fenêtre pour l\'écran de connexion');

  // Si la fenêtre principale existe déjà, l'utiliser au lieu d'en créer une nouvelle
  if (mainWindow && !mainWindow.isDestroyed()) {
    log.info('Réutilisation de la fenêtre principale existante pour l\'écran de connexion');

    // Fermer la fenêtre de chargement si elle existe
    if (splashWindow && !splashWindow.isDestroyed()) {
      log.info('Fermeture de la fenêtre de chargement');
      splashWindow.close();
      splashWindow = null;
    }

    // Charger l'interface de connexion dans la fenêtre principale
    log.info('Chargement de l\'interface de connexion dans la fenêtre principale');
    mainWindow.loadFile(path.join(__dirname, '../renderer/login.html'));

    // Afficher la fenêtre si elle n'est pas déjà visible
    if (!mainWindow.isVisible()) {
      mainWindow.show();
    }

    return mainWindow;
  } else {
    // Créer une nouvelle fenêtre de connexion
    log.info('Création d\'une nouvelle fenêtre de connexion');

    // Fermer la fenêtre de chargement si elle existe
    if (splashWindow && !splashWindow.isDestroyed()) {
      log.info('Fermeture de la fenêtre de chargement');
      splashWindow.close();
      splashWindow = null;
    }

    // Créer la fenêtre de connexion
    loginWindow = new BrowserWindow({
      width: 1200, // Utiliser les mêmes dimensions que la fenêtre principale
      height: 800,
      show: false,
      backgroundColor: '#181818', // Couleur de fond sombre
      frame: true, // Utiliser la barre native de macOS
      titleBarStyle: 'default', // Utiliser la barre de titre standard de macOS
      webPreferences: {
        preload: path.join(__dirname, 'preload.js'),
        // Utiliser les mêmes paramètres que la fenêtre principale pour la cohérence
        contextIsolation: false,
        nodeIntegration: false,
        offscreen: false,
        session: session.defaultSession,
        webSecurity: false
      }
    });

    // Activer les DevTools si la fonction est disponible
    if (typeof enableDevTools === 'function') {
      enableDevTools(loginWindow);
    }

    // Charger le fichier HTML de l'interface de connexion personnalisée
    log.info('Chargement de l\'interface de connexion personnalisée');
    loginWindow.loadFile(path.join(__dirname, '../renderer/login.html'));

    // Afficher la fenêtre avec une animation de fondu
    loginWindow.once('ready-to-show', () => {
      log.info('Fenêtre de connexion prête à être affichée');
      if (loginWindow && !loginWindow.isDestroyed()) {
        loginWindow.show();
      } else {
        log.warn('[createOrReuseWindowForLogin] Tentative d\'affichage d\'une fenêtre de connexion (loginWindow) qui est null ou détruite.');
      }
    });

    // Ferme la fenêtre de connexion quand elle est fermée
    loginWindow.on('closed', () => {
      loginWindow = null;
    });

    return loginWindow;
  }
}

/**
 * Charge l'écran de connexion dans la fenêtre unique
 * @param {string} serverUrl - L'URL du serveur Odoo (par défaut: null)
 * @returns {BrowserWindow} - La fenêtre unique
 */
async function loadLoginScreen(serverUrl = null) {
  log.info(`Chargement de l'écran de connexion dans la fenêtre unique`);

  // Si aucune URL n'est fournie, détecter automatiquement le meilleur serveur
  if (!serverUrl) {
    try {
      log.info('Aucune URL de serveur fournie, détection automatique du meilleur serveur...');
      serverUrl = await serverDetector.detectBestServer();
      log.info(`Meilleur serveur détecté: ${serverUrl}`);

      // Sauvegarder l'URL du serveur détecté dans les préférences
      userPreferences.setServerUrl(serverUrl);
      log.info(`URL du serveur sauvegardée dans les préférences: ${serverUrl}`);
    } catch (error) {
      log.error(`Erreur lors de la détection automatique du serveur: ${error.message}`);
      // En cas d'erreur, utiliser le serveur distant par défaut
      serverUrl = serverDetector.SERVER_URLS.REMOTE;
      log.info(`Utilisation du serveur distant par défaut: ${serverUrl}`);
    }
  }

  // S'assurer que serverUrl ne contient pas déjà un chemin
  let baseUrl = serverUrl;
  try {
    const urlObj = new URL(serverUrl);
    // Extraire uniquement le protocole, l'hôte et le port
    baseUrl = `${urlObj.protocol}//${urlObj.hostname}${urlObj.port ? `:${urlObj.port}` : ''}`;
  } catch (error) {
    log.error(`Erreur lors de l'analyse de l'URL du serveur: ${error.message}`);
    // Utiliser l'URL telle quelle si elle ne peut pas être analysée
  }

  // Stocker l'URL de base du serveur pour une utilisation ultérieure
  lastServerUrl = baseUrl;

  // Afficher un message indiquant le serveur utilisé
  const isLocalServer = baseUrl === serverDetector.SERVER_URLS.LOCAL;
  log.info(`Utilisation du serveur ${isLocalServer ? 'local' : 'distant'}: ${baseUrl}`);

  // S'assurer que la fenêtre unique existe
  if (!singleWindow || singleWindow.isDestroyed()) {
    log.info('La fenêtre unique n\'existe pas, création d\'une nouvelle fenêtre');
    createSingleWindow();
  }

  // Mettre à jour l'état de l'application
  currentAppState = 'login';

  // Charger le fichier HTML de l'interface de connexion personnalisée
  log.info('Chargement de l\'interface de connexion personnalisée dans la fenêtre unique');
  singleWindow.loadFile(path.join(__dirname, '../renderer/login.html'))
    .catch(error => {
      log.error(`Erreur lors du chargement de l'interface de connexion: ${error.message}`);
      // Réessayer avec une nouvelle fenêtre en cas d'échec
      if (!singleWindow || singleWindow.isDestroyed()) {
        createSingleWindow();
        singleWindow.loadFile(path.join(__dirname, '../renderer/login.html'));
      }
    });

  // Afficher la fenêtre si elle n'est pas déjà visible
  if (!singleWindow.isVisible()) {
    singleWindow.show();
  }

  // Retourner la fenêtre unique
  return singleWindow;
}

/**
 * Charge l'écran de connexion Odoo dans la fenêtre unique après déconnexion
 * @param {string} serverUrl - L'URL du serveur Odoo
 */
function loadLoginScreenAfterLogout(serverUrl) {
  log.info(`Chargement de l'écran de connexion dans la fenêtre unique après déconnexion pour ${serverUrl}`);

  // S'assurer que la fenêtre unique existe
  if (!singleWindow || singleWindow.isDestroyed()) {
    log.info('La fenêtre unique n\'existe pas, création d\'une nouvelle fenêtre');
    createSingleWindow();
  }

  // Mettre à jour l'état de l'application
  currentAppState = 'login';

  // Charger le fichier HTML de l'interface de connexion personnalisée
  log.info('Chargement de l\'interface de connexion personnalisée dans la fenêtre unique');
  singleWindow.loadFile(path.join(__dirname, '../renderer/login.html'));
}

/**
 * Charge l'interface principale Odoo dans la fenêtre unique
 * @param {Object} authData - Les données d'authentification
 */
function loadMainInterface(authData) {
  log.info('Chargement de l\'interface principale Odoo dans la fenêtre unique');

  // Extraire le domaine de base de l'URL Odoo
  const odooUrl = new URL(authData.odoo_url);
  const odooDomain = `${odooUrl.protocol}//${odooUrl.hostname}${odooUrl.port ? `:${odooUrl.port}` : ''}`;

  // Stocker l'URL du serveur pour une utilisation ultérieure
  lastServerUrl = odooDomain;
  log.info(`URL du serveur stockée: ${lastServerUrl}`);

  // Configurer l'interception des requêtes pour corriger les problèmes Content-Length
  log.info('Configuration de l\'interception des requêtes pour corriger Content-Length Mismatch');

  // Intercepter les requêtes AVANT qu'elles ne soient envoyées
  session.defaultSession.webRequest.onBeforeRequest((details, callback) => {
    // Vérifier si c'est un asset problématique
    if (details.url.includes('web.assets_backend') ||
        details.url.includes('web.assets_common') ||
        details.url.includes('load_menus')) {
      
      log.info(`INTERCEPTION: Requête asset détectée: ${details.url}`);
      
      // Ajouter des paramètres de cache-busting si pas déjà présents
      const url = new URL(details.url);
      if (!url.searchParams.has('_electron_fix')) {
        url.searchParams.set('_electron_fix', Date.now());
        url.searchParams.set('_no_cache', '1');
        
        log.info(`INTERCEPTION: Redirection avec cache-busting: ${url.toString()}`);
        callback({ redirectURL: url.toString() });
        return;
      }
    }
    
    callback({});
  });

  // Intercepter les en-têtes de réponse pour corriger Content-Length
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    // Vérifier si c'est un asset problématique
    if (details.url.includes('web.assets_backend') ||
        details.url.includes('web.assets_common') ||
        details.url.includes('load_menus')) {
      
      log.info(`INTERCEPTION: Modification des en-têtes pour: ${details.url}`);
      
      const responseHeaders = { ...details.responseHeaders };
      
      // Supprimer les en-têtes problématiques
      delete responseHeaders['content-length'];
      delete responseHeaders['Content-Length'];
      delete responseHeaders['content-encoding'];
      delete responseHeaders['Content-Encoding'];
      
      // Ajouter des en-têtes pour forcer le rechargement
      responseHeaders['Cache-Control'] = ['no-cache, no-store, must-revalidate'];
      responseHeaders['Pragma'] = ['no-cache'];
      responseHeaders['Expires'] = ['0'];
      
      log.info(`INTERCEPTION: En-têtes modifiés pour: ${details.url}`);
      callback({ responseHeaders });
      return;
    }

    // Configurer la Content Security Policy (CSP) pour réduire les avertissements de sécurité
    log.info('Configuration de la Content Security Policy (CSP) pour la fenêtre Odoo');
    // Vérifier si c'est une requête pour un service worker ou une ressource liée à Firebase
    const isServiceWorker = details.resourceType === 'serviceWorker' ||
                           details.url.includes('firebase-messaging-sw.js') ||
                           details.url.includes('firebasejs') ||
                           details.url.includes('gstatic.com/firebasejs') ||
                           details.url.includes('firebase-messaging.js') ||
                           details.url.includes('firebase-app.js') ||
                           details.url.includes('firebasejs/8.4.2') ||
                           details.url.includes('firebasejs/8.4.3');

    if (isServiceWorker) {
      log.info(`Application de la CSP pour le service worker ou ressource Firebase: ${details.url}`);

      // CSP extrêmement permissive pour les service workers et ressources Firebase
      const swCsp = [
        `default-src * 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ${odooDomain} https: http:`,
        `script-src * 'self' 'unsafe-inline' 'unsafe-eval' blob: ${odooDomain} https://www.gstatic.com https://*.googleapis.com https://*.gstatic.com https://firebase.googleapis.com https://fcm.googleapis.com https: http:`,
        `style-src * 'self' 'unsafe-inline' ${odooDomain} https://*.googleapis.com https://*.gstatic.com https://fonts.googleapis.com https: http:`,
        `connect-src * 'self' ${odooDomain} https://www.gstatic.com https://*.googleapis.com https://*.gstatic.com https://fcm.googleapis.com https://firebase.googleapis.com https://identitytoolkit.googleapis.com https: http: ws: wss:`,
        `worker-src * 'self' blob: ${odooDomain} https://www.gstatic.com https://*.gstatic.com https://firebase.googleapis.com https: http:`,
        `img-src * 'self' data: blob: ${odooDomain} https://www.gstatic.com https://*.gstatic.com https://*.googleapis.com https: http:`,
        `font-src * 'self' data: ${odooDomain} https://fonts.googleapis.com https://fonts.gstatic.com https://fonts.odoocdn.com https: http:`
      ].join('; ');

      log.info(`CSP appliquée au service worker: ${swCsp}`);

      // Créer un nouvel objet d'en-têtes avec la CSP
      const responseHeaders = { ...details.responseHeaders };
      responseHeaders['Content-Security-Policy'] = [swCsp];

      // Retourner les en-têtes modifiées
      callback({ responseHeaders });
      return;
    }
    // Vérifier si la requête provient de l'URL Odoo
    if (details.url.startsWith(odooDomain)) {
      log.info(`Application de la CSP pour la requête: ${details.url}`);

      // Construire la CSP en incluant le domaine Odoo
      // Utiliser une politique plus permissive pour Odoo qui utilise beaucoup de scripts et de styles
      const csp = [
        `default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ${odooDomain} https://*.gstatic.com https://*.googleapis.com https://www.gstatic.com https://firebase.googleapis.com https://fcm.googleapis.com`,
        `script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: ${odooDomain} https://*.gstatic.com https://*.googleapis.com https://www.gstatic.com https://firebase.googleapis.com https://fcm.googleapis.com`,
        `style-src 'self' 'unsafe-inline' ${odooDomain} https://*.googleapis.com https://*.gstatic.com https://fonts.googleapis.com https://fonts.odoocdn.com https://www.gstatic.com`,
        `img-src 'self' data: blob: ${odooDomain} https://*.gstatic.com https://*.googleapis.com https://www.gstatic.com https://firebase.googleapis.com https:`,
        `connect-src 'self' ${odooDomain} ws: wss: https://www.gstatic.com https://*.googleapis.com https://*.gstatic.com https://fcm.googleapis.com https://firebase.googleapis.com https://identitytoolkit.googleapis.com`,
        `font-src 'self' data: ${odooDomain} https://fonts.odoocdn.com https://fonts.googleapis.com https://fonts.gstatic.com https://*.gstatic.com https://www.gstatic.com`,
        `worker-src 'self' blob: ${odooDomain} https://*.gstatic.com https://*.googleapis.com https://www.gstatic.com https://firebase.googleapis.com`
      ].join('; ');

      // Log pour confirmer la mise à jour des directives CSP
      log.info(`CSP mise à jour avec connect-src: 'self' ${odooDomain} ws: wss: https://www.gstatic.com https://*.googleapis.com https://*.gstatic.com https://fcm.googleapis.com https://firebase.googleapis.com https://identitytoolkit.googleapis.com`);
      log.info(`CSP mise à jour avec font-src: 'self' data: ${odooDomain} https://fonts.odoocdn.com https://fonts.googleapis.com https://fonts.gstatic.com https://*.gstatic.com https://www.gstatic.com`);

      // Créer un nouvel objet d'en-têtes avec la CSP
      const responseHeaders = { ...details.responseHeaders };
      responseHeaders['Content-Security-Policy'] = [csp];

      log.info(`CSP appliquée: ${csp}`);

      // Retourner les en-têtes modifiées
      callback({ responseHeaders });
    } else if (details.url.includes('gstatic.com') || details.url.includes('googleapis.com') || details.url.includes('firebase')) {
      // CSP spécifique pour les ressources Firebase/Google qui ne sont pas des service workers
      log.info(`Application de la CSP pour la ressource Google/Firebase: ${details.url}`);

      const firebaseCsp = [
        `default-src * 'self' 'unsafe-inline' 'unsafe-eval' data: blob:`,
        `script-src * 'self' 'unsafe-inline' 'unsafe-eval' blob: https://www.gstatic.com https://*.googleapis.com https://firebase.googleapis.com https://fcm.googleapis.com`,
        `connect-src * 'self' https: http: ws: wss: https://www.gstatic.com https://*.googleapis.com https://firebase.googleapis.com https://fcm.googleapis.com https://*.gstatic.com`,
        `style-src * 'self' 'unsafe-inline' https://fonts.googleapis.com https://*.gstatic.com`,
        `img-src * 'self' data: blob: https://www.gstatic.com https://*.googleapis.com https://firebase.googleapis.com`,
        `font-src * 'self' data: https://fonts.googleapis.com https://fonts.gstatic.com https://*.gstatic.com`
      ].join('; ');

      // Créer un nouvel objet d'en-têtes avec la CSP
      const responseHeaders = { ...details.responseHeaders };
      responseHeaders['Content-Security-Policy'] = [firebaseCsp];

      log.info(`CSP appliquée à la ressource Google/Firebase: ${firebaseCsp}`);

      // Retourner les en-têtes modifiées
      callback({ responseHeaders });
    } else {
      // Pour les autres requêtes, ne pas modifier les en-têtes
      callback({ responseHeaders: details.responseHeaders });
    }
  });

  // S'assurer que la fenêtre unique existe
  if (!singleWindow || singleWindow.isDestroyed()) {
    log.info('La fenêtre unique n\'existe pas, création d\'une nouvelle fenêtre');
    createSingleWindow();
  }

  // Mettre à jour l'état de l'application
  currentAppState = 'main';

  // Utiliser la nouvelle fonction pour définir le cookie de session et charger l'URL Odoo
  if (authData.session_id) {
    log.info(`Définition du cookie de session avec la nouvelle fonction: ${authData.session_id.substring(0, 8)}...`);

    // Définir le cookie de session avec la fonction interne
    setOdooSessionCookieInternal(authData.session_id, odooDomain)
      .then(cookieSet => {
        if (cookieSet) {
          log.info('Cookie de session défini avec succès via la nouvelle fonction');

          // Construire l'URL de l'interface Odoo (sans paramètre session_id)
          const odooWebUrl = authData.odoo_url;
          log.info(`Chargement de l'URL Odoo après définition du cookie: ${odooWebUrl}`);

          // Charger l'URL Odoo dans la fenêtre unique
          return singleWindow.loadURL(odooWebUrl);
        } else {
          log.error('Échec de la définition du cookie de session via la nouvelle fonction');

          // Essayer avec l'ancienne méthode en cas d'échec
          log.info('Tentative avec l\'ancienne méthode...');

          // Stocker le cookie de session dans la variable globale
          storedSessionId = authData.session_id;

          // Extraire le domaine du serveur
          const serverDomain = new URL(odooDomain).hostname;

          // Définir le cookie avec tous les paramètres nécessaires
          const cookie = {
            url: odooDomain,
            name: 'session_id',
            value: authData.session_id,
            domain: serverDomain,
            path: '/',
            secure: odooDomain.startsWith('https'),
            httpOnly: false,
            expirationDate: Math.floor(Date.now() / 1000) + 86400 // 24 heures
          };

          return session.defaultSession.cookies.set(cookie)
            .then(() => {
              log.info('Cookie de session défini avec succès via l\'ancienne méthode');
              return singleWindow.loadURL(authData.odoo_url);
            });
        }
      })
      .then(() => {
        log.info('URL Odoo chargée avec succès');

        // Lire le contenu du script de gestion de session
        const fs = require('fs');
        const scriptPath = path.join(__dirname, '../assets/js/session-manager.js');

        try {
          const scriptContent = fs.readFileSync(scriptPath, 'utf8');

          // Injecter le script dans la page
          return singleWindow.webContents.executeJavaScript(scriptContent);
        } catch (error) {
          log.error(`Erreur lors de la lecture du script de gestion de session: ${error.message}`);
          return Promise.resolve();
        }
      })
      .then(() => {
        log.info('Script de gestion de session injecté avec succès');

        // Injecter le script de RECHARGEMENT IMMÉDIAT en PRIORITÉ ULTIME
        setTimeout(() => {
          try {
            const fs = require('fs');
            const immediateReloadPath = path.join(__dirname, '../assets/js/immediate-reload-fix.js');
            const immediateReloadContent = fs.readFileSync(immediateReloadPath, 'utf8');

            log.info('Injection du script de RECHARGEMENT IMMÉDIAT...');
            singleWindow.webContents.executeJavaScript(immediateReloadContent)
              .then(result => {
                log.info('Script de RECHARGEMENT IMMÉDIAT exécuté avec succès');
              })
              .catch(error => {
                log.error(`Erreur lors de l'exécution du script de rechargement immédiat: ${error.message}`);
              });
          } catch (error) {
            log.error(`Erreur lors de la lecture du script de rechargement immédiat: ${error.message}`);
          }
        }, 100); // Injecter EN PREMIER

        // Injecter le script de correction AGRESSIF en PRIORITÉ ABSOLUE
        setTimeout(() => {
          try {
            const fs = require('fs');
            const aggressiveFixPath = path.join(__dirname, '../assets/js/aggressive-content-fix.js');
            const aggressiveFixContent = fs.readFileSync(aggressiveFixPath, 'utf8');

            log.info('Injection du script de correction AGRESSIF...');
            singleWindow.webContents.executeJavaScript(aggressiveFixContent)
              .then(result => {
                log.info('Script de correction AGRESSIF exécuté avec succès');
              })
              .catch(error => {
                log.error(`Erreur lors de l'exécution du script agressif: ${error.message}`);
              });
          } catch (error) {
            log.error(`Erreur lors de la lecture du script agressif: ${error.message}`);
          }
        }, 200); // Injecter TRÈS tôt

        // Injecter le script de correction Content-Length Mismatch en PRIORITÉ
        setTimeout(() => {
          try {
            const fs = require('fs');
            const contentLengthFixPath = path.join(__dirname, '../assets/js/content-length-mismatch-fix.js');
            const contentLengthFixContent = fs.readFileSync(contentLengthFixPath, 'utf8');

            log.info('Injection du script de correction Content-Length Mismatch...');
            singleWindow.webContents.executeJavaScript(contentLengthFixContent)
              .then(result => {
                log.info('Script de correction Content-Length Mismatch exécuté avec succès');
              })
              .catch(error => {
                log.error(`Erreur lors de l'exécution du script de correction Content-Length: ${error.message}`);
              });
          } catch (error) {
            log.error(`Erreur lors de la lecture du script de correction Content-Length: ${error.message}`);
          }
        }, 500); // Injecter très tôt pour intercepter les erreurs

        // Injecter le script d'auto-reload intelligent
        setTimeout(() => {
          try {
            const fs = require('fs');
            const autoReloadFixPath = path.join(__dirname, '../assets/js/auto-reload-fix.js');
            const autoReloadFixContent = fs.readFileSync(autoReloadFixPath, 'utf8');

            log.info('Injection du script d\'auto-reload intelligent...');
            singleWindow.webContents.executeJavaScript(autoReloadFixContent)
              .then(result => {
                log.info('Script d\'auto-reload intelligent exécuté avec succès');
              })
              .catch(error => {
                log.error(`Erreur lors de l'exécution du script d'auto-reload: ${error.message}`);
              });
          } catch (error) {
            log.error(`Erreur lors de la lecture du script d'auto-reload: ${error.message}`);
          }
        }, 800); // Injecter après le script de correction

        // Injecter le script de vérification de session après un court délai
        setTimeout(() => {
          try {
            const fs = require('fs');
            const checkerPath = path.join(__dirname, '../assets/js/session-checker.js');
            const checkerContent = fs.readFileSync(checkerPath, 'utf8');

            log.info('Injection du script de vérification de session...');
            singleWindow.webContents.executeJavaScript(checkerContent)
              .then(result => {
                log.info('Script de vérification de session exécuté');
              })
              .catch(error => {
                log.error(`Erreur lors de l'exécution du script de vérification: ${error.message}`);
              });
          } catch (error) {
            log.error(`Erreur lors de la lecture du script de vérification: ${error.message}`);
          }
        }, 1000); // Attendre 1 seconde pour que la page soit complètement chargée

        // Injecter le nouveau script d'injection de session après un délai plus long
        setTimeout(() => {
          try {
            const fs = require('fs');
            const injectorPath = path.join(__dirname, '../assets/js/session-injector.js');
            const injectorContent = fs.readFileSync(injectorPath, 'utf8');

            log.info('Injection du script d\'injection de session...');
            singleWindow.webContents.executeJavaScript(injectorContent)
              .then(result => {
                log.info('Script d\'injection de session exécuté');
              })
              .catch(error => {
                log.error(`Erreur lors de l'exécution du script d'injection de session: ${error.message}`);
              });
          } catch (error) {
            log.error(`Erreur lors de la lecture du script d'injection de session: ${error.message}`);
          }
        }, 2000); // Attendre 2 secondes pour que la page soit complètement chargée
      })
      .catch(error => {
        log.error(`Erreur lors du chargement de l'URL ou de l'injection du script: ${error.message}`);

        // En cas d'erreur, essayer de recharger l'URL sans paramètres supplémentaires
        log.info('Tentative de rechargement de l\'URL Odoo');

        // Ajouter un paramètre nocache pour éviter les problèmes de cache
        const urlObj = new URL(authData.odoo_url);
        urlObj.searchParams.set('nocache', Date.now());
        const refreshedUrl = urlObj.toString();

        log.info(`Rechargement de l'URL Odoo: ${refreshedUrl}`);
        singleWindow.loadURL(refreshedUrl);
      });
  } else {
    // Charger directement l'URL d'Odoo sans cookie de session
    log.info('Chargement direct de l\'URL Odoo (sans cookie de session):', authData.odoo_url);
    singleWindow.loadURL(authData.odoo_url);
  }

  // Écouter les messages de la page web
  singleWindow.webContents.on('ipc-message', (event, channel, ...args) => {
    log.info(`Message IPC reçu de la page web: ${channel}`, args);
  });

  // Écouter les messages de la console de la page web
  singleWindow.webContents.on('console-message', (event, level, message, line, sourceId) => {
    if (message.includes('[Edara ERP]')) {
      log.info(`Console web (${sourceId}:${line}): ${message}`);
    }
  });

  // Détecter quand une page commence à charger pour injecter les scripts
  singleWindow.webContents.on('did-start-loading', () => {
    // Vérifier si la fenêtre existe toujours
    if (!singleWindow || singleWindow.isDestroyed() || !singleWindow.webContents) {
      log.warn('CHARGEMENT: Fenêtre unique non disponible lors du début du chargement de la page');
      return;
    }

    // Récupérer l'URL actuelle
    const currentUrl = singleWindow.webContents.getURL();
    log.info(`CHARGEMENT: Début du chargement de la page dans la fenêtre unique: ${currentUrl}`);

    // Préparer l'injection du script de correction Content-Length Mismatch pour toutes les pages
    try {
      // Injecter le script de correction des problèmes de Content-Length Mismatch
      const fs = require('fs');
      const contentLengthFixPath = path.join(__dirname, '../assets/js/content-length-mismatch-fix.js');

      log.info(`CHARGEMENT: Préparation du script de correction Content-Length Mismatch depuis ${contentLengthFixPath}`);

      // Lire le contenu du fichier
      const contentLengthFixContent = fs.readFileSync(contentLengthFixPath, 'utf8');

      // Stocker le contenu pour l'injecter plus tard
      global.contentLengthMismatchFixScript = contentLengthFixContent;
    } catch (error) {
      log.error(`CHARGEMENT: Erreur lors de la lecture du script de correction Content-Length Mismatch: ${error.message}`);
    }
  });

  // Détecter quand le DOM est prêt pour injecter les scripts
  singleWindow.webContents.on('dom-ready', () => {
    // Vérifier si la fenêtre existe toujours
    if (!singleWindow || singleWindow.isDestroyed() || !singleWindow.webContents) {
      log.warn('CHARGEMENT: Fenêtre unique non disponible lors de l\'initialisation du DOM');
      return;
    }

    // Récupérer l'URL actuelle
    const currentUrl = singleWindow.webContents.getURL();
    log.info(`CHARGEMENT: DOM prêt dans la fenêtre unique: ${currentUrl}`);

    // Injecter le script de correction Content-Length Mismatch pour toutes les pages Odoo
    if (currentUrl.includes('/web')) {
      if (global.contentLengthMismatchFixScript) {
        log.info(`CHARGEMENT: Injection du script de correction Content-Length Mismatch au moment du DOM ready`);

        // Injecter le script dans la page
        singleWindow.webContents.executeJavaScript(global.contentLengthMismatchFixScript)
          .then(() => {
            log.info('CHARGEMENT: Script de correction Content-Length Mismatch injecté avec succès au moment du DOM ready');
          })
          .catch(error => {
            log.error(`CHARGEMENT: Erreur lors de l'injection du script de correction Content-Length Mismatch: ${error.message}`);
          });
      } else {
        log.warn('CHARGEMENT: Script de correction Content-Length Mismatch non disponible');
      }
    }
  });

  // Détecter quand une page a fini de charger pour injecter les scripts
  singleWindow.webContents.on('did-finish-load', () => {
    // Vérifier si la fenêtre existe toujours
    if (!singleWindow || singleWindow.isDestroyed() || !singleWindow.webContents) {
      log.warn('CHARGEMENT: Fenêtre unique non disponible lors du chargement de la page');
      return;
    }

    // Récupérer l'URL actuelle
    const currentUrl = singleWindow.webContents.getURL();
    log.info(`CHARGEMENT: Page chargée dans la fenêtre unique: ${currentUrl}`);

    // Vérifier si l'URL correspond à l'interface Odoo
    if (currentUrl.includes('/web') && !currentUrl.includes('/web/login')) {
      try {
        // Injecter à nouveau le script de correction des problèmes de Content-Length Mismatch
        if (global.contentLengthMismatchFixScript) {
          log.info(`CHARGEMENT: Réinjection du script de correction Content-Length Mismatch après chargement complet`);

          // Injecter le script dans la page
          singleWindow.webContents.executeJavaScript(global.contentLengthMismatchFixScript)
            .then(() => {
              log.info('CHARGEMENT: Script de correction Content-Length réinjecté avec succès après chargement complet');
            })
            .catch(error => {
              log.error(`CHARGEMENT: Erreur lors de la réinjection du script de correction Content-Length: ${error.message}`);
            });
        } else {
          // Si le script n'est pas disponible globalement, le charger à nouveau
          const fs = require('fs');
          const contentLengthFixPath = path.join(__dirname, '../assets/js/content-length-fix.js');

          log.info(`CHARGEMENT: Injection du script de correction Content-Length depuis ${contentLengthFixPath}`);

          // Lire le contenu du fichier
          const contentLengthFixContent = fs.readFileSync(contentLengthFixPath, 'utf8');

          // Injecter le script dans la page
          singleWindow.webContents.executeJavaScript(contentLengthFixContent)
            .then(() => {
              log.info('CHARGEMENT: Script de correction Content-Length injecté avec succès');
            })
            .catch(error => {
              log.error(`CHARGEMENT: Erreur lors de l'injection du script de correction Content-Length: ${error.message}`);
            });
        }
      } catch (error) {
        log.error(`CHARGEMENT: Erreur lors de la lecture du script de correction Content-Length: ${error.message}`);
      }
    }
  });

  // Intercepter les navigations pour gérer la déconnexion
  singleWindow.webContents.on('will-navigate', (event, url) => {
    // Vérifier si la fenêtre existe toujours
    if (!singleWindow || singleWindow.isDestroyed()) {
      log.warn('NAVIGATION: Fenêtre non disponible lors de la tentative de navigation');
      return;
    }

    log.info(`NAVIGATION: Tentative de navigation détectée vers: ${url}`);
    log.info(`NAVIGATION: URL actuelle: ${singleWindow.webContents.getURL()}`);

    // Vérifier si l'URL correspond à une page de connexion ou de déconnexion Odoo
    try {
      const urlObj = new URL(url);

      // Détection précise des pages de connexion et de déconnexion
      const isLoginPage = urlObj.pathname === '/web/login' || urlObj.pathname.endsWith('/web/login');
      const isLogoutPage = urlObj.pathname === '/web/session/logout' || urlObj.pathname.endsWith('/web/session/logout');
      const hasLogoutParam = urlObj.search.includes('logout=1') || urlObj.search.includes('action=logout');

      // Log détaillé pour le débogage
      log.info(`NAVIGATION: Analyse de l'URL - pathname: ${urlObj.pathname}, search: ${urlObj.search}`);
      log.info(`NAVIGATION: Détection - isLoginPage: ${isLoginPage}, isLogoutPage: ${isLogoutPage}, hasLogoutParam: ${hasLogoutParam}`);

      if (isLogoutPage || hasLogoutParam) {
        log.info(`NAVIGATION: Déconnexion détectée (will-navigate): ${url}`);

        // Vérification du délai depuis la dernière navigation vers /web
        const timeSinceLastNavigation = Date.now() - lastNavigationToWeb;

        // Si moins de 10 secondes se sont écoulées depuis la dernière navigation vers /web, ignorer la déconnexion
        if (isLogoutPage && lastNavigationToWeb > 0 && timeSinceLastNavigation < 10000) {
          console.log('Déconnexion automatique empêchée après connexion récente');
          console.log(`Temps écoulé depuis la dernière navigation vers /web: ${timeSinceLastNavigation}ms`);

          // Empêcher la navigation vers la page de déconnexion
          event.preventDefault();
          return false; // Assurer que la navigation est bien arrêtée
        }

        // IMPORTANT: Empêcher la navigation vers la page de connexion/déconnexion Odoo
        event.preventDefault();
        log.info('NAVIGATION: Navigation empêchée avec event.preventDefault()');

        // Appeler handleOdooLogout avec l'URL complète
        log.info(`NAVIGATION: Appel de handleOdooLogout avec l'URL: ${url}`);
        handleOdooLogout(url);

        return false; // Assurer que la navigation est bien arrêtée
      } else {
        log.info(`NAVIGATION: Navigation autorisée vers: ${url} (pas une page de connexion/déconnexion)`);
      }
    } catch (error) {
      log.error(`NAVIGATION: Erreur lors de l'analyse de l'URL dans will-navigate: ${error.message}`);

      // Même en cas d'erreur d'analyse, si l'URL contient des mots-clés de déconnexion, intercepter
      if (url.includes('/web/login') || url.includes('/web/session/logout') || url.includes('logout=')) {
        log.info(`NAVIGATION: Déconnexion détectée par mots-clés après erreur d'analyse: ${url}`);

        // Vérification du délai depuis la dernière navigation vers /web
        console.log('Vérification du délai depuis la dernière navigation vers /web');
        const timeSinceLastNavigation = Date.now() - lastNavigationToWeb;

        // Si moins de 10 secondes se sont écoulées depuis la dernière navigation vers /web, ignorer la déconnexion
        if (url.includes('/web/session/logout') && lastNavigationToWeb > 0 && timeSinceLastNavigation < 10000) {
          console.log('Déconnexion automatique empêchée après connexion récente');
          console.log(`Temps écoulé depuis la dernière navigation vers /web: ${timeSinceLastNavigation}ms`);

          // Empêcher la navigation vers la page de déconnexion
          event.preventDefault();
          return false; // Assurer que la navigation est bien arrêtée
        }

        // IMPORTANT: Empêcher la navigation
        event.preventDefault();
        log.info('NAVIGATION: Navigation empêchée avec event.preventDefault() (après erreur)');

        // Appeler handleOdooLogout avec l'URL brute
        log.info(`NAVIGATION: Appel de handleOdooLogout avec l'URL brute: ${url}`);
        handleOdooLogout(url);

        return false; // Assurer que la navigation est bien arrêtée
      } else {
        log.info(`NAVIGATION: Navigation autorisée malgré l'erreur d'analyse: ${url}`);
      }
    }
  });

  // Détecter quand une page a fini de charger pour injecter le script d'interception
  mainWindow.webContents.on('did-finish-load', () => {
    // Vérifier si la fenêtre principale existe toujours
    if (!mainWindow || mainWindow.isDestroyed() || !mainWindow.webContents) {
      log.warn('CHARGEMENT: Fenêtre principale non disponible lors du chargement de la page');
      return;
    }

    // Récupérer l'URL actuelle
    const currentUrl = mainWindow.webContents.getURL();
    log.info(`CHARGEMENT: Page chargée: ${currentUrl}`);

    try {
      // Injecter le script d'interception de déconnexion amélioré
      const fs = require('fs');
      const scriptPath = path.join(__dirname, '../assets/js/simple-logout-interceptor.js');

      log.info(`CHARGEMENT: Injection du script d'interception amélioré depuis ${scriptPath}`);

      // Lire le contenu du fichier
      const scriptContent = fs.readFileSync(scriptPath, 'utf8');

      // Injecter le script dans la page
      mainWindow.webContents.executeJavaScript(scriptContent)
        .then(() => {
          log.info('CHARGEMENT: Script d\'interception amélioré injecté avec succès');
        })
        .catch(error => {
          log.error(`CHARGEMENT: Erreur lors de l'injection du script amélioré: ${error.message}`);
        });

      // Signaler à la fenêtre de chargement que le chargement est terminé
      if (splashWindow && !splashWindow.isDestroyed() && splashWindow.webContents) {
        log.info('CHARGEMENT: Envoi du message de chargement terminé à la fenêtre de chargement');
        splashWindow.webContents.send('load-odoo-interface', { action: 'loading-complete' });
      } else {
        // Si la fenêtre de chargement n'est plus disponible, afficher directement la fenêtre principale
        if (mainWindow && !mainWindow.isVisible()) {
          log.info('CHARGEMENT: Affichage de la fenêtre principale (fenêtre de chargement non disponible)');
          mainWindow.show();
        }
      }
    } catch (error) {
      log.error(`CHARGEMENT: Erreur lors du chargement de la page: ${error.message}`);

      // En cas d'erreur, afficher quand même la fenêtre principale si elle n'est pas visible
      if (mainWindow && !mainWindow.isVisible()) {
        log.info('CHARGEMENT: Affichage de la fenêtre principale malgré l\'erreur');
        mainWindow.show();
      }
    }
  });
}

// Écouteur pour intercepter le cookie session_id après l'authentification
ipcMain.on('capture-session-cookie', (event) => {
  console.log('[Main] Demande d\'interception du cookie session_id reçue');

  // Vérifier si la fenêtre principale existe
  if (!mainWindow || mainWindow.isDestroyed()) {
    console.log('[Main] Fenêtre principale non disponible, impossible d\'intercepter le cookie');
    return;
  }

  // Déterminer l'URL de base pour récupérer les cookies
  const baseUrl = lastServerUrl || 'http://localhost:8069';
  console.log('[Main] URL de base pour la récupération des cookies:', baseUrl);

  // Récupérer tous les cookies associés à l'URL de l'instance Odoo
  mainWindow.webContents.session.cookies.get({ url: baseUrl })
    .then((cookies) => {
      console.log('[Main] Cookies récupérés:', cookies.length);

      // Rechercher le cookie session_id
      const sessionCookie = cookies.find(cookie => cookie.name === 'session_id');

      if (sessionCookie) {
        storedSessionId = sessionCookie.value;
        console.log('[Main] Cookie session_id intercepté:', storedSessionId);
      } else {
        console.log('[Main] Cookie session_id non trouvé dans les cookies récupérés');
      }
    })
    .catch((error) => {
      console.error('[Main] Erreur lors de la récupération des cookies:', error);
    });
});

// Écouteur pour stocker le cookie session_id
ipcMain.on('store-session-cookie', (event, sessionId) => {
  log.info(`Cookie session_id stocké: ${sessionId.substring(0, 8)}...`);
  storedSessionId = sessionId;
});

// Écouteur pour recevoir les résultats de la vérification de session
ipcMain.on('session-check-result', (event, result) => {
  log.info('=== Résultat de la vérification de session ===');
  log.info(`Cookie session_id présent: ${result.hasSessionCookie ? 'Oui' : 'Non'}`);

  if (result.hasSessionCookie && result.sessionCookieValue) {
    log.info(`Valeur du cookie: ${result.sessionCookieValue.substring(0, 8)}...`);
  }

  if (result.isLoginPage) {
    log.warn('Page de connexion détectée - Session probablement expirée ou invalide');
  }

  if (result.sessionInfo) {
    log.info('Session Odoo active:');
    log.info(`- Utilisateur: ${result.sessionInfo.username}`);
    log.info(`- ID Utilisateur: ${result.sessionInfo.uid}`);
    log.info(`- Base de données: ${result.sessionInfo.db}`);
    log.info(`- Entreprise: ${result.sessionInfo.company_id}`);
  } else {
    log.warn('Aucune information de session Odoo détectée');
  }

  if (result.allCookies && result.allCookies.length > 0) {
    log.info(`Tous les cookies disponibles (${result.allCookies.length}):`);
    result.allCookies.forEach(cookie => {
      log.info(`- ${cookie}`);
    });
  } else if (result.allCookies) {
    log.warn('Aucun cookie disponible');
  }

  log.info('============================================');
});

// Écouteur pour la session expirée
ipcMain.on('session-expired', (event) => {
  log.info('Session Odoo expirée, redirection vers la page de connexion');

  // Récupérer l'URL du serveur
  const serverUrl = lastServerUrl || 'http://localhost:8069';

  // Gérer la déconnexion
  handleOdooLogout(serverUrl);
});

// Écouteur pour la session invalide
ipcMain.on('session-invalid', (event) => {
  log.info('Session Odoo invalide, redirection vers la page de connexion');

  // Récupérer l'URL du serveur
  const serverUrl = lastServerUrl || 'http://localhost:8069';

  // Gérer la déconnexion
  handleOdooLogout(serverUrl);
});

// Écouteur pour les résultats du script d'injection de session
ipcMain.on('session-injector-result', (event, result) => {
  log.info('=== Résultat du script d\'injection de session ===');
  log.info(`Cookie session_id présent: ${result.hasCookie ? 'Oui' : 'Non'}`);
  log.info(`Session Odoo valide: ${result.sessionValid === true ? 'Oui' : (result.sessionValid === false ? 'Non' : 'Indéterminé')}`);
  log.info(`URL actuelle: ${result.url}`);

  // Si le cookie n'est pas présent mais que nous avons un ID de session stocké, essayer de l'injecter
  if (!result.hasCookie && storedSessionId && singleWindow && !singleWindow.isDestroyed()) {
    log.info(`Tentative d'injection du cookie session_id via le script d'injection: ${storedSessionId.substring(0, 8)}...`);

    // Envoyer l'ID de session au script d'injection
    singleWindow.webContents.send('inject-session-cookie', storedSessionId);
  }
});

// Écouteur pour la détection de la page de connexion Odoo
ipcMain.on('odoo-login-page-detected', (event) => {
  log.info('Page de connexion Odoo détectée par le script d\'injection');

  // Si nous avons un ID de session stocké, essayer de le définir et de recharger la page
  if (storedSessionId && lastServerUrl) {
    log.info(`Tentative de définition du cookie session_id et de redirection vers /web: ${storedSessionId.substring(0, 8)}...`);

    // Définir le cookie de session
    setOdooSessionCookieInternal(storedSessionId, lastServerUrl)
      .then(cookieSet => {
        if (cookieSet) {
          log.info('Cookie défini avec succès, redirection vers /web');

          // Rediriger vers /web
          if (singleWindow && !singleWindow.isDestroyed()) {
            singleWindow.loadURL(`${lastServerUrl}/web`);
          }
        } else {
          log.error('Échec de la définition du cookie, impossible de rediriger');
        }
      })
      .catch(error => {
        log.error(`Erreur lors de la définition du cookie: ${error.message}`);
      });
  }
});

// Écouteur pour la détection de l'interface Odoo
ipcMain.on('odoo-interface-detected', (event) => {
  log.info('Interface Odoo détectée par le script d\'injection, la session est valide');
});

// Écouteur pour la détection du cookie session_id
ipcMain.on('session-cookie-found', (event, sessionId) => {
  log.info(`Cookie session_id trouvé par le script d\'injection: ${sessionId.substring(0, 8)}...`);

  // Stocker l'ID de session
  storedSessionId = sessionId;
});

// Écouteur pour l'absence du cookie session_id
ipcMain.on('session-cookie-missing', (event) => {
  log.info('Cookie session_id non trouvé par le script d\'injection');

  // Si nous avons un ID de session stocké, essayer de le définir
  if (storedSessionId && lastServerUrl && singleWindow && !singleWindow.isDestroyed()) {
    log.info(`Tentative de définition du cookie session_id: ${storedSessionId.substring(0, 8)}...`);

    // Définir le cookie de session
    setOdooSessionCookieInternal(storedSessionId, lastServerUrl)
      .then(cookieSet => {
        if (cookieSet) {
          log.info('Cookie défini avec succès, rechargement de la page');

          // Recharger la page actuelle
          singleWindow.reload();
        } else {
          log.error('Échec de la définition du cookie');
        }
      })
      .catch(error => {
        log.error(`Erreur lors de la définition du cookie: ${error.message}`);
      });
  }
});

// Écouteur pour la demande de déconnexion
ipcMain.on('logout-requested', (event, url) => {
  log.info(`Demande de déconnexion reçue depuis la page web: ${url}`);

  // Gérer la déconnexion
  handleOdooLogout(url);
});

// Écouteur pour gérer la navigation vers /web via le processus principal
ipcMain.on('navigate-to-odoo', (event, url) => {
  console.log('[Main] Message reçu pour naviguer vers:', url);

  // Vérifier si la fenêtre principale existe
  if (!singleWindow || singleWindow.isDestroyed()) {
    console.log('[Main] Fenêtre principale non disponible, impossible de naviguer');
    return;
  }

  // Construire l'URL complète en utilisant l'URL de base de l'instance Odoo
  let fullUrl = url;
  let baseUrl = lastServerUrl || 'http://localhost:8069';

  if (url.startsWith('/')) {
    // Si l'URL est relative (commence par /), ajouter l'URL de base
    fullUrl = baseUrl + url;
  }

  console.log(`[Main] URL complète à charger: ${fullUrl}`);

  // Si nous avons un cookie session_id stocké, le définir avant de charger l'URL
  if (storedSessionId) {
    console.log('[Main] Session ID stocké trouvé, définition du cookie avant navigation');

    // Utiliser la nouvelle fonction pour définir le cookie
    setOdooSessionCookieInternal(storedSessionId, baseUrl)
      .then(cookieSet => {
        if (cookieSet) {
          console.log('[Main] Cookie défini avec succès via la nouvelle fonction, chargement de l\'URL:', fullUrl);

          // Charger l'URL dans la fenêtre principale
          return singleWindow.loadURL(fullUrl);
        } else {
          console.error('[Main] Échec de la définition du cookie via la nouvelle fonction, tentative avec l\'ancienne méthode');

          // Extraire le domaine de l'URL
          try {
            const urlObj = new URL(baseUrl);
            const serverDomain = urlObj.hostname;

            // Définir le cookie avec tous les paramètres nécessaires
            const cookie = {
              url: baseUrl,
              name: 'session_id',
              value: storedSessionId,
              path: '/',
              domain: serverDomain,
              httpOnly: false,
              secure: baseUrl.startsWith('https'),
              expirationDate: Math.floor(Date.now() / 1000) + 86400 // 24 heures
            };

            return session.defaultSession.cookies.set(cookie)
              .then(() => {
                console.log('[Main] Cookie défini avec succès via l\'ancienne méthode, chargement de l\'URL:', fullUrl);
                return singleWindow.loadURL(fullUrl);
              });
          } catch (error) {
            console.error('[Main] Erreur lors de l\'analyse de l\'URL:', error);
            // En cas d'erreur, charger l'URL sans cookie
            return singleWindow.loadURL(fullUrl);
          }
        }
      })
      .then(() => {
        console.log('[Main] URL chargée avec succès');

        // Mettre à jour le timestamp de la dernière navigation vers /web
        if (url === '/web') {
          lastNavigationToWeb = Date.now();
          console.log('[Main] Timestamp de la dernière navigation vers /web mis à jour:', lastNavigationToWeb);
        }

        // Vérifier que le cookie est bien défini après le chargement
        return session.defaultSession.cookies.get({ url: baseUrl });
      })
      .then(cookies => {
        const sessionCookie = cookies.find(c => c.name === 'session_id');
        if (sessionCookie) {
          console.log(`[Main] Vérification après chargement: Cookie session_id présent: ${sessionCookie.value.substring(0, 8)}...`);
        } else {
          console.warn('[Main] Vérification après chargement: Cookie session_id absent!');
        }
      })
      .catch(error => {
        console.error('[Main] Erreur lors de la définition du cookie ou du chargement de l\'URL:', error);

        // En cas d'erreur, essayer de recharger l'URL sans paramètres supplémentaires
        console.log('[Main] Tentative de rechargement de l\'URL Odoo');

        // Ajouter un paramètre nocache pour éviter les problèmes de cache
        const urlObj = new URL(fullUrl);
        urlObj.searchParams.set('nocache', Date.now());
        const refreshedUrl = urlObj.toString();

        console.log('[Main] Rechargement de l\'URL Odoo:', refreshedUrl);
        singleWindow.loadURL(refreshedUrl);
      });
  } else {
    console.log('[Main] Aucun cookie session_id stocké, chargement de l\'URL sans cookie:', fullUrl);

    // Charger l'URL dans la fenêtre principale
    singleWindow.loadURL(fullUrl)
      .then(() => {
        // Mettre à jour le timestamp de la dernière navigation vers /web
        if (url === '/web') {
          lastNavigationToWeb = Date.now();
          console.log('[Main] Timestamp de la dernière navigation vers /web mis à jour:', lastNavigationToWeb);
        }
      })
      .catch(error => {
        console.error('[Main] Erreur lors du chargement de l\'URL:', error);
      });
  }
});

/**
 * Authentifie un utilisateur via l'API Odoo /web/session/authenticate et redirige vers /web
 * Cette fonction :
 * 1. Effectue une requête POST vers /web/session/authenticate avec axios
 * 2. Extrait le cookie session_id depuis la réponse
 * 3. Ajoute ce cookie à session.defaultSession.cookies
 * 4. Charge la page principale d'Odoo (/web) dans la BrowserWindow
 * 5. Affiche les erreurs si une étape échoue
 *
 * @param {string} username - Nom d'utilisateur ou email
 * @param {string} password - Mot de passe
 * @param {string} serverUrl - URL du serveur Odoo (ex: http://localhost:8069)
 * @param {string} dbName - Nom de la base de données Odoo (par défaut: 'ligne-digitale')
 * @param {BrowserWindow} window - Fenêtre Electron dans laquelle charger l'interface Odoo
 * @returns {Promise<Object>} - Résultat de l'authentification
 */
async function authenticateAndRedirect(username, password, serverUrl, dbName = 'ligne-digitale', window = null) {
  log.info(`Authentification via API /web/session/authenticate pour l'utilisateur ${username} sur ${serverUrl}`);

  try {
    // Vérifier que les paramètres requis sont fournis
    if (!username || !password || !serverUrl) {
      const missingParams = [];
      if (!username) missingParams.push('nom d\'utilisateur');
      if (!password) missingParams.push('mot de passe');
      if (!serverUrl) missingParams.push('URL du serveur');

      const errorMsg = `Paramètres manquants: ${missingParams.join(', ')}`;
      log.error(errorMsg);
      return {
        success: false,
        error: errorMsg,
        errorType: 'MISSING_PARAMS'
      };
    }

    // S'assurer que l'URL du serveur est valide
    let serverUrlObj;
    try {
      serverUrlObj = new URL(serverUrl);
    } catch (urlError) {
      const errorMsg = `URL du serveur invalide: ${serverUrl}`;
      log.error(errorMsg, urlError);
      return {
        success: false,
        error: errorMsg,
        errorType: 'INVALID_URL'
      };
    }

    // Construire l'URL de l'API d'authentification Odoo
    const authUrl = `${serverUrl}/web/session/authenticate`;
    log.info(`URL d'authentification: ${authUrl}`);

    // Préparer les données pour la requête
    const data = {
      jsonrpc: '2.0',
      method: 'call',
      params: {
        db: dbName,
        login: username,
        password: password,
        context: {}
      },
      id: Date.now().toString()
    };

    log.info(`Envoi de la requête d'authentification pour l'utilisateur ${username} sur la base de données ${dbName}`);

    // Effectuer la requête POST
    const response = await axios.post(authUrl, data, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      withCredentials: true,
      timeout: 30000
    });

    // Vérifier la réponse
    if (response.status === 200 && response.data && response.data.result) {
      const result = response.data.result;

      // Vérifier si l'authentification a réussi
      if (result.uid) {
        log.info(`Authentification réussie pour l'utilisateur ${username}. UID: ${result.uid}`);

        // Extraire le cookie de session
        const cookies = response.headers['set-cookie'];
        let sessionId = null;

        if (cookies && cookies.length > 0) {
          for (const cookie of cookies) {
            if (cookie.includes('session_id=')) {
              sessionId = cookie.split('session_id=')[1].split(';')[0];
              break;
            }
          }
        }

        if (sessionId) {
          log.info(`Cookie de session obtenu: ${sessionId.substring(0, 8)}...`);

          // Stocker le cookie de session dans la variable globale
          storedSessionId = sessionId;

          // Définir le cookie de session dans Electron
          const cookieSet = await setOdooSessionCookieInternal(sessionId, serverUrl);

          if (cookieSet) {
            log.info('Cookie de session défini avec succès dans Electron');

            // Construire l'URL de l'interface Odoo (sans paramètre session_id)
            const odooWebUrl = `${serverUrl}/web`;
            log.info(`URL de l'interface Odoo: ${odooWebUrl}`);

            // Si une fenêtre est fournie, charger l'URL Odoo dans cette fenêtre
            if (window && !window.isDestroyed()) {
              log.info(`Chargement de l'interface Odoo dans la fenêtre fournie`);
              await window.loadURL(odooWebUrl);
            }

            return {
              success: true,
              userId: result.uid,
              name: result.name || username,
              sessionId: sessionId,
              server: serverUrl,
              dbName: dbName,
              company_id: result.company_id,
              user_context: result.user_context,
              odoo_url: odooWebUrl
            };
          } else {
            log.error('Échec de la définition du cookie de session dans Electron');
            return {
              success: false,
              error: 'Échec de la définition du cookie de session',
              errorType: 'COOKIE_SET_FAILED',
              sessionId: sessionId // Retourner quand même l'ID de session pour une utilisation ultérieure
            };
          }
        } else {
          log.warn('Authentification réussie mais aucun cookie de session trouvé dans la réponse');

          // Même sans cookie, on peut considérer l'authentification comme réussie
          // car l'API a retourné un UID valide
          return {
            success: true,
            userId: result.uid,
            name: result.name || username,
            sessionId: null, // Pas de cookie de session
            server: serverUrl,
            dbName: dbName,
            company_id: result.company_id,
            user_context: result.user_context,
            warning: 'Aucun cookie de session trouvé dans la réponse'
          };
        }
      } else {
        log.error(`Échec de l'authentification: aucun UID retourné`);
        return {
          success: false,
          error: 'Identifiants incorrects',
          errorType: 'INVALID_CREDENTIALS'
        };
      }
    } else if (response.data && response.data.error) {
      // Erreur retournée par l'API Odoo
      const error = response.data.error;
      log.error(`Erreur d'authentification retournée par l'API Odoo:`, error);

      return {
        success: false,
        error: error.data && error.data.message ? error.data.message : 'Erreur d\'authentification',
        errorType: 'ODOO_API_ERROR',
        details: error
      };
    } else {
      // Réponse inattendue
      log.error(`Réponse inattendue de l'API d'authentification:`, response.data);

      return {
        success: false,
        error: 'Réponse inattendue du serveur',
        errorType: 'UNEXPECTED_RESPONSE',
        details: response.data
      };
    }
  } catch (error) {
    // Gérer les différents types d'erreurs
    log.error(`Erreur lors de l'authentification:`, error);

    let errorMsg = 'Erreur de connexion au serveur';
    let errorType = 'CONNECTION_ERROR';

    if (error.response) {
      // La requête a été effectuée et le serveur a répondu avec un code d'état
      // qui n'est pas dans la plage 2xx
      log.error(`Erreur de réponse HTTP: ${error.response.status}`);
      errorMsg = `Erreur de réponse HTTP: ${error.response.status}`;
      errorType = 'HTTP_ERROR';

      if (error.response.data && error.response.data.error) {
        errorMsg = error.response.data.error.message || errorMsg;
      }
    } else if (error.request) {
      // La requête a été effectuée mais aucune réponse n'a été reçue
      log.error('Aucune réponse reçue du serveur');
      errorMsg = 'Aucune réponse reçue du serveur';
      errorType = 'NO_RESPONSE';
    } else {
      // Une erreur s'est produite lors de la configuration de la requête
      errorMsg = `Erreur lors de la configuration de la requête: ${error.message}`;
      errorType = 'REQUEST_SETUP_ERROR';
    }

    return {
      success: false,
      error: errorMsg,
      errorType: errorType,
      details: error.message
    };
  }
}

// Exposer la fonction authenticateAndRedirect via IPC
ipcMain.handle('authenticate-and-redirect', async (event, { username, password, serverUrl, dbName }) => {
  log.info(`Demande d'authentification et redirection reçue pour l'utilisateur ${username} sur ${serverUrl}`);

  try {
    // Récupérer la fenêtre active
    let activeWindow = null;
    if (singleWindow && !singleWindow.isDestroyed()) {
      activeWindow = singleWindow;
    } else if (mainWindow && !mainWindow.isDestroyed()) {
      activeWindow = mainWindow;
    }

    // Appeler la fonction d'authentification
    const result = await authenticateAndRedirect(username, password, serverUrl, dbName, activeWindow);

    // Si l'authentification a réussi, stocker l'URL du serveur
    if (result.success) {
      lastServerUrl = serverUrl;
    }

    return result;
  } catch (error) {
    log.error(`Erreur lors de l'authentification et redirection:`, error);
    return {
      success: false,
      error: error.message || 'Erreur interne',
      errorType: 'INTERNAL_ERROR'
    };
  }
});

// Activer les DevTools en mode développement
const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged;

// Fonction pour activer les DevTools sur une fenêtre
function enableDevTools(window) {
  // Activer les DevTools
  if (isDev) {
    log.info('Mode développement détecté, activation des DevTools');
    window.webContents.openDevTools();
  }

  // Ajouter un raccourci clavier pour ouvrir les DevTools (Alt+Cmd+I sur macOS, Ctrl+Shift+I sur Windows/Linux)
  window.webContents.on('before-input-event', (event, input) => {
    // Vérifier si la combinaison de touches correspond à Alt+Cmd+I (macOS) ou Ctrl+Shift+I (Windows/Linux)
    const isMac = process.platform === 'darwin';
    const isDevToolsShortcut = isMac
      ? (input.alt && input.meta && input.key.toLowerCase() === 'i')
      : (input.control && input.shift && input.key.toLowerCase() === 'i');

    if (isDevToolsShortcut) {
      log.info('Raccourci clavier DevTools détecté');
      if (window.webContents.isDevToolsOpened()) {
        log.info('Fermeture des DevTools');
        window.webContents.closeDevTools();
      } else {
        log.info('Ouverture des DevTools');
        window.webContents.openDevTools();
      }
      event.preventDefault();
    }
  });
}

// Configurer une CSP globale pour les ressources Firebase
app.on('ready', () => {
  // Raccourcis clavier pour les pages de test
  const { globalShortcut } = require('electron');

  // Raccourci pour ouvrir la page de test d'authentification
  globalShortcut.register('CommandOrControl+Shift+T', () => {
    log.info('Raccourci clavier pour ouvrir la page de test d\'authentification');

    // Créer la fenêtre unique si elle n'existe pas
    if (!singleWindow) {
      createSingleWindow();
    }

    // Charger la page de test
    const testAuthPath = path.join(app.getAppPath(), 'src', 'renderer', 'login', 'test-auth.html');
    singleWindow.loadFile(testAuthPath);
  });

  // Raccourci pour ouvrir la page d'exemple d'authentification via API
  globalShortcut.register('CommandOrControl+Shift+A', () => {
    log.info('Raccourci clavier pour ouvrir la page d\'exemple d\'authentification via API');

    // Créer la fenêtre unique si elle n'existe pas
    if (!singleWindow) {
      createSingleWindow();
    }

    // Charger la page d'exemple
    const authExamplePath = path.join(app.getAppPath(), 'src', 'renderer', 'login', 'auth-example.html');
    singleWindow.loadFile(authExamplePath);
  });

  // Intercepter spécifiquement les requêtes pour le service worker Firebase
  session.defaultSession.webRequest.onBeforeRequest(
    { urls: ['http://*/firebase-messaging-sw.js', 'https://*/firebase-messaging-sw.js'] },
    (details, callback) => {
      log.info(`Service worker Firebase détecté: ${details.url}`);
      // Autoriser le service worker
      callback({ cancel: false });
    }
  );

  // Intercepter les requêtes FetchEvent du service worker
  session.defaultSession.webRequest.onBeforeRequest(
    { urls: ['https://www.gstatic.com/firebasejs/*/firebase-app.js', 'https://www.gstatic.com/firebasejs/*/firebase-messaging.js'] },
    (details, callback) => {
      log.info(`FetchEvent Firebase détecté: ${details.url}`);
      // Autoriser la requête
      callback({ cancel: false });
    }
  );

  // Intercepter TOUTES les requêtes vers les serveurs Odoo pour supprimer l'en-tête Content-Length
  session.defaultSession.webRequest.onHeadersReceived(
    { urls: ['*://*/*'] },  // Intercepter toutes les URLs
    (details, callback) => {
      // Vérifier si l'URL contient des éléments qui indiquent qu'il s'agit d'une ressource Odoo
      const isOdooResource = details.url.includes('/web/content/') ||
                           details.url.includes('/web/webclient/') ||
                           details.url.includes('/web/dataset/') ||
                           details.url.includes('/web/static/') ||
                           details.url.includes('/web?') ||
                           details.url.includes('/web/binary/') ||
                           details.url.includes('/web/image') ||
                           details.url.includes('/web/session/') ||
                           details.url.includes('/web_editor/') ||
                           details.url.includes('/web/assets/');

      if (isOdooResource) {
        log.info(`Ressource Odoo détectée: ${details.url}`);

        // Créer un nouvel objet d'en-têtes sans le Content-Length pour éviter les erreurs de mismatch
        const responseHeaders = { ...details.responseHeaders };

        // Supprimer l'en-tête Content-Length s'il existe
        if (responseHeaders['content-length'] || responseHeaders['Content-Length']) {
          log.info(`Suppression de l'en-tête Content-Length pour éviter les erreurs de mismatch`);
          delete responseHeaders['content-length'];
          delete responseHeaders['Content-Length'];
        }

        // Ajouter un en-tête Cache-Control pour éviter la mise en cache
        responseHeaders['Cache-Control'] = ['no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0'];
        responseHeaders['Pragma'] = ['no-cache'];
        responseHeaders['Expires'] = ['0'];

        // Ajouter des en-têtes CORS pour éviter les problèmes de CORS
        responseHeaders['Access-Control-Allow-Origin'] = ['*'];
        responseHeaders['Access-Control-Allow-Methods'] = ['GET, POST, OPTIONS, PUT, PATCH, DELETE'];
        responseHeaders['Access-Control-Allow-Headers'] = ['X-Requested-With, Content-Type, Accept'];

        // Retourner les en-têtes modifiées
        callback({ responseHeaders });
      } else {
        // Pour les autres requêtes, ne pas modifier les en-têtes
        callback({ responseHeaders: details.responseHeaders });
      }
    }
  );

  // Configurer un gestionnaire global pour les requêtes Firebase
  session.defaultSession.webRequest.onBeforeRequest(
    { urls: [
      'https://*.gstatic.com/firebasejs/*',
      'https://*.googleapis.com/firebase/*',
      'https://firebase.googleapis.com/*',
      'https://www.gstatic.com/firebasejs/*',
      'https://www.gstatic.com/firebasejs/8.4.2/*',
      'https://www.gstatic.com/firebasejs/8.4.3/*'
    ]},
    (details, callback) => {
      log.info(`Requête Firebase détectée: ${details.url}`);
      // Autoriser toutes les requêtes Firebase
      callback({ cancel: false });
    }
  );
});

/**
 * Crée la fenêtre de l'assistant d'installation
 */
function createInstallerWindow() {
  log.info('Création de la fenêtre de l\'assistant d\'installation');

  // Fermer la fenêtre de démarrage si elle existe
  if (splashWindow) {
    splashWindow.close();
    splashWindow = null;
  }

  // Créer la fenêtre de l'assistant d'installation
  installerWindow = new BrowserWindow({
    width: 1100,
    height: 600,
    resizable: true,
    backgroundColor: '#212226',
    frame: true, // Utiliser la barre native de macOS
    titleBarStyle: 'hiddenInset', // Barre de titre intégrée avec les contrôles visibles
    trafficLightPosition: { x: 10, y: 10 }, // Position des boutons de contrôle
    vibrancy: 'under-window', // Effet de vibrancy pour s'adapter au thème du système
    visualEffectState: 'active',
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      contextIsolation: false, // Désactiver l'isolation du contexte pour permettre l'accès aux APIs exposées
      nodeIntegration: false,
      session: session.defaultSession,
      webSecurity: false // Désactiver la sécurité web pour permettre les requêtes cross-origin
    }
  });

  // Activer les DevTools pour le débogage
  enableDevTools(installerWindow);

  // Déterminer le chemin correct des ressources
  let resourcePath;
  if (app.isPackaged) {
    // Dans une application packagée, les ressources sont dans le dossier Resources
    resourcePath = path.join(process.resourcesPath, 'installer', installerSteps[currentInstallerStep]);
    log.info(`Application packagée, chemin des ressources: ${process.resourcesPath}`);
  } else {
    // En développement, les ressources sont à la racine du projet
    resourcePath = path.join(app.getAppPath(), 'installer', installerSteps[currentInstallerStep]);
    log.info(`Application en développement, chemin des ressources: ${app.getAppPath()}`);
  }

  // Charger la première étape de l'assistant d'installation
  currentInstallerStep = 0;
  log.info(`Chargement de la première étape de l'assistant d'installation: ${resourcePath}`);

  // Vérifier si le fichier existe
  try {
    if (require('fs').existsSync(resourcePath)) {
      log.info(`Le fichier ${resourcePath} existe`);
    } else {
      log.error(`Le fichier ${resourcePath} n'existe pas`);

      // Essayer de trouver le fichier dans d'autres emplacements possibles
      const possiblePaths = [
        path.join(app.getAppPath(), 'installer', installerSteps[currentInstallerStep]),
        path.join(process.resourcesPath, 'installer', installerSteps[currentInstallerStep]),
        path.join(__dirname, '..', '..', 'installer', installerSteps[currentInstallerStep]),
        path.join(__dirname, '..', 'installer', installerSteps[currentInstallerStep]),
        path.join('installer', installerSteps[currentInstallerStep])
      ];

      log.info('Recherche du fichier dans d\'autres emplacements possibles:');
      for (const p of possiblePaths) {
        log.info(`- Vérification de ${p}`);
        if (require('fs').existsSync(p)) {
          log.info(`Fichier trouvé à ${p}`);
          resourcePath = p;
          break;
        }
      }
    }
  } catch (error) {
    log.error(`Erreur lors de la vérification de l'existence du fichier: ${error.message}`);
  }

  // Charger le fichier
  try {
    installerWindow.loadFile(resourcePath);
  } catch (error) {
    log.error(`Erreur lors du chargement du fichier: ${error.message}`);

    // En cas d'erreur, essayer de charger le fichier en tant qu'URL
    try {
      const fileUrl = require('url').format({
        pathname: resourcePath,
        protocol: 'file:',
        slashes: true
      });
      log.info(`Tentative de chargement en tant qu'URL: ${fileUrl}`);
      installerWindow.loadURL(fileUrl);
    } catch (urlError) {
      log.error(`Erreur lors du chargement de l'URL: ${urlError.message}`);
    }
  }

  // Ferme la fenêtre de l'assistant d'installation quand elle est fermée
  installerWindow.on('closed', () => {
    installerWindow = null;
  });
}

/**
 * Navigue vers l'étape suivante de l'assistant d'installation
 */
function goToNextInstallerStep() {
  if (!installerWindow || installerWindow.isDestroyed()) {
    log.error('Fenêtre de l\'assistant d\'installation non disponible');
    return;
  }

  // Passer à l'étape suivante
  currentInstallerStep++;

  // Vérifier si nous avons atteint la fin de l'assistant
  if (currentInstallerStep >= installerSteps.length) {
    log.info('Fin de l\'assistant d\'installation');
    return;
  }

  // Déterminer le chemin correct des ressources
  let resourcePath;
  if (app.isPackaged) {
    // Dans une application packagée, les ressources sont dans le dossier Resources
    resourcePath = path.join(process.resourcesPath, 'installer', installerSteps[currentInstallerStep]);
    log.info(`Application packagée, chemin des ressources: ${process.resourcesPath}`);
  } else {
    // En développement, les ressources sont à la racine du projet
    resourcePath = path.join(app.getAppPath(), 'installer', installerSteps[currentInstallerStep]);
    log.info(`Application en développement, chemin des ressources: ${app.getAppPath()}`);
  }

  log.info(`Chargement de l'étape ${currentInstallerStep} de l'assistant d'installation: ${resourcePath}`);

  // Vérifier si le fichier existe
  try {
    if (require('fs').existsSync(resourcePath)) {
      log.info(`Le fichier ${resourcePath} existe`);
    } else {
      log.error(`Le fichier ${resourcePath} n'existe pas`);

      // Essayer de trouver le fichier dans d'autres emplacements possibles
      const possiblePaths = [
        path.join(app.getAppPath(), 'installer', installerSteps[currentInstallerStep]),
        path.join(process.resourcesPath, 'installer', installerSteps[currentInstallerStep]),
        path.join(__dirname, '..', '..', 'installer', installerSteps[currentInstallerStep]),
        path.join(__dirname, '..', 'installer', installerSteps[currentInstallerStep]),
        path.join('installer', installerSteps[currentInstallerStep])
      ];

      log.info('Recherche du fichier dans d\'autres emplacements possibles:');
      for (const p of possiblePaths) {
        log.info(`- Vérification de ${p}`);
        if (require('fs').existsSync(p)) {
          log.info(`Fichier trouvé à ${p}`);
          resourcePath = p;
          break;
        }
      }
    }
  } catch (error) {
    log.error(`Erreur lors de la vérification de l'existence du fichier: ${error.message}`);
  }

  // Charger le fichier
  try {
    installerWindow.loadFile(resourcePath);
  } catch (error) {
    log.error(`Erreur lors du chargement du fichier: ${error.message}`);

    // En cas d'erreur, essayer de charger le fichier en tant qu'URL
    try {
      const fileUrl = require('url').format({
        pathname: resourcePath,
        protocol: 'file:',
        slashes: true
      });
      log.info(`Tentative de chargement en tant qu'URL: ${fileUrl}`);
      installerWindow.loadURL(fileUrl);
    } catch (urlError) {
      log.error(`Erreur lors du chargement de l'URL: ${urlError.message}`);
    }
  }
}

/**
 * Termine l'installation et lance l'application
 */
async function finishInstallation() {
  log.info('Fin de l\'installation');

  // Marquer l'application comme ayant déjà été lancée
  userPreferences.setNotFirstLaunch();

  // Créer un raccourci sur le bureau si demandé
  if (createShortcutOnFinish) {
    log.info('Création d\'un raccourci sur le bureau');
    try {
      const success = await shortcutManager.createDesktopShortcut();
      log.info(`Création du raccourci: ${success ? 'Réussie' : 'Échouée'}`);
    } catch (error) {
      log.error(`Erreur lors de la création du raccourci: ${error.message}`);
    }
  }

  // Fermer la fenêtre de l'assistant d'installation
  if (installerWindow && !installerWindow.isDestroyed()) {
    installerWindow.close();
    installerWindow = null;
  }

  // Afficher d'abord la fenêtre de démarrage (splash screen)
  log.info('Affichage de la fenêtre de démarrage après l\'installation');
  createSplashWindow();

  // Ne pas fermer automatiquement la fenêtre de démarrage
  // La fenêtre de démarrage restera ouverte jusqu'à ce que la connexion au serveur soit établie
  log.info('La fenêtre de démarrage restera ouverte jusqu\'à ce que la connexion au serveur soit établie');
}

// Définir le nom de l'application
app.setName('Edara ERP');

// Gestionnaire pour l'événement before-quit
app.on('before-quit', () => {
  log.info('Événement before-quit détecté, fermeture complète de l\'application');
  // Forcer la fermeture complète de l'application
  setTimeout(() => {
    process.exit(0);
  }, 100);
});

// Gestionnaire pour l'événement will-quit
app.on('will-quit', () => {
  log.info('Événement will-quit détecté, fermeture complète de l\'application');
  // Forcer la fermeture complète de l'application
  process.exit(0);
});

// Quand l'application est prête, crée la fenêtre de démarrage
app.whenReady().then(async () => {
  log.info('Application démarrée');
  log.info('Mode développement:', isDev ? 'Activé' : 'Désactivé');

  // Initialiser le gestionnaire de sauvegardes
  backupManager.initBackupManager();
  log.info('Gestionnaire de sauvegardes initialisé');

  // Vérifier si c'est le premier lancement de l'application
  const isFirstLaunch = userPreferences.isFirstLaunch();
  log.info(`Premier lancement de l'application: ${isFirstLaunch}`);
  log.info(`Chemin de l'application: ${app.getAppPath()}`);
  log.info(`Chemin des ressources: ${app.isPackaged ? process.resourcesPath : app.getAppPath()}`);
  log.info(`Application packagée: ${app.isPackaged}`);

  // Vérifier si le dossier installer existe
  const installerPath = path.join(app.isPackaged ? process.resourcesPath : app.getAppPath(), 'installer');
  const installerExists = require('fs').existsSync(installerPath);
  log.info(`Dossier installer: ${installerPath} (existe: ${installerExists})`);

  // Vérifier si le premier fichier d'installation existe
  const firstStepPath = path.join(installerPath, installerSteps[0]);
  const firstStepExists = require('fs').existsSync(firstStepPath);
  log.info(`Premier fichier d'installation: ${firstStepPath} (existe: ${firstStepExists})`);

  if (isFirstLaunch) {
    // Afficher l'assistant d'installation
    log.info('Affichage de l\'assistant d\'installation');
    createInstallerWindow();
  } else {
    // Vérifier s'il existe une session stockée localement
    log.info('Vérification de l\'existence d\'une session stockée localement');
    const sessionData = odooSessionManager.getStoredSession();

    if (sessionData && sessionData.session_id && sessionData.server_url) {
      log.info(`Session trouvée pour l'utilisateur ${sessionData.username} sur ${sessionData.server_url}`);

      try {
        // Créer la fenêtre unique
        log.info('Création de la fenêtre unique pour la validation de session');
        createSingleWindow();

        // Afficher la fenêtre de démarrage
        log.info('Affichage de la fenêtre de démarrage pour la validation de session');
        createSplashWindow();

        // Activer les DevTools sur la fenêtre de démarrage
        if (splashWindow) {
          enableDevTools(splashWindow);
        }

        // Valider la session auprès du serveur Odoo
        log.info(`Validation de la session auprès du serveur ${sessionData.server_url}`);
        const result = await odooSessionManager.validateSession(
          sessionData.session_id,
          sessionData.server_url
        );

        if (result.valid) {
          log.info(`Session valide pour l'utilisateur ${result.username}`);

          // Stocker l'URL du serveur pour une utilisation ultérieure
          lastServerUrl = sessionData.server_url;

          // Définir manuellement le cookie de session pour éviter les erreurs "Session Expired"
          try {
            // Utiliser la nouvelle fonction pour définir le cookie
            const cookieSet = await setOdooSessionCookieInternal(sessionData.session_id, sessionData.server_url);

            if (cookieSet) {
              log.info('✅ Cookie session Odoo défini avec succès via la nouvelle fonction pour le chargement initial');

              // Stocker l'ID de session dans la variable globale
              storedSessionId = sessionData.session_id;
            } else {
              log.error('❌ Échec de la définition du cookie via la nouvelle fonction, tentative avec l\'ancienne méthode');

              // Extraire le domaine du serveur
              const serverUrl = new URL(sessionData.server_url);
              const domain = serverUrl.hostname;

              // Définir le cookie avec tous les paramètres nécessaires
              const cookie = {
                url: sessionData.server_url,
                name: 'session_id',
                value: sessionData.session_id,
                path: '/',
                domain: domain,
                httpOnly: false,
                secure: sessionData.server_url.startsWith('https'),
                expirationDate: Math.floor(Date.now() / 1000) + 86400 // 24 heures
              };

              await session.defaultSession.cookies.set(cookie);
              log.info('✅ Cookie session Odoo défini avec succès via l\'ancienne méthode pour le chargement initial');

              // Stocker l'ID de session dans la variable globale
              storedSessionId = sessionData.session_id;
            }

            // Vérifier que le cookie a bien été défini
            const cookies = await session.defaultSession.cookies.get({ url: sessionData.server_url });
            const sessionCookie = cookies.find(c => c.name === 'session_id');

            if (sessionCookie) {
              log.info(`✅ Vérification réussie - Cookie session_id trouvé: ${sessionCookie.value.substring(0, 8)}...`);
            } else {
              log.warn('⚠️ Vérification échouée - Cookie session_id non trouvé après définition');
            }
          } catch (cookieError) {
            log.error(`❌ Erreur définition cookie session Odoo: ${cookieError.message}`);
            log.error(cookieError.stack);
          }

          // Construire l'URL de l'interface Odoo avec une URL simple
          const timestamp = Date.now(); // Ajouter un timestamp pour éviter le cache
          const odooWebUrl = `${sessionData.server_url}/web?nocache=${timestamp}`;
          log.info(`Chargement de l'interface Odoo dans la fenêtre unique: ${odooWebUrl}`);

          // Fermer la fenêtre de démarrage
          if (splashWindow && !splashWindow.isDestroyed()) {
            log.info('Fermeture de la fenêtre de démarrage');
            splashWindow.close();
            splashWindow = null;
          }

          // Mettre à jour l'état de l'application
          currentAppState = 'main';

          // Charger l'interface Odoo dans la fenêtre unique
          singleWindow.loadURL(odooWebUrl);
        } else {
          log.warn(`Session invalide: ${result.error}`);

          // Afficher la fenêtre de démarrage normale
          log.info('Affichage de la fenêtre de démarrage normale après échec de validation de session');

          // Ne pas fermer automatiquement la fenêtre de démarrage
          // La fenêtre de démarrage restera ouverte jusqu'à ce que la connexion au serveur soit établie
          log.info('La fenêtre de démarrage restera ouverte jusqu\'à ce que la connexion au serveur soit établie');
        }
      } catch (error) {
        log.error(`Erreur lors de la validation de la session: ${error.message}`);

        // Afficher la fenêtre de démarrage normale
        log.info('Affichage de la fenêtre de démarrage normale après erreur de validation de session');
        createSplashWindow();

        // Activer les DevTools sur la fenêtre de démarrage
        if (splashWindow) {
          enableDevTools(splashWindow);
        }

        // Ne pas fermer automatiquement la fenêtre de démarrage
        // La fenêtre de démarrage restera ouverte jusqu'à ce que la connexion au serveur soit établie
        log.info('La fenêtre de démarrage restera ouverte jusqu\'à ce que la connexion au serveur soit établie');
      }
    } else {
      // Aucune session stockée, afficher la fenêtre de démarrage normale
      log.info('Aucune session stockée, affichage de la fenêtre de démarrage normale');
      createSplashWindow();

      // Activer les DevTools sur la fenêtre de démarrage
      if (splashWindow) {
        enableDevTools(splashWindow);
      }

      // Ne pas fermer automatiquement la fenêtre de démarrage
      // La fenêtre de démarrage restera ouverte jusqu'à ce que la connexion au serveur soit établie
      log.info('La fenêtre de démarrage restera ouverte jusqu\'à ce que la connexion au serveur soit établie');
    }
  }

  app.on('activate', () => {
    // Sur macOS, il est courant de recréer une fenêtre dans l'application quand
    // l'icône du dock est cliquée et qu'il n'y a pas d'autres fenêtres ouvertes.
    if (BrowserWindow.getAllWindows().length === 0) {
      // Vérifier si c'est le premier lancement de l'application
      const isFirstLaunch = userPreferences.isFirstLaunch();

      if (isFirstLaunch) {
        // Afficher l'assistant d'installation
        createInstallerWindow();
      } else {
        // Afficher la fenêtre de démarrage qui vérifiera la connexion au serveur
        createSplashWindow();
      }
    }
  });
});

// Quitte l'application quand toutes les fenêtres sont fermées, sauf sur macOS
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    // Forcer la fermeture complète de l'application
    process.exit(0);
  }
});

// Gestionnaire pour supprimer les cookies (version avec réponse)
ipcMain.handle('remove-cookie', async (_, { url, name }) => {
  try {
    log.info(`Suppression du cookie ${name} pour ${url}`);
    await session.defaultSession.cookies.remove(url, name);
    return { success: true };
  } catch (error) {
    log.error('Erreur lors de la suppression du cookie:', error);
    return { success: false, error: error.message };
  }
});

// Gestionnaire pour supprimer les cookies (version sans réponse)
ipcMain.on('remove-cookie', (_, data) => {
  const { url, name } = data;
  log.info(`Suppression du cookie ${name} pour ${url} (via ipcMain.on)`);

  session.defaultSession.cookies.remove(url, name)
    .then(() => {
      log.info(`Cookie ${name} supprimé avec succès pour ${url}`);
    })
    .catch(error => {
      log.error(`Erreur lors de la suppression du cookie ${name} pour ${url}:`, error);
    });
});

// Importer le détecteur de serveur
const serverDetector = require('./server-detector');

// Importer le module de gestion de la dernière IP valide
const lastValidIp = require('./last-valid-ip');

// Gestionnaire pour récupérer la dernière adresse IP locale valide
ipcMain.handle('get-last-valid-ip', async (event) => {
  try {
    const ip = lastValidIp.getLastValidIp();
    log.info(`Dernière adresse IP valide récupérée: ${ip || 'aucune'}`);

    if (ip) {
      return {
        success: true,
        ip: ip,
        url: `http://${ip}:${serverDetector.ODOO_PORT}`
      };
    } else {
      return {
        success: false,
        message: 'Aucune adresse IP valide trouvée'
      };
    }
  } catch (error) {
    log.error(`Erreur lors de la récupération de la dernière adresse IP valide: ${error.message}`);
    return {
      success: false,
      error: error.message
    };
  }
});

// Gestionnaire pour vérifier la connexion au serveur
ipcMain.handle('check-server-connection', async (event, url) => {
  try {
    // Si une URL spécifique est fournie, l'utiliser
    if (url) {
      log.info(`URL spécifique fournie pour la vérification: ${url}`);

      // Tentative de connexion au serveur Odoo avec l'URL spécifiée
      const response = await axios.get(`${url}/web/webclient/version_info`, {
        timeout: 15000, // 15 secondes de timeout
        validateStatus: status => status >= 200 && status < 500
      });

      log.info(`Connexion au serveur réussie avec le code: ${response.status}`);

      // Déterminer si c'est une connexion locale ou distante
      const isLocal = url.includes('localhost') || url.match(/^http:\/\/\d+\.\d+\.\d+\.\d+/);
      const connectionType = isLocal ? 'local' : 'distance';

      return {
        success: true,
        status: response.status,
        serverUrl: url,
        isLocal: isLocal,
        connectionType: connectionType
      };
    }

    // Sinon, utiliser notre nouvelle fonction pour obtenir des informations complètes sur les serveurs disponibles
    log.info('Aucune URL spécifique fournie, détection automatique du serveur...');
    const serverInfo = await serverDetector.getServerInfo();

    // Sauvegarder l'URL du meilleur serveur détecté dans les préférences
    userPreferences.setServerUrl(serverInfo.bestServer.url);

    // Retourner les informations complètes sur les serveurs
    return {
      success: true,
      serverUrl: serverInfo.bestServer.url,
      isLocal: serverInfo.bestServer.isLocal,
      connectionType: serverInfo.bestServer.connectionType,
      localServer: serverInfo.local,
      remoteServer: serverInfo.remote
    };
  } catch (error) {
    // Journaliser l'erreur avec plus de détails
    log.error(`Échec de connexion au serveur ERP`);
    log.error(`Message d'erreur: ${error.message}`);
    if (error.code) {
      log.error(`Code d'erreur: ${error.code}`);
    }

    // En cas d'erreur, essayer de déterminer le meilleur serveur disponible
    try {
      const bestServer = await serverDetector.detectBestServer();

      // Sauvegarder l'URL du serveur détecté dans les préférences
      userPreferences.setServerUrl(bestServer.url);

      return {
        success: true,
        serverUrl: bestServer.url,
        isLocal: bestServer.isLocal,
        connectionType: bestServer.connectionType,
        warning: `Erreur lors de la vérification complète des serveurs: ${error.message}`
      };
    } catch (fallbackError) {
      // Si même la détection du meilleur serveur échoue, retourner une erreur
      log.error(`Échec de la détection du meilleur serveur: ${fallbackError.message}`);

      // Déterminer un message d'erreur plus convivial
      let userMessage = "Impossible de se connecter au serveur ERP.";
      if (error.code === 'ECONNREFUSED') {
        userMessage = "Le serveur ERP a refusé la connexion. Vérifiez qu'il est bien démarré.";
      } else if (error.code === 'ETIMEDOUT' || error.code === 'TIMEOUT') {
        userMessage = "Délai d'attente dépassé lors de la connexion au serveur ERP.";
      } else if (error.code === 'ENOTFOUND') {
        userMessage = "Serveur ERP introuvable. Vérifiez l'URL du serveur.";
      }

      return {
        success: false,
        error: error.message,
        userMessage: userMessage,
        code: error.code || 'UNKNOWN'
      };
    }
  }
});

// Gestionnaire pour signaler que l'écran de démarrage est prêt (conservé pour compatibilité)
ipcMain.on('splash-ready', (event) => {
  log.info('Écran de démarrage prêt (message obsolète, utiliser connection-successful)');
  // Ne rien faire ici, attendre le signal de connexion réussie
});

// Gestionnaire pour signaler que la connexion au serveur est réussie
ipcMain.on('connection-successful', async (_, data) => {
  log.info('Connexion au serveur réussie, transition vers l\'interface de connexion');

  // Récupérer l'URL du serveur depuis les données reçues ou détecter automatiquement
  let serverUrl = data && data.serverUrl ? data.serverUrl : null;

  // Si aucune URL n'est fournie, détecter automatiquement le meilleur serveur
  if (!serverUrl) {
    try {
      // Utiliser directement la fonction de détection du meilleur serveur
      serverUrl = await serverDetector.detectBestServer();
      log.info(`Meilleur serveur détecté: ${serverUrl}`);

      // Sauvegarder l'URL du serveur détecté dans les préférences
      userPreferences.setServerUrl(serverUrl);
    } catch (error) {
      log.error(`Erreur lors de la détection automatique du serveur: ${error.message}`);

      // En cas d'erreur, utiliser le serveur distant basé sur le nom de l'espace
      const workspaceName = userPreferences.getWorkspaceName();
      if (workspaceName && workspaceName !== 'edara') {
        serverUrl = `https://${workspaceName}.ligne-digitale.com`;
      } else {
        serverUrl = serverDetector.SERVER_URLS.REMOTE;
      }

      log.info(`Utilisation du serveur distant par défaut: ${serverUrl}`);
      userPreferences.setServerUrl(serverUrl);
    }
  } else {
    log.info(`Utilisation de l'URL du serveur fournie: ${serverUrl}`);
    // Sauvegarder l'URL du serveur fournie dans les préférences
    userPreferences.setServerUrl(serverUrl);
  }

  // Fermer immédiatement la fenêtre de démarrage si elle existe
  if (splashWindow && !splashWindow.isDestroyed()) {
    log.info('Fermeture immédiate de la fenêtre de démarrage');
    splashWindow.close();
    splashWindow = null;
  }

  // Attendre un délai d'une seconde avant de créer et d'afficher la fenêtre de connexion
  log.info('Attente d\'un délai d\'une seconde avant de créer la fenêtre de connexion');

  setTimeout(async () => {
    try {
      log.info('Création de la fenêtre de connexion après le délai');

      // Créer la fenêtre de connexion personnalisée
      // La fonction createLoginWindow va automatiquement gérer l'animation de fondu
      await createLoginWindow(serverUrl);

      log.info('Fenêtre de connexion créée et affichée avec animation');
    } catch (error) {
      log.error(`Erreur lors de la création de la fenêtre de connexion: ${error.message}`);
    }
  }, 1000); // Attendre 1 seconde avant de créer la fenêtre de connexion
});

// Gestionnaire pour charger la page de test d'authentification
ipcMain.on('load-auth-test', (event) => {
  log.info('Chargement de la page de test d\'authentification');

  // Créer la fenêtre unique si elle n'existe pas
  if (!singleWindow) {
    createSingleWindow();
  }

  // Charger la page de test
  const testAuthPath = path.join(app.getAppPath(), 'src', 'renderer', 'login', 'test-auth.html');
  singleWindow.loadFile(testAuthPath);
});

// Gestionnaire pour charger la page d'exemple d'authentification via API
ipcMain.on('load-auth-example', (event) => {
  log.info('Chargement de la page d\'exemple d\'authentification via API');

  // Créer la fenêtre unique si elle n'existe pas
  if (!singleWindow) {
    createSingleWindow();
  }

  // Charger la page d'exemple
  const authExamplePath = path.join(app.getAppPath(), 'src', 'renderer', 'login', 'auth-example.html');
  singleWindow.loadFile(authExamplePath);
});

// Gestionnaire pour les données d'authentification
ipcMain.on('auth-data', async (event, data) => {
  // Vérifier si les données nécessaires pour l'authentification sont présentes
  if (data.action === 'content-length-fix-initialized' || data.action === 'loading-screen-done' || data.action === 'splash-finished' || data.action === 'quit-application' || data.action === 'report-error' || data.action === 'report-promise-rejection') {
    log.info(`Données d'authentification reçues (action non-login): ${data.action}`);
    // Gérer les actions qui ne sont pas des tentatives de connexion
    if (data.action === 'loading-screen-done' || data.action === 'splash-finished') {
        // Fermer la fenêtre de splash si elle existe et que l'application n'est pas en mode splash
        if (splashWindow && !splashWindow.isDestroyed() && currentAppState !== 'splash') {
            log.info('Fermeture de la fenêtre de splash (action non-login)');
            splashWindow.close();
            splashWindow = null;
        }
    }
    return; // Ne pas procéder à l'authentification pour ces actions
  }

  // Si l'action est 'logout', gérer la déconnexion
  if (data.action === 'logout') {
    log.info('Déconnexion demandée via auth-data');
    await handleLogout(data.serverUrl || lastServerUrl);
    return;
  }

  // Si les données de connexion sont manquantes, ne pas tenter l'authentification
  if (!data.login || !data.password) {
    log.warn(`Tentative d'authentification annulée: données manquantes (login: ${data.login}, password: ${data.password ? 'présent' : 'absent'})`);
    // Optionnel: envoyer une erreur au renderer si nécessaire
    // if (loginWindow && !loginWindow.isDestroyed()) {
    //   loginWindow.webContents.send('auth-error', 'Nom d\'utilisateur ou mot de passe manquant.');
    // }
    return;
  }
  log.info(`Données d'authentification reçues pour l'utilisateur ${data.login}`);

  try {
    // Fermer la fenêtre de connexion
    if (loginWindow && !loginWindow.isDestroyed()) {
      log.info('Fermeture de la fenêtre de connexion');
      loginWindow.close();
      loginWindow = null;
    }

    // Utiliser l'URL du serveur fournie par le formulaire de connexion ou utiliser une valeur par défaut
    const serverUrl = data.serverUrl || lastServerUrl || userPreferences.getServerUrl() || 'http://localhost:8069';
    log.info(`URL du serveur Odoo: ${serverUrl}`);

    // Authentifier l'utilisateur avec Odoo
    log.info(`Authentification de l'utilisateur ${data.login} sur ${serverUrl}`);

    // Utiliser la fonction authenticateAndRedirectToOdoo qui définit correctement le cookie de session
    // et redirige directement vers /web sans passer par /web/login
    const authResult = await authenticateAndRedirectToOdoo(data.login, data.password, serverUrl, 'ligne-digitale');

    if (authResult.success) {
      log.info(`Authentification réussie pour l'utilisateur ${data.login}`);

      // Créer la fenêtre de splash screen
      log.info('Création de la fenêtre de splash screen');

      // Utiliser la fenêtre unique pour afficher le splash screen
      log.info('Utilisation de la fenêtre unique pour afficher le splash screen');

      // S'assurer que la fenêtre unique existe
      if (!singleWindow || singleWindow.isDestroyed()) {
        log.info('Création d\'une nouvelle fenêtre unique pour le splash screen');
        createSingleWindow();
      }

      // Mettre à jour l'état de l'application
      currentAppState = 'splash';

      // Charger le fichier HTML du splash screen dans la fenêtre unique
      log.info('Chargement du splash screen dans la fenêtre unique');
      singleWindow.loadFile(path.join(__dirname, '../renderer/splash-screen.html'));

      // Afficher la fenêtre si elle n'est pas déjà visible
      if (!singleWindow.isVisible()) {
        singleWindow.show();
      }

      // Récupérer l'ID de session pour l'utiliser ultérieurement
      const sessionId = authResult.sessionId; // Utiliser sessionId au lieu de session_id
      log.info(`ID de session obtenu: ${sessionId ? sessionId.substring(0, 8) + '...' : 'undefined'}`);

      // Stocker le sessionId dans la variable globale pour une utilisation ultérieure
      storedSessionId = sessionId;

      // Utiliser directement l'URL retournée par la fonction authenticateAndRedirectToOdoo
      // Cette URL contient déjà le paramètre #home qui redirige vers la page d'accueil configurée
      const odooWebUrl = authResult.odoo_url;
      log.info(`URL de l'interface Odoo: ${odooWebUrl}`);

      // Attendre que l'animation du splash screen soit terminée
      ipcMain.once('loading-screen-done', async () => {
        log.info('Animation du splash screen terminée, chargement de l\'interface Odoo dans la fenêtre unique');

        // Mettre à jour l'état de l'application
        currentAppState = 'main';

        // Ajouter un délai avant de charger l'URL pour s'assurer que les cookies sont correctement définis
        log.info('Attente de 500ms avant de charger l\'interface Odoo...');
        await new Promise(resolve => setTimeout(resolve, 500));

        // Charger l'interface Odoo dans la fenêtre unique
        if (singleWindow && !singleWindow.isDestroyed()) {
          log.info(`Chargement de l'interface Odoo dans la fenêtre unique: ${odooWebUrl}`);
          singleWindow.loadURL(odooWebUrl);
        } else {
          log.warn('Fenêtre unique non disponible pour le chargement de l\'interface Odoo');

          // Créer une nouvelle fenêtre unique si nécessaire
          log.info('Création d\'une nouvelle fenêtre unique pour l\'interface Odoo');
          createSingleWindow();
          singleWindow.loadURL(odooWebUrl);
        }
      });
    } else {
      log.error(`Échec de l'authentification pour l'utilisateur ${data.login}: ${authResult.error}`);

      // Recréer la fenêtre de connexion avec un message d'erreur
      createLoginWindow(serverUrl);

      // Envoyer un message d'erreur à la fenêtre de connexion
      if (loginWindow && !loginWindow.isDestroyed()) {
        loginWindow.webContents.once('did-finish-load', () => {
          loginWindow.webContents.send('auth-error', 'Adresse email ou mot de passe incorrect');
        });
      }
    }
  } catch (error) {
    log.error(`Erreur lors de l'authentification: ${error.message}`);

    // Recréer la fenêtre de connexion avec un message d'erreur
    createLoginWindow(lastServerUrl);

    // Envoyer un message d'erreur à la fenêtre de connexion
    if (loginWindow && !loginWindow.isDestroyed()) {
      loginWindow.webContents.once('did-finish-load', () => {
        loginWindow.webContents.send('auth-error', 'Erreur de connexion au serveur');
      });
    }
  }
});

// Gestionnaire pour quitter l'application depuis l'écran de démarrage
ipcMain.on('quit-application', () => {
  log.info('Demande de fermeture de l\'application depuis l\'écran de démarrage');
  app.quit();
});

// Gestionnaire pour l'authentification avec Odoo
ipcMain.handle('authenticate', async (event, { username, password, server, timeout, dbName }) => {
  log.info(`Demande d'authentification reçue pour l'utilisateur ${username} sur ${server}`);
  log.info(`Paramètres d'authentification: base de données=${dbName || 'ligne-digitale'}, timeout=${timeout || 30000}ms`);
  try {
    const result = await authenticateWithOdoo(username, password, server, timeout, dbName);
    log.info(`Résultat de l'authentification:`, result.success ? 'Succès' : `Échec: ${result.errorType}`);

    // Si l'authentification est réussie, établir la session web Odoo
    if (result.success) {
      log.info('Authentification réussie, établissement de la session web Odoo');

      // Construire l'URL de l'interface Odoo standard
      const odooWebUrl = `${server}/web`;
      log.info(`URL de l'interface Odoo: ${odooWebUrl}`);

      // Ajouter l'URL à l'objet result
      result.odoo_url = odooWebUrl;

      try {
        // Établir la session web Odoo en effectuant une requête POST à /web/login
        log.info(`Établissement de la session web Odoo pour l'utilisateur ${username}...`);
        const sessionResult = await establishOdooWebSession(server, dbName || 'ligne-digitale', username, password);

        if (sessionResult.success) {
          log.info(`Session web Odoo établie avec succès. ID de session: ${sessionResult.sessionId.substring(0, 8)}...`);

          // Ajouter l'ID de session à l'objet result
          result.session_id = sessionResult.sessionId;

          // Stocker l'URL du serveur dans l'objet result et dans la variable globale pour l'utiliser plus tard
          result.server = server;
          lastServerUrl = server;

          // Ne pas démarrer la séquence de chargement post-connexion
          // L'animation de chargement sera gérée dans la fenêtre de connexion
        } else {
          log.warn(`Échec de l'établissement de la session web Odoo: ${sessionResult.error}`);
          log.warn(`Tentative de chargement direct de l'URL Odoo sans session pré-établie.`);

          // Stocker l'URL du serveur dans l'objet result et dans la variable globale pour l'utiliser plus tard
          result.server = server;
          lastServerUrl = server;
        }
      } catch (sessionError) {
        log.error(`Erreur lors de l'établissement de la session web Odoo:`, sessionError);
        log.warn(`Tentative de chargement direct de l'URL Odoo sans session pré-établie.`);

        // Stocker l'URL du serveur dans l'objet result et dans la variable globale pour l'utiliser plus tard
        result.server = server;
        lastServerUrl = server;
      }
    }

    return result;
  } catch (error) {
    log.error('Erreur lors de l\'authentification dans le main process:', error);
    return {
      success: false,
      error: error.message || 'Erreur interne du serveur',
      errorType: 'MAIN_PROCESS_ERROR'
    };
  }
});

// Gestionnaire pour la vérification d'un token
ipcMain.handle('verify-token', async (event, { token, server, dbName }) => {
  log.info(`Demande de vérification de token reçue pour l'UID ${token} sur ${server}`);
  log.info(`Base de données utilisée pour la vérification: ${dbName || 'ligne-digitale'}`);
  try {
    const result = await verifyOdooToken(token, server, dbName);
    log.info(`Résultat de la vérification du token:`, result.valid ? 'Valide' : `Invalide: ${result.errorType}`);
    return result;
  } catch (error) {
    log.error('Erreur lors de la vérification du token dans le main process:', error);
    return {
      valid: false,
      error: error.message || 'Erreur interne du serveur',
      errorType: 'MAIN_PROCESS_ERROR'
    };
  }
});

// Gestionnaire pour la déconnexion
ipcMain.handle('logout', async (event, { token, server }) => {
  log.info(`Demande de déconnexion reçue pour l'UID ${token} sur ${server}`);
  try {
    const result = await logoutFromOdoo(token, server);
    log.info(`Résultat de la déconnexion:`, result.success ? 'Succès' : `Échec: ${result.errorType}`);

    // Utiliser la fonction handleOdooLogout pour gérer la déconnexion côté client
    if (result.success) {
      await handleOdooLogout(server);
    }

    return result;
  } catch (error) {
    log.error('Erreur lors de la déconnexion dans le main process:', error);
    return {
      success: false,
      error: error.message || 'Erreur interne du serveur',
      errorType: 'MAIN_PROCESS_ERROR'
    };
  }
});

/**
 * Établit une session web Odoo en effectuant une requête POST à /web/login
 * Cette fonction est nécessaire car l'API XML-RPC ne définit pas correctement le cookie de session
 * @param {string} odooUrl - L'URL de base du serveur Odoo (ex: http://localhost:8069)
 * @param {string} dbName - Le nom de la base de données Odoo
 * @param {string} username - Le nom d'utilisateur
 * @param {string} password - Le mot de passe
 * @returns {Promise<Object>} - Un objet indiquant si l'établissement de la session a réussi
 */
async function establishOdooWebSession(odooUrl, dbName, username, password) {
  log.info(`Tentative d'établissement d'une session web Odoo pour l'utilisateur ${username} sur ${odooUrl}`);

  try {
    // Construire l'URL de connexion
    const loginUrl = `${odooUrl}/web/login`;
    log.info(`URL de connexion: ${loginUrl}`);

    // Étape 1: Récupérer la page de connexion pour obtenir le token CSRF
    log.info(`Récupération de la page de connexion pour extraire le token CSRF...`);
    const loginPageResponse = await axios.get(loginUrl);

    // Extraire le token CSRF de la page de connexion
    const csrfToken = extractCsrfToken(loginPageResponse.data);
    if (!csrfToken) {
      log.warn(`Aucun token CSRF trouvé dans la page de connexion. Tentative sans token...`);
    } else {
      log.info(`Token CSRF extrait: ${csrfToken.substring(0, 8)}...`);
    }

    // Extraire les cookies de session de la réponse
    const initialCookies = loginPageResponse.headers['set-cookie'];
    if (initialCookies) {
      log.info(`Cookies reçus lors de la récupération de la page de connexion: ${initialCookies.length} cookie(s)`);
    }

    // Préparer les données du formulaire de connexion
    const postData = new URLSearchParams();
    postData.append('db', dbName);
    postData.append('login', username);
    postData.append('password', password);

    // Ajouter le token CSRF si disponible
    if (csrfToken) {
      postData.append('csrf_token', csrfToken);
    }

    log.info(`Préparation des données POST: db=${dbName}, login=${username}, password=********, csrf_token=${csrfToken ? csrfToken.substring(0, 8) + '...' : 'non disponible'}`);

    // Préparer les en-têtes pour la requête POST
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded'
    };

    // Ajouter les cookies de session si disponibles
    if (initialCookies && initialCookies.length > 0) {
      // Extraire les cookies de session et les ajouter à l'en-tête Cookie
      const sessionCookies = initialCookies.map(cookie => cookie.split(';')[0]).join('; ');
      headers['Cookie'] = sessionCookies;
      log.info(`Cookies de session ajoutés à la requête: ${sessionCookies.replace(/=([^;]+)/g, '=****')}`);
    }

    // Effectuer la requête POST
    log.info(`Envoi de la requête POST à ${loginUrl}...`);
    const response = await axios.post(loginUrl, postData.toString(), {
      headers,
      maxRedirects: 0, // Empêcher axios de suivre les redirections
      validateStatus: status => status >= 200 && status < 400, // Accepter les codes 2xx et 3xx
      withCredentials: true // Envoyer les cookies avec la requête
    });

    log.info(`Réponse reçue avec le code: ${response.status}`);

    // Vérifier si la réponse contient des en-têtes Set-Cookie
    const setCookieHeaders = response.headers['set-cookie'];
    if (!setCookieHeaders || setCookieHeaders.length === 0) {
      log.warn(`Aucun en-tête Set-Cookie trouvé dans la réponse!`);
      return { success: false, error: 'Aucun cookie de session trouvé dans la réponse' };
    }

    log.info(`En-têtes Set-Cookie trouvés: ${setCookieHeaders.length} cookie(s)`);

    // Rechercher le cookie session_id
    let sessionIdCookie = null;
    for (const cookieStr of setCookieHeaders) {
      log.info(`Analyse du cookie: ${cookieStr.replace(/=([^;]+)/, '=****')}`);

      if (cookieStr.startsWith('session_id=')) {
        sessionIdCookie = cookieStr;
        log.info(`Cookie session_id trouvé!`);
        break;
      }
    }

    if (!sessionIdCookie) {
      log.warn(`Aucun cookie 'session_id' trouvé dans les en-têtes Set-Cookie!`);
      return { success: false, error: 'Cookie session_id non trouvé' };
    }

    // Extraire les informations du cookie
    const cookieInfo = parseCookie(sessionIdCookie);
    log.info(`Informations du cookie session_id extraites:`, {
      name: cookieInfo.name,
      value: cookieInfo.value ? cookieInfo.value.substring(0, 8) + '...' : 'undefined',
      domain: cookieInfo.domain,
      path: cookieInfo.path,
      secure: cookieInfo.secure,
      httpOnly: cookieInfo.httpOnly,
      expires: cookieInfo.expires
    });

    // Extraire le domaine de base de l'URL Odoo
    const odooUrlObj = new URL(odooUrl);
    const odooDomain = `${odooUrlObj.protocol}//${odooUrlObj.hostname}${odooUrlObj.port ? `:${odooUrlObj.port}` : ''}`;

    // Préparer les options pour définir le cookie dans Electron
    const cookieOptions = {
      url: odooDomain,
      name: 'session_id',
      value: cookieInfo.value,
      domain: cookieInfo.domain || odooUrlObj.hostname,
      path: cookieInfo.path || '/',
      secure: cookieInfo.secure || odooUrlObj.protocol === 'https:',
      httpOnly: cookieInfo.httpOnly || true
    };

    // Ajouter une date d'expiration si disponible
    if (cookieInfo.expires) {
      try {
        const expirationDate = new Date(cookieInfo.expires);
        if (!isNaN(expirationDate.getTime())) {
          cookieOptions.expirationDate = Math.floor(expirationDate.getTime() / 1000);
          log.info(`Date d'expiration du cookie: ${new Date(cookieOptions.expirationDate * 1000).toISOString()}`);
        } else {
          // Utiliser une date d'expiration par défaut (24 heures)
          cookieOptions.expirationDate = Math.floor(Date.now() / 1000) + 86400;
          log.info(`Format de date d'expiration invalide, utilisation d'une valeur par défaut: ${new Date(cookieOptions.expirationDate * 1000).toISOString()}`);
        }
      } catch (dateError) {
        // Utiliser une date d'expiration par défaut (24 heures)
        cookieOptions.expirationDate = Math.floor(Date.now() / 1000) + 86400;
        log.info(`Erreur lors du traitement de la date d'expiration, utilisation d'une valeur par défaut: ${new Date(cookieOptions.expirationDate * 1000).toISOString()}`);
      }
    } else {
      // Utiliser une date d'expiration par défaut (24 heures)
      cookieOptions.expirationDate = Math.floor(Date.now() / 1000) + 86400;
      log.info(`Aucune date d'expiration fournie, utilisation d'une valeur par défaut: ${new Date(cookieOptions.expirationDate * 1000).toISOString()}`);
    }

    // Supprimer d'abord les cookies session_id existants
    log.info(`Recherche des cookies de session existants pour ${odooDomain}...`);
    const existingCookies = await session.defaultSession.cookies.get({
      url: odooDomain,
      name: 'session_id'
    });

    if (existingCookies.length > 0) {
      log.info(`Suppression de ${existingCookies.length} cookie(s) de session existant(s) avant stockage`);

      // Supprimer chaque cookie un par un
      for (const cookie of existingCookies) {
        await session.defaultSession.cookies.remove(odooDomain, 'session_id');
        log.info(`Cookie de session existant supprimé avec succès: ${cookie.value.substring(0, 8)}...`);
      }
    } else {
      log.info(`Aucun cookie de session existant trouvé pour ${odooDomain}`);
    }

    // Approche simplifiée pour définir le cookie de session
    log.info(`Définition du nouveau cookie de session...`);

    // Stocker le cookie de session dans la variable globale pour une utilisation ultérieure
    storedSessionId = cookieInfo.value;

    // Supprimer tous les cookies existants
    log.info(`Suppression de tous les cookies existants...`);
    await session.defaultSession.clearStorageData({
      storages: ['cookies']
    });
    log.info('Tous les cookies supprimés avec succès');

    // Définir le cookie avec des options permissives
    const simpleCookieOptions = {
      url: odooDomain,
      name: 'session_id',
      value: cookieInfo.value,
      domain: cookieOptions.domain,
      path: '/',
      secure: false,
      httpOnly: false,
      expirationDate: Math.floor(Date.now() / 1000) + 86400 // 24 heures
    };

    log.info(`Définition du cookie de session avec les options:`, {
      url: odooDomain,
      name: 'session_id',
      value: cookieInfo.value.substring(0, 8) + '...',
      domain: cookieOptions.domain,
      path: '/',
      secure: false,
      httpOnly: false
    });

    await session.defaultSession.cookies.set(simpleCookieOptions);
    log.info(`Cookie de session stocké avec succès pour ${odooDomain}`);

    // Retourner directement la valeur du cookie sans vérification
    return {
      success: true,
      message: 'Session web Odoo établie avec succès',
      sessionId: cookieInfo.value
    };

  } catch (error) {
    // Gérer les erreurs spécifiques
    if (error.response) {
      // La requête a été effectuée et le serveur a répondu avec un code d'état hors de la plage 2xx
      log.error(`Erreur de réponse HTTP: ${error.response.status}`, error.response.data);
      return {
        success: false,
        error: `Erreur de réponse HTTP: ${error.response.status}`,
        details: error.response.data
      };
    } else if (error.request) {
      // La requête a été effectuée mais aucune réponse n'a été reçue
      log.error(`Aucune réponse reçue du serveur:`, error.request);
      return {
        success: false,
        error: 'Aucune réponse reçue du serveur',
        details: error.message
      };
    } else {
      // Une erreur s'est produite lors de la configuration de la requête
      log.error(`Erreur lors de la configuration de la requête:`, error.message);
      return {
        success: false,
        error: 'Erreur lors de la configuration de la requête',
        details: error.message
      };
    }
  }
}

/**
 * Extrait le token CSRF d'une page HTML Odoo
 * @param {string} html - Le contenu HTML de la page
 * @returns {string|null} - Le token CSRF ou null s'il n'est pas trouvé
 */
function extractCsrfToken(html) {
  // Rechercher le token CSRF dans le HTML
  // Méthode 1: Rechercher l'input caché avec le nom csrf_token
  const inputMatch = html.match(/<input[^>]*name=["']csrf_token["'][^>]*value=["']([^"']*)["'][^>]*>/i);
  if (inputMatch && inputMatch[1]) {
    return inputMatch[1];
  }

  // Méthode 2: Rechercher la variable JavaScript csrf_token
  const scriptMatch = html.match(/var csrf_token\s*=\s*["']([^"']*)["']/i);
  if (scriptMatch && scriptMatch[1]) {
    return scriptMatch[1];
  }

  // Méthode 3: Rechercher la méta tag avec le nom csrf-token
  const metaMatch = html.match(/<meta[^>]*name=["']csrf-token["'][^>]*content=["']([^"']*)["'][^>]*>/i);
  if (metaMatch && metaMatch[1]) {
    return metaMatch[1];
  }

  return null;
}

/**
 * Analyse une chaîne de cookie et extrait ses composants
 * @param {string} cookieStr - La chaîne de cookie à analyser
 * @returns {Object} - Un objet contenant les composants du cookie
 */
function parseCookie(cookieStr) {
  const result = {
    name: '',
    value: '',
    path: '/',
    domain: null,
    expires: null,
    secure: false,
    httpOnly: false,
    sameSite: null
  };

  // Diviser la chaîne de cookie en parties
  const parts = cookieStr.split(';').map(part => part.trim());

  // La première partie contient le nom et la valeur
  const nameValue = parts[0].split('=');
  result.name = nameValue[0];
  result.value = nameValue[1];

  // Analyser les attributs restants
  for (let i = 1; i < parts.length; i++) {
    const part = parts[i];

    if (part.toLowerCase() === 'httponly') {
      result.httpOnly = true;
    } else if (part.toLowerCase() === 'secure') {
      result.secure = true;
    } else {
      const [name, value] = part.split('=').map(s => s.trim());
      const lowerName = name.toLowerCase();

      if (lowerName === 'path') {
        result.path = value;
      } else if (lowerName === 'domain') {
        result.domain = value;
      } else if (lowerName === 'expires') {
        result.expires = value;
      } else if (lowerName === 'samesite') {
        result.sameSite = value;
      }
    }
  }

  return result;
}

/**
 * Affiche l'écran de chargement post-connexion avec animation dans la fenêtre unique
 */
function showLoadingWindow() {
  log.info('Affichage de l\'écran de chargement post-connexion avec animation dans la fenêtre unique');

  // S'assurer que la fenêtre unique existe
  if (!singleWindow || singleWindow.isDestroyed()) {
    log.info('La fenêtre unique n\'existe pas, création d\'une nouvelle fenêtre');
    createSingleWindow();
  }

  // Mettre à jour l'état de l'application
  currentAppState = 'splash';

  // Charger la page d'animation de chargement dans la fenêtre unique
  log.info('Chargement de la page d\'animation de chargement dans la fenêtre unique');
  singleWindow.loadFile(path.join(__dirname, '../renderer/loading-animation.html'));

  // Afficher la fenêtre si elle n'est pas déjà visible
  if (!singleWindow.isVisible()) {
    singleWindow.show();
  }

  return singleWindow;
}

/**
 * Crée la fenêtre de démarrage (splash screen)
 * @returns {BrowserWindow} - La fenêtre de démarrage
 */
function createSplashWindow() {
  log.info('Création de la fenêtre de démarrage');

  // Si la fenêtre de démarrage existe déjà, la retourner
  if (splashWindow && !splashWindow.isDestroyed()) {
    log.info('La fenêtre de démarrage existe déjà, retour de la fenêtre existante');
    return splashWindow;
  }

  // Créer une nouvelle fenêtre spécifique pour le splash screen avec des dimensions précises
  log.info('Création d\'une nouvelle fenêtre pour le splash screen avec dimensions 800x600');
  splashWindow = new BrowserWindow({
    width: 900,
    height: 300,
    show: false,
    backgroundColor: '#212226', // Couleur de fond sombre
    frame: true, // Utiliser la barre native de macOS
    titleBarStyle: 'default', // Utiliser la barre de titre standard de macOS
    webPreferences: {
      preload: path.join(__dirname, '../renderer/preload.js'),
      // Configuration sécurisée avec contextIsolation activée
      contextIsolation: true,
      nodeIntegration: false,
      offscreen: false,
      session: session.defaultSession,
      webSecurity: false
    }
  });

  // Mettre à jour l'état de l'application
  currentAppState = 'splash';

  // Charger le fichier HTML du splash screen
  log.info('Chargement du splash screen');
  splashWindow.loadFile(path.join(__dirname, '../renderer/splash.html'));

  // Afficher la fenêtre quand elle est prête
  splashWindow.once('ready-to-show', () => {
    log.info('Fenêtre de démarrage prête à être affichée');
    splashWindow.show();
  });

  // Ferme la fenêtre de démarrage quand elle est fermée
  splashWindow.on('closed', () => {
    splashWindow = null;
  });

  return splashWindow;
}

/**
 * Gère la séquence de chargement post-connexion
 * @param {Object} authData - Les données d'authentification
 */
async function startPostLoginLoading(authData) {
  log.info('Démarrage de la séquence de chargement post-connexion');

  // Afficher l'écran de chargement dans la fenêtre unique
  showLoadingWindow();

  // Envoyer un message initial de progression à la fenêtre unique
  setTimeout(() => {
    if (singleWindow && !singleWindow.isDestroyed() && singleWindow.webContents && !singleWindow.webContents.isDestroyed()) {
      log.info('Envoi du message de progression initial (20%)');
      singleWindow.webContents.send('load-odoo-interface', {
        action: 'loading-progress',
        percent: 20,
        message: 'Préparation de l\'interface Odoo...'
      });
    } else {
      log.warn('Impossible d\'envoyer le message de progression initial - fenêtre unique non disponible');
    }
  }, 2000); // Attendre que l'animation initiale soit terminée

  // Préparer l'URL de l'interface Odoo
  const odooWebUrl = authData.odoo_url;
  log.info(`URL de l'interface Odoo à charger: ${odooWebUrl}`);

  // Envoyer un message de progression après un court délai
  setTimeout(() => {
    if (singleWindow && !singleWindow.isDestroyed() && singleWindow.webContents && !singleWindow.webContents.isDestroyed()) {
      log.info('Envoi du message de progression (40%)');
      singleWindow.webContents.send('load-odoo-interface', {
        action: 'loading-progress',
        percent: 40,
        message: 'Chargement de l\'interface Odoo...'
      });
    } else {
      log.warn('Impossible d\'envoyer le message de progression (40%) - fenêtre unique non disponible');
    }
  }, 3000);

  // Attendre un délai supplémentaire pour simuler le chargement
  setTimeout(() => {
    if (singleWindow && !singleWindow.isDestroyed() && singleWindow.webContents && !singleWindow.webContents.isDestroyed()) {
      log.info('Envoi du message de progression (60%)');
      singleWindow.webContents.send('load-odoo-interface', {
        action: 'loading-progress',
        percent: 60,
        message: 'Chargement des données Odoo...'
      });
    } else {
      log.warn('Impossible d\'envoyer le message de progression (60%) - fenêtre unique non disponible');
    }
  }, 4000);

  // Attendre un délai supplémentaire pour simuler le chargement
  setTimeout(() => {
    if (singleWindow && !singleWindow.isDestroyed() && singleWindow.webContents && !singleWindow.webContents.isDestroyed()) {
      log.info('Envoi du message de progression (80%)');
      singleWindow.webContents.send('load-odoo-interface', {
        action: 'loading-progress',
        percent: 80,
        message: 'Finalisation...'
      });
    } else {
      log.warn('Impossible d\'envoyer le message de progression (80%) - fenêtre unique non disponible');
    }
  }, 5000);

  // Attendre un délai final avant de charger l'interface Odoo
  setTimeout(() => {
    if (singleWindow && !singleWindow.isDestroyed() && singleWindow.webContents && !singleWindow.webContents.isDestroyed()) {
      log.info('Envoi du message de chargement terminé');
      singleWindow.webContents.send('load-odoo-interface', {
        action: 'loading-complete'
      });

      // Attendre que l'animation de chargement soit terminée avant de charger l'interface Odoo
      // Le gestionnaire 'loading-screen-done' sera appelé par le renderer quand l'animation sera terminée
      // Ce gestionnaire chargera alors l'interface Odoo dans la fenêtre unique
    } else {
      log.warn('Impossible d\'envoyer le message de chargement terminé - fenêtre unique non disponible');

      // Si la fenêtre unique n'est plus disponible, la recréer et charger directement l'interface Odoo
      log.info('Recréation de la fenêtre unique et chargement direct de l\'interface Odoo');
      createSingleWindow();

      // Mettre à jour l'état de l'application
      currentAppState = 'main';

      // Charger l'interface Odoo dans la fenêtre unique
      singleWindow.loadURL(odooWebUrl);
    }
  }, 6000); // Attendre 6 secondes pour simuler le chargement complet
}

/**
 * Définit le cookie de session Odoo avec les paramètres appropriés (implémentation interne)
 * @param {string} sessionId - L'ID de session à définir
 * @param {string} serverUrl - L'URL du serveur Odoo
 * @param {boolean} clearExisting - Si true, supprime les cookies existants avant d'en définir un nouveau
 * @returns {Promise<boolean>} - true si le cookie a été défini avec succès, false sinon
 */
async function setOdooSessionCookieInternal(sessionId, serverUrl, clearExisting = true) {
  log.info(`COOKIE: Définition du cookie session_id: ${sessionId ? sessionId.substring(0, 8) + '...' : 'undefined'}`);

  if (!sessionId) {
    log.error('COOKIE: Impossible de définir le cookie session_id: sessionId non fourni');
    return false;
  }

  if (!serverUrl) {
    log.error('COOKIE: Impossible de définir le cookie session_id: serverUrl non fourni');
    return false;
  }

  try {
    // Extraire le domaine du serveur
    const urlObj = new URL(serverUrl);
    const domain = urlObj.hostname;
    const isSecure = urlObj.protocol === 'https:';

    log.info(`COOKIE: Paramètres du serveur - domaine: ${domain}, protocole: ${urlObj.protocol}`);

    // Supprimer d'abord tous les cookies existants pour éviter les conflits
    if (clearExisting) {
      log.info('COOKIE: Suppression de tous les cookies existants...');
      await session.defaultSession.clearStorageData({ storages: ['cookies'] });
    }

    // Définir le cookie avec tous les paramètres nécessaires, y compris sameSite: 'no_restriction'
    const cookie = {
      url: serverUrl,
      name: 'session_id',
      value: sessionId,
      domain: domain,
      path: '/',
      httpOnly: false,
      secure: false, // Toujours false comme demandé, même pour HTTPS
      sameSite: 'no_restriction',
      expirationDate: Math.floor(Date.now() / 1000) + 86400 // 24 heures
    };

    log.info('COOKIE: Définition du cookie avec les paramètres:', {
      url: serverUrl,
      name: 'session_id',
      value: sessionId.substring(0, 8) + '...',
      domain: domain,
      path: '/',
      httpOnly: false,
      secure: false,
      sameSite: 'no_restriction',
      expirationDate: new Date((Math.floor(Date.now() / 1000) + 86400) * 1000).toISOString()
    });

    // Définir le cookie
    await session.defaultSession.cookies.set(cookie);
    log.info('COOKIE: Cookie session_id défini avec succès');

    // Stocker l'ID de session dans la variable globale
    storedSessionId = sessionId;

    // Vérifier que le cookie a bien été défini
    const cookies = await session.defaultSession.cookies.get({ url: serverUrl });
    const sessionCookie = cookies.find(c => c.name === 'session_id');

    if (sessionCookie) {
      log.info(`COOKIE: Vérification réussie - Cookie session_id trouvé: ${sessionCookie.value.substring(0, 8)}...`);
      log.info(`COOKIE: Paramètres du cookie défini:`, {
        name: sessionCookie.name,
        value: sessionCookie.value.substring(0, 8) + '...',
        domain: sessionCookie.domain,
        path: sessionCookie.path,
        secure: sessionCookie.secure,
        httpOnly: sessionCookie.httpOnly,
        sameSite: sessionCookie.sameSite
      });
      return true;
    } else {
      log.error('COOKIE: Vérification échouée - Cookie session_id non trouvé après définition');
      return false;
    }
  } catch (error) {
    log.error(`COOKIE: Erreur lors de la définition du cookie session_id: ${error.message}`);
    log.error(error.stack);
    return false;
  }
}

/**
 * Gère la déconnexion d'Odoo en supprimant les cookies de session et en rechargeant la page de connexion dans la même fenêtre
 * @param {string} serverUrl - L'URL du serveur Odoo
 */
async function handleOdooLogout(serverUrl) {
  log.info('LOGOUT: Exécution de la fonction handleOdooLogout avec URL:', serverUrl);

  // Utiliser lastServerUrl si serverUrl n'est pas fourni
  if (!serverUrl && lastServerUrl) {
    log.info(`LOGOUT: URL du serveur non fournie, utilisation de lastServerUrl: ${lastServerUrl}`);
    serverUrl = lastServerUrl;
  }

  // Ne pas stocker l'URL du serveur de déconnexion directement
  // L'URL de base sera extraite et stockée plus tard

  // Approche simplifiée pour supprimer tous les cookies
  log.info(`LOGOUT: Suppression de tous les cookies de session`);

  try {
    // Supprimer tous les cookies
    await session.defaultSession.clearStorageData({
      storages: ['cookies']
    });
    log.info('LOGOUT: Tous les cookies supprimés avec succès');

    // Réinitialiser la variable globale storedSessionId
    storedSessionId = null;
    log.info(`LOGOUT: Variable storedSessionId réinitialisée`);

    // Vider également le cache pour éviter les problèmes de session
    await session.defaultSession.clearCache();
    log.info('LOGOUT: Cache vidé avec succès');

    // Vider également le stockage local et les données de session
    await session.defaultSession.clearStorageData({
      storages: ['localstorage', 'sessionstorage']
    });
    log.info('LOGOUT: Stockage local et données de session vidés avec succès');
  } catch (error) {
    log.error(`LOGOUT: Erreur lors de la suppression des données de session:`, error);
  }

  // Extraire le domaine de base de l'URL du serveur
  let baseUrl = 'http://localhost:8069';
  if (serverUrl) {
    try {
      const urlObj = new URL(serverUrl);
      baseUrl = `${urlObj.protocol}//${urlObj.hostname}${urlObj.port ? `:${urlObj.port}` : ''}`;
      log.info(`LOGOUT: URL de base extraite: ${baseUrl}`);

      // Mettre à jour lastServerUrl avec l'URL de base
      lastServerUrl = baseUrl;
      log.info(`LOGOUT: URL de base stockée dans lastServerUrl: ${lastServerUrl}`);
    } catch (error) {
      log.error(`LOGOUT: Erreur lors de l'extraction de l'URL de base: ${error.message}`);
      // Utiliser l'URL par défaut ou lastServerUrl si disponible
      baseUrl = lastServerUrl || 'http://localhost:8069';
    }
  }

  // Charger l'écran de connexion dans la fenêtre unique
  log.info('LOGOUT: Chargement de l\'écran de connexion dans la fenêtre unique');

  // Ajouter un gestionnaire d'erreurs spécifique pour la déconnexion
  singleWindow.webContents.on('render-process-gone', (event, details) => {
    log.error('=== ERREUR LORS DE LA DÉCONNEXION (render-process-gone) ===');
    log.error('Raison:', details.reason);
    log.error('Code de sortie:', details.exitCode);
  });

  singleWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    log.error('=== ERREUR LORS DE LA DÉCONNEXION (did-fail-load) ===');
    log.error('Code d\'erreur:', errorCode);
    log.error('Description:', errorDescription);
  });

  try {
    // S'assurer que la fenêtre unique existe
    if (!singleWindow || singleWindow.isDestroyed()) {
      log.info('LOGOUT: La fenêtre unique n\'existe pas, création d\'une nouvelle fenêtre');
      createSingleWindow();
    }

    // Mettre à jour l'état de l'application
    currentAppState = 'login';

    // Charger le fichier HTML de l'interface de connexion personnalisée
    log.info('LOGOUT: Chargement de l\'interface de connexion personnalisée dans la fenêtre unique');
    singleWindow.loadFile(path.join(__dirname, '../renderer/login.html'));

    // Attendre que la page soit chargée pour envoyer l'événement de déconnexion
    singleWindow.webContents.once('did-finish-load', () => {
      log.info('LOGOUT: Page de connexion chargée, envoi de l\'événement logout-complete');
      singleWindow.webContents.send('logout-complete');
    });
  } catch (error) {
    log.error(`LOGOUT: Erreur lors du chargement de l'écran de connexion: ${error.message}`);

    // En cas d'erreur, essayer de recréer la fenêtre
    log.info('LOGOUT: Tentative de recréation de la fenêtre unique');

    // Détruire la fenêtre existante si elle existe
    if (singleWindow && !singleWindow.isDestroyed()) {
      singleWindow.destroy();
      singleWindow = null;
    }

    // Créer une nouvelle fenêtre unique
    createSingleWindow();

    // Charger l'écran de connexion
    log.info('LOGOUT: Chargement de l\'interface de connexion après recréation de la fenêtre');
    singleWindow.loadFile(path.join(__dirname, '../renderer/login.html'));

    // Attendre que la page soit chargée pour envoyer l'événement de déconnexion
    singleWindow.webContents.once('did-finish-load', () => {
      log.info('LOGOUT: Page de connexion chargée, envoi de l\'événement logout-complete');
      singleWindow.webContents.send('logout-complete');
    });
  }
}

// Note: Le gestionnaire pour 'logout' est déjà défini ailleurs dans le code

// Gestionnaire dédié pour la fin de l'écran de chargement
ipcMain.on('loading-screen-done', (_, data) => {
  if (data) {
    log.info('Message loading-screen-done reçu avec données:', data);
  } else {
    log.info('Message loading-screen-done reçu sans données');
  }

  // Afficher la fenêtre unique si elle existe
  if (singleWindow && !singleWindow.isDestroyed()) {
    log.info('Affichage de la fenêtre unique');

    // Si la fenêtre n'est pas déjà visible, l'afficher
    if (!singleWindow.isVisible()) {
      singleWindow.show();
    }

    // Mettre la fenêtre au premier plan
    singleWindow.focus();
  } else {
    log.warn('Fenêtre unique non disponible pour l\'affichage');
  }
});



// Gestionnaire pour sélectionner un dossier de sauvegarde
ipcMain.handle('select-backup-folder', async () => {
  log.info('Sélection d\'un dossier de sauvegarde');

  // Déterminer quelle fenêtre utiliser pour la boîte de dialogue
  let parentWindow = installerWindow;
  if (!parentWindow || parentWindow.isDestroyed()) {
    log.info('Fenêtre de l\'assistant d\'installation non disponible, utilisation de la fenêtre principale');
    parentWindow = singleWindow || mainWindow;

    if (!parentWindow || parentWindow.isDestroyed()) {
      log.error('Aucune fenêtre disponible pour afficher la boîte de dialogue');
      return { canceled: true, error: 'Aucune fenêtre disponible' };
    }
  }

  try {
    const result = await dialog.showOpenDialog(parentWindow, {
      properties: ['openDirectory', 'createDirectory'],
      title: 'Sélectionner un dossier pour les sauvegardes',
      buttonLabel: 'Sélectionner'
    });

    log.info(`Résultat de la sélection de dossier: ${JSON.stringify(result)}`);
    return result;
  } catch (error) {
    log.error(`Erreur lors de la sélection du dossier: ${error.message}`);
    return { canceled: true, error: error.message };
  }
});

// Gestionnaire pour enregistrer le chemin de sauvegarde
ipcMain.handle('save-backup-path', async (event, backupPath) => {
  log.info(`Enregistrement du chemin de sauvegarde via save-backup-path: ${backupPath}`);

  try {
    // Vérifier si le chemin est défini
    if (!backupPath) {
      log.error('Chemin de sauvegarde non défini');
      return { success: false, error: 'Chemin de sauvegarde non défini' };
    }

    // Créer le dossier de sauvegarde s'il n'existe pas
    log.info(`Appel de createBackupFolder avec le chemin: ${backupPath}`);
    const result = await backupManager.createBackupFolder(backupPath);

    // Sauvegarder le chemin dans les préférences utilisateur
    if (result.success) {
      userPreferences.setBackupPath(result.path);
      log.info(`Chemin de sauvegarde enregistré dans les préférences: ${result.path}`);
    }

    log.info(`Résultat de createBackupFolder: ${JSON.stringify(result)}`);
    return result;
  } catch (error) {
    log.error(`Erreur lors de l'enregistrement du chemin de sauvegarde: ${error.message}`);
    return { success: false, error: error.message || 'Erreur inconnue lors de la création du dossier' };
  }
});

// Gestionnaire pour les actions de l'assistant d'installation
ipcMain.on('installer-action', (_, data) => {
  log.info(`Action de l'assistant d'installation reçue: ${data.action}`);

  switch (data.action) {
    case 'accept-terms':
      // L'utilisateur a accepté ou refusé les conditions d'utilisation
      log.info(`Conditions d'utilisation ${data.accepted ? 'acceptées' : 'refusées'}`);

      if (data.accepted) {
        // Passer à l'étape suivante
        goToNextInstallerStep();
      }
      break;

    case 'save-backup-path':
      // L'utilisateur a sélectionné un dossier de sauvegarde
      log.info(`Dossier de sauvegarde sélectionné: ${data.path}`);

      // Sauvegarder le chemin de sauvegarde
      userPreferences.setBackupPath(data.path);
      break;

    case 'next-step':
      // Passer à l'étape suivante
      log.info('Passage à l\'étape suivante');
      goToNextInstallerStep();
      break;

    case 'create-shortcut':
      // L'utilisateur a choisi de créer ou non un raccourci sur le bureau
      log.info(`Création d'un raccourci sur le bureau: ${data.create}`);
      createShortcutOnFinish = data.create;
      break;

    case 'finish-install':
      // L'utilisateur a terminé l'installation
      log.info('Fin de l\'installation');
      finishInstallation();
      break;

    case 'cancel-installation':
      // L'utilisateur a annulé l'installation
      log.info('Annulation de l\'installation');
      if (installerWindow && !installerWindow.isDestroyed()) {
        installerWindow.close();
        installerWindow = null;
      }
      app.quit();
      break;

    case 'set-workspace-name':
      // L'utilisateur a défini le nom de l'espace de travail
      log.info(`Nom de l'espace de travail défini: ${data.name}`);
      // Stocker le nom de l'espace de travail dans les préférences utilisateur
      if (userPreferences && typeof userPreferences.setWorkspaceName === 'function') {
        userPreferences.setWorkspaceName(data.name);
      } else {
        log.warn('Impossible de stocker le nom de l\'espace de travail: userPreferences.setWorkspaceName n\'est pas disponible');
      }
      break;

    default:
      log.warn(`Action non reconnue: ${data.action}`);
  }
});

// Gestion des événements IPC
ipcMain.on('auth-data', async (_, data) => { // Utiliser _ pour indiquer un paramètre non utilisé
  log.info('Données d\'authentification reçues:', data.action);

  // Gérer différentes actions
  switch (data.action) {
    case 'splash-finished':
      // L'écran de démarrage a terminé son animation
      log.info('Écran de démarrage terminé');

      // Fermer la fenêtre de démarrage si elle existe
      if (splashWindow && !splashWindow.isDestroyed()) {
        log.info('Fermeture de la fenêtre de démarrage');
        splashWindow.close();
        splashWindow = null;
      }

      // Charger l'écran de connexion dans la fenêtre unique
      const defaultServerUrl = userPreferences.getServerUrl();
      loadLoginScreen(defaultServerUrl);
      break;

    case 'loading-screen-done':
      // Pour compatibilité avec le code existant, rediriger vers le gestionnaire dédié
      log.info('Message loading-screen-done reçu via auth-data, redirection vers le gestionnaire dédié');
      ipcMain.emit('loading-screen-done');
      break;

    case 'login':
      // Cette action est maintenant gérée directement dans le gestionnaire 'authenticate'
      log.info('Action "login" reçue mais ignorée - l\'authentification est gérée par le gestionnaire "authenticate"');
      break;

    case 'store-session':
      // Cette action est maintenant gérée directement dans le gestionnaire 'authenticate'
      log.info('Action "store-session" reçue mais ignorée - la gestion des cookies est gérée par le gestionnaire "authenticate"');
      break;

    case 'auth-success':
      // Cette action est maintenant gérée directement dans le gestionnaire 'authenticate'
      log.info('Action "auth-success" reçue mais ignorée - l\'authentification est gérée par le gestionnaire "authenticate"');
      break;

    case 'loading-animation-complete':
      // L'animation de chargement dans la fenêtre de connexion est terminée
      log.info('Animation de chargement terminée, chargement de l\'interface Odoo');

      // Récupérer les données d'authentification stockées
      const serverUrl = data.server || lastServerUrl || userPreferences.getServerUrl();

      if (serverUrl) {
        try {
          // Construire l'URL de l'interface Odoo sans paramètres spécifiques
          const timestamp = Date.now(); // Ajouter un timestamp pour éviter le cache
          const odooWebUrl = `${serverUrl}/web?nocache=${timestamp}`;
          log.info(`Chargement de l'interface Odoo dans la fenêtre unique: ${odooWebUrl}`);

          // Mettre à jour l'état de l'application
          currentAppState = 'main';

          // Ajouter un délai avant de charger l'URL pour s'assurer que les cookies sont correctement définis
          log.info('Attente de 500ms avant de charger l\'interface Odoo...');
          await new Promise(resolve => setTimeout(resolve, 500));

          // Charger l'interface Odoo dans la fenêtre unique
          if (singleWindow && !singleWindow.isDestroyed()) {
            singleWindow.loadURL(odooWebUrl);
          } else {
            log.warn('Fenêtre unique non disponible pour le chargement de l\'interface Odoo');
            // Créer une nouvelle fenêtre unique si nécessaire
            createSingleWindow();
            singleWindow.loadURL(odooWebUrl);
          }
        } catch (error) {
          log.error(`Erreur lors du chargement de l'interface Odoo: ${error.message}`);
        }
      } else {
        log.error('URL du serveur manquante pour le chargement de l\'interface Odoo');
      }
      break;

    case 'logout':
      // Gérer la déconnexion
      log.info('Déconnexion de l\'utilisateur demandée via IPC');
      handleOdooLogout(data.server);
      break;

    case 'quit-application':
      // Gérer la demande de fermeture de l'application
      log.info('Demande de fermeture de l\'application reçue via auth-data');
      app.quit();
      break;

    case 'report-error':
      // Gérer les erreurs rapportées par le renderer
      log.error('=== ERREUR RAPPORTÉE PAR LE RENDERER ===');
      if (data.error) {
        log.error('Message:', data.error.message);
        log.error('Source:', data.error.source);
        log.error('Ligne:', data.error.lineno, 'Colonne:', data.error.colno);
        log.error('Stack trace:', data.error.stack);
      }
      break;

    case 'report-promise-rejection':
      // Gérer les rejets de promesses rapportés par le renderer
      log.error('=== PROMESSE REJETÉE RAPPORTÉE PAR LE RENDERER ===');
      if (data.error) {
        log.error('Raison:', data.error.reason);
        log.error('Stack trace:', data.error.stack);
      }
      break;

    case 'content-length-fix-initialized':
      // Le script de correction Content-Length a été initialisé
      log.info('Script de correction Content-Length initialisé');
      break;

    default:
      log.warn('Action non reconnue:', data.action);
  }
});

// === Nouveaux gestionnaires pour le système d'authentification basé sur l'API ===

// Gestionnaire pour l'authentification avec Odoo (nouveau système)
ipcMain.handle('odoo-authenticate', async (event, { username, password, server, dbName }) => {
  log.info(`Demande d'authentification reçue via le nouveau système pour l'utilisateur ${username} sur ${server}`);

  try {
    // Authentifier l'utilisateur avec le nouveau système
    const result = await odooSessionManager.authenticate(username, password, server, dbName);

    if (result.success) {
      log.info(`Authentification réussie pour l'utilisateur ${username}`);

      // Stocker l'URL du serveur pour une utilisation ultérieure
      lastServerUrl = server;

      // Définir manuellement le cookie de session pour éviter les erreurs "Session Expired"
      if (result.session_id) {
        try {
          // Extraire le domaine du serveur
          const serverUrl = new URL(server);
          const domain = serverUrl.hostname;

          // Définir le cookie avec tous les paramètres nécessaires
          const cookie = {
            url: server,
            name: 'session_id',
            value: result.session_id,
            path: '/',
            domain: domain,
            httpOnly: false,
            secure: server.startsWith('https'),
          };

          await session.defaultSession.cookies.set(cookie);
          log.info('✅ Cookie session Odoo défini avec succès');
        } catch (cookieError) {
          log.error(`❌ Erreur définition cookie session Odoo: ${cookieError.message}`);
        }
      }

      // Notifier le processus de rendu que la session a été mise à jour
      if (singleWindow && !singleWindow.isDestroyed()) {
        singleWindow.webContents.send('odoo-session-event', {
          type: 'session-updated',
          data: {
            uid: result.uid,
            username: result.username,
            name: result.name,
            server_url: result.server_url
          }
        });
      }
    } else {
      log.error(`Échec de l'authentification: ${result.error}`);
    }

    return result;
  } catch (error) {
    log.error(`Erreur lors de l'authentification: ${error.message}`);
    return {
      success: false,
      error: error.message,
      errorType: 'UNEXPECTED_ERROR'
    };
  }
});

// Gestionnaire pour la validation de session
ipcMain.handle('odoo-validate-session', async (event) => {
  log.info('Demande de validation de session reçue');

  try {
    // Récupérer les données de session stockées
    const sessionData = odooSessionManager.getStoredSession();

    if (!sessionData || !sessionData.session_id || !sessionData.server_url) {
      log.warn('Aucune session stockée localement');
      return {
        valid: false,
        error: 'Aucune session stockée',
        errorType: 'NO_SESSION'
      };
    }

    // Définir manuellement le cookie de session pour éviter les erreurs "Session Expired"
    try {
      // Extraire le domaine du serveur
      const serverUrl = new URL(sessionData.server_url);
      const domain = serverUrl.hostname;

      // Définir le cookie avec tous les paramètres nécessaires
      const cookie = {
        url: sessionData.server_url,
        name: 'session_id',
        value: sessionData.session_id,
        path: '/',
        domain: domain,
        httpOnly: false,
        secure: sessionData.server_url.startsWith('https'),
      };

      await session.defaultSession.cookies.set(cookie);
      log.info('✅ Cookie session Odoo défini avec succès pour la validation');
    } catch (cookieError) {
      log.error(`❌ Erreur définition cookie session Odoo: ${cookieError.message}`);
    }

    // Valider la session auprès du serveur Odoo
    const result = await odooSessionManager.validateSession(
      sessionData.session_id,
      sessionData.server_url
    );

    if (result.valid) {
      log.info(`Session valide pour l'utilisateur ${result.username}`);

      // Stocker l'URL du serveur pour une utilisation ultérieure
      lastServerUrl = sessionData.server_url;

      // Notifier le processus de rendu que la session a été validée
      if (singleWindow && !singleWindow.isDestroyed()) {
        singleWindow.webContents.send('odoo-session-event', {
          type: 'session-validated',
          data: {
            uid: result.uid,
            username: result.username,
            name: result.name
          }
        });
      }
    } else {
      log.warn(`Session invalide: ${result.error}`);

      // Notifier le processus de rendu que la session est invalide
      if (singleWindow && !singleWindow.isDestroyed()) {
        singleWindow.webContents.send('odoo-session-event', {
          type: 'session-invalid',
          error: result.error
        });
      }
    }

    return result;
  } catch (error) {
    log.error(`Erreur lors de la validation de la session: ${error.message}`);
    return {
      valid: false,
      error: error.message,
      errorType: 'UNEXPECTED_ERROR'
    };
  }
});

// Gestionnaire pour la déconnexion
ipcMain.handle('odoo-logout', async (event) => {
  log.info('Demande de déconnexion reçue');

  try {
    // Récupérer les données de session stockées
    const sessionData = odooSessionManager.getStoredSession();

    if (!sessionData || !sessionData.session_id || !sessionData.server_url) {
      log.warn('Aucune session stockée localement pour la déconnexion');

      // Rediriger vers l'écran de connexion même s'il n'y a pas de session
      loadLoginScreen();

      return {
        success: true,
        message: 'Déconnexion locale réussie (aucune session stockée)'
      };
    }

    // Déconnecter l'utilisateur
    const result = await odooSessionManager.logout(
      sessionData.session_id,
      sessionData.server_url
    );

    // Rediriger vers l'écran de connexion
    loadLoginScreen(sessionData.server_url);

    // Notifier le processus de rendu que l'utilisateur a été déconnecté
    if (singleWindow && !singleWindow.isDestroyed()) {
      singleWindow.webContents.send('odoo-session-event', {
        type: 'logged-out'
      });
    }

    return result;
  } catch (error) {
    log.error(`Erreur lors de la déconnexion: ${error.message}`);

    // Rediriger vers l'écran de connexion même en cas d'erreur
    loadLoginScreen();

    return {
      success: true,
      warning: `Déconnexion locale réussie mais erreur: ${error.message}`
    };
  }
});

// Gestionnaire pour les appels à l'API Odoo
ipcMain.handle('odoo-call-api', async (event, { endpoint, params }) => {
  log.info(`Appel à l'API Odoo: ${endpoint}`);

  try {
    // Récupérer les données de session stockées
    const sessionData = odooSessionManager.getStoredSession();

    if (!sessionData || !sessionData.session_id || !sessionData.server_url) {
      log.warn('Aucune session stockée localement pour l\'appel à l\'API');
      return {
        success: false,
        error: 'Aucune session stockée',
        errorType: 'NO_SESSION'
      };
    }

    // Appeler l'API Odoo
    const result = await odooSessionManager.callOdooApi(
      endpoint,
      params,
      sessionData.session_id,
      sessionData.server_url
    );

    // Si la session est expirée, notifier le processus de rendu
    if (!result.success && result.errorType === 'SESSION_EXPIRED') {
      log.warn('Session expirée détectée lors de l\'appel à l\'API');

      if (singleWindow && !singleWindow.isDestroyed()) {
        singleWindow.webContents.send('odoo-session-event', {
          type: 'session-expired'
        });
      }
    }

    return result;
  } catch (error) {
    log.error(`Erreur lors de l'appel à l'API: ${error.message}`);
    return {
      success: false,
      error: error.message,
      errorType: 'UNEXPECTED_ERROR'
    };
  }
});