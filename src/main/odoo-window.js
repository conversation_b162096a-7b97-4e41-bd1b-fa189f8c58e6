/**
 * Module pour créer une fenêtre Electron avec le cookie de session Odoo injecté
 */

const { <PERSON><PERSON>erWindow, session } = require('electron');
const path = require('path');
const log = require('electron-log');
const fs = require('fs');

/**
 * Définit le cookie de session Odoo avec les paramètres appropriés
 * @param {string} sessionId - L'ID de session à définir
 * @param {string} serverUrl - L'URL du serveur Odoo
 * @returns {Promise<boolean>} - true si le cookie a été défini avec succès, false sinon
 */
async function setOdooSessionCookie(sessionId, serverUrl) {
  log.info(`COOKIE: Définition du cookie session_id: ${sessionId ? sessionId.substring(0, 8) + '...' : 'undefined'}`);
  
  if (!sessionId) {
    log.error('COOKIE: Impossible de définir le cookie session_id: sessionId non fourni');
    return false;
  }
  
  if (!serverUrl) {
    log.error('COOKIE: Impossible de définir le cookie session_id: serverUrl non fourni');
    return false;
  }
  
  try {
    // Extraire le domaine du serveur
    const urlObj = new URL(serverUrl);
    const domain = urlObj.hostname;
    
    log.info(`COOKIE: Paramètres du serveur - domaine: ${domain}, protocole: ${urlObj.protocol}`);
    
    // Supprimer d'abord tous les cookies existants pour éviter les conflits
    log.info('COOKIE: Suppression de tous les cookies existants...');
    await session.defaultSession.clearStorageData({ storages: ['cookies'] });
    
    // Définir le cookie avec tous les paramètres nécessaires, y compris sameSite: 'no_restriction'
    const cookie = {
      url: serverUrl,
      name: 'session_id',
      value: sessionId,
      domain: domain,
      path: '/',
      httpOnly: false,
      secure: false, // Toujours false comme demandé, même pour HTTPS
      sameSite: 'no_restriction',
      expirationDate: Math.floor(Date.now() / 1000) + 86400 // 24 heures
    };
    
    log.info('COOKIE: Définition du cookie avec les paramètres:', {
      url: serverUrl,
      name: 'session_id',
      value: sessionId.substring(0, 8) + '...',
      domain: domain,
      path: '/',
      httpOnly: false,
      secure: false,
      sameSite: 'no_restriction',
      expirationDate: new Date((Math.floor(Date.now() / 1000) + 86400) * 1000).toISOString()
    });
    
    // Définir le cookie
    await session.defaultSession.cookies.set(cookie);
    log.info('COOKIE: Cookie session_id défini avec succès');
    
    // Vérifier que le cookie a bien été défini
    const cookies = await session.defaultSession.cookies.get({ url: serverUrl });
    const sessionCookie = cookies.find(c => c.name === 'session_id');
    
    if (sessionCookie) {
      log.info(`COOKIE: Vérification réussie - Cookie session_id trouvé: ${sessionCookie.value.substring(0, 8)}...`);
      log.info(`COOKIE: Paramètres du cookie défini:`, {
        name: sessionCookie.name,
        value: sessionCookie.value.substring(0, 8) + '...',
        domain: sessionCookie.domain,
        path: sessionCookie.path,
        secure: sessionCookie.secure,
        httpOnly: sessionCookie.httpOnly,
        sameSite: sessionCookie.sameSite
      });
      return true;
    } else {
      log.error('COOKIE: Vérification échouée - Cookie session_id non trouvé après définition');
      return false;
    }
  } catch (error) {
    log.error(`COOKIE: Erreur lors de la définition du cookie session_id: ${error.message}`);
    log.error(error.stack);
    return false;
  }
}

/**
 * Crée une fenêtre Odoo avec le cookie de session injecté et charge l'interface web
 * @param {Object} options - Options pour la création de la fenêtre
 * @param {string} options.sessionId - ID de session Odoo
 * @param {string} options.serverUrl - URL du serveur Odoo (par défaut: http://localhost:8069)
 * @param {string} options.path - Chemin à charger (par défaut: /web)
 * @param {Object} options.windowOptions - Options supplémentaires pour la BrowserWindow
 * @returns {Promise<BrowserWindow>} - La fenêtre créée
 */
async function createOdooWindow(options) {
  const {
    sessionId,
    serverUrl = 'http://localhost:8069',
    path = '/web',
    windowOptions = {}
  } = options;
  
  log.info(`Création d'une fenêtre Odoo avec session injectée: ${sessionId ? sessionId.substring(0, 8) + '...' : 'undefined'}`);
  
  if (!sessionId) {
    log.error('Impossible de créer la fenêtre Odoo: sessionId non fourni');
    throw new Error('sessionId est requis pour créer une fenêtre Odoo');
  }
  
  // S'assurer que l'URL du serveur est complète
  let normalizedServerUrl = serverUrl;
  if (!normalizedServerUrl.startsWith('http')) {
    normalizedServerUrl = `http://${normalizedServerUrl}`;
  }
  
  // Normaliser l'URL du serveur (supprimer les barres obliques finales)
  normalizedServerUrl = normalizedServerUrl.replace(/\/+$/, '');
  
  // Construire l'URL complète
  const fullUrl = `${normalizedServerUrl}${path}`;
  log.info(`URL complète à charger: ${fullUrl}`);
  
  try {
    // Définir le cookie de session avant de créer la fenêtre
    const cookieSet = await setOdooSessionCookie(sessionId, normalizedServerUrl);
    
    if (!cookieSet) {
      log.error('Échec de la définition du cookie session_id, la fenêtre sera créée mais la session pourrait ne pas être reconnue');
    } else {
      log.info('Cookie session_id défini avec succès, création de la fenêtre...');
    }
    
    // Options par défaut pour la fenêtre
    const defaultOptions = {
      width: 1280,
      height: 800,
      minWidth: 1024,
      minHeight: 768,
      show: false,
      backgroundColor: '#212226',
      frame: true, // Utiliser la barre native de macOS
      titleBarStyle: 'hiddenInset', // Barre de titre intégrée avec les contrôles visibles
      trafficLightPosition: { x: 10, y: 10 }, // Position des boutons de contrôle
      webPreferences: {
        preload: path.join(__dirname, 'preload.js'),
        contextIsolation: false, // Désactiver l'isolation du contexte pour permettre l'accès aux cookies
        webSecurity: false, // Désactiver la sécurité web pour permettre les requêtes cross-origin
        nodeIntegration: true, // Activer l'intégration de Node.js
        enableRemoteModule: true, // Activer le module remote
        webviewTag: true, // Activer la balise webview
        allowRunningInsecureContent: true, // Autoriser le contenu non sécurisé
        plugins: true // Activer les plugins
      }
    };
    
    // Fusionner les options par défaut avec les options fournies
    const mergedOptions = {
      ...defaultOptions,
      ...windowOptions,
      webPreferences: {
        ...defaultOptions.webPreferences,
        ...(windowOptions.webPreferences || {})
      }
    };
    
    // Créer la fenêtre avec les options fusionnées
    const window = new BrowserWindow(mergedOptions);
    
    // Vérifier à nouveau que le cookie est bien défini
    const cookies = await session.defaultSession.cookies.get({ url: normalizedServerUrl });
    const sessionCookie = cookies.find(c => c.name === 'session_id');
    
    if (sessionCookie) {
      log.info(`Cookie session_id vérifié avant chargement: ${sessionCookie.value.substring(0, 8)}...`);
    } else {
      log.warn('Cookie session_id non trouvé avant chargement, tentative de redéfinition...');
      
      // Essayer de définir le cookie à nouveau
      await setOdooSessionCookie(sessionId, normalizedServerUrl);
    }
    
    // Charger l'URL Odoo
    log.info(`Chargement de l'URL Odoo: ${fullUrl}`);
    await window.loadURL(fullUrl);
    
    // Afficher la fenêtre lorsqu'elle est prête
    window.once('ready-to-show', () => {
      log.info('Fenêtre Odoo prête à être affichée');
      window.show();
    });
    
    // Injecter un script pour vérifier la session après le chargement
    window.webContents.on('did-finish-load', () => {
      log.info('Page Odoo chargée, vérification de la session...');
      
      // Script pour vérifier si nous sommes sur la page de connexion ou l'interface principale
      const checkSessionScript = `
        (function() {
          // Vérifier si nous sommes sur la page de connexion
          const loginForm = document.querySelector('form.oe_login_form');
          if (loginForm) {
            console.log('[Edara ERP] Page de connexion détectée, la session n'est pas valide');
            return { valid: false, page: 'login' };
          }
          
          // Vérifier si nous sommes sur l'interface principale
          const odooApp = document.querySelector('.o_web_client');
          if (odooApp) {
            console.log('[Edara ERP] Interface Odoo détectée, la session est valide');
            return { valid: true, page: 'main' };
          }
          
          console.log('[Edara ERP] Page indéterminée');
          return { valid: null, page: 'unknown' };
        })();
      `;
      
      window.webContents.executeJavaScript(checkSessionScript)
        .then(result => {
          if (result.valid === true) {
            log.info('Session Odoo valide, interface principale chargée');
          } else if (result.valid === false) {
            log.error('Session Odoo invalide, page de connexion affichée');
            log.info('Tentative de redéfinition du cookie et de rechargement...');
            
            // Redéfinir le cookie et recharger
            setOdooSessionCookie(sessionId, normalizedServerUrl)
              .then(success => {
                if (success) {
                  log.info('Cookie redéfini avec succès, rechargement de la page...');
                  window.loadURL(fullUrl);
                } else {
                  log.error('Échec de la redéfinition du cookie');
                }
              });
          } else {
            log.warn('État de session indéterminé');
          }
        })
        .catch(error => {
          log.error(`Erreur lors de la vérification de la session: ${error.message}`);
        });
    });
    
    return window;
  } catch (error) {
    log.error(`Erreur lors de la création de la fenêtre Odoo: ${error.message}`);
    log.error(error.stack);
    throw error;
  }
}

module.exports = {
  createOdooWindow,
  setOdooSessionCookie
};
