/**
 * Correction Immédiate des Erreurs ERR_CONTENT_LENGTH_MISMATCH
 * Solution ciblée pour résoudre le problème spécifique en mode local
 */

const { session } = require('electron');
const log = require('electron-log');

class ImmediateContentLengthFix {
  constructor() {
    this.isActive = false;
    this.interceptorsInstalled = false;
    this.fixedRequests = 0;
    this.errorCount = 0;

    // URLs problématiques identifiées
    this.problematicPatterns = [
      /web\.assets_backend\.js/,
      /web\.assets_common\.js/,
      /web\.assets_backend\.css/,
      /web\.assets_common\.css/,
      /load_menus/,
      /web\/webclient/
    ];

    // Configuration pour mode local
    this.localPatterns = [
      /192\.168\./,
      /localhost/,
      /127\.0\.0\.1/
    ];
  }

  /**
   * Active la correction immédiate
   */
  activate() {
    if (this.isActive) {
      log.info('🔧 [ImmediateFix] Correction déjà active');
      return;
    }

    log.info('🔧 [ImmediateFix] Activation de la correction immédiate Content-Length...');

    try {
      // Nettoyer les intercepteurs existants
      this.cleanupExistingInterceptors();

      // Installer les nouveaux intercepteurs optimisés
      this.installOptimizedInterceptors();

      this.isActive = true;
      this.interceptorsInstalled = true;

      log.info('✅ [ImmediateFix] Correction immédiate activée avec succès');
    } catch (error) {
      log.error(`❌ [ImmediateFix] Erreur lors de l'activation: ${error.message}`);
    }
  }

  /**
   * Nettoie les intercepteurs existants problématiques
   */
  cleanupExistingInterceptors() {
    log.info('🧹 [ImmediateFix] Nettoyage RADICAL des intercepteurs existants...');

    try {
      const defaultSession = session.defaultSession;

      // SOLUTION RADICALE: Remplacer TOUS les intercepteurs par des intercepteurs vides
      // Cela va écraser tous les intercepteurs problématiques existants

      log.info('🧹 [ImmediateFix] Remplacement de TOUS les intercepteurs webRequest...');

      // Remplacer onBeforeRequest
      defaultSession.webRequest.onBeforeRequest([], (details, callback) => {
        callback({});
      });

      // Remplacer onBeforeSendHeaders
      defaultSession.webRequest.onBeforeSendHeaders([], (details, callback) => {
        callback({});
      });

      // Remplacer onHeadersReceived
      defaultSession.webRequest.onHeadersReceived([], (details, callback) => {
        callback({});
      });

      // Remplacer onResponseStarted
      defaultSession.webRequest.onResponseStarted([], (details) => {
        // Ne rien faire
      });

      // Remplacer onBeforeRedirect
      defaultSession.webRequest.onBeforeRedirect([], (details) => {
        // Ne rien faire
      });

      // Remplacer onCompleted
      defaultSession.webRequest.onCompleted([], (details) => {
        // Ne rien faire
      });

      // Remplacer onErrorOccurred
      defaultSession.webRequest.onErrorOccurred([], (details) => {
        // Ne rien faire
      });

      log.info('✅ [ImmediateFix] TOUS les intercepteurs existants ont été remplacés');
    } catch (error) {
      log.warn(`⚠️ [ImmediateFix] Erreur lors du nettoyage radical: ${error.message}`);
    }
  }

  /**
   * Installe les intercepteurs optimisés
   */
  installOptimizedInterceptors() {
    log.info('🔧 [ImmediateFix] Installation des intercepteurs SUPER-OPTIMISÉS...');

    const defaultSession = session.defaultSession;

    // Intercepteur de requêtes SUPER-OPTIMISÉ
    defaultSession.webRequest.onBeforeSendHeaders([], (details, callback) => {
      if (this.isProblematicLocalRequest(details.url)) {
        log.info(`🔧 [ImmediateFix] OPTIMISATION REQUÊTE: ${details.url}`);

        const optimizedHeaders = { ...details.requestHeaders };

        // FORCER l'encoding identity pour éviter la compression
        optimizedHeaders['Accept-Encoding'] = ['identity'];
        optimizedHeaders['Cache-Control'] = ['no-cache, no-store, must-revalidate'];
        optimizedHeaders['Pragma'] = ['no-cache'];
        optimizedHeaders['Expires'] = ['0'];

        // Ajouter un header personnalisé pour tracking
        optimizedHeaders['X-Edara-Request-Fixed'] = ['true'];

        callback({
          cancel: false,
          requestHeaders: optimizedHeaders
        });
      } else {
        callback({ cancel: false });
      }
    });

    // Intercepteur de réponses SUPER-AGRESSIF - LE PLUS IMPORTANT
    defaultSession.webRequest.onHeadersReceived([], (details, callback) => {
      if (this.isProblematicLocalRequest(details.url)) {
        log.info(`🔧 [ImmediateFix] CORRECTION RÉPONSE: ${details.url}`);

        const responseHeaders = { ...details.responseHeaders };

        // CORRECTION SUPER-AGRESSIVE: TOUJOURS supprimer Content-Length pour les assets problématiques
        const hasContentLength = responseHeaders['content-length'] || responseHeaders['Content-Length'];
        const hasContentEncoding = responseHeaders['content-encoding'] || responseHeaders['Content-Encoding'];
        const hasTransferEncoding = responseHeaders['transfer-encoding'] || responseHeaders['Transfer-Encoding'];

        if (hasContentLength) {
          delete responseHeaders['content-length'];
          delete responseHeaders['Content-Length'];
          log.info(`🔧 [ImmediateFix] ✅ Content-Length SUPPRIMÉ pour: ${details.url}`);
          this.fixedRequests++;
        }

        // TOUJOURS supprimer les headers de compression
        if (hasContentEncoding) {
          delete responseHeaders['content-encoding'];
          delete responseHeaders['Content-Encoding'];
          log.info(`🔧 [ImmediateFix] ✅ Content-Encoding SUPPRIMÉ pour: ${details.url}`);
        }

        // Supprimer Transfer-Encoding si présent
        if (hasTransferEncoding) {
          delete responseHeaders['transfer-encoding'];
          delete responseHeaders['Transfer-Encoding'];
          log.info(`🔧 [ImmediateFix] ✅ Transfer-Encoding SUPPRIMÉ pour: ${details.url}`);
        }

        // FORCER le rechargement pour éviter le cache
        responseHeaders['Cache-Control'] = ['no-cache, no-store, must-revalidate'];
        responseHeaders['Pragma'] = ['no-cache'];
        responseHeaders['Expires'] = ['0'];
        responseHeaders['Last-Modified'] = ['0'];
        responseHeaders['ETag'] = [''];

        // Ajouter des headers de tracking
        responseHeaders['X-Edara-Response-Fixed'] = ['true'];
        responseHeaders['X-Edara-Fix-Time'] = [Date.now().toString()];

        callback({
          cancel: false,
          responseHeaders: responseHeaders
        });
      } else {
        callback({ cancel: false });
      }
    });

    // Intercepteur d'erreurs SUPER-VIGILANT pour monitoring
    defaultSession.webRequest.onErrorOccurred([], (details) => {
      if (this.isProblematicLocalRequest(details.url)) {
        this.errorCount++;
        log.error(`❌ [ImmediateFix] ERREUR DÉTECTÉE: ${details.url} - ${details.error}`);

        // Gestion spéciale pour Content-Length Mismatch
        if (details.error.includes('CONTENT_LENGTH_MISMATCH')) {
          log.error(`🚨 [ImmediateFix] ERREUR CONTENT-LENGTH MISMATCH DÉTECTÉE !`);
          log.error(`🚨 [ImmediateFix] URL: ${details.url}`);
          log.error(`🚨 [ImmediateFix] Cette erreur devrait être ÉLIMINÉE par notre correction !`);
        }

        // Émettre un événement pour déclencher un rechargement si nécessaire
        this.handleContentLengthError(details);
      }
    });

    log.info('✅ [ImmediateFix] Intercepteurs SUPER-OPTIMISÉS installés avec succès');
  }

  /**
   * Vérifie si une URL est une requête problématique en mode local
   */
  isProblematicLocalRequest(url) {
    // Vérifier si c'est une URL locale
    const isLocal = this.localPatterns.some(pattern => pattern.test(url));

    // Vérifier si c'est un asset problématique
    const isProblematic = this.problematicPatterns.some(pattern => pattern.test(url));

    return isLocal && isProblematic;
  }

  /**
   * Gère les erreurs Content-Length détectées
   */
  handleContentLengthError(details) {
    log.warn(`⚠️ [ImmediateFix] Gestion erreur Content-Length: ${details.url}`);

    // Pour l'instant, juste logger - peut être étendu pour déclencher des actions
    // comme un rechargement automatique de l'asset
  }

  /**
   * Effectue un nettoyage agressif du cache
   */
  async performAggressiveCacheClearing() {
    log.info('🧹 [ImmediateFix] Nettoyage agressif du cache...');

    try {
      const defaultSession = session.defaultSession;

      // Nettoyer tout le cache
      await defaultSession.clearCache();

      // Nettoyer les données de stockage
      await defaultSession.clearStorageData({
        storages: [
          'cookies',
          'localstorage',
          'sessionstorage',
          'indexdb',
          'websql',
          'serviceworkers',
          'cachestorage'
        ]
      });

      // Nettoyer le cache d'authentification
      await defaultSession.clearAuthCache();

      log.info('✅ [ImmediateFix] Nettoyage agressif terminé');
    } catch (error) {
      log.error(`❌ [ImmediateFix] Erreur lors du nettoyage: ${error.message}`);
    }
  }

  /**
   * Désactive la correction
   */
  deactivate() {
    if (!this.isActive) {
      return;
    }

    log.info('🔧 [ImmediateFix] Désactivation de la correction...');

    this.isActive = false;
    this.interceptorsInstalled = false;

    // Note: Les intercepteurs Electron ne peuvent pas être supprimés,
    // ils seront remplacés lors de la prochaine activation

    log.info('✅ [ImmediateFix] Correction désactivée');
  }

  /**
   * Obtient les statistiques
   */
  getStats() {
    return {
      isActive: this.isActive,
      interceptorsInstalled: this.interceptorsInstalled,
      fixedRequests: this.fixedRequests,
      errorCount: this.errorCount
    };
  }

  /**
   * Réinitialise les statistiques
   */
  resetStats() {
    this.fixedRequests = 0;
    this.errorCount = 0;
    log.info('📊 [ImmediateFix] Statistiques réinitialisées');
  }
}

// Instance singleton
let immediateFixInstance = null;

/**
 * Obtient l'instance singleton de la correction
 */
function getImmediateFix() {
  if (!immediateFixInstance) {
    immediateFixInstance = new ImmediateContentLengthFix();
  }
  return immediateFixInstance;
}

/**
 * Active la correction immédiate
 */
function activateImmediateFix() {
  const fix = getImmediateFix();
  fix.activate();
  return fix;
}

/**
 * Effectue un nettoyage complet et active la correction
 */
async function performCompleteFixAndClearing() {
  log.info('🔄 [ImmediateFix] Correction complète et nettoyage...');

  const fix = getImmediateFix();

  // 1. Nettoyage agressif du cache
  await fix.performAggressiveCacheClearing();

  // 2. Activation de la correction
  fix.activate();

  log.info('✅ [ImmediateFix] Correction complète terminée');
  return fix;
}

/**
 * Fonction utilitaire pour intégration rapide dans main.js
 */
function setupImmediateContentLengthFix() {
  log.info('🚀 [ImmediateFix] Configuration de la correction immédiate...');

  // Activer la correction dès que possible
  const fix = activateImmediateFix();

  // Retourner l'instance pour utilisation ultérieure
  return fix;
}

module.exports = {
  ImmediateContentLengthFix,
  getImmediateFix,
  activateImmediateFix,
  performCompleteFixAndClearing,
  setupImmediateContentLengthFix
};
