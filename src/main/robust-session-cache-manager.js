/**
 * Gestionnaire Robuste de Session et Cache pour Electron
 * Garantit un nettoyage complet et une réinitialisation fiable
 */

const { session } = require('electron');
const log = require('electron-log');
const { EventEmitter } = require('events');

class RobustSessionCacheManager extends EventEmitter {
  constructor() {
    super();
    
    this.config = {
      // Stratégies de nettoyage
      enableAggressiveClearing: true,
      enableMultiPassClearing: true,
      enableSessionRecreation: true,
      enableStorageValidation: true,
      
      // Timeouts et délais
      clearingTimeout: 10000,         // 10s max pour nettoyage
      validationDelay: 1000,          // 1s entre nettoyage et validation
      recreationDelay: 2000,          // 2s avant recréation session
      
      // Validation
      maxClearingAttempts: 3,
      validateAfterClearing: true,
      
      // Debug
      debug: true
    };
    
    this.state = {
      isClearing: false,
      lastClearingTime: 0,
      clearingAttempts: 0,
      sessionRecreated: false,
      validationResults: null
    };
    
    this.originalSession = null;
    this.cleanSession = null;
  }

  /**
   * Effectue un nettoyage complet et robuste du cache et des données
   */
  async performRobustClearing(options = {}) {
    const {
      includeCache = true,
      includeStorageData = true,
      includeCookies = true,
      includeAuthCache = true,
      recreateSession = false,
      validateResults = true
    } = options;
    
    if (this.state.isClearing) {
      log.warn('🔄 [SessionCache] Nettoyage déjà en cours, attente...');
      return await this.waitForClearingCompletion();
    }
    
    this.state.isClearing = true;
    this.state.clearingAttempts++;
    
    try {
      log.info('🧹 [SessionCache] Démarrage du nettoyage robuste...');
      
      // Phase 1: Nettoyage multi-passes
      if (this.config.enableMultiPassClearing) {
        await this.performMultiPassClearing({
          includeCache,
          includeStorageData,
          includeCookies,
          includeAuthCache
        });
      } else {
        await this.performSinglePassClearing({
          includeCache,
          includeStorageData,
          includeCookies,
          includeAuthCache
        });
      }
      
      // Phase 2: Validation du nettoyage
      if (validateResults && this.config.validateAfterClearing) {
        await this.delay(this.config.validationDelay);
        const validationResult = await this.validateClearing();
        
        if (!validationResult.success && this.state.clearingAttempts < this.config.maxClearingAttempts) {
          log.warn('⚠️ [SessionCache] Validation échouée, nouvelle tentative...');
          this.state.isClearing = false;
          return await this.performRobustClearing(options);
        }
        
        this.state.validationResults = validationResult;
      }
      
      // Phase 3: Recréation de session si demandée
      if (recreateSession && this.config.enableSessionRecreation) {
        await this.delay(this.config.recreationDelay);
        await this.recreateCleanSession();
      }
      
      this.state.lastClearingTime = Date.now();
      log.info('✅ [SessionCache] Nettoyage robuste terminé avec succès');
      
      this.emit('clearing-completed', {
        attempts: this.state.clearingAttempts,
        validation: this.state.validationResults,
        sessionRecreated: this.state.sessionRecreated
      });
      
      return {
        success: true,
        attempts: this.state.clearingAttempts,
        validation: this.state.validationResults
      };
      
    } catch (error) {
      log.error(`❌ [SessionCache] Erreur lors du nettoyage: ${error.message}`);
      throw error;
    } finally {
      this.state.isClearing = false;
    }
  }

  /**
   * Effectue un nettoyage en plusieurs passes pour garantir l'efficacité
   */
  async performMultiPassClearing(options) {
    log.info('🔄 [SessionCache] Nettoyage multi-passes...');
    
    const passes = [
      // Passe 1: Nettoyage standard
      async () => {
        log.debug('🔄 [SessionCache] Passe 1: Nettoyage standard');
        await this.performSinglePassClearing(options);
      },
      
      // Passe 2: Nettoyage agressif
      async () => {
        log.debug('🔄 [SessionCache] Passe 2: Nettoyage agressif');
        await this.performAggressiveClearing(options);
      },
      
      // Passe 3: Nettoyage de vérification
      async () => {
        log.debug('🔄 [SessionCache] Passe 3: Nettoyage de vérification');
        await this.performVerificationClearing(options);
      }
    ];
    
    for (let i = 0; i < passes.length; i++) {
      try {
        await passes[i]();
        await this.delay(500); // Délai entre les passes
      } catch (error) {
        log.warn(`⚠️ [SessionCache] Erreur lors de la passe ${i + 1}: ${error.message}`);
      }
    }
  }

  /**
   * Effectue un nettoyage standard en une seule passe
   */
  async performSinglePassClearing(options) {
    const targetSession = session.defaultSession;
    const clearingPromises = [];
    
    if (options.includeCache) {
      log.debug('🧹 [SessionCache] Nettoyage du cache...');
      clearingPromises.push(
        targetSession.clearCache().catch(error => {
          log.warn(`⚠️ [SessionCache] Erreur cache: ${error.message}`);
        })
      );
    }
    
    if (options.includeStorageData) {
      log.debug('🧹 [SessionCache] Nettoyage des données de stockage...');
      clearingPromises.push(
        targetSession.clearStorageData({
          storages: [
            'cookies',
            'localstorage',
            'sessionstorage',
            'indexdb',
            'websql',
            'serviceworkers',
            'cachestorage',
            'appcache'
          ]
        }).catch(error => {
          log.warn(`⚠️ [SessionCache] Erreur stockage: ${error.message}`);
        })
      );
    }
    
    if (options.includeCookies) {
      log.debug('🧹 [SessionCache] Nettoyage spécifique des cookies...');
      clearingPromises.push(
        this.clearAllCookies(targetSession).catch(error => {
          log.warn(`⚠️ [SessionCache] Erreur cookies: ${error.message}`);
        })
      );
    }
    
    if (options.includeAuthCache) {
      log.debug('🧹 [SessionCache] Nettoyage du cache d\'authentification...');
      clearingPromises.push(
        targetSession.clearAuthCache().catch(error => {
          log.warn(`⚠️ [SessionCache] Erreur auth cache: ${error.message}`);
        })
      );
    }
    
    // Attendre toutes les opérations de nettoyage
    await Promise.allSettled(clearingPromises);
  }

  /**
   * Effectue un nettoyage agressif avec méthodes avancées
   */
  async performAggressiveClearing(options) {
    const targetSession = session.defaultSession;
    
    try {
      // Nettoyage agressif du cache avec options étendues
      await targetSession.clearCache();
      
      // Nettoyage étendu des données de stockage
      await targetSession.clearStorageData({
        storages: [
          'cookies',
          'localstorage',
          'sessionstorage',
          'indexdb',
          'websql',
          'serviceworkers',
          'cachestorage',
          'appcache',
          'shadercache',
          'webrtc'
        ],
        quotas: ['temporary', 'persistent', 'syncable']
      });
      
      // Nettoyage manuel des cookies par domaine
      await this.clearCookiesByDomains(targetSession, [
        'localhost',
        '127.0.0.1',
        '192.168.',
        '10.',
        '172.'
      ]);
      
      // Nettoyage du cache d'authentification
      await targetSession.clearAuthCache();
      
      // Nettoyage des données de navigation
      await this.clearNavigationData(targetSession);
      
    } catch (error) {
      log.error(`❌ [SessionCache] Erreur lors du nettoyage agressif: ${error.message}`);
    }
  }

  /**
   * Effectue un nettoyage de vérification pour s'assurer que tout est propre
   */
  async performVerificationClearing(options) {
    const targetSession = session.defaultSession;
    
    try {
      // Vérifier et nettoyer les cookies restants
      const remainingCookies = await targetSession.cookies.get({});
      if (remainingCookies.length > 0) {
        log.debug(`🧹 [SessionCache] ${remainingCookies.length} cookies restants, nettoyage...`);
        for (const cookie of remainingCookies) {
          await targetSession.cookies.remove(cookie.url || `http://${cookie.domain}`, cookie.name);
        }
      }
      
      // Nettoyage final du cache
      await targetSession.clearCache();
      
      // Nettoyage final des données de stockage
      await targetSession.clearStorageData();
      
    } catch (error) {
      log.error(`❌ [SessionCache] Erreur lors du nettoyage de vérification: ${error.message}`);
    }
  }

  /**
   * Nettoie tous les cookies de manière exhaustive
   */
  async clearAllCookies(targetSession) {
    try {
      // Méthode 1: Nettoyage global
      await targetSession.clearStorageData({ storages: ['cookies'] });
      
      // Méthode 2: Nettoyage manuel de tous les cookies
      const allCookies = await targetSession.cookies.get({});
      
      for (const cookie of allCookies) {
        try {
          const url = cookie.url || `http${cookie.secure ? 's' : ''}://${cookie.domain}${cookie.path}`;
          await targetSession.cookies.remove(url, cookie.name);
        } catch (error) {
          log.debug(`⚠️ [SessionCache] Impossible de supprimer le cookie ${cookie.name}: ${error.message}`);
        }
      }
      
      log.debug(`🧹 [SessionCache] ${allCookies.length} cookies nettoyés`);
    } catch (error) {
      log.error(`❌ [SessionCache] Erreur lors du nettoyage des cookies: ${error.message}`);
    }
  }

  /**
   * Nettoie les cookies par domaines spécifiques
   */
  async clearCookiesByDomains(targetSession, domains) {
    for (const domain of domains) {
      try {
        const domainCookies = await targetSession.cookies.get({ domain });
        
        for (const cookie of domainCookies) {
          const url = `http://${cookie.domain}${cookie.path}`;
          await targetSession.cookies.remove(url, cookie.name);
        }
        
        if (domainCookies.length > 0) {
          log.debug(`🧹 [SessionCache] ${domainCookies.length} cookies nettoyés pour ${domain}`);
        }
      } catch (error) {
        log.debug(`⚠️ [SessionCache] Erreur nettoyage domaine ${domain}: ${error.message}`);
      }
    }
  }

  /**
   * Nettoie les données de navigation
   */
  async clearNavigationData(targetSession) {
    try {
      // Nettoyer l'historique de navigation si disponible
      if (targetSession.clearHistory) {
        await targetSession.clearHistory();
      }
      
      // Nettoyer les données de formulaires si disponible
      if (targetSession.clearFormData) {
        await targetSession.clearFormData();
      }
      
      // Nettoyer les mots de passe si disponible
      if (targetSession.clearPasswords) {
        await targetSession.clearPasswords();
      }
      
    } catch (error) {
      log.debug(`⚠️ [SessionCache] Erreur nettoyage navigation: ${error.message}`);
    }
  }

  /**
   * Valide que le nettoyage a été effectué correctement
   */
  async validateClearing() {
    const validation = {
      success: true,
      issues: [],
      details: {
        cookiesRemaining: 0,
        cacheSize: 0,
        storageDataPresent: false
      }
    };
    
    try {
      const targetSession = session.defaultSession;
      
      // Vérifier les cookies restants
      const remainingCookies = await targetSession.cookies.get({});
      validation.details.cookiesRemaining = remainingCookies.length;
      
      if (remainingCookies.length > 0) {
        validation.success = false;
        validation.issues.push(`${remainingCookies.length} cookies restants`);
      }
      
      // Vérifier la taille du cache (approximative)
      // Note: Electron ne fournit pas de méthode directe pour obtenir la taille du cache
      
      log.debug(`🔍 [SessionCache] Validation: ${validation.details.cookiesRemaining} cookies restants`);
      
    } catch (error) {
      validation.success = false;
      validation.issues.push(`Erreur de validation: ${error.message}`);
    }
    
    return validation;
  }

  /**
   * Recrée une session propre
   */
  async recreateCleanSession() {
    try {
      log.info('🔄 [SessionCache] Recréation d\'une session propre...');
      
      // Sauvegarder la session originale
      this.originalSession = session.defaultSession;
      
      // Créer une nouvelle session avec partition unique
      const partitionName = `clean-session-${Date.now()}`;
      this.cleanSession = session.fromPartition(partitionName, { cache: false });
      
      // Configurer la nouvelle session
      await this.configureCleanSession(this.cleanSession);
      
      this.state.sessionRecreated = true;
      log.info('✅ [SessionCache] Session propre créée avec succès');
      
    } catch (error) {
      log.error(`❌ [SessionCache] Erreur lors de la recréation de session: ${error.message}`);
      throw error;
    }
  }

  /**
   * Configure une session propre
   */
  async configureCleanSession(cleanSession) {
    // Configurer les permissions
    cleanSession.setPermissionRequestHandler((webContents, permission, callback) => {
      const allowedPermissions = ['notifications', 'media'];
      callback(allowedPermissions.includes(permission));
    });
    
    // Désactiver le cache persistant
    await cleanSession.clearCache();
    await cleanSession.clearStorageData();
    
    // Configuration optimisée pour Odoo
    cleanSession.setUserAgent('Edara-ERP-Clean/1.0 (Electron)');
  }

  /**
   * Obtient la session à utiliser (propre si disponible, sinon par défaut)
   */
  getOptimalSession() {
    return this.cleanSession || session.defaultSession;
  }

  /**
   * Attend la fin du nettoyage en cours
   */
  async waitForClearingCompletion() {
    return new Promise((resolve) => {
      if (!this.state.isClearing) {
        resolve({ success: true, waited: false });
        return;
      }
      
      const checkInterval = setInterval(() => {
        if (!this.state.isClearing) {
          clearInterval(checkInterval);
          resolve({ success: true, waited: true });
        }
      }, 100);
      
      // Timeout de sécurité
      setTimeout(() => {
        clearInterval(checkInterval);
        resolve({ success: false, timeout: true });
      }, this.config.clearingTimeout);
    });
  }

  /**
   * Obtient les statistiques du gestionnaire
   */
  getStats() {
    return {
      isClearing: this.state.isClearing,
      lastClearingTime: this.state.lastClearingTime,
      clearingAttempts: this.state.clearingAttempts,
      sessionRecreated: this.state.sessionRecreated,
      validationResults: this.state.validationResults,
      hasCleanSession: !!this.cleanSession
    };
  }

  /**
   * Utilitaire de délai
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Nettoie les ressources
   */
  cleanup() {
    this.removeAllListeners();
    
    if (this.cleanSession) {
      this.cleanSession.clearCache().catch(() => {});
      this.cleanSession = null;
    }
    
    this.originalSession = null;
    
    log.info('🧹 [SessionCache] Gestionnaire nettoyé');
  }
}

module.exports = RobustSessionCacheManager;
