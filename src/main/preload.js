/**
 * Script de préchargement pour l'application Edara ERP
 * Ce fichier est chargé avant le processus de rendu et permet une communication
 * entre le processus principal et le processus de rendu
 *
 * Version adaptée pour fonctionner sans isolation du contexte (contextIsolation: false)
 */

const { ipc<PERSON><PERSON><PERSON> } = require('electron');
const log = require('electron-log');
const fs = require('fs');
const path = require('path');

// Configuration de electron-log pour le processus de rendu
log.transports.file.level = 'info';
log.transports.console.level = 'debug';
log.transports.console.format = '[{level}] {text}';

// Gestionnaire d'erreurs non capturées dans le processus de rendu
window.addEventListener('error', (event) => {
  const errorInfo = {
    message: event.message,
    source: event.filename,
    lineno: event.lineno,
    colno: event.colno,
    error: event.error ? (event.error.stack || event.error.toString()) : 'Pas de stack trace disponible'
  };

  log.error('ERREUR NON CAPTURÉE DANS LE PROCESSUS DE RENDU:', errorInfo.message);
  log.error('Source:', errorInfo.source);
  log.error('Ligne:', errorInfo.lineno, 'Colonne:', errorInfo.colno);
  log.error('Stack trace:', errorInfo.error);

  // Envoyer l'erreur au processus principal pour l'enregistrer
  ipcRenderer.send('renderer-error', errorInfo);
});

// Gestionnaire de rejets de promesses non gérés
window.addEventListener('unhandledrejection', (event) => {
  const errorInfo = {
    message: event.reason ? (event.reason.message || event.reason.toString()) : 'Rejet de promesse non géré',
    stack: event.reason && event.reason.stack ? event.reason.stack : 'Pas de stack trace disponible'
  };

  log.error('PROMESSE REJETÉE NON GÉRÉE DANS LE PROCESSUS DE RENDU:', errorInfo.message);
  log.error('Stack trace:', errorInfo.stack);

  // Envoyer l'erreur au processus principal pour l'enregistrer
  ipcRenderer.send('renderer-promise-rejection', errorInfo);
});

// Expose des API directement dans l'objet window
console.log('PRELOAD MAIN: Exposition des APIs directement dans window (sans contextBridge)');

// Exposer les APIs directement dans l'objet window
window.electronAPI = {
  // === APIs pour l'authentification Odoo ===

  // API pour authentifier un utilisateur avec Odoo
  authenticate: (username, password, server, dbName) => {
    return ipcRenderer.invoke('odoo-authenticate', { username, password, server, dbName });
  },

  // API pour vérifier si une session est valide
  validateSession: () => {
    return ipcRenderer.invoke('odoo-validate-session');
  },

  // API pour déconnecter l'utilisateur
  logout: () => {
    return ipcRenderer.invoke('odoo-logout');
  },

  // API pour appeler l'API Odoo
  callOdooApi: (endpoint, params) => {
    return ipcRenderer.invoke('odoo-call-api', { endpoint, params });
  },

  // API pour recevoir les événements de session
  onSessionEvent: (callback) => {
    ipcRenderer.on('odoo-session-event', (_, data) => callback(data));
  },

  // === APIs pour la compatibilité avec le code existant ===

  // API pour envoyer des données d'authentification au processus principal (obsolète)
  sendAuthData: (data) => {
    ipcRenderer.send('auth-data', data);
  },

  // API pour recevoir les erreurs d'authentification
  onAuthError: (callback) => {
    ipcRenderer.on('auth-error', (_, message) => callback(message));
  },

  // API pour supprimer un cookie d'un domaine spécifique (avec réponse)
  removeCookie: (url, name) => {
    return ipcRenderer.invoke('remove-cookie', { url, name });
  },

  // API pour supprimer un cookie d'un domaine spécifique (sans réponse)
  removeCookieNoResponse: (url, name) => {
    ipcRenderer.send('remove-cookie', { url, name });
  },

  // API pour écouter l'événement load-odoo-interface
  onLoadOdooInterface: (callback) => {
    ipcRenderer.on('load-odoo-interface', (_, data) => callback(data));
  },

  // API pour signaler que l'écran de chargement est terminé
  sendLoadingScreenDone: () => {
    ipcRenderer.send('loading-screen-done');
  },

  // API pour signaler que l'écran de démarrage est prêt
  splashReady: () => {
    ipcRenderer.send('splash-ready');
  },

  // API pour vérifier la connexion au serveur
  checkServerConnection: (url) => {
    return ipcRenderer.invoke('check-server-connection', url);
  },

  // API pour obtenir des informations sur les serveurs disponibles
  getServerInfo: () => {
    return ipcRenderer.invoke('check-server-connection');
  },

  // API pour récupérer la dernière adresse IP locale valide
  getLastValidIp: () => {
    return ipcRenderer.invoke('get-last-valid-ip');
  },

  // === APIs pour l'assistant d'installation ===

  // API pour accepter les conditions d'utilisation
  acceptTerms: (accepted) => {
    ipcRenderer.send('installer-action', { action: 'accept-terms', accepted });
  },

  // API pour sélectionner un dossier de sauvegarde
  selectBackupFolder: () => {
    return ipcRenderer.invoke('select-backup-folder');
  },

  // API pour sauvegarder le chemin de sauvegarde
  saveBackupPath: (path) => {
    ipcRenderer.send('installer-action', { action: 'save-backup-path', path });
  },

  // API pour passer à l'étape suivante de l'installation
  goToNextStep: () => {
    ipcRenderer.send('installer-action', { action: 'next-step' });
  },

  // API pour créer un raccourci sur le bureau
  createDesktopShortcut: (create) => {
    ipcRenderer.send('installer-action', { action: 'create-shortcut', create });
  },

  // API pour terminer l'installation et lancer l'application
  finishInstallAndLaunchApp: () => {
    ipcRenderer.send('installer-action', { action: 'finish-install' });
  },

  // API pour annuler l'installation
  cancelInstallation: () => {
    ipcRenderer.send('installer-action', { action: 'cancel-installation' });
  },

  // API pour définir le nom de l'espace de travail
  setWorkspaceName: (name) => {
    ipcRenderer.send('installer-action', { action: 'set-workspace-name', name });
  }
};

// Inject content-length-fix.js to attempt to mitigate asset loading issues
window.addEventListener('DOMContentLoaded', () => {
  log.info('[Edara ERP Preload] DOMContentLoaded: Attempting to inject content-length-fix.js');
  try {
    // Resolve path from src/main/preload.js to src/assets/js/content-length-fix.js
    const fixScriptPath = path.resolve(__dirname, '../assets/js/content-length-fix.js');
    if (fs.existsSync(fixScriptPath)) {
      const fixScriptContent = fs.readFileSync(fixScriptPath, 'utf8');
      const scriptElement = document.createElement('script');
      scriptElement.type = 'text/javascript';
      scriptElement.textContent = fixScriptContent;
      document.head.appendChild(scriptElement);
      log.info('[Edara ERP Preload] content-length-fix.js successfully injected and executed.');
    } else {
      log.error(`[Edara ERP Preload] content-length-fix.js not found at: ${fixScriptPath}`);
    }
  } catch (error) {
    log.error('[Edara ERP Preload] Error injecting content-length-fix.js:', error.message, error.stack);
  }
});

// Log to indicate preload script has finished loading its main parts
log.info('[Edara ERP Preload] Preload script main execution finished.');

// Exposer ipcRenderer directement dans l'objet window.electron
window.electron = {
  ipcRenderer: {
    send: (channel, ...args) => {
      console.log(`Appel de ipcRenderer.send avec canal: ${channel}, arguments:`, args);
      try {
        return ipcRenderer.send(channel, ...args);
      } catch (error) {
        console.error(`Erreur lors de l'appel à ipcRenderer.send(${channel}):`, error);
      }
    },
    on: (channel, listener) => {
      console.log(`Enregistrement d'un écouteur pour le canal: ${channel}`);
      try {
        ipcRenderer.on(channel, listener);
        return () => ipcRenderer.removeListener(channel, listener);
      } catch (error) {
        console.error(`Erreur lors de l'enregistrement d'un écouteur pour ${channel}:`, error);
      }
    },
    invoke: (channel, ...args) => {
      console.log(`Appel de ipcRenderer.invoke avec canal: ${channel}, arguments:`, args);
      try {
        return ipcRenderer.invoke(channel, ...args);
      } catch (error) {
        console.error(`Erreur lors de l'appel à ipcRenderer.invoke(${channel}):`, error);
        return Promise.reject(error);
      }
    }
  }
};

// Vérifier la disponibilité des cookies après le chargement du DOM
window.addEventListener('DOMContentLoaded', () => {
  console.log('PRELOAD MAIN: Événement DOMContentLoaded déclenché');

  // Vérifier la disponibilité des cookies
  setTimeout(() => {
    try {
      console.log('Cookies disponibles:', document.cookie);

      // Vérifier si le cookie session_id est présent
      const hasSessionCookie = document.cookie.includes('session_id');
      console.log('Cookie session_id présent:', hasSessionCookie);

      if (hasSessionCookie) {
        const sessionCookie = document.cookie.split(';').find(c => c.trim().startsWith('session_id='));
        if (sessionCookie) {
          const sessionId = sessionCookie.split('=')[1];
          console.log(`Cookie session_id trouvé: ${sessionId.substring(0, 8)}...`);
        }
      }
    } catch (error) {
      console.error('Erreur lors de la vérification des cookies:', error);
    }
  }, 1000);
});

// Vous pouvez également ajouter des écouteurs d'événements ici
// Par exemple, pour écouter les événements du processus principal
// ipcRenderer.on('some-event', (event, ...args) => {
//   // Faire quelque chose avec les arguments
// });
