/**
 * Module de gestion de la dernière adresse IP valide du serveur Odoo
 * Ce module gère le stockage et la récupération de la dernière adresse IP valide
 */

const { app } = require('electron');
const path = require('path');
const fs = require('fs');
const log = require('electron-log');

// Chemin vers le fichier de stockage de la dernière IP valide
const userDataPath = app.getPath('userData');
const lastValidIpPath = path.join(userDataPath, 'last_edara_ip.txt');

/**
 * Sauvegarde la dernière adresse IP valide du serveur Odoo
 * @param {string} ip - L'adresse IP à sauvegarder
 * @returns {boolean} - true si la sauvegarde a réussi, false sinon
 */
function saveLastValidIp(ip) {
  try {
    // Vérifier si l'IP est valide
    if (!ip || !isValidIpAddress(ip)) {
      log.warn(`Tentative de sauvegarde d'une adresse IP invalide: ${ip}`);
      return false;
    }

    // C<PERSON>er le répertoire s'il n'existe pas
    const directory = path.dirname(lastValidIpPath);
    if (!fs.existsSync(directory)) {
      fs.mkdirSync(directory, { recursive: true });
    }

    // Sauvegarder l'IP
    fs.writeFileSync(lastValidIpPath, ip, 'utf8');
    log.info(`Dernière adresse IP valide sauvegardée: ${ip}`);
    return true;
  } catch (error) {
    log.error(`Erreur lors de la sauvegarde de la dernière adresse IP valide: ${error.message}`);
    return false;
  }
}

/**
 * Récupère la dernière adresse IP valide du serveur Odoo
 * @returns {string|null} - La dernière adresse IP valide ou null si aucune n'est disponible
 */
function getLastValidIp() {
  try {
    // Vérifier si le fichier existe
    if (!fs.existsSync(lastValidIpPath)) {
      log.info('Aucune dernière adresse IP valide trouvée');
      return null;
    }

    // Lire l'IP
    const ip = fs.readFileSync(lastValidIpPath, 'utf8').trim();

    // Vérifier si l'IP est valide
    if (!isValidIpAddress(ip)) {
      log.warn(`Adresse IP stockée invalide: ${ip}`);
      return null;
    }

    log.info(`Dernière adresse IP valide récupérée: ${ip}`);
    return ip;
  } catch (error) {
    log.error(`Erreur lors de la récupération de la dernière adresse IP valide: ${error.message}`);
    return null;
  }
}

/**
 * Vérifie si une adresse IP est valide
 * @param {string} ip - L'adresse IP à vérifier
 * @returns {boolean} - true si l'adresse IP est valide, false sinon
 */
function isValidIpAddress(ip) {
  // Vérifier si l'IP est une chaîne de caractères
  if (typeof ip !== 'string') {
    return false;
  }

  // Vérifier si l'IP est au format IPv4
  const ipv4Regex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;
  const match = ip.match(ipv4Regex);

  if (!match) {
    return false;
  }

  // Vérifier que chaque octet est entre 0 et 255
  for (let i = 1; i <= 4; i++) {
    const octet = parseInt(match[i], 10);
    if (octet < 0 || octet > 255) {
      return false;
    }
  }

  return true;
}

/**
 * Supprime le fichier de la dernière adresse IP valide
 * @returns {boolean} - true si la suppression a réussi, false sinon
 */
function clearLastValidIp() {
  try {
    // Vérifier si le fichier existe
    if (!fs.existsSync(lastValidIpPath)) {
      log.info('Aucun fichier de dernière adresse IP valide à supprimer');
      return true;
    }

    // Supprimer le fichier
    fs.unlinkSync(lastValidIpPath);
    log.info('Fichier de dernière adresse IP valide supprimé avec succès');
    return true;
  } catch (error) {
    log.error(`Erreur lors de la suppression du fichier de dernière adresse IP valide: ${error.message}`);
    return false;
  }
}

// Exporter les fonctions
module.exports = {
  saveLastValidIp,
  getLastValidIp,
  clearLastValidIp,
  isValidIpAddress
};
