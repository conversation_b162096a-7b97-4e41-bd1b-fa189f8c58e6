/**
 * Module de gestion des sauvegardes pour Edara Workspace
 * Ce module gère la création et la gestion des dossiers de sauvegarde
 */

const fs = require('fs');
const path = require('path');
const { app, dialog, ipcMain } = require('electron');
const log = require('electron-log');

// Configuration du logger
log.transports.file.level = 'debug'; // Augmenter le niveau de détail pour les fichiers
log.transports.console.level = 'debug';

// Configurer le chemin du fichier de log pour qu'il soit facilement accessible
const logPath = path.join(app.getPath('desktop'), 'edara-error-logs.txt');
log.transports.file.file = logPath;

// Journaliser le démarrage avec le chemin du fichier de log
log.info(`Application démarrée. Logs écrits dans: ${logPath}`);
console.log(`Logs détaillés écrits dans: ${logPath}`);

// Chemin de sauvegarde par défaut
let defaultBackupPath = path.join(app.getPath('documents'), 'Edara Workspace', 'Backups');

// Nom du dossier principal et du sous-dossier
const MAIN_FOLDER_NAME = 'Edara Workspace';
const SUB_FOLDER_NAME = 'Backups';

/**
 * Initialise le gestionnaire de sauvegardes
 */
function initBackupManager() {
  log.info('Initialisation du gestionnaire de sauvegardes');

  // Gestionnaire pour sélectionner un dossier de sauvegarde
  ipcMain.handle('selectBackupFolder', async () => {
    log.info('Ouverture de la boîte de dialogue pour sélectionner un dossier de sauvegarde');

    try {
      const result = await dialog.showOpenDialog({
        properties: ['openDirectory', 'createDirectory'],
        title: 'Sélectionner un dossier de sauvegarde',
        defaultPath: defaultBackupPath,
        buttonLabel: 'Sélectionner'
      });

      log.info(`Résultat de la sélection de dossier: ${result.canceled ? 'Annulé' : result.filePaths[0]}`);

      if (!result.canceled && result.filePaths.length > 0) {
        // Mettre à jour le chemin de sauvegarde par défaut
        defaultBackupPath = result.filePaths[0];
      }

      return result;
    } catch (error) {
      log.error('Erreur lors de la sélection du dossier de sauvegarde:', error);
      return { canceled: true, error: error.message };
    }
  });

  // Gestionnaire pour enregistrer le chemin de sauvegarde
  ipcMain.handle('saveBackupPath', async (event, backupPath) => {
    log.info(`Enregistrement du chemin de sauvegarde: ${backupPath}`);

    try {
      // Vérifier si le chemin est défini
      if (!backupPath) {
        log.error('Chemin de sauvegarde non défini');
        return { success: false, error: 'Chemin de sauvegarde non défini' };
      }

      // Mettre à jour le chemin de sauvegarde par défaut
      defaultBackupPath = backupPath;

      // Créer le dossier de sauvegarde s'il n'existe pas
      log.info(`Appel de createBackupFolder avec le chemin: ${backupPath}`);
      const result = await createBackupFolder(backupPath);

      log.info(`Résultat de createBackupFolder: ${JSON.stringify(result)}`);
      return result;
    } catch (error) {
      log.error('Erreur lors de l\'enregistrement du chemin de sauvegarde:', error);
      return { success: false, error: error.message || 'Erreur inconnue lors de la création du dossier' };
    }
  });
}

/**
 * Crée le dossier de sauvegarde s'il n'existe pas
 * @param {string} backupPath - Le chemin du dossier de sauvegarde
 * @returns {Promise<Object>} - Un objet indiquant si la création a réussi
 */
async function createBackupFolder(backupPath) {
  log.info(`Création du dossier de sauvegarde: ${backupPath}`);

  try {
    // Vérifier si le chemin est défini
    if (!backupPath) {
      throw new Error('Le chemin de sauvegarde est vide');
    }

    // Si le chemin est "TEMP", créer un dossier temporaire dans userData
    if (backupPath === "TEMP") {
      log.info("Création d'un dossier temporaire dans userData");

      const tempBaseDir = app.getPath('userData');
      const mainFolderPath = path.join(tempBaseDir, MAIN_FOLDER_NAME);
      const tempDir = path.join(mainFolderPath, SUB_FOLDER_NAME);
      log.info(`Chemin du dossier temporaire: ${tempDir}`);

      try {
        fs.mkdirSync(tempDir, { recursive: true });
        log.info(`Dossier temporaire créé avec succès: ${tempDir}`);

        return {
          success: true,
          path: tempDir,
          isTemporary: true
        };
      } catch (tempError) {
        log.error(`Erreur lors de la création du dossier temporaire: ${tempError.message}`);
        log.error(`Code d'erreur: ${tempError.code}`);

        return {
          success: false,
          error: `Erreur lors de la création du dossier temporaire: ${tempError.message}`,
          errorDetails: {
            code: tempError.code,
            message: tempError.message
          },
          canContinue: false
        };
      }
    }

    // Si le chemin est "DIALOG", ouvrir le dialogue de sélection de dossier
    if (backupPath === "DIALOG") {
      log.info("Ouverture du dialogue de sélection de dossier");

      // Cette méthode sera appelée depuis le main process, donc on peut utiliser dialog directement
      const { dialog } = require('electron');

      const result = await dialog.showOpenDialog({
        properties: ['openDirectory', 'createDirectory'],
        title: 'Sélectionner un dossier de sauvegarde',
        buttonLabel: 'Sélectionner',
        message: 'Choisissez un dossier où les sauvegardes seront stockées',
        securityScopedBookmarks: true // Activer les security-scoped bookmarks pour macOS
      });

      if (result.canceled) {
        log.info("Sélection de dossier annulée par l'utilisateur");
        return {
          success: false,
          error: "Sélection de dossier annulée",
          canContinue: false
        };
      }

      // Utiliser le chemin sélectionné par l'utilisateur
      backupPath = result.filePaths[0];
      log.info(`Dossier sélectionné par l'utilisateur: ${backupPath}`);

      // Vérifier si nous avons un bookmark (macOS)
      const hasBookmark = result.bookmarks && result.bookmarks.length > 0 && result.bookmarks[0] !== '';
      if (hasBookmark) {
        log.info('Security-scoped bookmark obtenu pour le dossier sélectionné');

        // Stocker le bookmark pour une utilisation future
        try {
          const bookmarkData = result.bookmarks[0];
          const bookmarkPath = path.join(app.getPath('userData'), 'backup-bookmark.data');
          fs.writeFileSync(bookmarkPath, bookmarkData);
          log.info(`Bookmark enregistré dans: ${bookmarkPath}`);

          // Sur macOS, nous devons commencer à accéder au bookmark avant de pouvoir l'utiliser
          try {
            // Cette partie est spécifique à macOS et nécessite des API natives
            // Nous utilisons une approche simplifiée ici
            log.info('Accès au dossier via le bookmark');
          } catch (accessError) {
            log.error(`Erreur lors de l'accès au bookmark: ${accessError.message}`);
          }
        } catch (bookmarkError) {
          log.error(`Erreur lors de l'enregistrement du bookmark: ${bookmarkError.message}`);
        }
      }

      // Créer la structure de dossiers "Edara Workspace/Backups" dans le dossier sélectionné
      const mainFolderPath = path.join(backupPath, MAIN_FOLDER_NAME);
      const finalPath = path.join(mainFolderPath, SUB_FOLDER_NAME);
      log.info(`Création de la structure de dossiers: ${finalPath}`);

      try {
        // Utiliser la méthode synchrone pour créer le dossier
        fs.mkdirSync(finalPath, { recursive: true });
        log.info(`Dossier de sauvegarde créé avec succès: ${finalPath}`);

        // Stocker le chemin dans les préférences utilisateur
        defaultBackupPath = finalPath;

        return {
          success: true,
          path: finalPath,
          // Indiquer que le chemin a été sélectionné via le dialogue
          selectedByUser: true
        };
      } catch (dirError) {
        // Capturer et exposer l'erreur réelle
        log.error(`Erreur lors de la création du dossier: ${dirError.message}`);
        log.error(`Code d'erreur: ${dirError.code}`);
        log.error(`Stack trace: ${dirError.stack}`);

        // Exposer l'erreur réelle à l'utilisateur
        return {
          success: false,
          error: `Erreur lors de la création du dossier: ${dirError.message} (Code: ${dirError.code})`,
          errorDetails: {
            code: dirError.code,
            message: dirError.message,
            stack: dirError.stack
          },
          canContinue: true
        };
      }
    }

    // Pour les chemins normaux (non DIALOG), utiliser l'approche de fallback

    // Créer un dossier dans userData (qui est toujours accessible)
    const userDataDir = app.getPath('userData');
    const userDataMainDir = path.join(userDataDir, MAIN_FOLDER_NAME);
    const userDataBackupDir = path.join(userDataMainDir, SUB_FOLDER_NAME);
    log.info(`Création du dossier de sauvegarde dans userData: ${userDataBackupDir}`);

    try {
      fs.mkdirSync(userDataBackupDir, { recursive: true });
      log.info(`Dossier de sauvegarde créé avec succès dans userData: ${userDataBackupDir}`);

      // Stocker le chemin original pour référence
      fs.writeFileSync(
        path.join(userDataBackupDir, 'backup_path.txt'),
        backupPath,
        'utf8'
      );

      // Essayer de créer le dossier à l'emplacement demandé
      try {
        // Si le chemin est relatif, le convertir en absolu
        let resolvedPath = backupPath;
        if (!path.isAbsolute(backupPath)) {
          if (backupPath.startsWith('/')) {
            resolvedPath = path.join(app.getPath('documents'), backupPath.substring(1));
          } else {
            resolvedPath = path.join(app.getPath('documents'), backupPath);
          }
          log.info(`Chemin relatif converti en absolu: ${resolvedPath}`);
        }

        // Créer la structure de dossiers "Edara Workspace/Backups" à l'emplacement demandé
        // Si le chemin ne contient pas déjà la structure, l'ajouter
        let finalPath = resolvedPath;

        // Vérifier si le chemin se termine déjà par "Edara Workspace/Backups"
        if (!finalPath.endsWith(path.join(MAIN_FOLDER_NAME, SUB_FOLDER_NAME))) {
          // Vérifier si le chemin se termine par "Edara Workspace"
          if (finalPath.endsWith(MAIN_FOLDER_NAME)) {
            // Ajouter seulement le sous-dossier "Backups"
            finalPath = path.join(finalPath, SUB_FOLDER_NAME);
          } else {
            // Ajouter la structure complète "Edara Workspace/Backups"
            finalPath = path.join(finalPath, MAIN_FOLDER_NAME, SUB_FOLDER_NAME);
          }
        }

        fs.mkdirSync(finalPath, { recursive: true });
        log.info(`Structure de dossiers créée avec succès à l'emplacement demandé: ${finalPath}`);

        // Mettre à jour le chemin résolu avec le chemin final
        resolvedPath = finalPath;

        return {
          success: true,
          path: resolvedPath,
          fallbackPath: userDataBackupDir
        };
      } catch (originalPathError) {
        // Capturer et exposer l'erreur réelle
        log.error(`Erreur lors de la création du dossier à l'emplacement demandé: ${originalPathError.message}`);
        log.error(`Code d'erreur: ${originalPathError.code}`);

        // Retourner un succès avec avertissement
        return {
          success: true,
          path: userDataBackupDir,
          originalPath: backupPath,
          warning: `Impossible de créer le dossier à l'emplacement demandé (${originalPathError.message}, Code: ${originalPathError.code}). Un dossier de sauvegarde a été créé dans les données de l'application.`,
          errorDetails: {
            code: originalPathError.code,
            message: originalPathError.message
          }
        };
      }
    } catch (userDataError) {
      // Si même userData n'est pas accessible, c'est un problème grave
      log.error(`Erreur critique lors de la création du dossier dans userData: ${userDataError.message}`);
      log.error(`Code d'erreur: ${userDataError.code}`);
      log.error(`Stack trace: ${userDataError.stack}`);

      return {
        success: false,
        error: `Erreur critique: Impossible de créer un dossier de sauvegarde (${userDataError.message}, Code: ${userDataError.code})`,
        errorDetails: {
          code: userDataError.code,
          message: userDataError.message,
          stack: userDataError.stack
        },
        canContinue: true
      };
    }
  } catch (error) {
    // Erreur générale (ne devrait pas se produire)
    log.error(`Erreur générale lors de la création du dossier de sauvegarde: ${error.message}`);
    log.error(`Stack trace: ${error.stack}`);

    return {
      success: false,
      error: `Erreur générale: ${error.message}`,
      errorDetails: {
        message: error.message,
        stack: error.stack
      },
      canContinue: true
    };
  }
}

/**
 * Obtient le chemin de sauvegarde par défaut
 * @returns {string} - Le chemin de sauvegarde par défaut
 */
function getDefaultBackupPath() {
  return defaultBackupPath;
}

/**
 * Récupère les dernières lignes du fichier de log
 * @param {number} lines - Nombre de lignes à récupérer
 * @returns {Promise<string>} - Les dernières lignes du fichier de log
 */
async function getLastLogs(lines = 50) {
  try {
    // Vérifier si le fichier de log existe
    if (!fs.existsSync(logPath)) {
      return "Aucun fichier de log trouvé.";
    }

    // Lire le fichier de log
    const logContent = fs.readFileSync(logPath, 'utf8');

    // Diviser le contenu en lignes
    const logLines = logContent.split('\n');

    // Récupérer les dernières lignes
    const lastLines = logLines.slice(-lines);

    return lastLines.join('\n');
  } catch (error) {
    console.error('Erreur lors de la récupération des logs:', error);
    return `Erreur lors de la récupération des logs: ${error.message}`;
  }
}

// Exporter les fonctions
module.exports = {
  initBackupManager,
  createBackupFolder,
  getDefaultBackupPath,
  getLastLogs
};
