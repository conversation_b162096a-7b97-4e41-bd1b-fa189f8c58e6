/**
 * Gestionnaire Intégré pour Mode Local
 * Combine toutes les solutions pour éliminer ERR_CONTENT_LENGTH_MISMATCH en mode local
 */

const { BrowserWindow, session } = require('electron');
const log = require('electron-log');
const { EventEmitter } = require('events');

const LocalModeContentLengthFix = require('./local-mode-content-length-fix');
const RobustSessionCacheManager = require('./robust-session-cache-manager');
const ElectronDiagnosticManager = require('./electron-diagnostic-manager');

class IntegratedLocalModeManager extends EventEmitter {
  constructor() {
    super();
    
    this.config = {
      // Activation des composants
      enableContentLengthFix: true,
      enableRobustCaching: true,
      enableDiagnostic: true,
      enableAutoRecovery: true,
      
      // Configuration du mode local
      autoDetectLocalMode: true,
      forceLocalModeOptimizations: false,
      
      // Stratégies de récupération
      maxRecoveryAttempts: 3,
      recoveryDelay: 2000,
      
      // Debug et logging
      debug: true,
      logLevel: 'info'
    };
    
    this.state = {
      isInitialized: false,
      localModeActive: false,
      recoveryAttempts: 0,
      lastRecoveryTime: 0,
      currentWindow: null
    };
    
    // Composants spécialisés
    this.contentLengthFix = null;
    this.sessionCacheManager = null;
    this.diagnosticManager = null;
    
    // Métriques globales
    this.metrics = {
      totalErrors: 0,
      resolvedErrors: 0,
      recoverySuccessRate: 0,
      averageRecoveryTime: 0
    };
  }

  /**
   * Initialise le gestionnaire intégré
   */
  async initialize() {
    try {
      log.info('🚀 [IntegratedManager] Initialisation du gestionnaire intégré pour mode local...');
      
      // Initialiser les composants
      await this.initializeComponents();
      
      // Configurer les événements
      this.setupEventHandlers();
      
      // Démarrer la surveillance
      this.startMonitoring();
      
      this.state.isInitialized = true;
      log.info('✅ [IntegratedManager] Gestionnaire intégré initialisé avec succès');
      
      this.emit('initialized');
    } catch (error) {
      log.error(`❌ [IntegratedManager] Erreur lors de l'initialisation: ${error.message}`);
      throw error;
    }
  }

  /**
   * Initialise tous les composants spécialisés
   */
  async initializeComponents() {
    // 1. Gestionnaire de correction Content-Length
    if (this.config.enableContentLengthFix) {
      this.contentLengthFix = new LocalModeContentLengthFix();
      await this.contentLengthFix.initialize();
      log.info('✅ [IntegratedManager] Gestionnaire Content-Length initialisé');
    }
    
    // 2. Gestionnaire de session et cache robuste
    if (this.config.enableRobustCaching) {
      this.sessionCacheManager = new RobustSessionCacheManager();
      log.info('✅ [IntegratedManager] Gestionnaire de session/cache initialisé');
    }
    
    // 3. Gestionnaire de diagnostic
    if (this.config.enableDiagnostic) {
      this.diagnosticManager = new ElectronDiagnosticManager();
      this.diagnosticManager.startRealTimeDiagnostic();
      log.info('✅ [IntegratedManager] Gestionnaire de diagnostic initialisé');
    }
  }

  /**
   * Configure les gestionnaires d'événements
   */
  setupEventHandlers() {
    // Événements du gestionnaire Content-Length
    if (this.contentLengthFix) {
      this.contentLengthFix.on('network-error', (error) => {
        this.handleContentLengthError(error);
      });
      
      this.contentLengthFix.on('initialized', () => {
        log.info('🔧 [IntegratedManager] Content-Length Fix prêt');
      });
    }
    
    // Événements du gestionnaire de diagnostic
    if (this.diagnosticManager) {
      this.diagnosticManager.on('content-length-error', (error) => {
        this.handleContentLengthError(error);
      });
      
      this.diagnosticManager.on('critical-alert', (alert) => {
        this.handleCriticalAlert(alert);
      });
      
      this.diagnosticManager.on('network-alert', (alert) => {
        this.handleNetworkAlert(alert);
      });
    }
    
    // Événements du gestionnaire de session
    if (this.sessionCacheManager) {
      this.sessionCacheManager.on('clearing-completed', (result) => {
        log.info(`🧹 [IntegratedManager] Nettoyage terminé: ${result.attempts} tentative(s)`);
      });
    }
  }

  /**
   * Démarre la surveillance automatique
   */
  startMonitoring() {
    // Surveillance périodique de la santé du système
    setInterval(() => {
      this.performHealthCheck();
    }, 60000); // Toutes les minutes
    
    log.info('👁️ [IntegratedManager] Surveillance démarrée');
  }

  /**
   * Crée une BrowserWindow optimisée pour le mode local
   */
  createOptimizedBrowserWindow(options = {}) {
    const defaultOptions = {
      width: 1200,
      height: 800,
      show: false,
      webPreferences: {
        // Configuration de sécurité optimisée
        webSecurity: true,              // Réactiver webSecurity
        contextIsolation: true,         // Activer l'isolation
        nodeIntegration: false,         // Désactiver nodeIntegration
        
        // Session optimisée
        session: this.getOptimalSession(),
        
        // Optimisations spécifiques
        backgroundThrottling: false,
        offscreen: false,
        
        // Permissions
        allowRunningInsecureContent: false,
        experimentalFeatures: false,
        
        // Preload script si nécessaire
        preload: options.preload
      }
    };
    
    // Fusionner avec les options fournies
    const finalOptions = { ...defaultOptions, ...options };
    
    // Utiliser les webPreferences optimisées du Content-Length Fix si disponible
    if (this.contentLengthFix) {
      finalOptions.webPreferences = {
        ...finalOptions.webPreferences,
        ...this.contentLengthFix.getOptimizedWebPreferences()
      };
    }
    
    const window = new BrowserWindow(finalOptions);
    this.state.currentWindow = window;
    
    // Configurer les événements de la fenêtre
    this.setupWindowEventHandlers(window);
    
    log.info('🪟 [IntegratedManager] Fenêtre optimisée créée');
    return window;
  }

  /**
   * Configure les gestionnaires d'événements de la fenêtre
   */
  setupWindowEventHandlers(window) {
    // Événement de chargement terminé
    window.webContents.on('did-finish-load', () => {
      log.info('✅ [IntegratedManager] Page chargée avec succès');
      this.state.recoveryAttempts = 0; // Réinitialiser les tentatives
    });
    
    // Événement d'échec de chargement
    window.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
      log.error(`❌ [IntegratedManager] Échec de chargement: ${errorCode} - ${errorDescription}`);
      this.handleLoadFailure(window, errorCode, errorDescription, validatedURL);
    });
    
    // Événement de crash du processus de rendu
    window.webContents.on('render-process-gone', (event, details) => {
      log.error(`💥 [IntegratedManager] Crash du processus de rendu: ${details.reason}`);
      this.handleRenderProcessCrash(window, details);
    });
    
    // Événement d'erreur de certificat
    window.webContents.on('certificate-error', (event, url, error, certificate, callback) => {
      if (this.isLocalUrl(url)) {
        // Accepter les certificats auto-signés pour les URLs locales
        event.preventDefault();
        callback(true);
        log.warn(`⚠️ [IntegratedManager] Certificat auto-signé accepté pour: ${url}`);
      } else {
        callback(false);
      }
    });
  }

  /**
   * Gère les erreurs Content-Length
   */
  async handleContentLengthError(error) {
    this.metrics.totalErrors++;
    
    log.warn(`⚠️ [IntegratedManager] Erreur Content-Length détectée: ${error.url}`);
    
    if (this.config.enableAutoRecovery && this.state.recoveryAttempts < this.config.maxRecoveryAttempts) {
      await this.performAutoRecovery(error);
    } else {
      log.error(`❌ [IntegratedManager] Nombre maximum de tentatives de récupération atteint`);
      this.emit('recovery-failed', error);
    }
  }

  /**
   * Effectue une récupération automatique
   */
  async performAutoRecovery(error) {
    this.state.recoveryAttempts++;
    this.state.lastRecoveryTime = Date.now();
    
    const startTime = Date.now();
    
    try {
      log.info(`🔄 [IntegratedManager] Tentative de récupération ${this.state.recoveryAttempts}/${this.config.maxRecoveryAttempts}...`);
      
      // Étape 1: Nettoyage agressif du cache et des données
      if (this.sessionCacheManager) {
        await this.sessionCacheManager.performRobustClearing({
          includeCache: true,
          includeStorageData: true,
          includeCookies: true,
          includeAuthCache: true,
          recreateSession: true,
          validateResults: true
        });
      }
      
      // Étape 2: Réinitialisation du gestionnaire Content-Length
      if (this.contentLengthFix) {
        await this.contentLengthFix.forceReset();
      }
      
      // Étape 3: Attendre avant de recharger
      await this.delay(this.config.recoveryDelay);
      
      // Étape 4: Recharger la page si une fenêtre est disponible
      if (this.state.currentWindow && !this.state.currentWindow.isDestroyed()) {
        const currentUrl = this.state.currentWindow.webContents.getURL();
        if (currentUrl && this.isLocalUrl(currentUrl)) {
          // Ajouter un paramètre de cache-busting
          const url = new URL(currentUrl);
          url.searchParams.set('recovery', Date.now().toString());
          
          log.info(`🔄 [IntegratedManager] Rechargement avec URL optimisée: ${url.toString()}`);
          await this.state.currentWindow.loadURL(url.toString());
        }
      }
      
      const recoveryTime = Date.now() - startTime;
      this.metrics.resolvedErrors++;
      this.updateRecoveryMetrics(recoveryTime);
      
      log.info(`✅ [IntegratedManager] Récupération réussie en ${recoveryTime}ms`);
      this.emit('recovery-success', { error, recoveryTime, attempts: this.state.recoveryAttempts });
      
    } catch (recoveryError) {
      log.error(`❌ [IntegratedManager] Erreur lors de la récupération: ${recoveryError.message}`);
      this.emit('recovery-error', { originalError: error, recoveryError });
    }
  }

  /**
   * Gère les alertes critiques
   */
  handleCriticalAlert(alert) {
    log.error(`🚨 [IntegratedManager] Alerte critique: ${alert.message}`);
    
    // Déclencher une récupération automatique pour les alertes Content-Length
    if (alert.type === 'content-length' && this.config.enableAutoRecovery) {
      this.performAutoRecovery({ url: 'system', error: alert.message });
    }
    
    this.emit('critical-alert', alert);
  }

  /**
   * Gère les alertes réseau
   */
  handleNetworkAlert(alert) {
    log.warn(`⚠️ [IntegratedManager] Alerte réseau: ${alert.message}`);
    this.emit('network-alert', alert);
  }

  /**
   * Gère les échecs de chargement
   */
  async handleLoadFailure(window, errorCode, errorDescription, validatedURL) {
    if (this.isContentLengthError(errorDescription)) {
      await this.handleContentLengthError({
        url: validatedURL,
        error: errorDescription,
        errorCode
      });
    }
  }

  /**
   * Gère les crashes du processus de rendu
   */
  async handleRenderProcessCrash(window, details) {
    if (details.reason === 'crashed' || details.reason === 'oom') {
      log.info('🔄 [IntegratedManager] Tentative de récupération après crash...');
      
      // Nettoyage agressif
      if (this.sessionCacheManager) {
        await this.sessionCacheManager.performRobustClearing({
          includeCache: true,
          includeStorageData: true,
          recreateSession: true
        });
      }
      
      // Recharger la page
      if (!window.isDestroyed()) {
        window.reload();
      }
    }
  }

  /**
   * Effectue une vérification de santé
   */
  async performHealthCheck() {
    try {
      const healthReport = {
        timestamp: Date.now(),
        components: {},
        overall: 'healthy'
      };
      
      // Vérifier le gestionnaire Content-Length
      if (this.contentLengthFix) {
        const stats = this.contentLengthFix.getStats();
        healthReport.components.contentLengthFix = {
          active: stats.isActive,
          localModeDetected: stats.localModeDetected,
          requestCount: stats.requestCount,
          fixedRequests: stats.fixedRequests,
          errorCount: stats.errorCount
        };
        
        if (stats.errorCount > 10) {
          healthReport.overall = 'warning';
        }
      }
      
      // Vérifier le gestionnaire de session
      if (this.sessionCacheManager) {
        const stats = this.sessionCacheManager.getStats();
        healthReport.components.sessionCache = {
          isClearing: stats.isClearing,
          clearingAttempts: stats.clearingAttempts,
          sessionRecreated: stats.sessionRecreated
        };
      }
      
      // Vérifier le gestionnaire de diagnostic
      if (this.diagnosticManager) {
        const report = this.diagnosticManager.generateDiagnosticReport();
        healthReport.components.diagnostic = {
          totalRequests: report.summary.totalRequests,
          totalErrors: report.summary.totalErrors,
          averageResponseTime: report.summary.averageResponseTime,
          contentLengthIssues: report.contentLength.recentIssues
        };
        
        if (report.contentLength.recentIssues > 0) {
          healthReport.overall = 'critical';
        }
      }
      
      // Émettre le rapport de santé
      this.emit('health-report', healthReport);
      
      if (this.config.debug && healthReport.overall !== 'healthy') {
        log.warn(`⚠️ [IntegratedManager] État de santé: ${healthReport.overall}`);
      }
      
    } catch (error) {
      log.error(`❌ [IntegratedManager] Erreur lors de la vérification de santé: ${error.message}`);
    }
  }

  /**
   * Obtient la session optimale à utiliser
   */
  getOptimalSession() {
    if (this.sessionCacheManager) {
      return this.sessionCacheManager.getOptimalSession();
    }
    return session.defaultSession;
  }

  /**
   * Met à jour les métriques de récupération
   */
  updateRecoveryMetrics(recoveryTime) {
    const totalRecoveries = this.metrics.resolvedErrors;
    const currentAvg = this.metrics.averageRecoveryTime;
    
    this.metrics.averageRecoveryTime = 
      (currentAvg * (totalRecoveries - 1) + recoveryTime) / totalRecoveries;
    
    this.metrics.recoverySuccessRate = 
      this.metrics.resolvedErrors / this.metrics.totalErrors;
  }

  /**
   * Vérifie si une URL est locale
   */
  isLocalUrl(url) {
    return url.includes('localhost') ||
           url.includes('127.0.0.1') ||
           url.includes('192.168.') ||
           url.includes('10.') ||
           url.includes('172.');
  }

  /**
   * Vérifie si une erreur est liée au Content-Length
   */
  isContentLengthError(errorDescription) {
    return errorDescription.includes('CONTENT_LENGTH_MISMATCH') ||
           errorDescription.includes('ERR_CONTENT_LENGTH_MISMATCH');
  }

  /**
   * Obtient les statistiques complètes
   */
  getComprehensiveStats() {
    return {
      state: this.state,
      metrics: this.metrics,
      components: {
        contentLengthFix: this.contentLengthFix?.getStats(),
        sessionCache: this.sessionCacheManager?.getStats(),
        diagnostic: this.diagnosticManager?.generateDiagnosticReport()
      }
    };
  }

  /**
   * Force une réinitialisation complète
   */
  async forceCompleteReset() {
    log.info('🔄 [IntegratedManager] Réinitialisation complète forcée...');
    
    try {
      // Réinitialiser tous les composants
      if (this.contentLengthFix) {
        await this.contentLengthFix.forceReset();
      }
      
      if (this.sessionCacheManager) {
        await this.sessionCacheManager.performRobustClearing({
          includeCache: true,
          includeStorageData: true,
          includeCookies: true,
          includeAuthCache: true,
          recreateSession: true,
          validateResults: true
        });
      }
      
      // Réinitialiser les métriques
      this.metrics = {
        totalErrors: 0,
        resolvedErrors: 0,
        recoverySuccessRate: 0,
        averageRecoveryTime: 0
      };
      
      this.state.recoveryAttempts = 0;
      
      log.info('✅ [IntegratedManager] Réinitialisation complète terminée');
      this.emit('complete-reset');
      
    } catch (error) {
      log.error(`❌ [IntegratedManager] Erreur lors de la réinitialisation: ${error.message}`);
      throw error;
    }
  }

  /**
   * Utilitaire de délai
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Nettoie toutes les ressources
   */
  cleanup() {
    // Nettoyer les composants
    if (this.contentLengthFix) {
      this.contentLengthFix.cleanup();
    }
    
    if (this.sessionCacheManager) {
      this.sessionCacheManager.cleanup();
    }
    
    if (this.diagnosticManager) {
      this.diagnosticManager.cleanup();
    }
    
    // Nettoyer les événements
    this.removeAllListeners();
    
    this.state.isInitialized = false;
    log.info('🧹 [IntegratedManager] Gestionnaire intégré nettoyé');
  }
}

module.exports = IntegratedLocalModeManager;
