/**
 * Gestionnaire d'événements de session centralisé pour Edara ERP
 * Coordonne les événements entre le gestionnaire de session, la récupération et l'interface utilisateur
 */

const log = require('electron-log');
const { BrowserWindow } = require('electron');

class SessionEventHandler {
  constructor(sessionManager, recoveryManager, mainWindow) {
    this.sessionManager = sessionManager;
    this.recoveryManager = recoveryManager;
    this.mainWindow = mainWindow;
    
    // État des notifications
    this.notificationState = {
      isShowingRecovery: false,
      lastNotification: null,
      notificationTimeout: null
    };
    
    this.setupEventListeners();
  }

  /**
   * Configure tous les écouteurs d'événements
   */
  setupEventListeners() {
    // Événements du gestionnaire de session
    this.sessionManager.on('session-established', this.onSessionEstablished.bind(this));
    this.sessionManager.on('session-expired', this.onSessionExpired.bind(this));
    this.sessionManager.on('session-invalid', this.onSessionInvalid.bind(this));
    this.sessionManager.on('session-cleared', this.onSessionCleared.bind(this));
    this.sessionManager.on('session-validated', this.onSessionValidated.bind(this));
    this.sessionManager.on('authentication-failed', this.onAuthenticationFailed.bind(this));
    
    // Événements du gestionnaire de récupération
    this.recoveryManager.on('recovery-started', this.onRecoveryStarted.bind(this));
    this.recoveryManager.on('recovery-completed', this.onRecoveryCompleted.bind(this));
    this.recoveryManager.on('recovery-failed', this.onRecoveryFailed.bind(this));
    this.recoveryManager.on('session-renewed', this.onSessionRenewed.bind(this));
    this.recoveryManager.on('connection-lost', this.onConnectionLost.bind(this));
    this.recoveryManager.on('authentication-failed-final', this.onAuthenticationFailedFinal.bind(this));
    
    log.info('Gestionnaire d\'événements de session initialisé');
  }

  /**
   * Gère l'établissement d'une nouvelle session
   */
  onSessionEstablished(sessionData) {
    log.info(`Session établie pour ${sessionData.username} (UID: ${sessionData.userId})`);
    
    // Masquer toute notification de récupération en cours
    this.hideRecoveryNotification();
    
    // Notifier l'interface utilisateur
    this.sendToRenderer('session-event', {
      type: 'session-established',
      data: {
        username: sessionData.username,
        userId: sessionData.userId,
        serverUrl: sessionData.serverUrl,
        timestamp: Date.now()
      },
      message: `Connecté en tant que ${sessionData.username}`
    });
    
    // Afficher une notification de succès temporaire
    this.showTemporaryNotification('Connexion réussie', 'success', 3000);
  }

  /**
   * Gère l'expiration de session
   */
  onSessionExpired() {
    log.warn('Session expirée détectée');
    
    this.sendToRenderer('session-event', {
      type: 'session-expired',
      message: 'Votre session a expiré. Tentative de reconnexion automatique...',
      timestamp: Date.now()
    });
    
    // Afficher une notification d'expiration
    this.showPersistentNotification('Session expirée', 'warning');
  }

  /**
   * Gère les sessions invalides
   */
  onSessionInvalid() {
    log.warn('Session invalide détectée');
    
    this.sendToRenderer('session-event', {
      type: 'session-invalid',
      message: 'Session invalide détectée. Reconnexion en cours...',
      timestamp: Date.now()
    });
  }

  /**
   * Gère le nettoyage de session
   */
  onSessionCleared() {
    log.info('Session nettoyée');
    
    this.hideRecoveryNotification();
    
    this.sendToRenderer('session-event', {
      type: 'session-cleared',
      message: 'Session fermée',
      timestamp: Date.now()
    });
  }

  /**
   * Gère la validation de session
   */
  onSessionValidated(sessionData) {
    // Validation silencieuse - pas de notification utilisateur
    log.debug(`Session validée pour ${sessionData.username}`);
    
    this.sendToRenderer('session-event', {
      type: 'session-validated',
      data: sessionData,
      timestamp: Date.now()
    });
  }

  /**
   * Gère les échecs d'authentification
   */
  onAuthenticationFailed(event) {
    log.warn(`Échec d'authentification: ${event.error}`);
    
    this.sendToRenderer('session-event', {
      type: 'authentication-failed',
      error: event.error,
      message: 'Échec de l\'authentification. Veuillez vérifier vos identifiants.',
      timestamp: Date.now()
    });
    
    this.showTemporaryNotification('Échec de connexion', 'error', 5000);
  }

  /**
   * Gère le démarrage de la récupération
   */
  onRecoveryStarted({ reason }) {
    log.info(`Récupération de session démarrée: ${reason}`);
    
    this.notificationState.isShowingRecovery = true;
    
    const messages = {
      'session-expired': 'Renouvellement de session en cours...',
      'session-invalid': 'Reconnexion en cours...',
      'connection-lost': 'Reconnexion après perte de réseau...'
    };
    
    const message = messages[reason] || 'Récupération de session en cours...';
    
    this.sendToRenderer('session-event', {
      type: 'recovery-started',
      reason,
      message,
      timestamp: Date.now()
    });
    
    this.showPersistentNotification(message, 'info');
  }

  /**
   * Gère la réussite de la récupération
   */
  onRecoveryCompleted({ duration, attempts }) {
    log.info(`Récupération réussie en ${duration}ms après ${attempts} tentative(s)`);
    
    this.hideRecoveryNotification();
    
    this.sendToRenderer('session-event', {
      type: 'recovery-completed',
      data: { duration, attempts },
      message: 'Reconnexion réussie',
      timestamp: Date.now()
    });
    
    this.showTemporaryNotification('Reconnexion réussie', 'success', 3000);
  }

  /**
   * Gère l'échec de la récupération
   */
  onRecoveryFailed({ reason, duration, attempts }) {
    log.error(`Récupération échouée après ${duration}ms et ${attempts} tentative(s): ${reason}`);
    
    this.hideRecoveryNotification();
    
    this.sendToRenderer('session-event', {
      type: 'recovery-failed',
      reason,
      data: { duration, attempts },
      message: 'Impossible de restaurer la session. Veuillez vous reconnecter.',
      timestamp: Date.now()
    });
    
    this.showTemporaryNotification('Reconnexion échouée', 'error', 5000);
    
    // Rediriger vers l'écran de connexion après un délai
    setTimeout(() => {
      this.loadLoginScreen();
    }, 2000);
  }

  /**
   * Gère le renouvellement de session
   */
  onSessionRenewed() {
    log.info('Session renouvelée avec succès');
    
    this.sendToRenderer('session-event', {
      type: 'session-renewed',
      message: 'Session renouvelée',
      timestamp: Date.now()
    });
    
    this.showTemporaryNotification('Session renouvelée', 'success', 2000);
  }

  /**
   * Gère la perte de connexion
   */
  onConnectionLost({ timestamp }) {
    log.warn('Perte de connexion réseau détectée');
    
    this.sendToRenderer('session-event', {
      type: 'connection-lost',
      message: 'Connexion réseau perdue. Tentative de reconnexion...',
      timestamp
    });
    
    this.showPersistentNotification('Connexion perdue', 'warning');
  }

  /**
   * Gère l'échec final d'authentification
   */
  onAuthenticationFailedFinal(event) {
    log.error(`Échec final d'authentification: ${event.error}`);
    
    this.hideRecoveryNotification();
    
    this.sendToRenderer('session-event', {
      type: 'authentication-failed-final',
      error: event.error,
      message: 'Impossible de vous authentifier. Veuillez vérifier vos identifiants.',
      timestamp: Date.now()
    });
    
    this.showTemporaryNotification('Authentification impossible', 'error', 5000);
    
    // Rediriger vers l'écran de connexion
    setTimeout(() => {
      this.loadLoginScreen();
    }, 1000);
  }

  /**
   * Envoie un message au processus de rendu
   */
  sendToRenderer(channel, data) {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      try {
        this.mainWindow.webContents.send(channel, data);
      } catch (error) {
        log.error(`Erreur lors de l'envoi au renderer: ${error.message}`);
      }
    }
  }

  /**
   * Affiche une notification temporaire
   */
  showTemporaryNotification(message, type = 'info', duration = 3000) {
    this.clearNotificationTimeout();
    
    this.notificationState.lastNotification = { message, type, temporary: true };
    
    this.sendToRenderer('notification', {
      message,
      type,
      temporary: true,
      duration
    });
    
    // Auto-masquer après la durée spécifiée
    this.notificationState.notificationTimeout = setTimeout(() => {
      this.hideNotification();
    }, duration);
  }

  /**
   * Affiche une notification persistante
   */
  showPersistentNotification(message, type = 'info') {
    this.clearNotificationTimeout();
    
    this.notificationState.lastNotification = { message, type, temporary: false };
    
    this.sendToRenderer('notification', {
      message,
      type,
      temporary: false
    });
  }

  /**
   * Masque la notification de récupération
   */
  hideRecoveryNotification() {
    if (this.notificationState.isShowingRecovery) {
      this.notificationState.isShowingRecovery = false;
      this.hideNotification();
    }
  }

  /**
   * Masque toute notification
   */
  hideNotification() {
    this.clearNotificationTimeout();
    
    this.sendToRenderer('notification', {
      type: 'hide'
    });
    
    this.notificationState.lastNotification = null;
  }

  /**
   * Nettoie le timeout de notification
   */
  clearNotificationTimeout() {
    if (this.notificationState.notificationTimeout) {
      clearTimeout(this.notificationState.notificationTimeout);
      this.notificationState.notificationTimeout = null;
    }
  }

  /**
   * Charge l'écran de connexion
   */
  loadLoginScreen() {
    log.info('Redirection vers l\'écran de connexion');
    
    this.sendToRenderer('navigate', {
      target: 'login',
      reason: 'session-failure'
    });
    
    // Si nécessaire, créer une nouvelle fenêtre de connexion
    // Cette logique dépend de l'architecture de votre application
  }

  /**
   * Nettoie les ressources
   */
  cleanup() {
    this.clearNotificationTimeout();
    
    // Supprimer tous les écouteurs
    if (this.sessionManager) {
      this.sessionManager.removeAllListeners();
    }
    
    if (this.recoveryManager) {
      this.recoveryManager.removeAllListeners();
    }
    
    log.info('Gestionnaire d\'événements de session nettoyé');
  }

  // Getters pour l'état
  get isShowingRecovery() { return this.notificationState.isShowingRecovery; }
  get lastNotification() { return this.notificationState.lastNotification; }
}

module.exports = SessionEventHandler;
