/**
 * Gestionnaire Préventif des Erreurs Content-Length Mismatch
 * Élimine les causes racines avant qu'elles ne se manifestent
 */

const { session } = require('electron');
const log = require('electron-log');
const { EventEmitter } = require('events');

class PreventiveContentLengthManager extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.config = {
      // Stratégies de prévention
      disableCompression: true,           // Désactiver compression côté client
      forceIdentityEncoding: true,        // Forcer Accept-Encoding: identity
      optimizeBuffering: true,            // Optimiser les buffers
      enableSmartCaching: true,           // Cache intelligent
      monitorAssetHealth: true,           // Surveillance proactive
      
      // Timeouts optimisés
      assetTimeout: 30000,                // 30s pour assets volumineux
      retryTimeout: 5000,                 // 5s entre tentatives
      healthCheckInterval: 60000,         // 1 minute entre vérifications
      
      // Seuils de détection
      maxAssetSize: 5 * 1024 * 1024,     // 5MB max par asset
      maxRetries: 3,                      // 3 tentatives max
      errorThreshold: 5,                  // 5 erreurs/heure max
      
      ...options
    };
    
    // État du gestionnaire
    this.state = {
      isActive: false,
      interceptorsInstalled: false,
      assetCache: new Map(),
      errorHistory: [],
      healthMetrics: {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        averageResponseTime: 0
      }
    };
    
    // Patterns d'assets critiques
    this.criticalAssetPatterns = [
      /web\.assets_backend\.js/,
      /web\.assets_common\.js/,
      /web\.assets_backend\.css/,
      /web\.assets_common\.css/,
      /load_menus/,
      /web\/webclient/
    ];
    
    this.initialize();
  }

  /**
   * Initialise le gestionnaire préventif
   */
  async initialize() {
    try {
      log.info('🛡️ Initialisation du gestionnaire préventif Content-Length');
      
      // Installer les intercepteurs
      await this.installRequestInterceptors();
      await this.installResponseInterceptors();
      
      // Configurer la session Electron
      await this.configureElectronSession();
      
      // Démarrer la surveillance
      this.startHealthMonitoring();
      
      this.state.isActive = true;
      log.info('✅ Gestionnaire préventif Content-Length initialisé');
      
      this.emit('initialized');
    } catch (error) {
      log.error(`❌ Erreur lors de l'initialisation: ${error.message}`);
      throw error;
    }
  }

  /**
   * Installe les intercepteurs de requêtes
   */
  async installRequestInterceptors() {
    const defaultSession = session.defaultSession;
    
    // Intercepteur pour les requêtes sortantes
    defaultSession.webRequest.onBeforeSendHeaders((details, callback) => {
      if (this.isCriticalAsset(details.url)) {
        const optimizedHeaders = this.optimizeRequestHeaders(details.requestHeaders);
        
        log.debug(`🔧 Optimisation headers pour: ${details.url}`);
        
        callback({ 
          cancel: false, 
          requestHeaders: optimizedHeaders 
        });
      } else {
        callback({ cancel: false });
      }
    });
    
    // Intercepteur pour les redirections
    defaultSession.webRequest.onBeforeRedirect((details) => {
      if (this.isCriticalAsset(details.url)) {
        log.debug(`🔄 Redirection détectée: ${details.url} -> ${details.redirectURL}`);
        this.trackAssetRedirection(details.url, details.redirectURL);
      }
    });
    
    log.info('✅ Intercepteurs de requêtes installés');
  }

  /**
   * Installe les intercepteurs de réponses
   */
  async installResponseInterceptors() {
    const defaultSession = session.defaultSession;
    
    // Intercepteur pour les réponses entrantes
    defaultSession.webRequest.onHeadersReceived((details, callback) => {
      if (this.isCriticalAsset(details.url)) {
        const optimizedHeaders = this.optimizeResponseHeaders(details.responseHeaders);
        
        log.debug(`🔧 Optimisation réponse pour: ${details.url}`);
        
        callback({ 
          cancel: false, 
          responseHeaders: optimizedHeaders 
        });
      } else {
        callback({ cancel: false });
      }
    });
    
    // Intercepteur pour les erreurs de réponse
    defaultSession.webRequest.onErrorOccurred((details) => {
      if (this.isCriticalAsset(details.url)) {
        log.warn(`❌ Erreur réseau détectée: ${details.url} - ${details.error}`);
        this.handleNetworkError(details);
      }
    });
    
    log.info('✅ Intercepteurs de réponses installés');
  }

  /**
   * Optimise les headers de requête
   */
  optimizeRequestHeaders(headers) {
    const optimized = { ...headers };
    
    if (this.config.disableCompression) {
      // Forcer l'encoding identity pour éviter la compression
      optimized['Accept-Encoding'] = ['identity'];
      
      // Supprimer les headers de compression existants
      delete optimized['accept-encoding'];
    }
    
    if (this.config.forceIdentityEncoding) {
      // S'assurer qu'aucune compression n'est demandée
      optimized['Accept-Encoding'] = ['identity'];
      optimized['Cache-Control'] = ['no-cache, no-store, must-revalidate'];
      optimized['Pragma'] = ['no-cache'];
    }
    
    // Ajouter des headers pour optimiser la livraison
    optimized['Connection'] = ['keep-alive'];
    optimized['User-Agent'] = ['Edara-ERP/1.0 (Electron; Optimized)'];
    
    return optimized;
  }

  /**
   * Optimise les headers de réponse
   */
  optimizeResponseHeaders(headers) {
    const optimized = { ...headers };
    
    // Supprimer Content-Length si compression détectée
    if (headers['content-encoding'] && headers['content-encoding'][0] !== 'identity') {
      log.debug('🗜️ Compression détectée, suppression Content-Length');
      delete optimized['content-length'];
      delete optimized['Content-Length'];
    }
    
    // Forcer le cache pour les assets statiques
    if (this.config.enableSmartCaching) {
      optimized['Cache-Control'] = ['public, max-age=3600, immutable'];
      optimized['ETag'] = [this.generateETag()];
    }
    
    return optimized;
  }

  /**
   * Configure la session Electron pour optimiser les assets
   */
  async configureElectronSession() {
    const defaultSession = session.defaultSession;
    
    // Configurer le cache
    if (this.config.enableSmartCaching) {
      await defaultSession.clearCache();
      
      // Définir la taille du cache
      await defaultSession.setCacheSize(100 * 1024 * 1024); // 100MB
    }
    
    // Configurer les permissions
    defaultSession.setPermissionRequestHandler((webContents, permission, callback) => {
      // Autoriser les permissions nécessaires pour Odoo
      const allowedPermissions = ['notifications', 'media', 'geolocation'];
      callback(allowedPermissions.includes(permission));
    });
    
    // Optimiser les protocoles
    defaultSession.protocol.interceptHttpProtocol('http', (request, callback) => {
      if (this.isCriticalAsset(request.url)) {
        // Appliquer des optimisations spécifiques
        const optimizedRequest = this.optimizeAssetRequest(request);
        callback(optimizedRequest);
      } else {
        callback(request);
      }
    });
    
    log.info('✅ Session Electron configurée');
  }

  /**
   * Optimise une requête d'asset
   */
  optimizeAssetRequest(request) {
    // Ajouter des paramètres d'optimisation
    const url = new URL(request.url);
    
    // Cache busting intelligent
    if (!url.searchParams.has('v')) {
      url.searchParams.set('v', this.generateVersionHash());
    }
    
    // Forcer l'encoding identity
    url.searchParams.set('encoding', 'identity');
    
    return {
      ...request,
      url: url.toString(),
      headers: {
        ...request.headers,
        'Accept-Encoding': 'identity',
        'Cache-Control': 'no-cache'
      }
    };
  }

  /**
   * Vérifie si une URL correspond à un asset critique
   */
  isCriticalAsset(url) {
    return this.criticalAssetPatterns.some(pattern => pattern.test(url));
  }

  /**
   * Gère les erreurs réseau
   */
  handleNetworkError(details) {
    const error = {
      url: details.url,
      error: details.error,
      timestamp: Date.now(),
      method: details.method
    };
    
    this.state.errorHistory.push(error);
    
    // Garder seulement les 100 dernières erreurs
    if (this.state.errorHistory.length > 100) {
      this.state.errorHistory = this.state.errorHistory.slice(-100);
    }
    
    // Analyser les patterns d'erreur
    this.analyzeErrorPatterns();
    
    // Émettre un événement pour traitement externe
    this.emit('network-error', error);
  }

  /**
   * Analyse les patterns d'erreur pour détecter les problèmes systémiques
   */
  analyzeErrorPatterns() {
    const recentErrors = this.state.errorHistory.filter(
      error => Date.now() - error.timestamp < 3600000 // Dernière heure
    );
    
    if (recentErrors.length >= this.config.errorThreshold) {
      log.warn(`⚠️ Seuil d'erreurs atteint: ${recentErrors.length} erreurs/heure`);
      
      // Analyser les causes communes
      const errorsByType = recentErrors.reduce((acc, error) => {
        acc[error.error] = (acc[error.error] || 0) + 1;
        return acc;
      }, {});
      
      // Émettre une alerte
      this.emit('error-threshold-exceeded', {
        count: recentErrors.length,
        errorsByType,
        recommendations: this.generateRecommendations(errorsByType)
      });
    }
  }

  /**
   * Génère des recommandations basées sur les patterns d'erreur
   */
  generateRecommendations(errorsByType) {
    const recommendations = [];
    
    if (errorsByType['ERR_CONTENT_LENGTH_MISMATCH']) {
      recommendations.push('Désactiver la compression Gzip côté serveur');
      recommendations.push('Augmenter les timeouts de proxy');
    }
    
    if (errorsByType['ERR_CONNECTION_TIMED_OUT']) {
      recommendations.push('Optimiser la configuration réseau');
      recommendations.push('Augmenter les timeouts de connexion');
    }
    
    if (errorsByType['ERR_EMPTY_RESPONSE']) {
      recommendations.push('Vérifier la configuration du serveur Odoo');
      recommendations.push('Optimiser les workers Odoo');
    }
    
    return recommendations;
  }

  /**
   * Démarre la surveillance de santé
   */
  startHealthMonitoring() {
    setInterval(() => {
      this.performHealthCheck();
    }, this.config.healthCheckInterval);
    
    log.info('✅ Surveillance de santé démarrée');
  }

  /**
   * Effectue une vérification de santé
   */
  async performHealthCheck() {
    try {
      const metrics = this.calculateHealthMetrics();
      
      log.debug(`📊 Métriques de santé: ${JSON.stringify(metrics)}`);
      
      // Vérifier les seuils critiques
      if (metrics.errorRate > this.config.errorThreshold) {
        log.warn(`⚠️ Taux d'erreur élevé: ${metrics.errorRate}%`);
        this.emit('health-warning', { type: 'high-error-rate', metrics });
      }
      
      if (metrics.averageResponseTime > this.config.assetTimeout / 2) {
        log.warn(`⚠️ Temps de réponse élevé: ${metrics.averageResponseTime}ms`);
        this.emit('health-warning', { type: 'slow-response', metrics });
      }
      
      this.emit('health-check', metrics);
    } catch (error) {
      log.error(`❌ Erreur lors de la vérification de santé: ${error.message}`);
    }
  }

  /**
   * Calcule les métriques de santé
   */
  calculateHealthMetrics() {
    const total = this.state.healthMetrics.totalRequests;
    const failed = this.state.healthMetrics.failedRequests;
    
    return {
      totalRequests: total,
      successRate: total > 0 ? ((total - failed) / total) * 100 : 100,
      errorRate: total > 0 ? (failed / total) * 100 : 0,
      averageResponseTime: this.state.healthMetrics.averageResponseTime,
      recentErrors: this.state.errorHistory.filter(
        error => Date.now() - error.timestamp < 3600000
      ).length
    };
  }

  /**
   * Suit une redirection d'asset
   */
  trackAssetRedirection(originalUrl, redirectUrl) {
    log.debug(`🔄 Redirection: ${originalUrl} -> ${redirectUrl}`);
    
    // Mettre à jour le cache avec la nouvelle URL
    if (this.state.assetCache.has(originalUrl)) {
      const cacheEntry = this.state.assetCache.get(originalUrl);
      this.state.assetCache.set(redirectUrl, cacheEntry);
      this.state.assetCache.delete(originalUrl);
    }
  }

  /**
   * Génère un hash de version pour le cache busting
   */
  generateVersionHash() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
  }

  /**
   * Génère un ETag pour le cache
   */
  generateETag() {
    return `"${Date.now().toString(16)}-${Math.random().toString(16).substr(2, 8)}"`;
  }

  /**
   * Obtient les statistiques du gestionnaire
   */
  getStats() {
    return {
      config: this.config,
      state: this.state,
      healthMetrics: this.calculateHealthMetrics(),
      recentErrors: this.state.errorHistory.slice(-10)
    };
  }

  /**
   * Réinitialise les statistiques
   */
  resetStats() {
    this.state.errorHistory = [];
    this.state.healthMetrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0
    };
    
    log.info('📊 Statistiques réinitialisées');
  }

  /**
   * Nettoie les ressources
   */
  cleanup() {
    this.state.isActive = false;
    this.removeAllListeners();
    
    log.info('🧹 Gestionnaire préventif nettoyé');
  }
}

module.exports = PreventiveContentLengthManager;
