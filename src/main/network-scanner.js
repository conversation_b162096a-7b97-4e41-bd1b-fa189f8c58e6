/**
 * <PERSON><PERSON>r réseau optimisé pour la détection de serveurs Odoo
 * Utilise des techniques avancées pour un balayage rapide et efficace
 */

const net = require('net');
const os = require('os');
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const log = require('electron-log');

class NetworkScanner {
  constructor(options = {}) {
    this.config = {
      maxConcurrent: options.maxConcurrent || 20,
      portTimeout: options.portTimeout || 200,
      httpTimeout: options.httpTimeout || 800,
      maxWorkers: options.maxWorkers || 4,
      retryAttempts: options.retryAttempts || 1,
      adaptiveTimeout: options.adaptiveTimeout || true,
      ...options
    };
    
    this.stats = {
      scansPerformed: 0,
      averageResponseTime: 0,
      successRate: 0,
      adaptiveTimeouts: new Map()
    };
    
    this.workers = [];
    this.isScanning = false;
  }

  /**
   * Balayage réseau intelligent avec adaptation dynamique
   */
  async scanNetwork(baseIp, options = {}) {
    if (this.isScanning) {
      throw new Error('Un balayage est déjà en cours');
    }
    
    this.isScanning = true;
    const startTime = Date.now();
    
    try {
      log.info(`Démarrage du balayage réseau pour ${baseIp}`);
      
      // 1. Générer la liste des IPs à scanner
      const ipList = this.generateSmartIpList(baseIp, options);
      log.info(`${ipList.length} IPs à scanner`);
      
      // 2. Balayage adaptatif par phases
      const results = await this.performAdaptiveScan(ipList);
      
      // 3. Analyser et optimiser les résultats
      const servers = this.analyzeResults(results);
      
      const duration = Date.now() - startTime;
      this.updateStats(servers, duration);
      
      log.info(`Balayage terminé en ${duration}ms: ${servers.length} serveur(s) trouvé(s)`);
      
      return servers;
      
    } finally {
      this.isScanning = false;
    }
  }

  /**
   * Génère une liste d'IPs intelligente basée sur l'analyse réseau
   */
  generateSmartIpList(baseIp, options = {}) {
    const ipParts = baseIp.split('.').map(Number);
    const networkBase = ipParts.slice(0, 3).join('.');
    const currentHost = ipParts[3];
    
    const ips = [];
    
    // Phase 1: IPs prioritaires (serveurs communs)
    const priorityHosts = [
      27,    // Serveur Odoo standard
      1,     // Gateway/Router
      100,   // Serveurs communs
      101,
      200,
      254    // Broadcast - 1
    ];
    
    priorityHosts.forEach(host => {
      if (host !== currentHost && host >= 1 && host <= 254) {
        ips.push({
          ip: `${networkBase}.${host}`,
          priority: 1,
          reason: 'priority-host'
        });
      }
    });
    
    // Phase 2: Plage autour de l'IP locale (serveurs proches)
    const localRange = options.localRange || 10;
    for (let i = Math.max(1, currentHost - localRange); 
         i <= Math.min(254, currentHost + localRange); 
         i++) {
      const ip = `${networkBase}.${i}`;
      if (!ips.find(entry => entry.ip === ip) && i !== currentHost) {
        ips.push({
          ip,
          priority: 2,
          reason: 'local-range'
        });
      }
    }
    
    // Phase 3: Plages de serveurs typiques
    const serverRanges = [
      { start: 10, end: 50 },   // Serveurs bas
      { start: 150, end: 199 }  // Serveurs hauts
    ];
    
    serverRanges.forEach(range => {
      for (let i = range.start; i <= range.end; i++) {
        const ip = `${networkBase}.${i}`;
        if (!ips.find(entry => entry.ip === ip) && i !== currentHost) {
          ips.push({
            ip,
            priority: 3,
            reason: 'server-range'
          });
        }
      }
    });
    
    // Phase 4: Balayage complet (si demandé)
    if (options.fullScan) {
      for (let i = 1; i <= 254; i++) {
        const ip = `${networkBase}.${i}`;
        if (!ips.find(entry => entry.ip === ip) && i !== currentHost) {
          ips.push({
            ip,
            priority: 4,
            reason: 'full-scan'
          });
        }
      }
    }
    
    // Trier par priorité
    ips.sort((a, b) => a.priority - b.priority);
    
    // Limiter le nombre d'IPs si nécessaire
    const maxIps = options.maxIps || 100;
    return ips.slice(0, maxIps);
  }

  /**
   * Balayage adaptatif par phases avec optimisation dynamique
   */
  async performAdaptiveScan(ipList) {
    const results = [];
    
    // Phase 1: Scan rapide des IPs prioritaires
    const priorityIps = ipList.filter(entry => entry.priority === 1);
    if (priorityIps.length > 0) {
      log.info(`Phase 1: Scan prioritaire de ${priorityIps.length} IPs`);
      const priorityResults = await this.fastParallelScan(priorityIps, {
        timeout: this.config.portTimeout,
        concurrent: Math.min(this.config.maxConcurrent, priorityIps.length)
      });
      results.push(...priorityResults);
      
      // Si on trouve des serveurs, adapter la stratégie
      const foundServers = priorityResults.filter(r => r.success);
      if (foundServers.length > 0) {
        this.adaptScanStrategy(foundServers);
      }
    }
    
    // Phase 2: Scan des IPs locales si nécessaire
    const localIps = ipList.filter(entry => entry.priority === 2);
    if (localIps.length > 0 && results.filter(r => r.success).length < 3) {
      log.info(`Phase 2: Scan local de ${localIps.length} IPs`);
      const localResults = await this.adaptiveParallelScan(localIps);
      results.push(...localResults);
    }
    
    // Phase 3: Scan étendu si aucun serveur trouvé
    const extendedIps = ipList.filter(entry => entry.priority >= 3);
    if (extendedIps.length > 0 && results.filter(r => r.success).length === 0) {
      log.info(`Phase 3: Scan étendu de ${extendedIps.length} IPs`);
      const extendedResults = await this.adaptiveParallelScan(extendedIps);
      results.push(...extendedResults);
    }
    
    return results;
  }

  /**
   * Scan parallèle rapide avec timeout fixe
   */
  async fastParallelScan(ipEntries, options = {}) {
    const { timeout = this.config.portTimeout, concurrent = this.config.maxConcurrent } = options;
    const results = [];
    
    // Traiter par chunks pour limiter la concurrence
    for (let i = 0; i < ipEntries.length; i += concurrent) {
      const chunk = ipEntries.slice(i, i + concurrent);
      
      const chunkPromises = chunk.map(entry => 
        this.scanSingleHost(entry.ip, { timeout, priority: entry.priority })
      );
      
      const chunkResults = await Promise.allSettled(chunkPromises);
      
      chunkResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({
            ip: chunk[index].ip,
            success: false,
            error: result.reason?.message || 'Unknown error',
            priority: chunk[index].priority
          });
        }
      });
      
      // Petit délai entre les chunks pour éviter la surcharge
      if (i + concurrent < ipEntries.length) {
        await this.delay(50);
      }
    }
    
    return results;
  }

  /**
   * Scan parallèle adaptatif avec timeout dynamique
   */
  async adaptiveParallelScan(ipEntries) {
    const results = [];
    const concurrent = Math.min(this.config.maxConcurrent, ipEntries.length);
    
    for (let i = 0; i < ipEntries.length; i += concurrent) {
      const chunk = ipEntries.slice(i, i + concurrent);
      
      // Adapter le timeout basé sur les résultats précédents
      const adaptiveTimeout = this.getAdaptiveTimeout(chunk[0].priority);
      
      const chunkPromises = chunk.map(entry => 
        this.scanSingleHost(entry.ip, { 
          timeout: adaptiveTimeout, 
          priority: entry.priority,
          adaptive: true
        })
      );
      
      const chunkResults = await Promise.allSettled(chunkPromises);
      
      chunkResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
          
          // Mettre à jour les métriques adaptatives
          if (result.value.success) {
            this.updateAdaptiveTimeout(chunk[index].priority, result.value.responseTime);
          }
        } else {
          results.push({
            ip: chunk[index].ip,
            success: false,
            error: result.reason?.message || 'Unknown error',
            priority: chunk[index].priority
          });
        }
      });
      
      await this.delay(30);
    }
    
    return results;
  }

  /**
   * Scan d'un hôte unique avec métriques
   */
  async scanSingleHost(ip, options = {}) {
    const { timeout = this.config.portTimeout, priority = 3 } = options;
    const startTime = Date.now();
    
    try {
      // 1. Vérification rapide du port
      const portOpen = await this.checkPort(ip, 8069, timeout);
      
      if (!portOpen) {
        return {
          ip,
          success: false,
          reason: 'port-closed',
          responseTime: Date.now() - startTime,
          priority
        };
      }
      
      // 2. Vérification HTTP basique (optionnelle pour la vitesse)
      if (options.skipHttp) {
        return {
          ip,
          success: true,
          reason: 'port-open',
          responseTime: Date.now() - startTime,
          priority,
          needsValidation: true
        };
      }
      
      // 3. Validation HTTP rapide
      const httpValid = await this.quickHttpCheck(ip, this.config.httpTimeout);
      
      return {
        ip,
        success: httpValid,
        reason: httpValid ? 'http-valid' : 'http-invalid',
        responseTime: Date.now() - startTime,
        priority,
        validated: httpValid
      };
      
    } catch (error) {
      return {
        ip,
        success: false,
        error: error.message,
        responseTime: Date.now() - startTime,
        priority
      };
    }
  }

  /**
   * Vérification ultra-rapide de port
   */
  async checkPort(host, port, timeout) {
    return new Promise((resolve) => {
      const socket = new net.Socket();
      let resolved = false;
      
      const cleanup = (result) => {
        if (!resolved) {
          resolved = true;
          socket.destroy();
          resolve(result);
        }
      };
      
      socket.setTimeout(timeout);
      socket.on('connect', () => cleanup(true));
      socket.on('timeout', () => cleanup(false));
      socket.on('error', () => cleanup(false));
      
      try {
        socket.connect(port, host);
      } catch (error) {
        cleanup(false);
      }
      
      // Timeout de sécurité
      setTimeout(() => cleanup(false), timeout + 50);
    });
  }

  /**
   * Vérification HTTP rapide
   */
  async quickHttpCheck(ip, timeout) {
    try {
      const axios = require('axios');
      const response = await axios.get(`http://${ip}:8069/web/database/selector`, {
        timeout,
        validateStatus: status => status >= 200 && status < 500,
        headers: {
          'User-Agent': 'Edara-Scanner/1.0',
          'Accept': 'text/html',
          'Connection': 'close'
        }
      });
      
      return response.status < 400;
    } catch (error) {
      return error.response?.status === 401 || error.response?.status === 403;
    }
  }

  /**
   * Adapte la stratégie de scan basée sur les résultats
   */
  adaptScanStrategy(foundServers) {
    // Analyser les patterns des serveurs trouvés
    const ips = foundServers.map(s => s.ip);
    const responseTimes = foundServers.map(s => s.responseTime);
    
    // Adapter les timeouts basés sur les temps de réponse observés
    const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    
    if (avgResponseTime < 100) {
      // Réseau rapide, réduire les timeouts
      this.config.portTimeout = Math.max(150, this.config.portTimeout * 0.8);
      this.config.httpTimeout = Math.max(400, this.config.httpTimeout * 0.8);
    } else if (avgResponseTime > 500) {
      // Réseau lent, augmenter les timeouts
      this.config.portTimeout = Math.min(500, this.config.portTimeout * 1.2);
      this.config.httpTimeout = Math.min(1500, this.config.httpTimeout * 1.2);
    }
    
    log.debug(`Stratégie adaptée: port=${this.config.portTimeout}ms, http=${this.config.httpTimeout}ms`);
  }

  /**
   * Obtient un timeout adaptatif basé sur la priorité et l'historique
   */
  getAdaptiveTimeout(priority) {
    if (!this.config.adaptiveTimeout) {
      return this.config.portTimeout;
    }
    
    const baseTimeout = this.config.portTimeout;
    const adaptive = this.stats.adaptiveTimeouts.get(priority);
    
    if (adaptive && adaptive.samples > 5) {
      // Utiliser 2x le temps de réponse moyen observé
      return Math.min(baseTimeout * 2, adaptive.avgResponseTime * 2);
    }
    
    // Timeouts par défaut basés sur la priorité
    const priorityMultipliers = { 1: 0.8, 2: 1.0, 3: 1.2, 4: 1.5 };
    return baseTimeout * (priorityMultipliers[priority] || 1.0);
  }

  /**
   * Met à jour les timeouts adaptatifs
   */
  updateAdaptiveTimeout(priority, responseTime) {
    if (!this.stats.adaptiveTimeouts.has(priority)) {
      this.stats.adaptiveTimeouts.set(priority, {
        samples: 0,
        totalTime: 0,
        avgResponseTime: 0
      });
    }
    
    const adaptive = this.stats.adaptiveTimeouts.get(priority);
    adaptive.samples++;
    adaptive.totalTime += responseTime;
    adaptive.avgResponseTime = adaptive.totalTime / adaptive.samples;
  }

  /**
   * Analyse les résultats et extrait les serveurs valides
   */
  analyzeResults(results) {
    const servers = results
      .filter(result => result.success)
      .map(result => ({
        ip: result.ip,
        url: `http://${result.ip}:8069`,
        responseTime: result.responseTime,
        priority: result.priority,
        validated: result.validated || false,
        needsValidation: result.needsValidation || false,
        method: 'network-scan',
        discovered: Date.now()
      }))
      .sort((a, b) => {
        // Trier par priorité puis par temps de réponse
        if (a.priority !== b.priority) {
          return a.priority - b.priority;
        }
        return a.responseTime - b.responseTime;
      });
    
    return servers;
  }

  /**
   * Met à jour les statistiques
   */
  updateStats(servers, duration) {
    this.stats.scansPerformed++;
    
    if (servers.length > 0) {
      const avgResponseTime = servers.reduce((sum, s) => sum + s.responseTime, 0) / servers.length;
      this.stats.averageResponseTime = 
        (this.stats.averageResponseTime * (this.stats.scansPerformed - 1) + avgResponseTime) / this.stats.scansPerformed;
    }
    
    this.stats.successRate = servers.length > 0 ? 1 : 0;
  }

  /**
   * Utilitaire de délai
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Obtient les statistiques du scanner
   */
  getStats() {
    return {
      ...this.stats,
      config: this.config,
      isScanning: this.isScanning
    };
  }

  /**
   * Réinitialise les statistiques
   */
  resetStats() {
    this.stats = {
      scansPerformed: 0,
      averageResponseTime: 0,
      successRate: 0,
      adaptiveTimeouts: new Map()
    };
  }
}

module.exports = NetworkScanner;
