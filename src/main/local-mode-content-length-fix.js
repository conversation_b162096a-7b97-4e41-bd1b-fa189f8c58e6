/**
 * Gestionnaire Spécialisé pour les Erreurs Content-Length en Mode Local
 * Résout spécifiquement les problèmes ERR_CONTENT_LENGTH_MISMATCH avec Odoo local
 */

const { session } = require('electron');
const log = require('electron-log');
const { EventEmitter } = require('events');

class LocalModeContentLengthFix extends EventEmitter {
  constructor() {
    super();
    
    this.config = {
      // Détection du mode local
      localPatterns: [
        /^http:\/\/localhost:/,
        /^http:\/\/127\.0\.0\.1:/,
        /^http:\/\/192\.168\./,
        /^http:\/\/10\./,
        /^http:\/\/172\.(1[6-9]|2[0-9]|3[0-1])\./
      ],
      
      // Assets critiques Odoo
      criticalAssets: [
        /web\.assets_backend\.js/,
        /web\.assets_common\.js/,
        /web\.assets_backend\.css/,
        /web\.assets_common\.css/,
        /load_menus/,
        /web\/webclient/
      ],
      
      // Configuration spécifique mode local
      enableAggressiveClearing: true,
      enableDedicatedSession: true,
      enableRequestOptimization: true,
      enableResponseFixing: true,
      
      // Debug
      debug: true
    };
    
    this.state = {
      isActive: false,
      localModeDetected: false,
      dedicatedSession: null,
      interceptorsInstalled: false,
      requestCount: 0,
      fixedRequests: 0,
      errors: []
    };
    
    this.originalInterceptors = new Map();
  }

  /**
   * Initialise le gestionnaire pour le mode local
   */
  async initialize() {
    try {
      log.info('🔧 [LocalMode] Initialisation du gestionnaire Content-Length pour mode local');
      
      // Nettoyer les intercepteurs existants problématiques
      await this.cleanupExistingInterceptors();
      
      // Créer une session dédiée pour le mode local
      if (this.config.enableDedicatedSession) {
        await this.createDedicatedSession();
      }
      
      // Installer les nouveaux intercepteurs optimisés
      await this.installOptimizedInterceptors();
      
      this.state.isActive = true;
      log.info('✅ [LocalMode] Gestionnaire Content-Length initialisé avec succès');
      
      this.emit('initialized');
    } catch (error) {
      log.error(`❌ [LocalMode] Erreur lors de l'initialisation: ${error.message}`);
      throw error;
    }
  }

  /**
   * Nettoie les intercepteurs existants qui causent des conflits
   */
  async cleanupExistingInterceptors() {
    log.info('🧹 [LocalMode] Nettoyage des intercepteurs existants...');
    
    try {
      // Sauvegarder les intercepteurs existants pour restauration si nécessaire
      const defaultSession = session.defaultSession;
      
      // Supprimer tous les intercepteurs webRequest existants
      // Note: Electron ne fournit pas de méthode directe pour supprimer les intercepteurs
      // Nous devons donc les remplacer par des intercepteurs "passthrough"
      
      // Installer des intercepteurs temporaires qui ne font rien
      defaultSession.webRequest.onBeforeRequest((details, callback) => {
        callback({});
      });
      
      defaultSession.webRequest.onHeadersReceived((details, callback) => {
        callback({});
      });
      
      log.info('✅ [LocalMode] Intercepteurs existants nettoyés');
    } catch (error) {
      log.warn(`⚠️ [LocalMode] Erreur lors du nettoyage: ${error.message}`);
    }
  }

  /**
   * Crée une session dédiée pour les requêtes Odoo locales
   */
  async createDedicatedSession() {
    try {
      log.info('🔧 [LocalMode] Création d\'une session dédiée pour Odoo local...');
      
      // Créer une session avec partition dédiée
      this.state.dedicatedSession = session.fromPartition('persist:odoo-local', {
        cache: true
      });
      
      // Configuration optimisée pour le mode local
      await this.state.dedicatedSession.clearCache();
      await this.state.dedicatedSession.clearStorageData();
      
      // Configurer les permissions
      this.state.dedicatedSession.setPermissionRequestHandler((webContents, permission, callback) => {
        const allowedPermissions = ['notifications', 'media'];
        callback(allowedPermissions.includes(permission));
      });
      
      log.info('✅ [LocalMode] Session dédiée créée avec succès');
    } catch (error) {
      log.error(`❌ [LocalMode] Erreur lors de la création de session: ${error.message}`);
      throw error;
    }
  }

  /**
   * Installe les intercepteurs optimisés pour le mode local
   */
  async installOptimizedInterceptors() {
    log.info('🔧 [LocalMode] Installation des intercepteurs optimisés...');
    
    const targetSession = this.state.dedicatedSession || session.defaultSession;
    
    // Intercepteur de requêtes optimisé
    targetSession.webRequest.onBeforeSendHeaders((details, callback) => {
      if (this.isLocalOdooRequest(details.url)) {
        this.state.requestCount++;
        
        const optimizedHeaders = this.optimizeRequestHeaders(details.requestHeaders, details.url);
        
        if (this.config.debug) {
          log.debug(`🔧 [LocalMode] Optimisation requête: ${details.url}`);
        }
        
        callback({
          cancel: false,
          requestHeaders: optimizedHeaders
        });
      } else {
        callback({ cancel: false });
      }
    });
    
    // Intercepteur de réponses optimisé
    targetSession.webRequest.onHeadersReceived((details, callback) => {
      if (this.isLocalOdooRequest(details.url)) {
        const fixedHeaders = this.fixResponseHeaders(details.responseHeaders, details.url);
        
        if (fixedHeaders !== details.responseHeaders) {
          this.state.fixedRequests++;
          
          if (this.config.debug) {
            log.debug(`🔧 [LocalMode] Correction réponse: ${details.url}`);
          }
        }
        
        callback({
          cancel: false,
          responseHeaders: fixedHeaders
        });
      } else {
        callback({ cancel: false });
      }
    });
    
    // Intercepteur d'erreurs
    targetSession.webRequest.onErrorOccurred((details) => {
      if (this.isLocalOdooRequest(details.url)) {
        log.warn(`⚠️ [LocalMode] Erreur réseau: ${details.error} pour ${details.url}`);
        
        this.state.errors.push({
          url: details.url,
          error: details.error,
          timestamp: Date.now()
        });
        
        this.emit('network-error', {
          url: details.url,
          error: details.error
        });
      }
    });
    
    this.state.interceptorsInstalled = true;
    log.info('✅ [LocalMode] Intercepteurs optimisés installés');
  }

  /**
   * Optimise les headers de requête pour le mode local
   */
  optimizeRequestHeaders(headers, url) {
    const optimized = { ...headers };
    
    if (this.isCriticalAsset(url)) {
      // Forcer l'encoding identity pour éviter la compression
      optimized['Accept-Encoding'] = ['identity'];
      
      // Supprimer les headers de compression
      delete optimized['accept-encoding'];
      
      // Optimiser le cache
      optimized['Cache-Control'] = ['no-cache, no-store, must-revalidate'];
      optimized['Pragma'] = ['no-cache'];
      
      // Headers spécifiques pour Electron
      optimized['User-Agent'] = ['Edara-ERP-Local/1.0 (Electron)'];
      optimized['X-Requested-With'] = ['Electron'];
      
      if (this.config.debug) {
        log.debug(`🔧 [LocalMode] Headers optimisés pour asset critique: ${url}`);
      }
    }
    
    return optimized;
  }

  /**
   * Corrige les headers de réponse problématiques
   */
  fixResponseHeaders(headers, url) {
    const fixed = { ...headers };
    
    if (this.isCriticalAsset(url)) {
      // Problème principal: Content-Length avec compression
      const hasContentEncoding = headers['content-encoding'] || headers['Content-Encoding'];
      const hasContentLength = headers['content-length'] || headers['Content-Length'];
      
      if (hasContentEncoding && hasContentLength) {
        // Supprimer Content-Length si compression détectée
        delete fixed['content-length'];
        delete fixed['Content-Length'];
        
        if (this.config.debug) {
          log.debug(`🔧 [LocalMode] Suppression Content-Length (compression détectée): ${url}`);
        }
      }
      
      // Supprimer les headers de compression pour forcer identity
      delete fixed['content-encoding'];
      delete fixed['Content-Encoding'];
      
      // Forcer le rechargement pour les assets critiques
      fixed['Cache-Control'] = ['no-cache, no-store, must-revalidate'];
      fixed['Pragma'] = ['no-cache'];
      fixed['Expires'] = ['0'];
      
      // Ajouter un header personnalisé pour tracking
      fixed['X-Edara-Fixed'] = ['true'];
    }
    
    return fixed;
  }

  /**
   * Détecte si une URL est une requête Odoo locale
   */
  isLocalOdooRequest(url) {
    // Vérifier si c'est une URL locale
    const isLocal = this.config.localPatterns.some(pattern => pattern.test(url));
    
    if (isLocal) {
      this.state.localModeDetected = true;
    }
    
    // Vérifier si c'est une ressource Odoo
    const isOdoo = url.includes('/web/') || 
                   url.includes('/odoo/') ||
                   url.includes(':8069') ||
                   url.includes('load_menus') ||
                   url.includes('assets');
    
    return isLocal && isOdoo;
  }

  /**
   * Détecte si une URL est un asset critique
   */
  isCriticalAsset(url) {
    return this.config.criticalAssets.some(pattern => pattern.test(url));
  }

  /**
   * Nettoie agressivement le cache et les données de session
   */
  async performAggressiveClearing() {
    if (!this.config.enableAggressiveClearing) return;
    
    try {
      log.info('🧹 [LocalMode] Nettoyage agressif du cache et des données...');
      
      const targetSession = this.state.dedicatedSession || session.defaultSession;
      
      // Nettoyer tout le cache
      await targetSession.clearCache();
      
      // Nettoyer toutes les données de stockage
      await targetSession.clearStorageData({
        storages: [
          'cookies',
          'localstorage',
          'sessionstorage',
          'indexdb',
          'websql',
          'serviceworkers',
          'cachestorage'
        ]
      });
      
      // Nettoyer les données d'authentification
      await targetSession.clearAuthCache();
      
      log.info('✅ [LocalMode] Nettoyage agressif terminé');
    } catch (error) {
      log.error(`❌ [LocalMode] Erreur lors du nettoyage: ${error.message}`);
    }
  }

  /**
   * Applique une configuration BrowserWindow optimisée pour le mode local
   */
  getOptimizedWebPreferences() {
    return {
      // Sécurité optimisée pour le mode local
      webSecurity: true,              // Réactiver webSecurity
      contextIsolation: true,         // Activer l'isolation
      nodeIntegration: false,         // Désactiver nodeIntegration
      
      // Session dédiée si disponible
      session: this.state.dedicatedSession || session.defaultSession,
      
      // Optimisations spécifiques
      backgroundThrottling: false,    // Éviter le throttling
      offscreen: false,               // Rendu normal
      
      // Cache et stockage
      partition: this.state.dedicatedSession ? 'persist:odoo-local' : undefined,
      
      // Permissions
      allowRunningInsecureContent: false,
      experimentalFeatures: false
    };
  }

  /**
   * Obtient les statistiques du gestionnaire
   */
  getStats() {
    return {
      isActive: this.state.isActive,
      localModeDetected: this.state.localModeDetected,
      requestCount: this.state.requestCount,
      fixedRequests: this.state.fixedRequests,
      errorCount: this.state.errors.length,
      recentErrors: this.state.errors.slice(-5),
      hasDedicatedSession: !!this.state.dedicatedSession,
      interceptorsInstalled: this.state.interceptorsInstalled
    };
  }

  /**
   * Force une réinitialisation complète
   */
  async forceReset() {
    log.info('🔄 [LocalMode] Réinitialisation forcée...');
    
    try {
      // Nettoyer agressivement
      await this.performAggressiveClearing();
      
      // Réinitialiser les statistiques
      this.state.requestCount = 0;
      this.state.fixedRequests = 0;
      this.state.errors = [];
      
      // Réinstaller les intercepteurs
      if (this.state.interceptorsInstalled) {
        await this.installOptimizedInterceptors();
      }
      
      log.info('✅ [LocalMode] Réinitialisation terminée');
      this.emit('reset-completed');
    } catch (error) {
      log.error(`❌ [LocalMode] Erreur lors de la réinitialisation: ${error.message}`);
      throw error;
    }
  }

  /**
   * Diagnostic des problèmes de Content-Length
   */
  async diagnoseContentLengthIssues() {
    const diagnosis = {
      timestamp: Date.now(),
      localModeDetected: this.state.localModeDetected,
      sessionType: this.state.dedicatedSession ? 'dedicated' : 'default',
      interceptorsActive: this.state.interceptorsInstalled,
      recentErrors: this.state.errors.slice(-10),
      recommendations: []
    };
    
    // Analyser les erreurs récentes
    const recentContentLengthErrors = this.state.errors.filter(
      error => error.error.includes('CONTENT_LENGTH_MISMATCH') &&
               Date.now() - error.timestamp < 300000 // 5 minutes
    );
    
    if (recentContentLengthErrors.length > 0) {
      diagnosis.recommendations.push('Erreurs Content-Length détectées récemment');
      diagnosis.recommendations.push('Recommandation: Effectuer un nettoyage agressif');
    }
    
    if (!this.state.localModeDetected) {
      diagnosis.recommendations.push('Mode local non détecté - vérifier la configuration réseau');
    }
    
    if (!this.state.interceptorsInstalled) {
      diagnosis.recommendations.push('Intercepteurs non installés - réinitialiser le gestionnaire');
    }
    
    return diagnosis;
  }

  /**
   * Nettoie les ressources
   */
  cleanup() {
    this.state.isActive = false;
    this.removeAllListeners();
    
    if (this.state.dedicatedSession) {
      // Note: Electron ne permet pas de détruire une session
      // Nous pouvons seulement la nettoyer
      this.state.dedicatedSession.clearCache().catch(() => {});
      this.state.dedicatedSession = null;
    }
    
    log.info('🧹 [LocalMode] Gestionnaire nettoyé');
  }
}

module.exports = LocalModeContentLengthFix;
