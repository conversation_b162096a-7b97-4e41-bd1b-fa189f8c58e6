/**
 * Solution ULTIME pour ERR_CONTENT_LENGTH_MISMATCH
 * Approche au niveau le plus bas d'Electron - DOIT fonctionner
 */

const { app, session, protocol } = require('electron');
const log = require('electron-log');
const { net } = require('electron');

class UltimateContentLengthFix {
  constructor() {
    this.isActive = false;
    this.fixedRequests = 0;
    this.interceptedUrls = new Set();
  }

  /**
   * Active la solution ultime AVANT que l'app soit ready
   */
  activateBeforeAppReady() {
    log.info('🚀 [ULTIMATE] ========================================');
    log.info('🚀 [ULTIMATE] ACTIVATION DE LA SOLUTION ULTIME');
    log.info('🚀 [ULTIMATE] ========================================');

    try {
      // ÉTAPE 1: Intercepter au niveau protocole
      this.setupProtocolInterception();

      // ÉTAPE 2: Configurer les sessions avant app.ready
      this.setupSessionBeforeReady();

      this.isActive = true;
      log.info('✅ [ULTIMATE] Solution ultime activée AVANT app.ready');

    } catch (error) {
      log.error(`❌ [ULTIMATE] Erreur lors de l'activation: ${error.message}`);
    }
  }

  /**
   * Active la solution ultime APRÈS que l'app soit ready
   */
  activateAfterAppReady() {
    log.info('🔧 [ULTIMATE] Activation post-app.ready...');

    try {
      // ÉTAPE 3: Configurer les intercepteurs de session
      this.setupSessionInterceptors();

      // ÉTAPE 4: Configurer les intercepteurs de requêtes
      this.setupRequestInterceptors();

      log.info('✅ [ULTIMATE] Solution ultime complètement activée');

    } catch (error) {
      log.error(`❌ [ULTIMATE] Erreur post-app.ready: ${error.message}`);
    }
  }

  /**
   * Intercepte au niveau protocole (le plus bas niveau possible)
   */
  setupProtocolInterception() {
    log.info('🔧 [ULTIMATE] Configuration de l\'interception protocole...');

    // Intercepter le protocole HTTP pour les requêtes locales
    protocol.interceptHttpProtocol('http', (request, callback) => {
      const url = request.url;
      
      if (this.isTargetUrl(url)) {
        log.info(`🔧 [ULTIMATE] INTERCEPTION PROTOCOLE: ${url}`);
        
        // Créer une requête personnalisée avec headers optimisés
        const customRequest = net.request({
          method: request.method,
          url: url,
          headers: {
            ...request.headers,
            'Accept-Encoding': 'identity',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'X-Ultimate-Fix': 'true'
          }
        });

        customRequest.on('response', (response) => {
          log.info(`🔧 [ULTIMATE] RÉPONSE PROTOCOLE: ${url} - Status: ${response.statusCode}`);
          
          // Modifier les headers de réponse
          const modifiedHeaders = { ...response.headers };
          
          // SUPPRIMER TOUS les headers problématiques
          delete modifiedHeaders['content-length'];
          delete modifiedHeaders['Content-Length'];
          delete modifiedHeaders['content-encoding'];
          delete modifiedHeaders['Content-Encoding'];
          
          // Ajouter header de tracking
          modifiedHeaders['X-Ultimate-Fixed'] = 'true';
          
          this.fixedRequests++;
          log.info(`✅ [ULTIMATE] PROTOCOLE CORRIGÉ (${this.fixedRequests}): ${url}`);
          
          // Retourner la réponse modifiée
          callback({
            statusCode: response.statusCode,
            headers: modifiedHeaders,
            data: response
          });
        });

        customRequest.on('error', (error) => {
          log.error(`❌ [ULTIMATE] Erreur requête protocole: ${error.message}`);
          callback({ error: -2 }); // NET_FAILED
        });

        customRequest.end();
      } else {
        // Laisser passer les autres requêtes normalement
        callback({ cancel: false });
      }
    });

    log.info('✅ [ULTIMATE] Interception protocole configurée');
  }

  /**
   * Configure les sessions avant app.ready
   */
  setupSessionBeforeReady() {
    log.info('🔧 [ULTIMATE] Configuration session avant app.ready...');

    // Configurer la session par défaut dès que possible
    app.on('ready', () => {
      this.setupDefaultSession();
    });

    log.info('✅ [ULTIMATE] Configuration session avant app.ready terminée');
  }

  /**
   * Configure la session par défaut
   */
  setupDefaultSession() {
    log.info('🔧 [ULTIMATE] Configuration de la session par défaut...');

    const defaultSession = session.defaultSession;

    // Nettoyer complètement la session
    defaultSession.clearCache().then(() => {
      log.info('✅ [ULTIMATE] Cache session nettoyé');
    });

    defaultSession.clearStorageData().then(() => {
      log.info('✅ [ULTIMATE] Données session nettoyées');
    });

    // Configurer les permissions
    defaultSession.setPermissionRequestHandler((webContents, permission, callback) => {
      callback(true); // Autoriser toutes les permissions pour éviter les blocages
    });

    log.info('✅ [ULTIMATE] Session par défaut configurée');
  }

  /**
   * Configure les intercepteurs de session
   */
  setupSessionInterceptors() {
    log.info('🔧 [ULTIMATE] Configuration des intercepteurs de session...');

    const defaultSession = session.defaultSession;

    // INTERCEPTEUR ULTIME POUR LES REQUÊTES
    defaultSession.webRequest.onBeforeSendHeaders([], (details, callback) => {
      if (this.isTargetUrl(details.url)) {
        log.info(`🔧 [ULTIMATE] SESSION REQUÊTE: ${details.url}`);
        
        const headers = { ...details.requestHeaders };
        
        // FORCER les headers optimisés
        headers['Accept-Encoding'] = ['identity'];
        headers['Cache-Control'] = ['no-cache, no-store, must-revalidate'];
        headers['Pragma'] = ['no-cache'];
        headers['Connection'] = ['close']; // Forcer la fermeture de connexion
        headers['X-Ultimate-Request'] = ['true'];
        
        callback({
          cancel: false,
          requestHeaders: headers
        });
      } else {
        callback({ cancel: false });
      }
    });

    // INTERCEPTEUR ULTIME POUR LES RÉPONSES
    defaultSession.webRequest.onHeadersReceived([], (details, callback) => {
      if (this.isTargetUrl(details.url)) {
        log.info(`🔧 [ULTIMATE] SESSION RÉPONSE: ${details.url}`);
        
        const headers = { ...details.responseHeaders };
        
        // SUPPRESSION ULTRA-AGRESSIVE
        delete headers['content-length'];
        delete headers['Content-Length'];
        delete headers['content-encoding'];
        delete headers['Content-Encoding'];
        delete headers['transfer-encoding'];
        delete headers['Transfer-Encoding'];
        delete headers['content-range'];
        delete headers['Content-Range'];
        
        // FORCER les headers de cache
        headers['Cache-Control'] = ['no-cache, no-store, must-revalidate'];
        headers['Pragma'] = ['no-cache'];
        headers['Expires'] = ['0'];
        headers['Connection'] = ['close'];
        
        // Headers de tracking
        headers['X-Ultimate-Response-Fixed'] = ['true'];
        headers['X-Ultimate-Fix-Count'] = [this.fixedRequests.toString()];
        
        this.fixedRequests++;
        this.interceptedUrls.add(details.url);
        
        log.info(`✅ [ULTIMATE] SESSION CORRIGÉE (${this.fixedRequests}): ${details.url}`);
        
        callback({
          cancel: false,
          responseHeaders: headers
        });
      } else {
        callback({ cancel: false });
      }
    });

    // INTERCEPTEUR ULTIME POUR LES ERREURS
    defaultSession.webRequest.onErrorOccurred([], (details) => {
      if (this.isTargetUrl(details.url)) {
        log.error(`🚨 [ULTIMATE] ERREUR SESSION: ${details.url} - ${details.error}`);
        
        if (details.error.includes('CONTENT_LENGTH_MISMATCH')) {
          log.error(`🚨 [ULTIMATE] ❌ ERREUR CONTENT-LENGTH MISMATCH ENCORE PRÉSENTE !`);
          log.error(`🚨 [ULTIMATE] ❌ URL: ${details.url}`);
          log.error(`🚨 [ULTIMATE] ❌ La solution ultime a échoué pour cette requête !`);
        }
      }
    });

    log.info('✅ [ULTIMATE] Intercepteurs de session configurés');
  }

  /**
   * Configure les intercepteurs de requêtes
   */
  setupRequestInterceptors() {
    log.info('🔧 [ULTIMATE] Configuration des intercepteurs de requêtes...');

    // Intercepter toutes les requêtes sortantes
    const { net } = require('electron');
    
    // Monkey-patch net.request pour intercepter toutes les requêtes
    const originalNetRequest = net.request;
    net.request = (options) => {
      if (typeof options === 'string') {
        options = { url: options };
      }
      
      if (this.isTargetUrl(options.url)) {
        log.info(`🔧 [ULTIMATE] NET REQUEST INTERCEPTÉ: ${options.url}`);
        
        // Modifier les options de requête
        options.headers = {
          ...options.headers,
          'Accept-Encoding': 'identity',
          'Cache-Control': 'no-cache',
          'Connection': 'close',
          'X-Ultimate-Net-Request': 'true'
        };
      }
      
      return originalNetRequest(options);
    };

    log.info('✅ [ULTIMATE] Intercepteurs de requêtes configurés');
  }

  /**
   * Vérifie si une URL est ciblée
   */
  isTargetUrl(url) {
    if (!url) return false;
    
    const isLocal = url.includes('**************:8069') || 
                   url.includes('localhost:8069') || 
                   url.includes('127.0.0.1:8069');
    
    const isAsset = url.includes('web.assets_') || 
                   url.includes('load_menus') || 
                   url.includes('web/webclient') ||
                   url.includes('.js') ||
                   url.includes('.css');
    
    return isLocal && isAsset;
  }

  /**
   * Obtient les statistiques
   */
  getStats() {
    return {
      isActive: this.isActive,
      fixedRequests: this.fixedRequests,
      interceptedUrls: Array.from(this.interceptedUrls)
    };
  }
}

// Instance globale
let ultimateFixInstance = null;

/**
 * Obtient l'instance de la solution ultime
 */
function getUltimateFix() {
  if (!ultimateFixInstance) {
    ultimateFixInstance = new UltimateContentLengthFix();
  }
  return ultimateFixInstance;
}

/**
 * Active la solution ultime AVANT app.ready
 */
function activateUltimateFixBeforeReady() {
  const fix = getUltimateFix();
  fix.activateBeforeAppReady();
  return fix;
}

/**
 * Active la solution ultime APRÈS app.ready
 */
function activateUltimateFixAfterReady() {
  const fix = getUltimateFix();
  fix.activateAfterAppReady();
  return fix;
}

/**
 * Initialise la solution ultime complète
 */
function initializeUltimateFix() {
  log.info('🚀 [ULTIMATE] INITIALISATION DE LA SOLUTION ULTIME...');
  
  try {
    // Activer avant app.ready
    const fix = activateUltimateFixBeforeReady();
    
    // Programmer l'activation après app.ready
    app.whenReady().then(() => {
      activateUltimateFixAfterReady();
    });
    
    log.info('✅ [ULTIMATE] SOLUTION ULTIME INITIALISÉE');
    return fix;
  } catch (error) {
    log.error(`❌ [ULTIMATE] Erreur lors de l'initialisation: ${error.message}`);
    throw error;
  }
}

module.exports = {
  UltimateContentLengthFix,
  getUltimateFix,
  activateUltimateFixBeforeReady,
  activateUltimateFixAfterReady,
  initializeUltimateFix
};
