/**
 * Solution Proxy pour ERR_CONTENT_LENGTH_MISMATCH
 * Crée un proxy local qui corrige les réponses du serveur Odoo
 */

const http = require('http');
const httpProxy = require('http-proxy');
const log = require('electron-log');

class ProxyContentLengthFix {
  constructor() {
    this.proxy = null;
    this.server = null;
    this.proxyPort = 8070; // Port du proxy local
    this.targetHost = '**************';
    this.targetPort = 8069;
    this.isRunning = false;
    this.fixedRequests = 0;
  }

  /**
   * Démarre le proxy de correction
   */
  async startProxy() {
    if (this.isRunning) {
      log.info('🔧 [Proxy] Proxy déjà en cours d\'exécution');
      return;
    }

    log.info('🚀 [Proxy] ========================================');
    log.info('🚀 [Proxy] DÉMARRAGE DU PROXY DE CORRECTION');
    log.info('🚀 [Proxy] ========================================');

    try {
      // Créer le proxy
      this.proxy = httpProxy.createProxyServer({
        target: `http://${this.targetHost}:${this.targetPort}`,
        changeOrigin: true,
        timeout: 30000,
        proxyTimeout: 30000
      });

      // Configurer les événements du proxy
      this.setupProxyEvents();

      // Créer le serveur HTTP
      this.server = http.createServer((req, res) => {
        this.handleRequest(req, res);
      });

      // Démarrer le serveur
      await new Promise((resolve, reject) => {
        this.server.listen(this.proxyPort, 'localhost', (error) => {
          if (error) {
            reject(error);
          } else {
            this.isRunning = true;
            log.info(`✅ [Proxy] Proxy démarré sur http://localhost:${this.proxyPort}`);
            log.info(`✅ [Proxy] Redirection vers ${this.targetHost}:${this.targetPort}`);
            resolve();
          }
        });
      });

    } catch (error) {
      log.error(`❌ [Proxy] Erreur lors du démarrage: ${error.message}`);
      throw error;
    }
  }

  /**
   * Configure les événements du proxy
   */
  setupProxyEvents() {
    // Intercepter les réponses pour les corriger
    this.proxy.on('proxyRes', (proxyRes, req, res) => {
      this.handleProxyResponse(proxyRes, req, res);
    });

    // Intercepter les requêtes pour les optimiser
    this.proxy.on('proxyReq', (proxyReq, req, res) => {
      this.handleProxyRequest(proxyReq, req, res);
    });

    // Gérer les erreurs
    this.proxy.on('error', (error, req, res) => {
      log.error(`❌ [Proxy] Erreur proxy: ${error.message}`);
      if (res && !res.headersSent) {
        res.writeHead(500, { 'Content-Type': 'text/plain' });
        res.end('Erreur du proxy de correction');
      }
    });
  }

  /**
   * Gère les requêtes entrantes
   */
  handleRequest(req, res) {
    const isAssetRequest = this.isAssetRequest(req.url);
    
    if (isAssetRequest) {
      log.info(`🔧 [Proxy] REQUÊTE ASSET: ${req.method} ${req.url}`);
      
      // Modifier les headers de requête pour les assets
      req.headers['accept-encoding'] = 'identity';
      req.headers['cache-control'] = 'no-cache';
      req.headers['pragma'] = 'no-cache';
      req.headers['x-proxy-fixed'] = 'true';
    }

    // Proxifier la requête
    this.proxy.web(req, res);
  }

  /**
   * Gère les requêtes sortantes vers le serveur
   */
  handleProxyRequest(proxyReq, req, res) {
    if (this.isAssetRequest(req.url)) {
      // Forcer les headers optimisés pour les assets
      proxyReq.setHeader('Accept-Encoding', 'identity');
      proxyReq.setHeader('Cache-Control', 'no-cache');
      proxyReq.setHeader('Connection', 'close');
      proxyReq.setHeader('X-Proxy-Request', 'true');
      
      log.debug(`🔧 [Proxy] Headers requête optimisés pour: ${req.url}`);
    }
  }

  /**
   * Gère les réponses du serveur pour les corriger
   */
  handleProxyResponse(proxyRes, req, res) {
    const isAssetRequest = this.isAssetRequest(req.url);
    
    if (isAssetRequest) {
      log.info(`🔧 [Proxy] RÉPONSE ASSET: ${req.url} - Status: ${proxyRes.statusCode}`);
      
      // Analyser les headers originaux
      const originalContentLength = proxyRes.headers['content-length'];
      const originalContentEncoding = proxyRes.headers['content-encoding'];
      
      if (originalContentLength) {
        log.info(`🔧 [Proxy] Content-Length original: ${originalContentLength}`);
      }
      if (originalContentEncoding) {
        log.info(`🔧 [Proxy] Content-Encoding original: ${originalContentEncoding}`);
      }

      // CORRECTION: Supprimer les headers problématiques
      delete proxyRes.headers['content-length'];
      delete proxyRes.headers['content-encoding'];
      delete proxyRes.headers['transfer-encoding'];
      
      // Ajouter des headers de cache optimisés
      proxyRes.headers['cache-control'] = 'no-cache, no-store, must-revalidate';
      proxyRes.headers['pragma'] = 'no-cache';
      proxyRes.headers['expires'] = '0';
      
      // Headers de tracking
      proxyRes.headers['x-proxy-fixed'] = 'true';
      proxyRes.headers['x-proxy-fix-time'] = Date.now().toString();
      
      this.fixedRequests++;
      log.info(`✅ [Proxy] RÉPONSE CORRIGÉE (${this.fixedRequests}): ${req.url}`);
      
      // Collecter les données pour recalculer la taille
      let data = Buffer.alloc(0);
      const originalWrite = res.write.bind(res);
      const originalEnd = res.end.bind(res);
      
      // Intercepter les données
      proxyRes.on('data', (chunk) => {
        data = Buffer.concat([data, chunk]);
      });
      
      proxyRes.on('end', () => {
        // Définir la taille correcte
        proxyRes.headers['content-length'] = data.length.toString();
        
        log.info(`🔧 [Proxy] Taille recalculée: ${data.length} bytes pour ${req.url}`);
        
        // Envoyer les headers corrigés
        res.writeHead(proxyRes.statusCode, proxyRes.headers);
        
        // Envoyer les données
        res.end(data);
      });
      
      // Empêcher l'envoi automatique
      res.write = () => {};
      res.end = () => {};
      
    } else {
      // Pour les autres requêtes, laisser passer normalement
      log.debug(`🔧 [Proxy] Requête non-asset: ${req.url}`);
    }
  }

  /**
   * Vérifie si une URL est un asset problématique
   */
  isAssetRequest(url) {
    if (!url) return false;
    
    return url.includes('web.assets_backend.js') ||
           url.includes('web.assets_common.js') ||
           url.includes('web.assets_backend.css') ||
           url.includes('web.assets_common.css') ||
           url.includes('load_menus') ||
           url.includes('web/webclient');
  }

  /**
   * Arrête le proxy
   */
  async stopProxy() {
    if (!this.isRunning) {
      return;
    }

    log.info('🛑 [Proxy] Arrêt du proxy...');

    try {
      if (this.server) {
        await new Promise((resolve) => {
          this.server.close(() => {
            log.info('✅ [Proxy] Serveur arrêté');
            resolve();
          });
        });
      }

      if (this.proxy) {
        this.proxy.close();
        log.info('✅ [Proxy] Proxy fermé');
      }

      this.isRunning = false;
      log.info('✅ [Proxy] Proxy arrêté avec succès');

    } catch (error) {
      log.error(`❌ [Proxy] Erreur lors de l'arrêt: ${error.message}`);
    }
  }

  /**
   * Obtient l'URL du proxy pour remplacer l'URL originale
   */
  getProxyUrl() {
    return `http://localhost:${this.proxyPort}`;
  }

  /**
   * Obtient les statistiques
   */
  getStats() {
    return {
      isRunning: this.isRunning,
      proxyPort: this.proxyPort,
      targetHost: this.targetHost,
      targetPort: this.targetPort,
      fixedRequests: this.fixedRequests,
      proxyUrl: this.getProxyUrl()
    };
  }
}

// Instance globale
let proxyFixInstance = null;

/**
 * Obtient l'instance du proxy
 */
function getProxyFix() {
  if (!proxyFixInstance) {
    proxyFixInstance = new ProxyContentLengthFix();
  }
  return proxyFixInstance;
}

/**
 * Démarre le proxy de correction
 */
async function startProxyContentLengthFix() {
  const proxy = getProxyFix();
  await proxy.startProxy();
  return proxy;
}

/**
 * Arrête le proxy de correction
 */
async function stopProxyContentLengthFix() {
  const proxy = getProxyFix();
  await proxy.stopProxy();
}

/**
 * Initialise le proxy de correction
 */
async function initializeProxyFix() {
  log.info('🚀 [Proxy] INITIALISATION DU PROXY DE CORRECTION...');
  
  try {
    const proxy = await startProxyContentLengthFix();
    log.info('✅ [Proxy] PROXY DE CORRECTION INITIALISÉ AVEC SUCCÈS');
    return proxy;
  } catch (error) {
    log.error(`❌ [Proxy] Erreur lors de l'initialisation: ${error.message}`);
    throw error;
  }
}

module.exports = {
  ProxyContentLengthFix,
  getProxyFix,
  startProxyContentLengthFix,
  stopProxyContentLengthFix,
  initializeProxyFix
};
