/**
 * Module pour détecter automatiquement les serveurs disponibles
 * et choisir le serveur approprié selon les priorités définies
 */

const axios = require('axios');
const log = require('electron-log');
const os = require('os');
const net = require('net');
const userPreferences = require('./user-preferences');
const lastValidIp = require('./last-valid-ip');

// Port Odoo standard
const ODOO_PORT = 8069;

// URLs des serveurs à vérifier (valeurs par défaut)
const SERVER_URLS = {
  LOCAL: 'http://localhost:8069',
  REMOTE: 'https://edara.ligne-digitale.com'
};

// Variable pour stocker le résultat des vérifications de serveur
const serverAvailabilityCache = new Map();
const SERVER_CHECK_CACHE_DURATION = 2 * 60 * 1000; // 2 minutes en millisecondes

/**
 * Vérifie si un serveur est disponible
 * @param {string} url - L'URL du serveur à vérifier
 * @param {number} timeout - Le délai d'attente en millisecondes
 * @param {boolean} [useCache=true] - Indique si on doit utiliser le cache pour la vérification
 * @returns {Promise<boolean>} - true si le serveur est disponible, false sinon
 */
async function checkServerAvailability(url, timeout = 3000, useCache = true) {
  // Vérifier si on a un résultat en cache pour cette URL
  const now = Date.now();
  const cacheKey = `${url}_${timeout}`;
  const cachedResult = serverAvailabilityCache.get(cacheKey);

  if (useCache && cachedResult && (now - cachedResult.timestamp < SERVER_CHECK_CACHE_DURATION)) {
    log.info(`Utilisation du résultat en cache pour ${url} (${Math.round((now - cachedResult.timestamp) / 1000)}s): ${cachedResult.available}`);
    return cachedResult.available;
  }

  try {
    log.info(`Vérification de la disponibilité du serveur: ${url} (timeout: ${timeout}ms)`);

    // Déterminer si c'est une vérification locale ou distante
    const isLocal = url.includes('localhost') || url.includes('127.0.0.1') || url.match(/^http:\/\/\d+\.\d+\.\d+\.\d+/);
    const logLevel = isLocal ? 'info' : 'warn'; // Utiliser 'warn' pour les serveurs distants uniquement en cas d'erreur

    // Essayer d'abord avec une URL simple pour vérifier si le serveur répond
    try {
      const baseResponse = await axios.get(`${url}/web`, {
        timeout: timeout,
        validateStatus: status => status >= 200 && status < 500, // Accepter tous les codes de statut entre 200 et 499
        headers: {
          'Accept': 'text/html',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      // Si le serveur répond avec un code 200, 301, 302 ou 303, c'est probablement un serveur Odoo
      if (baseResponse.status >= 200 && baseResponse.status < 400) {
        log.info(`Serveur ${url} disponible, code de statut: ${baseResponse.status}`);
        // Mettre en cache le résultat
        serverAvailabilityCache.set(cacheKey, { available: true, timestamp: now });
        return true;
      }

      // Si le serveur répond avec un code 400 ou 401, c'est peut-être un serveur Odoo qui demande une authentification
      if (baseResponse.status === 400 || baseResponse.status === 401) {
        log.info(`Serveur ${url} disponible (authentification requise), code de statut: ${baseResponse.status}`);
        // Mettre en cache le résultat
        serverAvailabilityCache.set(cacheKey, { available: true, timestamp: now });
        return true;
      }
    } catch (baseError) {
      // Ne pas afficher d'avertissement pour les timeouts sur les serveurs distants
      if (baseError.code === 'ECONNABORTED' && !isLocal) {
        log[logLevel](`Timeout lors de la vérification de base de ${url}: ${baseError.message}`);
      } else {
        log[logLevel](`Erreur lors de la vérification de base de ${url}: ${baseError.message}`);
      }
      // Continuer avec la vérification suivante
    }

    // Essayer ensuite avec l'URL de vérification de version
    try {
      const versionResponse = await axios.get(`${url}/web/webclient/version_info`, {
        timeout: timeout,
        validateStatus: status => status >= 200 && status < 500, // Accepter tous les codes de statut entre 200 et 499
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      // Si le serveur répond avec un code 200 et des données valides, c'est un serveur Odoo
      if (versionResponse.data && (versionResponse.data.result || versionResponse.data.server_version)) {
        log.info(`Serveur Odoo ${url} disponible et valide, code de statut: ${versionResponse.status}`);
        // Mettre en cache le résultat
        serverAvailabilityCache.set(cacheKey, { available: true, timestamp: now });
        return true;
      }

      // Si le serveur répond avec un code 200, 400 ou 401, c'est probablement un serveur Odoo
      if (versionResponse.status === 200 || versionResponse.status === 400 || versionResponse.status === 401) {
        log.info(`Serveur ${url} disponible, code de statut: ${versionResponse.status}`);
        // Mettre en cache le résultat
        serverAvailabilityCache.set(cacheKey, { available: true, timestamp: now });
        return true;
      }
    } catch (versionError) {
      // Ne pas afficher d'avertissement pour les timeouts sur les serveurs distants
      if (versionError.code === 'ECONNABORTED' && !isLocal) {
        log[logLevel](`Timeout lors de la vérification de version de ${url}: ${versionError.message}`);
      } else {
        log[logLevel](`Erreur lors de la vérification de version de ${url}: ${versionError.message}`);
      }
      // Continuer avec la vérification suivante
    }

    // Essayer enfin avec l'URL de login
    try {
      const loginResponse = await axios.get(`${url}/web/login`, {
        timeout: timeout,
        validateStatus: status => status >= 200 && status < 500, // Accepter tous les codes de statut entre 200 et 499
        headers: {
          'Accept': 'text/html',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      // Si le serveur répond avec un code 200, c'est probablement un serveur Odoo
      if (loginResponse.status >= 200 && loginResponse.status < 400) {
        log.info(`Serveur ${url} disponible (page de login), code de statut: ${loginResponse.status}`);
        // Mettre en cache le résultat
        serverAvailabilityCache.set(cacheKey, { available: true, timestamp: now });
        return true;
      }
    } catch (loginError) {
      // Ne pas afficher d'avertissement pour les timeouts sur les serveurs distants
      if (loginError.code === 'ECONNABORTED' && !isLocal) {
        log[logLevel](`Timeout lors de la vérification de login de ${url}: ${loginError.message}`);
      } else {
        log[logLevel](`Erreur lors de la vérification de login de ${url}: ${loginError.message}`);
      }
    }

    // Si toutes les vérifications ont échoué, le serveur n'est pas disponible
    log.info(`Serveur ${url} non disponible après toutes les vérifications`);
    // Mettre en cache le résultat
    serverAvailabilityCache.set(cacheKey, { available: false, timestamp: now });
    return false;
  } catch (error) {
    log.warn(`Serveur ${url} non disponible: ${error.message}`);
    // Mettre en cache le résultat
    serverAvailabilityCache.set(cacheKey, { available: false, timestamp: now });
    return false;
  }
}

/**
 * Récupère l'adresse IP locale (non-interne) de la machine
 * @returns {string|null} - L'adresse IP locale ou null si aucune n'est trouvée
 */
function getLocalIpAddress() {
  try {
    const interfaces = os.networkInterfaces();
    let ipAddress = null;

    // Parcourir toutes les interfaces réseau
    for (const ifaceName in interfaces) {
      const iface = interfaces[ifaceName];

      // Parcourir toutes les adresses de l'interface
      for (const alias of iface) {
        // Ignorer les adresses IPv6 et les interfaces internes (comme 127.0.0.1)
        if (alias.family === 'IPv4' && !alias.internal) {
          log.info(`Adresse IP locale trouvée: ${alias.address} (interface: ${ifaceName})`);
          return alias.address;
        }
      }
    }

    log.warn('Aucune adresse IP locale trouvée');
    return null;
  } catch (error) {
    log.error(`Erreur lors de la récupération de l'adresse IP locale: ${error.message}`);
    return null;
  }
}

/**
 * Génère l'URL du serveur Odoo en remplaçant le dernier octet de l'adresse IP par .27
 * @param {string} localIp - L'adresse IP locale
 * @param {boolean} [useLastValid=true] - Indique si on doit utiliser la dernière IP valide si disponible
 * @returns {string|null} - L'URL du serveur Odoo ou null si l'adresse IP n'est pas valide
 */
function getOdooServerUrl(localIp, useLastValid = true) {
  // Vérifier d'abord si on a une dernière IP valide
  if (useLastValid) {
    const lastIp = lastValidIp.getLastValidIp();
    if (lastIp) {
      log.info(`Utilisation de la dernière adresse IP valide pour le serveur Odoo: ${lastIp}`);
      return `http://${lastIp}:${ODOO_PORT}`;
    }
  }

  if (!localIp) return null;

  try {
    // Diviser l'adresse IP en octets
    const ipParts = localIp.split('.');
    if (ipParts.length !== 4) {
      log.warn(`Format d'adresse IP invalide: ${localIp}`);
      return null;
    }

    // Remplacer le dernier octet par 27
    ipParts[3] = '27';

    // Reconstruire l'adresse IP
    const odooIp = ipParts.join('.');
    log.info(`Adresse IP du serveur Odoo générée: ${odooIp} (basée sur l'IP locale: ${localIp})`);

    return `http://${odooIp}:${ODOO_PORT}`;
  } catch (error) {
    log.error(`Erreur lors de la génération de l'URL du serveur Odoo: ${error.message}`);
    return null;
  }
}

/**
 * Construit l'URL du serveur local en utilisant l'adresse IP locale
 * @returns {string|null} - L'URL du serveur local ou null si aucune adresse IP locale n'est trouvée
 */
function getLocalServerUrl() {
  const localIp = getLocalIpAddress();
  if (localIp) {
    // Essayer d'abord de générer l'URL du serveur Odoo en remplaçant le dernier octet par .27
    const odooServerUrl = getOdooServerUrl(localIp);
    if (odooServerUrl) {
      log.info(`URL du serveur Odoo générée: ${odooServerUrl}`);
      return odooServerUrl;
    }

    // Si la génération de l'URL du serveur Odoo échoue, utiliser l'adresse IP locale
    log.info(`Utilisation de l'adresse IP locale pour l'URL du serveur: ${localIp}`);
    return `http://${localIp}:${ODOO_PORT}`;
  }

  log.info(`Aucune adresse IP locale trouvée, utilisation de localhost: ${SERVER_URLS.LOCAL}`);
  return SERVER_URLS.LOCAL; // Fallback sur localhost
}

/**
 * Vérifie si un serveur Odoo est disponible en local (sur l'adresse IP locale ou localhost)
 * @returns {Promise<{available: boolean, url: string|null}>} - Un objet indiquant si un serveur local est disponible et son URL
 */
async function checkLocalServerAvailability() {
  log.info('Vérification de la disponibilité d\'un serveur Odoo en local');

  // Tableau des URLs à vérifier
  const urlsToCheck = [];

  // 0. Vérifier d'abord la dernière adresse IP valide (PRIORITÉ ABSOLUE)
  const lastIp = lastValidIp.getLastValidIp();
  if (lastIp) {
    // Construire l'URL avec la dernière IP valide
    const lastValidIpUrl = `http://${lastIp}:${ODOO_PORT}`;
    log.info(`Ajout de l'URL basée sur la dernière IP valide à vérifier en priorité absolue: ${lastValidIpUrl}`);
    urlsToCheck.push({ url: lastValidIpUrl, type: 'last_valid_ip', priority: 0 });
  }

  // 1. Ajouter l'URL du serveur Odoo avec le dernier octet .27 (PRIORITAIRE)
  const localIp = getLocalIpAddress();
  if (localIp) {
    const odooServerUrl = getOdooServerUrl(localIp);
    if (odooServerUrl) {
      log.info(`Ajout de l'URL du serveur Odoo (dernier octet .27) à vérifier en priorité: ${odooServerUrl}`);
      urlsToCheck.push({ url: odooServerUrl, type: 'odoo_server', priority: 1 });
    }

    // Ajouter aussi l'URL basée sur l'adresse IP locale (comme fallback)
    const localIpUrl = `http://${localIp}:${ODOO_PORT}`;
    log.info(`Ajout de l'URL basée sur l'adresse IP locale à vérifier: ${localIpUrl}`);
    urlsToCheck.push({ url: localIpUrl, type: 'ip', priority: 2 });
  }

  // 2. Ajouter localhost
  log.info(`Ajout de localhost à vérifier: ${SERVER_URLS.LOCAL}`);
  urlsToCheck.push({ url: SERVER_URLS.LOCAL, type: 'localhost', priority: 3 });

  // 3. Ajouter 127.0.0.1 explicitement (au cas où la résolution DNS de localhost échoue)
  const loopbackUrl = `http://127.0.0.1:${ODOO_PORT}`;
  log.info(`Ajout de l'adresse de bouclage à vérifier: ${loopbackUrl}`);
  urlsToCheck.push({ url: loopbackUrl, type: 'loopback', priority: 4 });

  // Trier les URLs par priorité
  urlsToCheck.sort((a, b) => a.priority - b.priority);

  // Vérifier chaque URL
  for (const { url, type, priority } of urlsToCheck) {
    log.info(`Vérification de la disponibilité d'Odoo sur ${type} (priorité: ${priority}): ${url}`);

    try {
      // Vérifier d'abord si le port est ouvert (pour les adresses IP et le serveur Odoo)
      if (type === 'ip' || type === 'odoo_server') {
        const host = url.replace(/^https?:\/\//, '').split(':')[0];
        // Réduire le timeout pour la vérification du port à 500ms
        const isPortAvailable = await checkPortAvailability(host, ODOO_PORT, 500);

        if (!isPortAvailable) {
          log.info(`Port ${ODOO_PORT} non disponible sur ${host}, passage à l'URL suivante`);
          continue;
        }
      }

      // Ensuite, vérifier si le serveur Odoo répond correctement
      // Réduire le timeout pour la vérification du serveur à 1000ms
      const isServerAvailable = await checkServerAvailability(url, 1000);

      if (isServerAvailable) {
        log.info(`Serveur Odoo disponible sur ${type}: ${url}`);

        // Extraire l'adresse IP de l'URL et la sauvegarder comme dernière IP valide
        try {
          const urlObj = new URL(url);
          const host = urlObj.hostname;

          // Vérifier si c'est une adresse IP (pas localhost ou 127.0.0.1)
          if (lastValidIp.isValidIpAddress(host)) {
            log.info(`Sauvegarde de l'adresse IP valide: ${host}`);
            lastValidIp.saveLastValidIp(host);
          }
        } catch (error) {
          log.warn(`Erreur lors de l'extraction de l'adresse IP de l'URL ${url}: ${error.message}`);
        }

        return { available: true, url: url };
      } else {
        log.info(`Serveur Odoo non disponible sur ${type}: ${url}`);
      }
    } catch (error) {
      log.warn(`Erreur lors de la vérification de ${url}: ${error.message}`);
    }
  }

  log.info('Aucun serveur Odoo disponible en local après vérification de toutes les URLs');
  return { available: false, url: null };
}

/**
 * Vérifie si un service est disponible sur une adresse IP et un port spécifiques
 * @param {string} host - L'adresse IP ou le nom d'hôte à vérifier
 * @param {number} port - Le port à vérifier
 * @param {number} [timeout=2000] - Le délai d'attente en millisecondes
 * @returns {Promise<boolean>} - true si le service est disponible, false sinon
 */
function checkPortAvailability(host, port, timeout = 2000) {
  return new Promise((resolve) => {
    // Vérifier si l'hôte est valide
    if (!host) {
      log.warn(`Hôte invalide pour la vérification du port ${port}`);
      resolve(false);
      return;
    }

    const socket = new net.Socket();
    let status = false;
    let isResolved = false;

    // Fonction pour résoudre la promesse une seule fois
    const resolveOnce = (value) => {
      if (!isResolved) {
        isResolved = true;
        resolve(value);
      }
    };

    // Définir le timeout pour la connexion
    socket.setTimeout(timeout);

    // Gérer les événements de connexion
    socket.on('connect', () => {
      log.info(`Port ${port} ouvert sur ${host}`);
      status = true;
      socket.destroy();
    });

    socket.on('timeout', () => {
      log.warn(`Timeout lors de la vérification du port ${port} sur ${host}`);
      socket.destroy();
    });

    socket.on('error', (error) => {
      // Si l'erreur est ECONNREFUSED, cela signifie que l'hôte existe mais que le port n'est pas ouvert
      if (error.code === 'ECONNREFUSED') {
        log.info(`Hôte ${host} existe mais le port ${port} n'est pas ouvert`);
      } else {
        log.warn(`Erreur lors de la vérification du port ${port} sur ${host}: ${error.message} (${error.code})`);
      }
      socket.destroy();
    });

    socket.on('close', () => {
      log.info(`Socket fermé pour la vérification du port ${port} sur ${host}, statut: ${status}`);
      resolveOnce(status);
    });

    // Tenter de se connecter
    try {
      log.info(`Tentative de connexion au port ${port} sur ${host}`);
      socket.connect(port, host);
    } catch (error) {
      log.error(`Exception lors de la connexion au port ${port} sur ${host}: ${error.message}`);
      socket.destroy();
      resolveOnce(false);
    }

    // Ajouter un timeout supplémentaire au cas où la socket ne se ferme pas correctement
    setTimeout(() => {
      if (!isResolved) {
        log.warn(`Timeout global lors de la vérification du port ${port} sur ${host}`);
        socket.destroy();
        resolveOnce(false);
      }
    }, timeout + 500); // Ajouter 500ms au timeout de la socket
  });
}

/**
 * Construit l'URL du serveur distant en fonction du nom de l'espace de travail
 * @returns {string} - L'URL du serveur distant
 */
function getRemoteServerUrl() {
  try {
    // Récupérer le nom de l'espace de travail depuis les préférences utilisateur
    const workspaceName = userPreferences.getWorkspaceName();

    if (workspaceName && workspaceName !== 'edara') {
      const remoteUrl = `https://${workspaceName}.ligne-digitale.com`;
      log.info(`URL du serveur distant construite à partir du nom de l'espace: ${remoteUrl}`);
      return remoteUrl;
    }

    // Si le nom de l'espace est 'edara' ou non défini, utiliser l'URL par défaut
    log.info(`Utilisation de l'URL distante par défaut: ${SERVER_URLS.REMOTE}`);
    return SERVER_URLS.REMOTE;
  } catch (error) {
    log.error(`Erreur lors de la construction de l'URL distante: ${error.message}`);
    return SERVER_URLS.REMOTE;
  }
}

// Variable pour stocker le résultat de la dernière vérification du serveur distant
let lastRemoteCheckResult = null;
let lastRemoteCheckTime = 0;
const REMOTE_CHECK_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes en millisecondes

/**
 * Vérifie si un serveur Odoo est disponible à distance
 * @param {boolean} [useCache=true] - Indique si on doit utiliser le cache pour la vérification
 * @returns {Promise<{available: boolean, url: string}>} - Un objet indiquant si un serveur distant est disponible et son URL
 */
async function checkRemoteServerAvailability(useCache = true) {
  // Si on utilise le cache et qu'une vérification récente existe, la retourner
  const now = Date.now();
  if (useCache && lastRemoteCheckResult && (now - lastRemoteCheckTime < REMOTE_CHECK_CACHE_DURATION)) {
    log.info(`Utilisation du résultat en cache pour le serveur distant (${Math.round((now - lastRemoteCheckTime) / 1000)}s): ${lastRemoteCheckResult.url}, disponible: ${lastRemoteCheckResult.available}`);
    return lastRemoteCheckResult;
  }

  log.info('Vérification de la disponibilité d\'un serveur Odoo à distance');

  // Construire l'URL du serveur distant
  const remoteUrl = getRemoteServerUrl();
  log.info(`Vérification de la disponibilité d'Odoo sur le serveur distant: ${remoteUrl}`);

  try {
    // Vérifier si le serveur distant est disponible avec un timeout plus long (8 secondes)
    const isRemoteAvailable = await checkServerAvailability(remoteUrl, 8000);
    if (isRemoteAvailable) {
      log.info(`Serveur Odoo disponible à distance: ${remoteUrl}`);
      const result = { available: true, url: remoteUrl };

      // Mettre en cache le résultat
      lastRemoteCheckResult = result;
      lastRemoteCheckTime = now;

      return result;
    }

    // Si le serveur distant personnalisé n'est pas disponible, essayer l'URL par défaut
    if (remoteUrl !== SERVER_URLS.REMOTE) {
      log.info(`Vérification de la disponibilité d'Odoo sur le serveur distant par défaut: ${SERVER_URLS.REMOTE}`);

      try {
        const isDefaultRemoteAvailable = await checkServerAvailability(SERVER_URLS.REMOTE, 8000);
        if (isDefaultRemoteAvailable) {
          log.info(`Serveur Odoo disponible sur le serveur distant par défaut: ${SERVER_URLS.REMOTE}`);
          const result = { available: true, url: SERVER_URLS.REMOTE };

          // Mettre en cache le résultat
          lastRemoteCheckResult = result;
          lastRemoteCheckTime = now;

          return result;
        }
      } catch (defaultError) {
        log.warn(`Erreur lors de la vérification du serveur distant par défaut: ${defaultError.message}`);
      }
    }

    log.info('Aucun serveur Odoo disponible à distance');
    const result = { available: false, url: remoteUrl }; // Retourner l'URL même si le serveur n'est pas disponible

    // Mettre en cache le résultat
    lastRemoteCheckResult = result;
    lastRemoteCheckTime = now;

    return result;
  } catch (error) {
    log.warn(`Erreur lors de la vérification du serveur distant: ${error.message}`);

    // En cas d'erreur, supposer que le serveur est disponible pour éviter de bloquer l'utilisateur
    const result = { available: true, url: remoteUrl, warning: error.message };

    // Mettre en cache le résultat
    lastRemoteCheckResult = result;
    lastRemoteCheckTime = now;

    return result;
  }
}

// Variable pour stocker le résultat de la dernière détection de serveur
let lastBestServerResult = null;
let lastBestServerCheckTime = 0;
const BEST_SERVER_CACHE_DURATION = 3 * 60 * 1000; // 3 minutes en millisecondes

/**
 * Détecte automatiquement le serveur disponible selon les priorités
 * @param {boolean} [useCache=true] - Indique si on doit utiliser le cache pour la détection
 * @param {boolean} [forceLocalCheck=false] - Force la vérification du serveur local même si on utilise le cache
 * @returns {Promise<{url: string, isLocal: boolean, connectionType: string}>} - Informations sur le serveur détecté
 */
async function detectBestServer(useCache = true, forceLocalCheck = false) {
  log.info('Détection automatique du meilleur serveur...');

  // Si on utilise le cache et qu'une détection récente existe
  const now = Date.now();
  if (useCache && lastBestServerResult && (now - lastBestServerCheckTime < BEST_SERVER_CACHE_DURATION)) {
    // Si le dernier résultat était un serveur distant et qu'on force la vérification locale, on vérifie quand même le serveur local
    if (forceLocalCheck && !lastBestServerResult.isLocal) {
      log.info('Vérification forcée du serveur local malgré le cache...');
      const localServer = await checkLocalServerAvailability(false); // Ne pas utiliser le cache pour cette vérification

      if (localServer.available) {
        log.info(`Serveur local détecté et disponible (vérification forcée): ${localServer.url}`);
        const result = {
          url: localServer.url,
          isLocal: true,
          connectionType: 'local'
        };

        // Mettre à jour le cache
        lastBestServerResult = result;
        lastBestServerCheckTime = now;

        return result;
      }

      // Si le serveur local n'est pas disponible, utiliser le résultat en cache
      log.info(`Aucun serveur local disponible, utilisation du résultat en cache: ${lastBestServerResult.url}`);
      return lastBestServerResult;
    }

    // Sinon, utiliser simplement le résultat en cache
    log.info(`Utilisation du résultat en cache pour le meilleur serveur (${Math.round((now - lastBestServerCheckTime) / 1000)}s): ${lastBestServerResult.url}, type: ${lastBestServerResult.connectionType}`);
    return lastBestServerResult;
  }

  try {
    // 1. Vérifier d'abord si un serveur local est disponible
    const localServer = await checkLocalServerAvailability();
    if (localServer.available) {
      log.info(`Serveur local détecté et disponible: ${localServer.url}`);
      const result = {
        url: localServer.url,
        isLocal: true,
        connectionType: 'local'
      };

      // Mettre en cache le résultat
      lastBestServerResult = result;
      lastBestServerCheckTime = now;

      return result;
    }

    // 2. Si aucun serveur local n'est disponible, vérifier le serveur distant
    const remoteServer = await checkRemoteServerAvailability();
    if (remoteServer.available) {
      log.info(`Serveur distant détecté et disponible: ${remoteServer.url}`);
      const result = {
        url: remoteServer.url,
        isLocal: false,
        connectionType: 'distance'
      };

      // Mettre en cache le résultat
      lastBestServerResult = result;
      lastBestServerCheckTime = now;

      return result;
    }

    // 3. Si aucun serveur n'est disponible, retourner le serveur distant par défaut
    log.warn('Aucun serveur disponible, utilisation du serveur distant par défaut');
    const defaultRemoteUrl = getRemoteServerUrl();
    const result = {
      url: defaultRemoteUrl,
      isLocal: false,
      connectionType: 'distance'
    };

    // Mettre en cache le résultat
    lastBestServerResult = result;
    lastBestServerCheckTime = now;

    return result;
  } catch (error) {
    log.error(`Erreur lors de la détection du serveur: ${error.message}`);

    // En cas d'erreur, retourner le serveur distant par défaut
    const defaultRemoteUrl = getRemoteServerUrl();
    const result = {
      url: defaultRemoteUrl,
      isLocal: false,
      connectionType: 'distance'
    };

    // Mettre en cache le résultat
    lastBestServerResult = result;
    lastBestServerCheckTime = now;

    return result;
  }
}

/**
 * Obtient les informations sur les serveurs disponibles (local et distant)
 * @param {boolean} [useCache=true] - Indique si on doit utiliser le cache pour les vérifications
 * @param {boolean} [prioritizeLocal=true] - Indique si on doit privilégier le serveur local
 * @returns {Promise<{local: {available: boolean, url: string}, remote: {available: boolean, url: string}, bestServer: {url: string, isLocal: boolean, connectionType: string}}>}
 */
async function getServerInfo(useCache = true, prioritizeLocal = true) {
  log.info('Récupération des informations sur les serveurs disponibles');

  try {
    // Vérifier la disponibilité du serveur local
    const localServer = await checkLocalServerAvailability();

    // Vérifier la disponibilité du serveur distant avec un timeout plus long
    // Utiliser le cache pour éviter de vérifier le serveur distant à chaque fois
    const remoteServer = await checkRemoteServerAvailability(useCache);

    // Déterminer le meilleur serveur
    let bestServer;

    // Si le serveur local est disponible et qu'on privilégie le local, l'utiliser
    if (localServer.available && prioritizeLocal) {
      bestServer = {
        url: localServer.url,
        isLocal: true,
        connectionType: 'local'
      };
    }
    // Sinon, si le serveur distant est disponible, l'utiliser
    else if (remoteServer.available) {
      bestServer = {
        url: remoteServer.url,
        isLocal: false,
        connectionType: 'distance'
      };
    }
    // Si le serveur local est disponible mais qu'on ne privilégie pas le local, l'utiliser comme fallback
    else if (localServer.available) {
      bestServer = {
        url: localServer.url,
        isLocal: true,
        connectionType: 'local'
      };
    }
    // Si aucun serveur n'est disponible, utiliser l'URL distante
    else {
      bestServer = {
        url: remoteServer.url, // Utiliser l'URL distante même si elle n'est pas disponible
        isLocal: false,
        connectionType: 'distance'
      };
    }

    // Mettre à jour le cache de détection du meilleur serveur
    lastBestServerResult = bestServer;
    lastBestServerCheckTime = Date.now();

    return {
      local: localServer,
      remote: remoteServer,
      bestServer: bestServer
    };
  } catch (error) {
    log.error(`Erreur lors de la récupération des informations sur les serveurs: ${error.message}`);

    // En cas d'erreur, retourner des valeurs par défaut
    const defaultBestServer = {
      url: getRemoteServerUrl(),
      isLocal: false,
      connectionType: 'distance'
    };

    // Mettre à jour le cache de détection du meilleur serveur
    lastBestServerResult = defaultBestServer;
    lastBestServerCheckTime = Date.now();

    return {
      local: { available: false, url: SERVER_URLS.LOCAL },
      remote: { available: false, url: getRemoteServerUrl() },
      bestServer: defaultBestServer
    };
  }
}

module.exports = {
  SERVER_URLS,
  ODOO_PORT,
  checkServerAvailability,
  checkPortAvailability,
  getLocalIpAddress,
  getOdooServerUrl,
  getLocalServerUrl,
  checkLocalServerAvailability,
  getRemoteServerUrl,
  checkRemoteServerAvailability,
  detectBestServer,
  getServerInfo
};
