/**
 * Détecteur de serveur Odoo amélioré avec découverte de service intelligente
 * Utilise des mécanismes de découverte optimisés et un cache intelligent
 */

const axios = require('axios');
const log = require('electron-log');
const os = require('os');
const net = require('net');
const { EventEmitter } = require('events');
const userPreferences = require('./user-preferences');

class EnhancedServerDetector extends EventEmitter {
  constructor() {
    super();

    // Configuration optimisée
    this.config = {
      odooPort: 8069,
      timeouts: {
        portCheck: 300,      // 300ms pour vérification de port
        httpCheck: 800,      // 800ms pour vérification HTTP
        remoteCheck: 3000    // 3s pour serveurs distants
      },
      cache: {
        ttl: 5 * 60 * 1000,           // 5 minutes TTL
        revalidateAfter: 2 * 60 * 1000, // Revalider après 2 minutes
        maxEntries: 50
      },
      discovery: {
        maxConcurrent: 10,    // Balayage parallèle
        networkScanRange: 5,  // Scanner ±5 IPs autour de l'IP locale
        retryAttempts: 2,
        retryDelay: 500
      }
    };

    // Cache intelligent avec métadonnées
    this.serverCache = new Map();
    this.networkCache = new Map();

    // État de découverte
    this.discoveryState = {
      isScanning: false,
      lastScanTime: 0,
      knownServers: new Set(),
      preferredServer: null
    };

    // Métriques de performance
    this.metrics = {
      scanDuration: 0,
      serversFound: 0,
      cacheHits: 0,
      cacheMisses: 0
    };

    this.initializeDetector();
  }

  /**
   * Initialise le détecteur
   */
  initializeDetector() {
    // Nettoyer le cache périodiquement
    setInterval(() => {
      this.cleanupCache();
    }, 60000); // Toutes les minutes

    log.info('Enhanced Server Detector initialisé');
  }

  /**
   * Découverte de service principale avec mécanismes multiples
   */
  async discoverOdooServers(options = {}) {
    const {
      useCache = true,
      forceScan = false,
      includeRemote = true,
      timeout = this.config.timeouts.httpCheck
    } = options;

    const startTime = Date.now();

    try {
      log.info('Démarrage de la découverte de serveurs Odoo...');

      // 1. Vérifier le cache d'abord
      if (useCache && !forceScan) {
        const cachedResult = this.getCachedBestServer();
        if (cachedResult) {
          this.metrics.cacheHits++;
          log.info(`Serveur trouvé en cache: ${cachedResult.url}`);
          return cachedResult;
        }
      }

      this.metrics.cacheMisses++;
      this.discoveryState.isScanning = true;

      // 2. Découverte parallèle multi-méthodes
      const discoveryPromises = [];

      // Méthode 1: mDNS/Bonjour (si disponible)
      discoveryPromises.push(this.discoverViaMDNS());

      // Méthode 2: Balayage réseau intelligent
      discoveryPromises.push(this.discoverViaNetworkScan());

      // Méthode 3: Serveurs connus/préférés
      discoveryPromises.push(this.discoverViaKnownServers());

      // Méthode 4: Serveur distant (si demandé)
      if (includeRemote) {
        discoveryPromises.push(this.discoverRemoteServer());
      }

      // Attendre toutes les découvertes avec timeout global
      const results = await Promise.allSettled(discoveryPromises);

      // 3. Analyser et prioriser les résultats
      const servers = this.analyzeDiscoveryResults(results);

      // 4. Sélectionner le meilleur serveur
      const bestServer = this.selectBestServer(servers);

      // 5. Mettre en cache et retourner
      if (bestServer) {
        this.cacheServer(bestServer);
        this.discoveryState.preferredServer = bestServer;
        this.metrics.serversFound = servers.length;
      }

      this.metrics.scanDuration = Date.now() - startTime;
      this.discoveryState.isScanning = false;
      this.discoveryState.lastScanTime = Date.now();

      log.info(`Découverte terminée en ${this.metrics.scanDuration}ms, ${servers.length} serveur(s) trouvé(s)`);

      this.emit('discovery-completed', {
        bestServer,
        allServers: servers,
        metrics: this.metrics
      });

      return bestServer;

    } catch (error) {
      this.discoveryState.isScanning = false;
      log.error(`Erreur lors de la découverte: ${error.message}`);

      // Fallback sur le cache ou serveur par défaut
      return this.getFallbackServer();
    }
  }

  /**
   * Découverte via mDNS/Bonjour (si Odoo est configuré pour cela)
   */
  async discoverViaMDNS() {
    try {
      // Note: Nécessiterait le module 'mdns' ou 'bonjour'
      // Pour l'instant, on simule cette fonctionnalité
      log.debug('Tentative de découverte mDNS...');

      // Placeholder pour future implémentation mDNS
      // const mdns = require('mdns');
      // const browser = mdns.createBrowser(mdns.tcp('http'));

      return [];
    } catch (error) {
      log.debug(`mDNS non disponible: ${error.message}`);
      return [];
    }
  }

  /**
   * Balayage réseau intelligent et parallèle
   */
  async discoverViaNetworkScan() {
    try {
      const localIp = this.getLocalIpAddress();
      if (!localIp) {
        log.warn('Aucune IP locale trouvée pour le balayage réseau');
        return [];
      }

      const ipRange = this.generateIpRange(localIp);
      log.info(`Balayage réseau de ${ipRange.length} IPs autour de ${localIp}`);

      // Balayage parallèle avec limitation de concurrence
      const servers = await this.parallelNetworkScan(ipRange);

      log.info(`Balayage réseau terminé: ${servers.length} serveur(s) trouvé(s)`);
      return servers;

    } catch (error) {
      log.error(`Erreur lors du balayage réseau: ${error.message}`);
      return [];
    }
  }

  /**
   * Génère une plage d'IPs intelligente autour de l'IP locale
   */
  generateIpRange(localIp) {
    const ipParts = localIp.split('.').map(Number);
    const baseNetwork = ipParts.slice(0, 3).join('.');
    const currentHost = ipParts[3];

    const ipsToScan = [];

    // 1. IPs prioritaires (serveurs communs)
    const priorityHosts = [27, 1, 100, 101, 200, 254];
    priorityHosts.forEach(host => {
      if (host !== currentHost && host >= 1 && host <= 254) {
        ipsToScan.push(`${baseNetwork}.${host}`);
      }
    });

    // 2. Plage autour de l'IP locale
    const scanRange = this.config.discovery.networkScanRange;
    for (let i = Math.max(1, currentHost - scanRange);
         i <= Math.min(254, currentHost + scanRange);
         i++) {
      const ip = `${baseNetwork}.${i}`;
      if (!ipsToScan.includes(ip) && i !== currentHost) {
        ipsToScan.push(ip);
      }
    }

    // 3. Dernière IP valide connue (priorité absolue)
    const lastValidIp = this.getLastValidIp();
    if (lastValidIp && !ipsToScan.includes(lastValidIp)) {
      ipsToScan.unshift(lastValidIp); // Ajouter en premier
    }

    return ipsToScan;
  }

  /**
   * Balayage réseau parallèle avec limitation de concurrence
   */
  async parallelNetworkScan(ipList) {
    const servers = [];
    const maxConcurrent = this.config.discovery.maxConcurrent;

    // Diviser en chunks pour limiter la concurrence
    for (let i = 0; i < ipList.length; i += maxConcurrent) {
      const chunk = ipList.slice(i, i + maxConcurrent);

      const chunkPromises = chunk.map(ip => this.checkOdooServer(ip));
      const chunkResults = await Promise.allSettled(chunkPromises);

      chunkResults.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value) {
          servers.push(result.value);
        }
      });

      // Petit délai entre les chunks pour éviter la surcharge réseau
      if (i + maxConcurrent < ipList.length) {
        await this.delay(100);
      }
    }

    return servers;
  }

  /**
   * Vérification rapide d'un serveur Odoo sur une IP
   */
  async checkOdooServer(ip) {
    try {
      // 1. Vérification rapide du port
      const portOpen = await this.fastPortCheck(ip, this.config.odooPort);
      if (!portOpen) {
        return null;
      }

      // 2. Vérification HTTP rapide
      const url = `http://${ip}:${this.config.odooPort}`;
      const isOdoo = await this.fastOdooCheck(url);

      if (isOdoo) {
        const server = {
          ip,
          url,
          type: 'local',
          responseTime: Date.now(),
          discovered: Date.now(),
          method: 'network-scan'
        };

        log.info(`Serveur Odoo trouvé: ${url}`);
        return server;
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Vérification ultra-rapide de port avec timeout optimisé
   */
  async fastPortCheck(host, port) {
    return new Promise((resolve) => {
      const socket = new net.Socket();
      let resolved = false;

      const cleanup = () => {
        if (!resolved) {
          resolved = true;
          socket.destroy();
        }
      };

      socket.setTimeout(this.config.timeouts.portCheck);

      socket.on('connect', () => {
        cleanup();
        resolve(true);
      });

      socket.on('timeout', () => {
        cleanup();
        resolve(false);
      });

      socket.on('error', () => {
        cleanup();
        resolve(false);
      });

      try {
        socket.connect(port, host);
      } catch (error) {
        cleanup();
        resolve(false);
      }

      // Timeout de sécurité
      setTimeout(() => {
        cleanup();
        resolve(false);
      }, this.config.timeouts.portCheck + 100);
    });
  }

  /**
   * Vérification HTTP rapide pour confirmer qu'il s'agit d'Odoo
   */
  async fastOdooCheck(url) {
    try {
      const response = await axios.get(`${url}/web/database/selector`, {
        timeout: this.config.timeouts.httpCheck,
        validateStatus: status => status >= 200 && status < 500,
        headers: {
          'User-Agent': 'Edara-ERP-Detector/1.0',
          'Accept': 'text/html,application/json',
          'Cache-Control': 'no-cache'
        }
      });

      // Vérifier les indicateurs Odoo
      const isOdoo = response.status < 400 ||
                     response.headers['server']?.includes('odoo') ||
                     response.data?.includes('odoo') ||
                     response.data?.includes('database');

      return isOdoo;
    } catch (error) {
      // Certaines erreurs peuvent indiquer un serveur Odoo
      return error.response?.status === 401 ||
             error.response?.status === 403;
    }
  }

  /**
   * Découverte via serveurs connus/préférés
   */
  async discoverViaKnownServers() {
    const knownServers = [];

    try {
      // 1. Serveur préféré en cache
      if (this.discoveryState.preferredServer) {
        const server = await this.validateCachedServer(this.discoveryState.preferredServer);
        if (server) knownServers.push(server);
      }

      // 2. Serveurs des préférences utilisateur
      const userServerUrl = userPreferences.getServerUrl();
      if (userServerUrl) {
        const server = await this.validateServerUrl(userServerUrl);
        if (server) knownServers.push(server);
      }

      // 3. Localhost standard
      const localhostServer = await this.validateServerUrl('http://localhost:8069');
      if (localhostServer) knownServers.push(localhostServer);

      return knownServers;
    } catch (error) {
      log.error(`Erreur lors de la vérification des serveurs connus: ${error.message}`);
      return knownServers;
    }
  }

  /**
   * Découverte du serveur distant
   */
  async discoverRemoteServer() {
    try {
      const remoteUrl = this.getRemoteServerUrl();
      const server = await this.validateServerUrl(remoteUrl, this.config.timeouts.remoteCheck);

      if (server) {
        server.type = 'remote';
        server.method = 'remote-check';
        return [server];
      }

      return [];
    } catch (error) {
      log.error(`Erreur lors de la vérification du serveur distant: ${error.message}`);
      return [];
    }
  }

  /**
   * Valide un serveur en cache
   */
  async validateCachedServer(cachedServer) {
    const cacheKey = `server:${cachedServer.url}`;
    const cached = this.serverCache.get(cacheKey);

    if (cached && this.isCacheValid(cached)) {
      // Revalidation en arrière-plan si nécessaire
      if (this.shouldRevalidate(cached)) {
        this.revalidateServerInBackground(cachedServer);
      }
      return cachedServer;
    }

    // Cache expiré, revalider
    return await this.validateServerUrl(cachedServer.url);
  }

  /**
   * Valide une URL de serveur
   */
  async validateServerUrl(url, timeout = this.config.timeouts.httpCheck) {
    try {
      const startTime = Date.now();
      const isValid = await this.fastOdooCheck(url);

      if (isValid) {
        const responseTime = Date.now() - startTime;
        return {
          url,
          responseTime,
          validated: Date.now(),
          method: 'url-validation'
        };
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Analyse les résultats de découverte
   */
  analyzeDiscoveryResults(results) {
    const servers = [];

    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && Array.isArray(result.value)) {
        servers.push(...result.value);
      }
    });

    // Déduplication par URL
    const uniqueServers = servers.filter((server, index, self) =>
      index === self.findIndex(s => s.url === server.url)
    );

    return uniqueServers;
  }

  /**
   * Sélectionne le meilleur serveur selon les critères
   */
  selectBestServer(servers) {
    if (servers.length === 0) {
      return this.getFallbackServer();
    }

    // Critères de sélection (par ordre de priorité)
    const priorityOrder = [
      // 1. Serveurs locaux d'abord
      server => server.type === 'local' ? 1000 : 0,
      // 2. Temps de réponse (plus rapide = mieux)
      server => server.responseTime ? Math.max(0, 1000 - server.responseTime) : 0,
      // 3. Méthode de découverte (préférence pour les serveurs connus)
      server => {
        const methodScores = {
          'known-server': 500,
          'network-scan': 300,
          'remote-check': 100
        };
        return methodScores[server.method] || 0;
      }
    ];

    // Calculer le score pour chaque serveur
    const scoredServers = servers.map(server => ({
      ...server,
      score: priorityOrder.reduce((total, criterion) => total + criterion(server), 0)
    }));

    // Trier par score décroissant
    scoredServers.sort((a, b) => b.score - a.score);

    const bestServer = scoredServers[0];
    log.info(`Meilleur serveur sélectionné: ${bestServer.url} (score: ${bestServer.score})`);

    return bestServer;
  }

  // Méthodes utilitaires
  getLocalIpAddress() {
    try {
      const interfaces = os.networkInterfaces();
      for (const ifaceName in interfaces) {
        const iface = interfaces[ifaceName];
        for (const alias of iface) {
          if (alias.family === 'IPv4' && !alias.internal) {
            return alias.address;
          }
        }
      }
      return null;
    } catch (error) {
      log.error(`Erreur lors de la récupération de l'IP locale: ${error.message}`);
      return null;
    }
  }

  getRemoteServerUrl() {
    const workspaceName = userPreferences.getWorkspaceName();
    if (workspaceName && workspaceName !== 'edara') {
      return `https://${workspaceName}.ligne-digitale.com`;
    }
    return 'https://edara.ligne-digitale.com';
  }

  getLastValidIp() {
    // Intégration avec le module last-valid-ip existant
    try {
      const lastValidIp = require('./last-valid-ip');
      return lastValidIp.getLastValidIp();
    } catch (error) {
      return null;
    }
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Récupère le meilleur serveur en cache
   */
  getCachedBestServer() {
    const cacheKey = 'best-server';
    const cached = this.serverCache.get(cacheKey);

    if (cached && this.isCacheValid(cached)) {
      log.debug(`Serveur trouvé en cache: ${cached.data.url}`);
      return cached.data;
    }

    return null;
  }

  /**
   * Met en cache un serveur avec métadonnées
   */
  cacheServer(server) {
    const cacheKey = `server:${server.url}`;
    const bestServerKey = 'best-server';

    const cacheEntry = {
      data: server,
      timestamp: Date.now(),
      ttl: this.config.cache.ttl,
      revalidateAfter: this.config.cache.revalidateAfter,
      hits: 0,
      lastValidated: Date.now()
    };

    // Mettre en cache le serveur spécifique
    this.serverCache.set(cacheKey, cacheEntry);

    // Mettre en cache comme meilleur serveur
    this.serverCache.set(bestServerKey, cacheEntry);

    // Sauvegarder l'IP si c'est un serveur local
    if (server.type === 'local' && server.ip) {
      this.saveLastValidIp(server.ip);
    }

    log.debug(`Serveur mis en cache: ${server.url}`);
  }

  /**
   * Vérifie si une entrée de cache est valide
   */
  isCacheValid(cached) {
    const now = Date.now();
    const age = now - cached.timestamp;

    return age < cached.ttl;
  }

  /**
   * Détermine si une entrée de cache doit être revalidée
   */
  shouldRevalidate(cached) {
    const now = Date.now();
    const age = now - cached.timestamp;

    return age > cached.revalidateAfter;
  }

  /**
   * Revalide un serveur en arrière-plan
   */
  async revalidateServerInBackground(server) {
    try {
      log.debug(`Revalidation en arrière-plan: ${server.url}`);

      const validatedServer = await this.validateServerUrl(server.url);
      if (validatedServer) {
        this.cacheServer({ ...server, ...validatedServer });
        this.emit('server-revalidated', validatedServer);
      } else {
        // Serveur non disponible, marquer comme invalide
        this.invalidateServerCache(server.url);
        this.emit('server-invalidated', server);
      }
    } catch (error) {
      log.warn(`Erreur lors de la revalidation de ${server.url}: ${error.message}`);
    }
  }

  /**
   * Invalide le cache d'un serveur
   */
  invalidateServerCache(url) {
    const cacheKey = `server:${url}`;
    this.serverCache.delete(cacheKey);

    // Si c'était le meilleur serveur, l'invalider aussi
    const bestServer = this.serverCache.get('best-server');
    if (bestServer && bestServer.data.url === url) {
      this.serverCache.delete('best-server');
    }

    log.debug(`Cache invalidé pour: ${url}`);
  }

  /**
   * Nettoie le cache des entrées expirées
   */
  cleanupCache() {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, cached] of this.serverCache.entries()) {
      if (!this.isCacheValid(cached)) {
        this.serverCache.delete(key);
        cleaned++;
      }
    }

    // Limiter la taille du cache
    if (this.serverCache.size > this.config.cache.maxEntries) {
      const entries = Array.from(this.serverCache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp); // Plus ancien en premier

      const toRemove = this.serverCache.size - this.config.cache.maxEntries;
      for (let i = 0; i < toRemove; i++) {
        this.serverCache.delete(entries[i][0]);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      log.debug(`Cache nettoyé: ${cleaned} entrée(s) supprimée(s)`);
    }
  }

  /**
   * Retourne un serveur de fallback
   */
  getFallbackServer() {
    // 1. Essayer le serveur distant par défaut
    const remoteUrl = this.getRemoteServerUrl();

    // 2. Essayer localhost en dernier recours
    const fallbackServers = [
      {
        url: remoteUrl,
        type: 'remote',
        method: 'fallback',
        responseTime: null,
        fallback: true
      },
      {
        url: 'http://localhost:8069',
        type: 'local',
        method: 'fallback',
        responseTime: null,
        fallback: true
      }
    ];

    log.warn('Utilisation du serveur de fallback');
    return fallbackServers[0];
  }

  /**
   * Sauvegarde la dernière IP valide
   */
  saveLastValidIp(ip) {
    try {
      const lastValidIp = require('./last-valid-ip');
      lastValidIp.saveLastValidIp(ip);
    } catch (error) {
      log.warn(`Impossible de sauvegarder l'IP valide: ${error.message}`);
    }
  }

  /**
   * Obtient les statistiques du détecteur
   */
  getStats() {
    return {
      metrics: this.metrics,
      cache: {
        size: this.serverCache.size,
        entries: this.serverCache.size
      },
      discovery: this.discoveryState,
      config: this.config
    };
  }

  /**
   * Réinitialise le cache et force une nouvelle découverte
   */
  async resetAndRediscover() {
    log.info('Réinitialisation du cache et nouvelle découverte...');

    // Vider le cache
    this.serverCache.clear();
    this.discoveryState.preferredServer = null;
    this.discoveryState.knownServers.clear();

    // Nouvelle découverte
    return await this.discoverOdooServers({ useCache: false, forceScan: true });
  }

  /**
   * Nettoie les ressources
   */
  cleanup() {
    this.serverCache.clear();
    this.removeAllListeners();
    log.info('Enhanced Server Detector nettoyé');
  }
}

module.exports = EnhancedServerDetector;
