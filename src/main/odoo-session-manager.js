/**
 * Module de gestion de session Odoo pour Edara ERP
 * Ce module gère l'authentification et la persistance des sessions Odoo
 */

const axios = require('axios');
const log = require('electron-log');
const secureStorage = require('./secure-storage');

/**
 * Authentifie un utilisateur auprès d'un serveur Odoo en utilisant l'API de session
 * @param {string} username - L'identifiant (nom d'utilisateur ou email) de l'utilisateur
 * @param {string} password - Le mot de passe de l'utilisateur
 * @param {string} server - L'URL du serveur Odoo
 * @param {string} [dbName='ligne-digitale'] - Le nom de la base de données Odoo
 * @param {number} [timeout=30000] - D<PERSON>lai d'expiration de la requête en millisecondes
 * @returns {Promise<Object>} - Un objet contenant le résultat de l'authentification
 */
async function authenticate(username, password, server, dbName = 'ligne-digitale', timeout = 30000) {
  const startTime = Date.now();
  log.info(`Tentative d'authentification pour l'utilisateur ${username} sur ${server}`);
  
  try {
    // Construire l'URL de l'API de session Odoo
    const sessionUrl = `${server}/web/session/authenticate`;
    log.info(`URL d'authentification: ${sessionUrl}`);
    
    // Préparer les données pour la requête
    const data = {
      jsonrpc: '2.0',
      method: 'call',
      params: {
        db: dbName,
        login: username,
        password: password,
        context: {}
      },
      id: Date.now().toString()
    };
    
    // Effectuer la requête POST
    const response = await axios.post(sessionUrl, data, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      timeout: timeout
    });
    
    // Vérifier la réponse
    if (response.status === 200 && response.data && response.data.result) {
      const result = response.data.result;
      
      // Extraire le session_id des cookies de la réponse
      const cookies = response.headers['set-cookie'];
      let sessionId = null;
      
      if (cookies && cookies.length > 0) {
        // Parcourir tous les cookies pour trouver session_id
        for (const cookie of cookies) {
          if (cookie.includes('session_id=')) {
            // Extraire la valeur du cookie session_id
            const match = cookie.match(/session_id=([^;]+)/);
            if (match && match[1]) {
              sessionId = match[1];
              break;
            }
          }
        }
      }
      
      if (!sessionId) {
        log.warn('Authentification réussie mais aucun session_id trouvé dans les cookies');
      } else {
        log.info(`Session ID obtenu: ${sessionId.substring(0, 8)}...`);
      }
      
      // Créer l'objet de session
      const sessionData = {
        session_id: sessionId,
        uid: result.uid,
        username: result.username || username,
        name: result.name,
        company_id: result.company_id,
        partner_id: result.partner_id,
        server_url: server,
        db_name: dbName,
        created_at: Date.now(),
        expires_at: Date.now() + (24 * 60 * 60 * 1000) // 24 heures par défaut
      };
      
      // Sauvegarder les données de session
      const saved = secureStorage.saveSessionData(sessionData);
      if (!saved) {
        log.warn('Impossible de sauvegarder les données de session');
      }
      
      const executionTime = Date.now() - startTime;
      log.info(`Authentification réussie en ${executionTime}ms`);
      
      return {
        success: true,
        session_id: sessionId,
        uid: result.uid,
        username: result.username || username,
        name: result.name,
        company_id: result.company_id,
        partner_id: result.partner_id,
        server_url: server,
        db_name: dbName,
        executionTime
      };
    } else {
      // Authentification échouée
      const errorMsg = response.data && response.data.error ? response.data.error.data.message : 'Identifiants invalides';
      log.error(`Échec de l'authentification: ${errorMsg}`);
      
      const executionTime = Date.now() - startTime;
      return {
        success: false,
        error: errorMsg,
        errorType: 'AUTHENTICATION_FAILED',
        executionTime
      };
    }
  } catch (error) {
    // Erreur lors de la requête
    const executionTime = Date.now() - startTime;
    let errorMsg, errorType;
    
    if (error.code === 'ECONNREFUSED') {
      errorMsg = `Impossible de se connecter au serveur ${server}`;
      errorType = 'CONNECTION_REFUSED';
    } else if (error.code === 'ETIMEDOUT' || error.code === 'ESOCKETTIMEDOUT') {
      errorMsg = `Délai d'attente dépassé lors de la connexion au serveur ${server}`;
      errorType = 'TIMEOUT';
    } else {
      errorMsg = error.message || 'Erreur inconnue';
      errorType = 'REQUEST_ERROR';
    }
    
    log.error(`Erreur lors de l'authentification: ${errorMsg}`);
    
    return {
      success: false,
      error: errorMsg,
      errorType: errorType,
      executionTime
    };
  }
}

/**
 * Vérifie si une session est valide
 * @param {string} sessionId - L'ID de session à vérifier
 * @param {string} server - L'URL du serveur Odoo
 * @param {number} [timeout=10000] - Délai d'expiration de la requête en millisecondes
 * @returns {Promise<Object>} - Un objet contenant le résultat de la vérification
 */
async function validateSession(sessionId, server, timeout = 10000) {
  const startTime = Date.now();
  log.info(`Vérification de la validité de la session ${sessionId.substring(0, 8)}... sur ${server}`);
  
  try {
    // Construire l'URL de l'API de session Odoo
    const sessionUrl = `${server}/web/session/get_session_info`;
    log.info(`URL de vérification de session: ${sessionUrl}`);
    
    // Préparer les données pour la requête
    const data = {
      jsonrpc: '2.0',
      method: 'call',
      params: {},
      id: Date.now().toString()
    };
    
    // Effectuer la requête POST avec le cookie de session
    const response = await axios.post(sessionUrl, data, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Cookie': `session_id=${sessionId}`
      },
      timeout: timeout
    });
    
    // Vérifier la réponse
    if (response.status === 200 && response.data && response.data.result) {
      const result = response.data.result;
      
      // Vérifier si l'utilisateur est authentifié
      if (result.uid) {
        const executionTime = Date.now() - startTime;
        log.info(`Session valide pour l'utilisateur ${result.username} (UID: ${result.uid})`);
        
        // Mettre à jour les données de session
        const sessionData = secureStorage.loadSessionData();
        if (sessionData) {
          sessionData.last_validated_at = Date.now();
          sessionData.expires_at = Date.now() + (24 * 60 * 60 * 1000); // Prolonger de 24 heures
          secureStorage.saveSessionData(sessionData);
        }
        
        return {
          valid: true,
          uid: result.uid,
          username: result.username,
          name: result.name,
          company_id: result.company_id,
          partner_id: result.partner_id,
          executionTime
        };
      } else {
        // Session expirée ou invalide
        const executionTime = Date.now() - startTime;
        log.warn('Session expirée ou invalide');
        
        return {
          valid: false,
          error: 'Session expirée ou invalide',
          errorType: 'SESSION_EXPIRED',
          executionTime
        };
      }
    } else {
      // Erreur dans la réponse
      const executionTime = Date.now() - startTime;
      log.error('Erreur lors de la vérification de la session');
      
      return {
        valid: false,
        error: 'Erreur lors de la vérification de la session',
        errorType: 'VALIDATION_ERROR',
        executionTime
      };
    }
  } catch (error) {
    // Erreur lors de la requête
    const executionTime = Date.now() - startTime;
    let errorMsg, errorType;
    
    if (error.code === 'ECONNREFUSED') {
      errorMsg = `Impossible de se connecter au serveur ${server}`;
      errorType = 'CONNECTION_REFUSED';
    } else if (error.code === 'ETIMEDOUT' || error.code === 'ESOCKETTIMEDOUT') {
      errorMsg = `Délai d'attente dépassé lors de la connexion au serveur ${server}`;
      errorType = 'TIMEOUT';
    } else {
      errorMsg = error.message || 'Erreur inconnue';
      errorType = 'REQUEST_ERROR';
    }
    
    log.error(`Erreur lors de la vérification de la session: ${errorMsg}`);
    
    return {
      valid: false,
      error: errorMsg,
      errorType: errorType,
      executionTime
    };
  }
}

/**
 * Déconnecte l'utilisateur
 * @param {string} sessionId - L'ID de session à déconnecter
 * @param {string} server - L'URL du serveur Odoo
 * @returns {Promise<Object>} - Un objet contenant le résultat de la déconnexion
 */
async function logout(sessionId, server) {
  const startTime = Date.now();
  log.info(`Déconnexion de la session ${sessionId ? sessionId.substring(0, 8) + '...' : 'inconnue'} sur ${server}`);
  
  try {
    // Supprimer les données de session locales
    secureStorage.clearSessionData();
    
    // Si aucun sessionId n'est fourni, considérer la déconnexion comme réussie
    if (!sessionId) {
      const executionTime = Date.now() - startTime;
      log.info('Aucun ID de session fourni, déconnexion locale uniquement');
      
      return {
        success: true,
        message: 'Déconnexion locale réussie',
        executionTime
      };
    }
    
    // Construire l'URL de l'API de déconnexion Odoo
    const logoutUrl = `${server}/web/session/destroy`;
    log.info(`URL de déconnexion: ${logoutUrl}`);
    
    // Préparer les données pour la requête
    const data = {
      jsonrpc: '2.0',
      method: 'call',
      params: {},
      id: Date.now().toString()
    };
    
    // Effectuer la requête POST avec le cookie de session
    const response = await axios.post(logoutUrl, data, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Cookie': `session_id=${sessionId}`
      },
      timeout: 10000
    });
    
    // Vérifier la réponse
    if (response.status === 200) {
      const executionTime = Date.now() - startTime;
      log.info(`Déconnexion réussie en ${executionTime}ms`);
      
      return {
        success: true,
        message: 'Déconnexion réussie',
        executionTime
      };
    } else {
      // Erreur dans la réponse
      const executionTime = Date.now() - startTime;
      log.warn(`Déconnexion partiellement réussie (erreur côté serveur) en ${executionTime}ms`);
      
      return {
        success: true, // Considérer comme un succès car les données locales ont été supprimées
        warning: 'Déconnexion locale réussie mais erreur côté serveur',
        executionTime
      };
    }
  } catch (error) {
    // Erreur lors de la requête
    const executionTime = Date.now() - startTime;
    log.error(`Erreur lors de la déconnexion: ${error.message}`);
    
    // Même en cas d'erreur, considérer la déconnexion comme réussie car les données locales ont été supprimées
    return {
      success: true,
      warning: `Déconnexion locale réussie mais erreur lors de la déconnexion du serveur: ${error.message}`,
      executionTime
    };
  }
}

/**
 * Récupère les données de session stockées localement
 * @returns {Object|null} - Les données de session ou null si aucune session n'est stockée
 */
function getStoredSession() {
  return secureStorage.loadSessionData();
}

/**
 * Effectue une requête à l'API Odoo avec le token de session
 * @param {string} endpoint - L'endpoint de l'API (ex: '/web/dataset/call_kw')
 * @param {Object} params - Les paramètres de la requête
 * @param {string} sessionId - L'ID de session
 * @param {string} server - L'URL du serveur Odoo
 * @param {number} [timeout=30000] - Délai d'expiration de la requête en millisecondes
 * @returns {Promise<Object>} - La réponse de l'API
 */
async function callOdooApi(endpoint, params, sessionId, server, timeout = 30000) {
  log.info(`Appel à l'API Odoo: ${endpoint}`);
  
  try {
    // Construire l'URL complète
    const url = `${server}${endpoint}`;
    
    // Préparer les données pour la requête
    const data = {
      jsonrpc: '2.0',
      method: 'call',
      params: params,
      id: Date.now().toString()
    };
    
    // Effectuer la requête POST avec le cookie de session
    const response = await axios.post(url, data, {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Cookie': `session_id=${sessionId}`
      },
      timeout: timeout
    });
    
    // Vérifier la réponse
    if (response.status === 200) {
      if (response.data.error) {
        // Erreur dans la réponse JSON-RPC
        const error = response.data.error;
        
        // Vérifier si l'erreur est due à une session expirée
        if (error.data && error.data.name === 'odoo.http.SessionExpiredException') {
          log.warn('Session expirée détectée lors de l\'appel à l\'API');
          return {
            success: false,
            error: 'Session expirée',
            errorType: 'SESSION_EXPIRED'
          };
        }
        
        log.error(`Erreur dans la réponse JSON-RPC: ${error.message}`);
        return {
          success: false,
          error: error.data ? error.data.message : error.message,
          errorType: 'API_ERROR'
        };
      }
      
      // Succès
      return {
        success: true,
        result: response.data.result
      };
    } else {
      // Erreur HTTP
      log.error(`Erreur HTTP ${response.status} lors de l'appel à l'API`);
      return {
        success: false,
        error: `Erreur HTTP ${response.status}`,
        errorType: 'HTTP_ERROR'
      };
    }
  } catch (error) {
    // Erreur lors de la requête
    log.error(`Erreur lors de l'appel à l'API: ${error.message}`);
    
    // Vérifier si l'erreur est due à une session expirée
    if (error.response && error.response.status === 401) {
      return {
        success: false,
        error: 'Session expirée',
        errorType: 'SESSION_EXPIRED'
      };
    }
    
    return {
      success: false,
      error: error.message,
      errorType: 'REQUEST_ERROR'
    };
  }
}

// Exporter les fonctions
module.exports = {
  authenticate,
  validateSession,
  logout,
  getStoredSession,
  callOdooApi
};
