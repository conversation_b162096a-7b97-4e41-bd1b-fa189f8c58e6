/**
 * Diagnostic Complet du Serveur Odoo
 * Identifie la cause exacte des erreurs ERR_CONTENT_LENGTH_MISMATCH
 */

const { app, BrowserWindow } = require('electron');
const log = require('electron-log');
const { net } = require('electron');
const fs = require('fs');

class OdooServerDiagnostic {
  constructor() {
    this.results = {
      directRequests: [],
      electronRequests: [],
      browserComparison: null,
      serverAnalysis: null
    };
  }

  async runCompleteDiagnostic() {
    log.info('🔍 [Diagnostic] ========================================');
    log.info('🔍 [Diagnostic] DIAGNOSTIC COMPLET DU SERVEUR ODOO');
    log.info('🔍 [Diagnostic] ========================================');

    try {
      // Étape 1: Test des requêtes directes
      await this.testDirectRequests();

      // Étape 2: Test avec Electron
      await this.testElectronRequests();

      // Étape 3: Analyse des headers du serveur
      await this.analyzeServerHeaders();

      // Étape 4: Test de différentes configurations
      await this.testDifferentConfigurations();

      // Étape 5: Générer le rapport final
      this.generateFinalReport();

    } catch (error) {
      log.error(`❌ [Diagnostic] Erreur fatale: ${error.message}`);
    }
  }

  async testDirectRequests() {
    log.info('🔍 [Diagnostic] Test des requêtes directes...');

    const testUrls = [
      'http://**************:8069/web/content/566-ba15075/web.assets_backend.js',
      'http://**************:8069/web/content/565-ba15075/web.assets_backend.css',
      'http://**************:8069/web/content/526-9329c0b/web.assets_common.js'
    ];

    for (const url of testUrls) {
      await this.testSingleDirectRequest(url);
    }
  }

  async testSingleDirectRequest(url) {
    log.info(`🔍 [Diagnostic] Test direct: ${url}`);

    return new Promise((resolve) => {
      const request = net.request({
        method: 'GET',
        url: url,
        headers: {
          'Accept-Encoding': 'identity',
          'User-Agent': 'Edara-Diagnostic/1.0'
        }
      });

      const result = {
        url: url,
        success: false,
        statusCode: null,
        headers: {},
        contentLength: null,
        actualLength: null,
        mismatch: false,
        error: null
      };

      request.on('response', (response) => {
        result.statusCode = response.statusCode;
        result.headers = response.headers;

        const contentLength = response.headers['content-length'];
        if (contentLength) {
          result.contentLength = parseInt(contentLength[0]);
        }

        let data = '';
        response.on('data', (chunk) => {
          data += chunk;
        });

        response.on('end', () => {
          result.actualLength = Buffer.byteLength(data, 'utf8');
          result.success = true;

          if (result.contentLength && result.contentLength !== result.actualLength) {
            result.mismatch = true;
            log.error(`❌ [Diagnostic] MISMATCH DÉTECTÉ: ${url}`);
            log.error(`❌ [Diagnostic] Content-Length: ${result.contentLength}, Actual: ${result.actualLength}`);
          } else {
            log.info(`✅ [Diagnostic] OK: ${url}`);
          }

          this.results.directRequests.push(result);
          resolve(result);
        });
      });

      request.on('error', (error) => {
        result.error = error.message;
        log.error(`❌ [Diagnostic] Erreur requête directe: ${error.message}`);
        this.results.directRequests.push(result);
        resolve(result);
      });

      request.end();
    });
  }

  async testElectronRequests() {
    log.info('🔍 [Diagnostic] Test avec Electron BrowserWindow...');

    const testWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true
      }
    });

    let electronErrors = [];

    testWindow.webContents.on('console-message', (event, level, message) => {
      if (message.includes('ERR_CONTENT_LENGTH_MISMATCH')) {
        electronErrors.push(message);
        log.error(`🔍 [Diagnostic] Electron Error: ${message}`);
      }
    });

    try {
      await testWindow.loadURL('http://**************:8069/web');
      
      // Attendre 10 secondes pour collecter les erreurs
      await new Promise(resolve => setTimeout(resolve, 10000));

      this.results.electronRequests = {
        errors: electronErrors,
        errorCount: electronErrors.length
      };

      log.info(`🔍 [Diagnostic] Erreurs Electron collectées: ${electronErrors.length}`);

    } catch (error) {
      log.error(`❌ [Diagnostic] Erreur test Electron: ${error.message}`);
    }

    testWindow.close();
  }

  async analyzeServerHeaders() {
    log.info('🔍 [Diagnostic] Analyse des headers du serveur...');

    const testUrl = 'http://**************:8069/web/content/566-ba15075/web.assets_backend.js';

    // Test avec différents Accept-Encoding
    const encodings = ['identity', 'gzip', 'deflate', 'gzip, deflate', '*'];
    
    for (const encoding of encodings) {
      await this.testWithEncoding(testUrl, encoding);
    }
  }

  async testWithEncoding(url, encoding) {
    log.info(`🔍 [Diagnostic] Test avec Accept-Encoding: ${encoding}`);

    return new Promise((resolve) => {
      const request = net.request({
        method: 'GET',
        url: url,
        headers: {
          'Accept-Encoding': encoding,
          'User-Agent': 'Edara-Diagnostic-Encoding/1.0'
        }
      });

      const result = {
        encoding: encoding,
        statusCode: null,
        headers: {},
        hasContentLength: false,
        hasContentEncoding: false,
        contentLength: null,
        actualLength: null,
        mismatch: false
      };

      request.on('response', (response) => {
        result.statusCode = response.statusCode;
        result.headers = response.headers;

        const contentLength = response.headers['content-length'];
        const contentEncoding = response.headers['content-encoding'];

        if (contentLength) {
          result.hasContentLength = true;
          result.contentLength = parseInt(contentLength[0]);
        }

        if (contentEncoding) {
          result.hasContentEncoding = true;
        }

        let data = Buffer.alloc(0);
        response.on('data', (chunk) => {
          data = Buffer.concat([data, chunk]);
        });

        response.on('end', () => {
          result.actualLength = data.length;

          if (result.contentLength && result.contentLength !== result.actualLength) {
            result.mismatch = true;
            log.error(`❌ [Diagnostic] MISMATCH avec ${encoding}: CL=${result.contentLength}, Actual=${result.actualLength}`);
          } else {
            log.info(`✅ [Diagnostic] OK avec ${encoding}`);
          }

          if (!this.results.serverAnalysis) {
            this.results.serverAnalysis = [];
          }
          this.results.serverAnalysis.push(result);
          resolve(result);
        });
      });

      request.on('error', (error) => {
        log.error(`❌ [Diagnostic] Erreur avec ${encoding}: ${error.message}`);
        resolve(result);
      });

      request.end();
    });
  }

  async testDifferentConfigurations() {
    log.info('🔍 [Diagnostic] Test de différentes configurations...');

    const testUrl = 'http://**************:8069/web/content/566-ba15075/web.assets_backend.js';

    // Test avec différents User-Agent
    const userAgents = [
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      'Edara-ERP/1.0 (Electron)',
      'curl/7.68.0',
      'Python-requests/2.25.1'
    ];

    for (const userAgent of userAgents) {
      await this.testWithUserAgent(testUrl, userAgent);
    }
  }

  async testWithUserAgent(url, userAgent) {
    log.info(`🔍 [Diagnostic] Test avec User-Agent: ${userAgent.substring(0, 30)}...`);

    return new Promise((resolve) => {
      const request = net.request({
        method: 'GET',
        url: url,
        headers: {
          'Accept-Encoding': 'identity',
          'User-Agent': userAgent
        }
      });

      request.on('response', (response) => {
        const contentLength = response.headers['content-length'];
        const contentEncoding = response.headers['content-encoding'];

        log.info(`🔍 [Diagnostic] UA Test - Status: ${response.statusCode}, CL: ${contentLength ? contentLength[0] : 'none'}, CE: ${contentEncoding ? contentEncoding[0] : 'none'}`);

        response.on('data', () => {});
        response.on('end', () => resolve());
      });

      request.on('error', (error) => {
        log.error(`❌ [Diagnostic] Erreur UA test: ${error.message}`);
        resolve();
      });

      request.end();
    });
  }

  generateFinalReport() {
    log.info('📋 [Diagnostic] ========================================');
    log.info('📋 [Diagnostic] RAPPORT FINAL - DIAGNOSTIC SERVEUR ODOO');
    log.info('📋 [Diagnostic] ========================================');

    // Analyser les requêtes directes
    const directMismatches = this.results.directRequests.filter(r => r.mismatch);
    log.info(`📊 [Diagnostic] Requêtes directes testées: ${this.results.directRequests.length}`);
    log.info(`📊 [Diagnostic] Mismatches détectés: ${directMismatches.length}`);

    if (directMismatches.length > 0) {
      log.error('❌ [Diagnostic] PROBLÈME CONFIRMÉ AU NIVEAU SERVEUR ODOO !');
      directMismatches.forEach(r => {
        log.error(`❌ [Diagnostic] ${r.url}: CL=${r.contentLength}, Actual=${r.actualLength}`);
      });
    } else {
      log.info('✅ [Diagnostic] Aucun mismatch dans les requêtes directes');
    }

    // Analyser les erreurs Electron
    log.info(`📊 [Diagnostic] Erreurs Electron: ${this.results.electronRequests.errorCount}`);

    // Analyser les tests d'encoding
    if (this.results.serverAnalysis) {
      const encodingMismatches = this.results.serverAnalysis.filter(r => r.mismatch);
      log.info(`📊 [Diagnostic] Tests d'encoding: ${this.results.serverAnalysis.length}`);
      log.info(`📊 [Diagnostic] Mismatches par encoding: ${encodingMismatches.length}`);

      encodingMismatches.forEach(r => {
        log.error(`❌ [Diagnostic] Mismatch avec ${r.encoding}: CL=${r.contentLength}, Actual=${r.actualLength}`);
      });
    }

    // Recommandations
    log.info('💡 [Diagnostic] RECOMMANDATIONS:');

    if (directMismatches.length > 0) {
      log.info('💡 [Diagnostic] 1. PROBLÈME SERVEUR ODOO CONFIRMÉ');
      log.info('💡 [Diagnostic] 2. Désactiver la compression dans Odoo');
      log.info('💡 [Diagnostic] 3. Vérifier la configuration nginx/apache');
      log.info('💡 [Diagnostic] 4. Mettre à jour Odoo vers une version plus récente');
    } else if (this.results.electronRequests.errorCount > 0) {
      log.info('💡 [Diagnostic] 1. Problème spécifique à Electron');
      log.info('💡 [Diagnostic] 2. Mettre à jour Electron');
      log.info('💡 [Diagnostic] 3. Utiliser une session personnalisée');
    } else {
      log.info('💡 [Diagnostic] 1. Problème intermittent ou de timing');
      log.info('💡 [Diagnostic] 2. Ajouter des délais dans les requêtes');
      log.info('💡 [Diagnostic] 3. Implémenter un système de retry');
    }

    log.info('📋 [Diagnostic] ========================================');

    // Sauvegarder le rapport
    const reportPath = `diagnostic-odoo-server-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
    log.info(`💾 [Diagnostic] Rapport sauvegardé: ${reportPath}`);
  }
}

// Lancer le diagnostic
app.whenReady().then(async () => {
  const diagnostic = new OdooServerDiagnostic();
  await diagnostic.runCompleteDiagnostic();
  
  // Garder l'application ouverte pour voir les résultats
  setTimeout(() => {
    app.quit();
  }, 5000);
});

app.on('window-all-closed', () => {
  // Ne pas quitter automatiquement
});
