/**
 * Exemple d'intégration du système de gestion de session unifié dans main.js
 * Ce fichier montre comment remplacer l'ancien système par le nouveau
 */

const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const log = require('electron-log');

// Nouveaux gestionnaires de session
const UnifiedSessionManager = require('../src/main/unified-session-manager');
const SessionRecoveryManager = require('../src/main/session-recovery-manager');
const SessionEventHandler = require('../src/main/session-events');

class EdaraApp {
  constructor() {
    this.mainWindow = null;
    this.loginWindow = null;
    this.splashWindow = null;
    
    // Gestionnaires de session
    this.sessionManager = null;
    this.recoveryManager = null;
    this.eventHandler = null;
    
    this.initializeApp();
  }

  /**
   * Initialise l'application
   */
  initializeApp() {
    // Initialiser les gestionnaires de session
    this.sessionManager = new UnifiedSessionManager();
    this.recoveryManager = new SessionRecoveryManager(this.sessionManager);
    
    // Configurer les gestionnaires d'événements Electron
    this.setupElectronEvents();
    this.setupIpcHandlers();
    
    log.info('Application Edara ERP initialisée');
  }

  /**
   * Configure les événements Electron
   */
  setupElectronEvents() {
    app.whenReady().then(() => {
      this.createSplashWindow();
      this.checkForExistingSession();
    });

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        this.cleanup();
        app.quit();
      }
    });

    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createSplashWindow();
      }
    });

    app.on('before-quit', () => {
      this.cleanup();
    });
  }

  /**
   * Configure les gestionnaires IPC
   */
  setupIpcHandlers() {
    // Gestionnaire d'authentification simplifié
    ipcMain.handle('authenticate', async (event, credentials) => {
      try {
        log.info(`Tentative d'authentification pour ${credentials.username}`);
        
        // Utiliser le gestionnaire unifié
        const result = await this.sessionManager.authenticate(credentials);
        
        if (result.success) {
          // Stocker les credentials pour récupération automatique
          await this.recoveryManager.storeCredentialsForRecovery(credentials);
          
          // Charger l'interface Odoo
          await this.loadOdooInterface();
          
          log.info(`Authentification réussie pour ${result.sessionData.username}`);
        }
        
        return result;
      } catch (error) {
        log.error(`Erreur d'authentification: ${error.message}`);
        return { 
          success: false, 
          error: error.message 
        };
      }
    });

    // Gestionnaire de validation de session
    ipcMain.handle('validate-session', async () => {
      try {
        return await this.sessionManager.validateSession();
      } catch (error) {
        log.error(`Erreur de validation: ${error.message}`);
        return { valid: false, reason: error.message };
      }
    });

    // Gestionnaire de déconnexion
    ipcMain.handle('logout', async () => {
      try {
        await this.sessionManager.clearSession();
        this.loadLoginScreen();
        return { success: true };
      } catch (error) {
        log.error(`Erreur de déconnexion: ${error.message}`);
        return { success: false, error: error.message };
      }
    });

    // Gestionnaire d'état de session
    ipcMain.handle('get-session-state', () => {
      return {
        isActive: this.sessionManager.isActive,
        username: this.sessionManager.username,
        userId: this.sessionManager.userId,
        serverUrl: this.sessionManager.serverUrl
      };
    });

    // Gestionnaire de détection de serveur (simplifié)
    ipcMain.handle('detect-server', async () => {
      try {
        // Logique de détection de serveur existante
        // Peut être intégrée avec le gestionnaire de session
        return await this.detectOdooServer();
      } catch (error) {
        log.error(`Erreur de détection de serveur: ${error.message}`);
        return { success: false, error: error.message };
      }
    });
  }

  /**
   * Vérifie s'il existe une session persistante
   */
  async checkForExistingSession() {
    try {
      const validation = await this.sessionManager.validateSession();
      
      if (validation.valid) {
        log.info('Session existante trouvée et valide');
        await this.loadOdooInterface();
      } else {
        log.info('Aucune session valide trouvée');
        this.loadLoginScreen();
      }
    } catch (error) {
      log.error(`Erreur lors de la vérification de session: ${error.message}`);
      this.loadLoginScreen();
    } finally {
      this.closeSplashWindow();
    }
  }

  /**
   * Crée la fenêtre splash
   */
  createSplashWindow() {
    this.splashWindow = new BrowserWindow({
      width: 400,
      height: 300,
      frame: false,
      alwaysOnTop: true,
      transparent: true,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true
      }
    });

    this.splashWindow.loadFile(path.join(__dirname, '../src/renderer/splash.html'));
    
    this.splashWindow.on('closed', () => {
      this.splashWindow = null;
    });
  }

  /**
   * Ferme la fenêtre splash
   */
  closeSplashWindow() {
    if (this.splashWindow && !this.splashWindow.isDestroyed()) {
      this.splashWindow.close();
    }
  }

  /**
   * Charge l'écran de connexion
   */
  loadLoginScreen() {
    this.closeSplashWindow();
    
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.close();
    }

    this.loginWindow = new BrowserWindow({
      width: 400,
      height: 600,
      resizable: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, '../src/main/preload.js')
      }
    });

    this.loginWindow.loadFile(path.join(__dirname, '../src/renderer/login.html'));
    
    this.loginWindow.on('closed', () => {
      this.loginWindow = null;
    });

    log.info('Écran de connexion chargé');
  }

  /**
   * Charge l'interface Odoo principale
   */
  async loadOdooInterface() {
    this.closeSplashWindow();
    
    if (this.loginWindow && !this.loginWindow.isDestroyed()) {
      this.loginWindow.close();
    }

    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        session: this.sessionManager.session, // Utiliser la session dédiée
        preload: path.join(__dirname, '../src/main/preload.js')
      }
    });

    // Initialiser le gestionnaire d'événements avec la fenêtre principale
    if (!this.eventHandler) {
      this.eventHandler = new SessionEventHandler(
        this.sessionManager,
        this.recoveryManager,
        this.mainWindow
      );
    }

    // Charger l'interface Odoo
    const odooUrl = `${this.sessionManager.serverUrl}/web`;
    await this.mainWindow.loadURL(odooUrl);

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
      if (this.eventHandler) {
        this.eventHandler.cleanup();
        this.eventHandler = null;
      }
    });

    // Démarrer la surveillance de santé
    this.sessionManager.startHealthCheck();

    log.info(`Interface Odoo chargée: ${odooUrl}`);
  }

  /**
   * Détecte les serveurs Odoo disponibles
   */
  async detectOdooServer() {
    // Implémentation de la détection de serveur
    // Cette fonction peut utiliser la logique existante de server-detector.js
    // mais de manière simplifiée
    
    try {
      // Vérifier d'abord le serveur local
      const localResult = await this.checkOdooServer('http://localhost:8069');
      if (localResult.available) {
        return { 
          success: true, 
          serverUrl: 'http://localhost:8069',
          type: 'local'
        };
      }

      // Vérifier le serveur distant
      const remoteUrl = 'https://ligne-digitale.ligne-digitale.com';
      const remoteResult = await this.checkOdooServer(remoteUrl);
      if (remoteResult.available) {
        return { 
          success: true, 
          serverUrl: remoteUrl,
          type: 'remote'
        };
      }

      return { 
        success: false, 
        error: 'Aucun serveur Odoo disponible' 
      };
    } catch (error) {
      return { 
        success: false, 
        error: error.message 
      };
    }
  }

  /**
   * Vérifie la disponibilité d'un serveur Odoo
   */
  async checkOdooServer(url) {
    try {
      const axios = require('axios');
      const response = await axios.get(`${url}/web/database/selector`, {
        timeout: 5000
      });
      
      return { 
        available: response.status === 200,
        url 
      };
    } catch (error) {
      return { 
        available: false,
        error: error.message 
      };
    }
  }

  /**
   * Nettoie les ressources avant fermeture
   */
  cleanup() {
    log.info('Nettoyage des ressources...');
    
    // Arrêter la surveillance de santé
    if (this.sessionManager) {
      this.sessionManager.stopHealthCheck();
    }
    
    // Nettoyer le gestionnaire de récupération
    if (this.recoveryManager) {
      this.recoveryManager.cleanup();
    }
    
    // Nettoyer le gestionnaire d'événements
    if (this.eventHandler) {
      this.eventHandler.cleanup();
    }
    
    log.info('Nettoyage terminé');
  }
}

// Initialiser l'application
const edaraApp = new EdaraApp();

module.exports = EdaraApp;
