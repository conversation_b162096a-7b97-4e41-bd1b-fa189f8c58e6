/**
 * Exemple d'Intégration dans main.js
 * <PERSON><PERSON> comment intégrer le gestionnaire pour résoudre ERR_CONTENT_LENGTH_MISMATCH en mode local
 */

const { app, BrowserWindow, ipcMain, session } = require('electron');
const path = require('path');
const log = require('electron-log');

// Importer le gestionnaire intégré
const IntegratedLocalModeManager = require('../src/main/integrated-local-mode-manager');

class EdaraERPWithLocalModeFix {
  constructor() {
    this.mainWindow = null;
    this.localModeManager = null;
    this.isLocalMode = false;
    this.currentServerUrl = null;
    
    this.initializeApp();
  }

  /**
   * Initialise l'application avec le gestionnaire de mode local
   */
  async initializeApp() {
    try {
      log.info('🚀 [EdaraERP] Initialisation avec correction mode local...');
      
      // Créer et initialiser le gestionnaire de mode local
      this.localModeManager = new IntegratedLocalModeManager();
      await this.localModeManager.initialize();
      
      // Configurer les événements du gestionnaire
      this.setupLocalModeManagerEvents();
      
      // Configurer les gestionnaires IPC
      this.setupIpcHandlers();
      
      log.info('✅ [EdaraERP] Application initialisée avec succès');
    } catch (error) {
      log.error(`❌ [EdaraERP] Erreur lors de l'initialisation: ${error.message}`);
      throw error;
    }
  }

  /**
   * Configure les événements du gestionnaire de mode local
   */
  setupLocalModeManagerEvents() {
    // Événement de récupération réussie
    this.localModeManager.on('recovery-success', (data) => {
      log.info(`✅ [EdaraERP] Récupération réussie en ${data.recoveryTime}ms (${data.attempts} tentative(s))`);
      
      // Notifier l'interface utilisateur
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.webContents.send('recovery-success', {
          message: 'Problème résolu automatiquement',
          recoveryTime: data.recoveryTime,
          attempts: data.attempts
        });
      }
    });

    // Événement d'échec de récupération
    this.localModeManager.on('recovery-failed', (error) => {
      log.error(`❌ [EdaraERP] Échec de récupération: ${error.url}`);
      
      // Notifier l'utilisateur et proposer des actions
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.webContents.send('recovery-failed', {
          message: 'Impossible de résoudre automatiquement le problème',
          error: error.error,
          url: error.url,
          suggestions: [
            'Vérifier la connexion réseau',
            'Redémarrer le serveur Odoo local',
            'Passer en mode distant'
          ]
        });
      }
    });

    // Événement d'alerte critique
    this.localModeManager.on('critical-alert', (alert) => {
      log.error(`🚨 [EdaraERP] Alerte critique: ${alert.message}`);
      
      // Actions automatiques selon le type d'alerte
      if (alert.type === 'content-length') {
        this.handleCriticalContentLengthAlert(alert);
      }
    });

    // Événement de rapport de santé
    this.localModeManager.on('health-report', (report) => {
      if (report.overall !== 'healthy') {
        log.warn(`⚠️ [EdaraERP] État de santé: ${report.overall}`);
        
        // Envoyer les métriques à l'interface si nécessaire
        if (this.mainWindow && !this.mainWindow.isDestroyed()) {
          this.mainWindow.webContents.send('health-status', report);
        }
      }
    });
  }

  /**
   * Configure les gestionnaires IPC optimisés
   */
  setupIpcHandlers() {
    // Gestionnaire de connexion avec détection automatique du mode
    ipcMain.handle('connect-to-odoo', async (event, serverUrl) => {
      try {
        this.currentServerUrl = serverUrl;
        this.isLocalMode = this.isLocalUrl(serverUrl);
        
        log.info(`🔗 [EdaraERP] Connexion à Odoo: ${serverUrl} (Mode: ${this.isLocalMode ? 'Local' : 'Distant'})`);
        
        // Créer une fenêtre optimisée selon le mode
        if (this.isLocalMode) {
          await this.createOptimizedLocalWindow(serverUrl);
        } else {
          await this.createStandardWindow(serverUrl);
        }
        
        return {
          success: true,
          mode: this.isLocalMode ? 'local' : 'remote',
          url: serverUrl
        };
        
      } catch (error) {
        log.error(`❌ [EdaraERP] Erreur de connexion: ${error.message}`);
        return {
          success: false,
          error: error.message
        };
      }
    });

    // Gestionnaire de statistiques du mode local
    ipcMain.handle('get-local-mode-stats', () => {
      if (this.localModeManager) {
        return this.localModeManager.getComprehensiveStats();
      }
      return null;
    });

    // Gestionnaire de réinitialisation forcée
    ipcMain.handle('force-local-mode-reset', async () => {
      try {
        if (this.localModeManager) {
          await this.localModeManager.forceCompleteReset();
          
          // Recharger la fenêtre si elle existe
          if (this.mainWindow && !this.mainWindow.isDestroyed() && this.isLocalMode) {
            await this.reloadWithOptimizations();
          }
          
          return { success: true };
        }
        return { success: false, error: 'Gestionnaire non disponible' };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // Gestionnaire de diagnostic manuel
    ipcMain.handle('run-local-mode-diagnostic', () => {
      if (this.localModeManager && this.localModeManager.diagnosticManager) {
        return this.localModeManager.diagnosticManager.generateDiagnosticReport();
      }
      return null;
    });

    // Gestionnaire de basculement de mode
    ipcMain.handle('switch-connection-mode', async (event, newMode) => {
      try {
        if (newMode === 'local' && !this.isLocalMode) {
          // Basculer vers le mode local
          const localUrl = 'http://localhost:8069'; // ou détecter automatiquement
          return await this.ipcMain.emit('connect-to-odoo', event, localUrl);
        } else if (newMode === 'remote' && this.isLocalMode) {
          // Basculer vers le mode distant
          const remoteUrl = 'https://edara.ligne-digitale.com'; // ou depuis les préférences
          return await this.ipcMain.emit('connect-to-odoo', event, remoteUrl);
        }
        
        return { success: true, message: 'Déjà dans le bon mode' };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });
  }

  /**
   * Crée une fenêtre optimisée pour le mode local
   */
  async createOptimizedLocalWindow(serverUrl) {
    try {
      log.info('🪟 [EdaraERP] Création de fenêtre optimisée pour mode local...');
      
      // Fermer la fenêtre existante si elle existe
      if (this.mainWindow && !this.mainWindow.isDestroyed()) {
        this.mainWindow.close();
      }
      
      // Créer une nouvelle fenêtre avec optimisations mode local
      this.mainWindow = this.localModeManager.createOptimizedBrowserWindow({
        width: 1400,
        height: 900,
        show: false,
        title: 'Edara ERP - Mode Local',
        icon: path.join(__dirname, '../assets/icon.png'),
        webPreferences: {
          preload: path.join(__dirname, '../src/main/preload.js')
        }
      });
      
      // Configurer les événements de la fenêtre
      this.setupWindowEvents();
      
      // Effectuer un nettoyage préventif avant le chargement
      if (this.localModeManager.sessionCacheManager) {
        await this.localModeManager.sessionCacheManager.performRobustClearing({
          includeCache: true,
          includeStorageData: true,
          includeCookies: true,
          validateResults: true
        });
      }
      
      // Charger l'URL avec optimisations
      await this.loadUrlWithOptimizations(serverUrl);
      
      log.info('✅ [EdaraERP] Fenêtre mode local créée avec succès');
      
    } catch (error) {
      log.error(`❌ [EdaraERP] Erreur lors de la création de fenêtre mode local: ${error.message}`);
      throw error;
    }
  }

  /**
   * Crée une fenêtre standard pour le mode distant
   */
  async createStandardWindow(serverUrl) {
    log.info('🪟 [EdaraERP] Création de fenêtre standard pour mode distant...');
    
    // Fermer la fenêtre existante si elle existe
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.close();
    }
    
    // Créer une fenêtre standard
    this.mainWindow = new BrowserWindow({
      width: 1400,
      height: 900,
      show: false,
      title: 'Edara ERP - Mode Distant',
      icon: path.join(__dirname, '../assets/icon.png'),
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true,
        preload: path.join(__dirname, '../src/main/preload.js')
      }
    });
    
    // Configurer les événements
    this.setupWindowEvents();
    
    // Charger l'URL directement
    await this.mainWindow.loadURL(serverUrl);
    
    log.info('✅ [EdaraERP] Fenêtre mode distant créée avec succès');
  }

  /**
   * Configure les événements de la fenêtre
   */
  setupWindowEvents() {
    // Événement de fenêtre prête à être affichée
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();
      
      if (process.env.NODE_ENV === 'development') {
        this.mainWindow.webContents.openDevTools();
      }
    });

    // Événement de fermeture
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Événement de chargement terminé
    this.mainWindow.webContents.on('did-finish-load', () => {
      log.info('✅ [EdaraERP] Page chargée avec succès');
      
      // Injecter le script de récupération intelligent si en mode local
      if (this.isLocalMode) {
        this.injectIntelligentRecoveryScript();
      }
    });

    // Événement d'échec de chargement
    this.mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
      log.error(`❌ [EdaraERP] Échec de chargement: ${errorCode} - ${errorDescription}`);
      
      // En mode local, tenter une récupération automatique
      if (this.isLocalMode && this.isContentLengthError(errorDescription)) {
        this.handleLoadFailureInLocalMode(errorCode, errorDescription, validatedURL);
      }
    });
  }

  /**
   * Charge une URL avec optimisations pour le mode local
   */
  async loadUrlWithOptimizations(serverUrl) {
    try {
      // Ajouter des paramètres d'optimisation pour le mode local
      const url = new URL(serverUrl);
      url.searchParams.set('no-cache', Date.now().toString());
      url.searchParams.set('electron-mode', 'local');
      
      log.info(`🔗 [EdaraERP] Chargement URL optimisée: ${url.toString()}`);
      
      // Charger avec timeout étendu
      await this.mainWindow.loadURL(url.toString(), {
        userAgent: 'Edara-ERP-Local/1.0 (Electron)',
        extraHeaders: 'Accept-Encoding: identity\r\n'
      });
      
    } catch (error) {
      log.error(`❌ [EdaraERP] Erreur lors du chargement optimisé: ${error.message}`);
      throw error;
    }
  }

  /**
   * Recharge avec optimisations
   */
  async reloadWithOptimizations() {
    if (this.mainWindow && !this.mainWindow.isDestroyed() && this.currentServerUrl) {
      await this.loadUrlWithOptimizations(this.currentServerUrl);
    }
  }

  /**
   * Injecte le script de récupération intelligent
   */
  async injectIntelligentRecoveryScript() {
    try {
      const scriptPath = path.join(__dirname, '../src/assets/js/intelligent-content-recovery.js');
      const fs = require('fs');
      
      if (fs.existsSync(scriptPath)) {
        const script = fs.readFileSync(scriptPath, 'utf8');
        await this.mainWindow.webContents.executeJavaScript(script);
        log.info('✅ [EdaraERP] Script de récupération intelligent injecté');
      }
    } catch (error) {
      log.warn(`⚠️ [EdaraERP] Impossible d'injecter le script de récupération: ${error.message}`);
    }
  }

  /**
   * Gère les échecs de chargement en mode local
   */
  async handleLoadFailureInLocalMode(errorCode, errorDescription, validatedURL) {
    if (this.localModeManager) {
      await this.localModeManager.handleContentLengthError({
        url: validatedURL,
        error: errorDescription,
        errorCode
      });
    }
  }

  /**
   * Gère les alertes critiques de Content-Length
   */
  async handleCriticalContentLengthAlert(alert) {
    log.error(`🚨 [EdaraERP] Alerte critique Content-Length: ${alert.message}`);
    
    // Afficher une notification à l'utilisateur
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('critical-content-length-alert', {
        message: 'Problèmes critiques détectés avec le serveur local',
        details: alert.data,
        actions: [
          'Tentative de récupération automatique en cours...',
          'Si le problème persiste, basculer en mode distant'
        ]
      });
    }
  }

  /**
   * Vérifie si une URL est locale
   */
  isLocalUrl(url) {
    return url.includes('localhost') ||
           url.includes('127.0.0.1') ||
           url.includes('192.168.') ||
           url.includes('10.') ||
           url.includes('172.');
  }

  /**
   * Vérifie si une erreur est liée au Content-Length
   */
  isContentLengthError(errorDescription) {
    return errorDescription.includes('CONTENT_LENGTH_MISMATCH') ||
           errorDescription.includes('ERR_CONTENT_LENGTH_MISMATCH');
  }

  /**
   * Démarre l'application
   */
  async start() {
    try {
      log.info('🚀 [EdaraERP] Démarrage de l\'application...');
      
      // L'application est prête, attendre la connexion utilisateur
      log.info('✅ [EdaraERP] Application prête, en attente de connexion utilisateur');
      
    } catch (error) {
      log.error(`❌ [EdaraERP] Erreur lors du démarrage: ${error.message}`);
      throw error;
    }
  }

  /**
   * Nettoie les ressources avant fermeture
   */
  cleanup() {
    if (this.localModeManager) {
      this.localModeManager.cleanup();
    }
    
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.close();
    }
    
    log.info('🧹 [EdaraERP] Nettoyage terminé');
  }
}

// Initialisation de l'application
let edaraApp = null;

app.whenReady().then(async () => {
  try {
    edaraApp = new EdaraERPWithLocalModeFix();
    await edaraApp.start();
  } catch (error) {
    log.error(`❌ [EdaraERP] Erreur fatale: ${error.message}`);
    app.quit();
  }
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', async () => {
  if (BrowserWindow.getAllWindows().length === 0 && edaraApp) {
    // Recréer une fenêtre si nécessaire
    if (edaraApp.currentServerUrl) {
      await edaraApp.ipcMain.emit('connect-to-odoo', null, edaraApp.currentServerUrl);
    }
  }
});

app.on('before-quit', () => {
  if (edaraApp) {
    edaraApp.cleanup();
  }
});

// Export pour tests
module.exports = EdaraERPWithLocalModeFix;
