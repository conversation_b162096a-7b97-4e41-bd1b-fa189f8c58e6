# Configurations Serveur Optimisées pour Éliminer ERR_CONTENT_LENGTH_MISMATCH

## 🔧 **Configuration Odoo (odoo.conf)**

### **Configuration Optimisée Complète**
```ini
[options]
# === CONFIGURATION DE BASE ===
addons_path = /opt/odoo/addons,/opt/odoo/custom-addons
data_dir = /opt/odoo/data
admin_passwd = your_admin_password
db_host = localhost
db_port = 5432
db_user = odoo
db_password = your_db_password

# === OPTIMISATIONS POUR ASSETS ===
# Désactiver la compression Gzip côté serveur pour éviter Content-Length mismatch
gzip_level = 0
gzip_types = 

# Configuration des assets
assets_debug = False
assets_bundle = True
assets_minify = True

# Cache des assets optimisé
static_cache_timeout = 3600
max_cron_threads = 4

# === TIMEOUTS OPTIMISÉS ===
# Augmenter les timeouts pour éviter les interruptions
limit_time_cpu = 300          # 5 minutes CPU timeout
limit_time_real = 600         # 10 minutes real timeout
limit_time_real_cron = 1200   # 20 minutes pour les crons

# === MÉMOIRE ET PERFORMANCE ===
# Augmenter les limites mémoire
limit_memory_hard = 4294967296    # 4GB hard limit
limit_memory_soft = 3221225472    # 3GB soft limit

# Workers optimisés pour la charge
workers = 4                   # 4 workers pour gérer la charge
max_cron_threads = 2          # 2 threads pour les tâches de fond

# === CONFIGURATION RÉSEAU ===
# Interface et port
interface = 0.0.0.0
port = 8069
longpolling_port = 8072

# Proxy mode pour Nginx/Apache
proxy_mode = True

# === LOGGING OPTIMISÉ ===
log_level = info
log_handler = :INFO
logfile = /var/log/odoo/odoo.log
log_db = False

# === SÉCURITÉ ===
# Configuration sécurisée
list_db = False
admin_passwd = your_secure_admin_password

# === OPTIMISATIONS SPÉCIFIQUES ASSETS ===
# Désactiver la compression automatique
server_wide_modules = base,web

# Configuration de la base de données
db_maxconn = 64
db_template = template0

# === CONFIGURATION EMAIL (optionnel) ===
email_from = <EMAIL>
smtp_server = localhost
smtp_port = 587
smtp_user = 
smtp_password = 
smtp_ssl = False
```

### **Variables d'Environnement Optimisées**
```bash
# Fichier /etc/environment ou .env
export ODOO_GZIP_LEVEL=0
export ODOO_ASSETS_DEBUG=False
export ODOO_WORKERS=4
export ODOO_TIMEOUT_CPU=300
export ODOO_TIMEOUT_REAL=600
export ODOO_MEMORY_HARD=4294967296
export ODOO_MEMORY_SOFT=3221225472
```

## 🌐 **Configuration Nginx (Proxy Inverse)**

### **Configuration Optimisée Complète**
```nginx
# /etc/nginx/sites-available/odoo
upstream odoo {
    server 127.0.0.1:8069 weight=1 fail_timeout=0;
}

upstream odoochat {
    server 127.0.0.1:8072 weight=1 fail_timeout=0;
}

# Configuration pour éviter les Content-Length mismatch
map $http_upgrade $connection_upgrade {
    default upgrade;
    ''      close;
}

server {
    listen 80;
    server_name your-domain.com;
    
    # Redirection HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # === CONFIGURATION SSL ===
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # === OPTIMISATIONS POUR ASSETS ===
    # DÉSACTIVER la compression Nginx pour éviter double compression
    gzip off;
    gzip_vary off;
    
    # Buffers optimisés pour gros assets
    proxy_buffering on;
    proxy_buffer_size 128k;           # Buffer initial plus grand
    proxy_buffers 16 128k;            # Plus de buffers
    proxy_busy_buffers_size 256k;     # Buffer busy plus grand
    proxy_temp_file_write_size 256k;  # Écriture temporaire optimisée
    
    # === TIMEOUTS OPTIMISÉS ===
    # Timeouts augmentés pour assets volumineux
    proxy_connect_timeout 300s;       # 5 minutes connexion
    proxy_send_timeout 300s;          # 5 minutes envoi
    proxy_read_timeout 300s;          # 5 minutes lecture
    
    # Timeouts client
    client_max_body_size 100M;        # Upload jusqu'à 100MB
    client_body_timeout 300s;         # 5 minutes body timeout
    client_header_timeout 300s;       # 5 minutes header timeout
    
    # === HEADERS OPTIMISÉS ===
    # Headers pour éviter les problèmes de cache
    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-Host $http_host;
    
    # Headers spécifiques pour Odoo
    proxy_set_header X-Odoo-dbfilter ^%d$;
    
    # === CONFIGURATION CACHE ===
    # Cache pour assets statiques
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://odoo;
        proxy_cache_valid 200 1h;
        proxy_cache_valid 404 1m;
        add_header Cache-Control "public, max-age=3600";
        
        # Headers spécifiques pour éviter Content-Length mismatch
        proxy_set_header Accept-Encoding "identity";
        proxy_hide_header Content-Encoding;
    }
    
    # === ROUTES SPÉCIFIQUES ===
    # Assets backend et common (critiques)
    location ~* /web/content/.*\.(js|css)$ {
        proxy_pass http://odoo;
        
        # Configuration spéciale pour éviter Content-Length mismatch
        proxy_buffering off;              # Désactiver buffering pour assets JS/CSS
        proxy_set_header Accept-Encoding "identity";  # Forcer identity encoding
        proxy_hide_header Content-Encoding;           # Masquer encoding headers
        proxy_hide_header Content-Length;             # Masquer Content-Length
        
        # Timeouts spéciaux pour gros assets
        proxy_connect_timeout 600s;
        proxy_send_timeout 600s;
        proxy_read_timeout 600s;
        
        # Headers de cache optimisés
        add_header Cache-Control "public, max-age=86400, immutable";
    }
    
    # WebSocket pour longpolling
    location /longpolling {
        proxy_pass http://odoochat;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $connection_upgrade;
        proxy_set_header X-Forwarded-Host $http_host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Real-IP $remote_addr;
        
        # Timeouts pour WebSocket
        proxy_connect_timeout 3600s;
        proxy_send_timeout 3600s;
        proxy_read_timeout 3600s;
    }
    
    # Route principale Odoo
    location / {
        proxy_pass http://odoo;
        
        # Configuration standard
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeouts standards
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
    
    # === LOGS ===
    access_log /var/log/nginx/odoo_access.log;
    error_log /var/log/nginx/odoo_error.log;
}
```

## 🔧 **Configuration Apache (Alternative)**

### **Configuration Optimisée**
```apache
# /etc/apache2/sites-available/odoo.conf
<VirtualHost *:80>
    ServerName your-domain.com
    Redirect permanent / https://your-domain.com/
</VirtualHost>

<VirtualHost *:443>
    ServerName your-domain.com
    
    # === SSL CONFIGURATION ===
    SSLEngine on
    SSLCertificateFile /path/to/your/certificate.crt
    SSLCertificateKeyFile /path/to/your/private.key
    SSLProtocol all -SSLv2 -SSLv3 -TLSv1 -TLSv1.1
    SSLCipherSuite ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384
    
    # === DÉSACTIVER COMPRESSION ===
    # Désactiver mod_deflate pour éviter Content-Length mismatch
    SetEnv no-gzip 1
    SetEnv no-brotli 1
    
    # === PROXY CONFIGURATION ===
    ProxyPreserveHost On
    ProxyRequests Off
    
    # === OPTIMISATIONS BUFFERS ===
    # Buffers optimisés pour gros assets
    ProxyIOBufferSize 65536
    ProxyReceiveBufferSize 262144
    
    # === TIMEOUTS OPTIMISÉS ===
    ProxyTimeout 300
    Timeout 300
    
    # === HEADERS OPTIMISÉS ===
    ProxyPassReverse / http://127.0.0.1:8069/
    ProxyPassReverse /longpolling http://127.0.0.1:8072/
    
    # Headers pour Odoo
    ProxyPreserveHost On
    RequestHeader set X-Forwarded-Proto "https"
    RequestHeader set X-Forwarded-For %{REMOTE_ADDR}s
    
    # === ROUTES SPÉCIFIQUES ===
    # Assets critiques avec configuration spéciale
    <LocationMatch "^/web/content/.*\.(js|css)$">
        ProxyPass http://127.0.0.1:8069/
        ProxyPassReverse http://127.0.0.1:8069/
        
        # Désactiver compression pour ces assets
        SetEnv no-gzip 1
        SetEnv no-brotli 1
        
        # Headers spéciaux
        RequestHeader set Accept-Encoding "identity"
        Header unset Content-Encoding
        Header unset Content-Length
        
        # Cache optimisé
        Header set Cache-Control "public, max-age=86400, immutable"
    </LocationMatch>
    
    # WebSocket longpolling
    <Location "/longpolling">
        ProxyPass http://127.0.0.1:8072/longpolling
        ProxyPassReverse http://127.0.0.1:8072/longpolling
        ProxyPreserveHost On
        
        # WebSocket support
        RewriteEngine On
        RewriteCond %{HTTP:Upgrade} websocket [NC]
        RewriteCond %{HTTP:Connection} upgrade [NC]
        RewriteRule ^/?(.*) "ws://127.0.0.1:8072/$1" [P,L]
    </Location>
    
    # Route principale
    <Location "/">
        ProxyPass http://127.0.0.1:8069/
        ProxyPassReverse http://127.0.0.1:8069/
    </Location>
    
    # === LOGS ===
    ErrorLog ${APACHE_LOG_DIR}/odoo_error.log
    CustomLog ${APACHE_LOG_DIR}/odoo_access.log combined
</VirtualHost>
```

## 🐧 **Configuration Système (Linux)**

### **Optimisations Réseau**
```bash
# /etc/sysctl.conf - Optimisations réseau
# Augmenter les buffers réseau
net.core.rmem_default = 262144
net.core.rmem_max = 16777216
net.core.wmem_default = 262144
net.core.wmem_max = 16777216

# Optimiser TCP
net.ipv4.tcp_rmem = 4096 65536 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.ipv4.tcp_congestion_control = bbr

# Augmenter les connexions
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 65535

# Appliquer les changements
sudo sysctl -p
```

### **Limites Système**
```bash
# /etc/security/limits.conf
# Augmenter les limites pour l'utilisateur Odoo
odoo soft nofile 65535
odoo hard nofile 65535
odoo soft nproc 32768
odoo hard nproc 32768

# Pour tous les utilisateurs
* soft nofile 65535
* hard nofile 65535
```

### **Service Systemd Optimisé**
```ini
# /etc/systemd/system/odoo.service
[Unit]
Description=Odoo ERP Server
Documentation=https://www.odoo.com/documentation
After=network.target postgresql.service

[Service]
Type=simple
User=odoo
Group=odoo
ExecStart=/opt/odoo/venv/bin/python3 /opt/odoo/odoo-bin -c /etc/odoo/odoo.conf
WorkingDirectory=/opt/odoo
Environment=PATH="/opt/odoo/venv/bin:$PATH"

# Optimisations pour éviter les timeouts
TimeoutStartSec=300
TimeoutStopSec=300
Restart=always
RestartSec=10

# Limites de ressources
LimitNOFILE=65535
LimitNPROC=32768

# Variables d'environnement pour optimisations
Environment="ODOO_GZIP_LEVEL=0"
Environment="ODOO_ASSETS_DEBUG=False"

[Install]
WantedBy=multi-user.target
```

## 📊 **Scripts de Monitoring**

### **Script de Vérification Configuration**
```bash
#!/bin/bash
# check-content-length-config.sh

echo "🔍 Vérification de la configuration Content-Length Mismatch"

# Vérifier Odoo
echo "📋 Configuration Odoo:"
grep -E "(gzip_level|limit_time|workers)" /etc/odoo/odoo.conf

# Vérifier Nginx
echo "📋 Configuration Nginx:"
nginx -t && echo "✅ Configuration Nginx valide" || echo "❌ Erreur configuration Nginx"

# Vérifier les timeouts
echo "📋 Timeouts système:"
ulimit -n
cat /proc/sys/net/core/somaxconn

# Test de connectivité
echo "📋 Test connectivité Odoo:"
curl -I http://localhost:8069/web/database/selector

echo "✅ Vérification terminée"
```

Cette configuration élimine les principales causes des erreurs ERR_CONTENT_LENGTH_MISMATCH en désactivant la compression problématique, optimisant les buffers et augmentant les timeouts appropriés.
