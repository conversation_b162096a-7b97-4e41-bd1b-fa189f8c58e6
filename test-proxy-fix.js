/**
 * Test de la Solution Proxy pour ERR_CONTENT_LENGTH_MISMATCH
 * Teste le proxy local qui corrige les réponses du serveur Odoo
 */

const { app, BrowserWindow } = require('electron');
const log = require('electron-log');

async function testProxyFix() {
  log.info('🧪 [TestProxy] ========================================');
  log.info('🧪 [TestProxy] TEST DE LA SOLUTION PROXY');
  log.info('🧪 [TestProxy] ========================================');

  try {
    // Étape 1: Démarrer le proxy de correction
    log.info('🚀 [TestProxy] Démarrage du proxy de correction...');
    const { initializeProxyFix } = require('./src/main/proxy-content-length-fix');
    const proxy = await initializeProxyFix();
    const stats = proxy.getStats();
    
    log.info(`📊 [TestProxy] Proxy démarré: ${JSON.stringify(stats)}`);
    log.info(`🔗 [TestProxy] URL du proxy: ${stats.proxyUrl}`);

    // Étape 2: Attendre que le proxy soit complètement démarré
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Étape 3: Créer une fenêtre de test
    log.info('🪟 [TestProxy] Création de la fenêtre de test...');
    const testWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      show: true,
      title: 'Test Solution Proxy - Edara ERP',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true
      }
    });

    let errorCount = 0;
    let contentLengthErrors = 0;
    let progressEventErrors = 0;
    let odooDefineErrors = 0;
    let loadSuccess = false;
    const startTime = Date.now();

    // Étape 4: Surveiller TOUTES les erreurs
    testWindow.webContents.on('console-message', (event, level, message) => {
      if (message.includes('ERR_CONTENT_LENGTH_MISMATCH')) {
        contentLengthErrors++;
        errorCount++;
        log.error(`🚨 [TestProxy] ERREUR CONTENT-LENGTH: ${message}`);
      } else if (message.includes('ProgressEvent')) {
        progressEventErrors++;
        errorCount++;
        log.error(`🚨 [TestProxy] ERREUR PROGRESS-EVENT: ${message}`);
      } else if (message.includes('odoo.define is not a function')) {
        odooDefineErrors++;
        errorCount++;
        log.error(`🚨 [TestProxy] ERREUR ODOO-DEFINE: ${message}`);
      } else if (message.includes('🔧 [Proxy]')) {
        log.info(`📋 [TestProxy] Log proxy: ${message}`);
      } else if (message.includes('✅ [Proxy]')) {
        log.info(`✅ [TestProxy] Log proxy: ${message}`);
      } else if (message.includes('🚨 [Proxy]')) {
        log.error(`🚨 [TestProxy] Log proxy: ${message}`);
      } else if (message.includes('Missing dependencies')) {
        errorCount++;
        log.error(`🚨 [TestProxy] ERREUR DEPENDENCIES: ${message}`);
      }
    });

    testWindow.webContents.on('did-finish-load', () => {
      const loadTime = Date.now() - startTime;
      log.info(`✅ [TestProxy] PAGE CHARGÉE EN ${loadTime}ms`);
      loadSuccess = true;
    });

    testWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
      log.error(`❌ [TestProxy] ÉCHEC DE CHARGEMENT: ${errorCode} - ${errorDescription}`);
    });

    // Étape 5: Charger l'URL via le proxy
    const proxyUrl = `${stats.proxyUrl}/web`;
    log.info(`🔗 [TestProxy] Chargement via proxy: ${proxyUrl}`);
    
    await testWindow.loadURL(proxyUrl);

    // Étape 6: Attendre et analyser
    log.info('⏳ [TestProxy] Attente de 25 secondes pour analyse complète...');
    await new Promise(resolve => setTimeout(resolve, 25000));

    const totalTime = Date.now() - startTime;

    // Étape 7: Obtenir les statistiques finales
    const finalStats = proxy.getStats();

    // Étape 8: Rapport final détaillé
    log.info('📋 [TestProxy] ========================================');
    log.info('📋 [TestProxy] RAPPORT FINAL - SOLUTION PROXY');
    log.info('📋 [TestProxy] ========================================');
    log.info(`⏱️ [TestProxy] Temps total: ${totalTime}ms`);
    log.info(`✅ [TestProxy] Chargement réussi: ${loadSuccess ? 'OUI' : 'NON'}`);
    log.info(`❌ [TestProxy] Total erreurs: ${errorCount}`);
    log.info(`🚨 [TestProxy] Erreurs Content-Length: ${contentLengthErrors}`);
    log.info(`🚨 [TestProxy] Erreurs ProgressEvent: ${progressEventErrors}`);
    log.info(`🚨 [TestProxy] Erreurs Odoo.define: ${odooDefineErrors}`);
    log.info(`📊 [TestProxy] Proxy actif: ${finalStats.isRunning ? 'OUI' : 'NON'}`);
    log.info(`📊 [TestProxy] Requêtes corrigées par proxy: ${finalStats.fixedRequests}`);
    log.info(`📊 [TestProxy] URL proxy: ${finalStats.proxyUrl}`);
    log.info('📋 [TestProxy] ========================================');

    // Étape 9: Verdict final
    if (contentLengthErrors === 0 && progressEventErrors === 0 && odooDefineErrors === 0 && loadSuccess) {
      log.info('🎉 [TestProxy] ✅ SUCCÈS TOTAL ! LA SOLUTION PROXY FONCTIONNE PARFAITEMENT !');
      log.info('🎉 [TestProxy] ✅ AUCUNE ERREUR CONTENT-LENGTH !');
      log.info('🎉 [TestProxy] ✅ AUCUNE ERREUR PROGRESS-EVENT !');
      log.info('🎉 [TestProxy] ✅ AUCUNE ERREUR ODOO.DEFINE !');
      log.info('🎉 [TestProxy] ✅ INTERFACE ODOO CHARGÉE PARFAITEMENT !');
      log.info(`🎉 [TestProxy] ✅ ${finalStats.fixedRequests} REQUÊTES CORRIGÉES PAR LE PROXY !`);
    } else if (contentLengthErrors === 0 && progressEventErrors === 0) {
      log.info('🎯 [TestProxy] ✅ SUCCÈS PARTIEL - Erreurs Content-Length éliminées');
      log.info('🎯 [TestProxy] ⚠️ Mais autres problèmes persistent');
      log.info(`🎯 [TestProxy] ✅ ${finalStats.fixedRequests} REQUÊTES CORRIGÉES !`);
      
      if (odooDefineErrors > 0) {
        log.warn(`⚠️ [TestProxy] ${odooDefineErrors} erreurs odoo.define (problème de chargement JS)`);
      }
    } else {
      log.error('💥 [TestProxy] ❌ ÉCHEC - La solution proxy ne fonctionne pas');
      log.error(`💥 [TestProxy] ❌ ${contentLengthErrors} erreurs Content-Length persistent`);
      log.error(`💥 [TestProxy] ❌ ${progressEventErrors} erreurs ProgressEvent persistent`);
      log.error(`💥 [TestProxy] ❌ ${odooDefineErrors} erreurs Odoo.define persistent`);
      
      if (finalStats.fixedRequests === 0) {
        log.error('💥 [TestProxy] ❌ AUCUNE REQUÊTE N\'A ÉTÉ CORRIGÉE PAR LE PROXY !');
        log.error('💥 [TestProxy] ❌ Le proxy ne reçoit pas les requêtes !');
      } else {
        log.info(`💥 [TestProxy] ℹ️ ${finalStats.fixedRequests} requêtes ont été corrigées mais des erreurs persistent`);
        log.error('💥 [TestProxy] ❌ PROBLÈME PLUS PROFOND AU NIVEAU DU SERVEUR !');
      }
    }

    // Étape 10: Instructions pour l'utilisateur
    log.info('📋 [TestProxy] ========================================');
    log.info('📋 [TestProxy] INSTRUCTIONS POUR L\'UTILISATEUR');
    log.info('📋 [TestProxy] ========================================');
    
    if (contentLengthErrors === 0) {
      log.info('✅ [TestProxy] SOLUTION PROXY FONCTIONNE !');
      log.info('✅ [TestProxy] Pour utiliser cette solution dans l\'application :');
      log.info(`✅ [TestProxy] 1. Démarrer le proxy sur le port ${finalStats.proxyPort}`);
      log.info(`✅ [TestProxy] 2. Utiliser l'URL ${finalStats.proxyUrl} au lieu de http://192.168.100.27:8069`);
      log.info('✅ [TestProxy] 3. Le proxy corrigera automatiquement toutes les réponses');
    } else {
      log.error('❌ [TestProxy] SOLUTION PROXY NE FONCTIONNE PAS');
      log.error('❌ [TestProxy] Le problème est plus profond que prévu');
      log.error('❌ [TestProxy] Recommandation : Modifier la configuration du serveur Odoo');
    }

    log.info('📋 [TestProxy] ========================================');

    // Garder la fenêtre ouverte pour inspection
    log.info('🔍 [TestProxy] Fenêtre gardée ouverte pour inspection...');
    log.info('🔍 [TestProxy] Ouvrez les DevTools (F12) pour vérifier les détails');
    log.info('🔍 [TestProxy] Vérifiez l\'onglet Network pour voir les headers X-Proxy-Fixed');
    log.info('🔍 [TestProxy] Le proxy continue de fonctionner en arrière-plan');
    log.info('🔍 [TestProxy] Fermez la fenêtre pour arrêter le proxy et terminer');

    // Attendre que l'utilisateur ferme la fenêtre
    await new Promise((resolve) => {
      testWindow.on('closed', () => {
        resolve();
      });
    });

    // Étape 11: Arrêter le proxy
    log.info('🛑 [TestProxy] Arrêt du proxy...');
    const { stopProxyContentLengthFix } = require('./src/main/proxy-content-length-fix');
    await stopProxyContentLengthFix();
    log.info('✅ [TestProxy] Proxy arrêté');

  } catch (error) {
    log.error(`❌ [TestProxy] ERREUR FATALE: ${error.message}`);
    log.error(error.stack);
  }
}

// Lancer le test
app.whenReady().then(async () => {
  await testProxyFix();
  app.quit();
});

app.on('window-all-closed', () => {
  // Ne pas quitter automatiquement pendant le test
});
