# Plan d'Implémentation - Gestion Unifiée des Sessions Odoo

## 🎯 **Objectif**
Remplacer le système actuel de gestion des sessions par une architecture unifiée, robuste et sécurisée utilisant les API natives d'Electron.

## 📋 **Plan de Migration en 3 Phases**

### **Phase 1 : Préparation et Implémentation (Semaine 1-2)**

#### **1.1 Mise en Place de l'Infrastructure**
```bash
# Créer les nouveaux modules
src/main/unified-session-manager.js     ✅ Créé
src/main/session-recovery-manager.js    ✅ Créé
src/main/session-events.js              # À créer
tests/session-manager.test.js           # À créer
```

#### **1.2 Intégration dans main.js**
```javascript
// Remplacer les imports existants
const UnifiedSessionManager = require('./unified-session-manager');
const SessionRecoveryManager = require('./session-recovery-manager');

// Initialiser le gestionnaire unifié
const sessionManager = new UnifiedSessionManager();
const recoveryManager = new SessionRecoveryManager(sessionManager);
```

#### **1.3 Tests Unitaires**
- Tester l'authentification
- Tester la persistance des cookies
- Tester la récupération de session
- Tester les scénarios d'erreur

### **Phase 2 : Migration Progressive (Semaine 3-4)**

#### **2.1 Remplacement des Fonctions d'Authentification**

**Avant :**
```javascript
// Code actuel dans main.js (lignes ~600-700)
ipcMain.handle('authenticate', async (event, authData) => {
  // Code complexe avec multiples fallbacks
  const result = await authenticateWithOdoo(/* ... */);
  setOdooSessionCookieInternal(/* ... */);
  // ... 50+ lignes de code
});
```

**Après :**
```javascript
// Code simplifié
ipcMain.handle('authenticate', async (event, credentials) => {
  try {
    const result = await sessionManager.authenticate(credentials);

    if (result.success) {
      // Stocker les credentials pour récupération
      await recoveryManager.storeCredentialsForRecovery(credentials);

      // Charger l'interface Odoo
      await loadOdooInterface(sessionManager.serverUrl);
    }

    return result;
  } catch (error) {
    log.error(`Erreur d'authentification: ${error.message}`);
    return { success: false, error: error.message };
  }
});
```

#### **2.2 Remplacement de la Gestion des Cookies**

**Supprimer :**
- `setOdooSessionCookieInternal()` (lignes ~2800-3000)
- Variables globales `storedSessionId`
- Multiples fonctions de définition de cookies

**Remplacer par :**
```javascript
// Utilisation directe du gestionnaire unifié
const cookieSet = await sessionManager.setCookieInSession(sessionId, serverUrl);
```

#### **2.3 Migration des Événements de Session**

**Créer `src/main/session-events.js` :**
```javascript
/**
 * Gestionnaire d'événements de session centralisé
 */
class SessionEventHandler {
  constructor(sessionManager, recoveryManager, mainWindow) {
    this.sessionManager = sessionManager;
    this.recoveryManager = recoveryManager;
    this.mainWindow = mainWindow;

    this.setupEventListeners();
  }

  setupEventListeners() {
    // Événements de session
    this.sessionManager.on('session-established', this.onSessionEstablished.bind(this));
    this.sessionManager.on('session-expired', this.onSessionExpired.bind(this));

    // Événements de récupération
    this.recoveryManager.on('recovery-started', this.onRecoveryStarted.bind(this));
    this.recoveryManager.on('recovery-completed', this.onRecoveryCompleted.bind(this));
    this.recoveryManager.on('recovery-failed', this.onRecoveryFailed.bind(this));
  }

  onSessionEstablished(sessionData) {
    log.info(`Session établie pour ${sessionData.username}`);

    // Notifier l'interface utilisateur
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('session-event', {
        type: 'session-established',
        data: sessionData
      });
    }
  }

  onSessionExpired() {
    log.warn('Session expirée');

    // Afficher un message à l'utilisateur
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('session-event', {
        type: 'session-expired',
        message: 'Votre session a expiré. Reconnexion en cours...'
      });
    }
  }

  onRecoveryStarted({ reason }) {
    log.info(`Récupération de session démarrée: ${reason}`);

    // Afficher un indicateur de reconnexion
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('session-event', {
        type: 'recovery-started',
        message: 'Reconnexion en cours...'
      });
    }
  }

  onRecoveryCompleted({ duration, attempts }) {
    log.info(`Récupération réussie en ${duration}ms (${attempts} tentatives)`);

    // Masquer l'indicateur de reconnexion
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send('session-event', {
        type: 'recovery-completed',
        message: 'Reconnexion réussie'
      });
    }
  }

  onRecoveryFailed({ reason }) {
    log.error(`Récupération échouée: ${reason}`);

    // Rediriger vers l'écran de connexion
    this.loadLoginScreen();
  }

  loadLoginScreen() {
    // Logique pour charger l'écran de connexion
  }
}

module.exports = SessionEventHandler;
```

### **Phase 3 : Nettoyage et Optimisation (Semaine 5)**

#### **3.1 Suppression du Code Legacy**

**Fichiers à nettoyer :**
- `src/main/main.js` : Supprimer les anciennes fonctions de gestion de cookies
- `src/main/odoo-auth.js` : Simplifier en utilisant le gestionnaire unifié
- `src/assets/js/session-manager.js` : Adapter pour utiliser les nouveaux événements

**Fonctions à supprimer :**
```javascript
// Dans main.js
function setOdooSessionCookieInternal() { /* ... */ }  // Lignes ~2800-3000
let storedSessionId = null;  // Variable globale
// Multiples gestionnaires IPC redondants
```

#### **3.2 Tests d'Intégration**

**Scénarios à tester :**
1. **Authentification normale**
   - Connexion réussie
   - Persistance après redémarrage
   - Navigation dans Odoo

2. **Gestion d'erreurs**
   - Identifiants incorrects
   - Serveur indisponible
   - Perte de connexion réseau

3. **Récupération automatique**
   - Expiration de session
   - Reconnexion après perte réseau
   - Échec de récupération

4. **Performance**
   - Temps de connexion
   - Utilisation mémoire
   - Stabilité sur longue durée

#### **3.3 Documentation et Formation**

**Créer :**
- Guide d'utilisation pour les développeurs
- Documentation des événements de session
- Guide de débogage des problèmes de session

## 🔧 **Configuration Recommandée**

### **Paramètres de Session Optimaux**
```javascript
const sessionConfig = {
  // Durée de session : 8 heures (journée de travail)
  sessionTimeout: 8 * 60 * 60 * 1000,

  // Vérification de santé : toutes les 5 minutes
  checkInterval: 5 * 60 * 1000,

  // Récupération automatique
  autoReconnect: true,
  maxRetryAttempts: 3,
  retryDelay: 2000,

  // Période de grâce avant expiration : 10 minutes
  gracePeriod: 10 * 60 * 1000
};
```

### **Sécurité Renforcée**
```javascript
// Partition dédiée avec chiffrement
const sessionPartition = 'persist:odoo-session-encrypted';

// Cookies sécurisés
const cookieOptions = {
  secure: true,      // HTTPS uniquement
  httpOnly: false,   // Compatibilité Odoo
  sameSite: 'strict' // Protection CSRF
};
```

## 📊 **Métriques de Succès**

### **Objectifs Quantifiables**
- ✅ **Réduction des déconnexions** : -90%
- ✅ **Temps de reconnexion** : <5 secondes
- ✅ **Complexité du code** : -60% de lignes
- ✅ **Temps de débogage** : -80%

### **Indicateurs de Performance**
- Taux de succès d'authentification : >99%
- Temps de récupération automatique : <10 secondes
- Stabilité de session : >24 heures sans intervention
- Utilisation mémoire : <50MB pour la gestion de session

## 🚀 **Déploiement**

### **Stratégie de Rollout**
1. **Tests internes** (Semaine 5)
2. **Déploiement beta** (Semaine 6)
3. **Déploiement production** (Semaine 7)
4. **Monitoring et ajustements** (Semaine 8)

### **Plan de Rollback**
- Conserver l'ancien code en commentaire pendant 2 semaines
- Tests de régression automatisés
- Procédure de rollback en <1 heure

Cette approche garantit une migration sûre et progressive vers un système de gestion de session robuste et maintenable.
   - Perte de connexion réseau

3. **Récupération automatique**
   - Expiration de session
   - Reconnexion après perte réseau
   - Échec de récupération

4. **Performance**
   - Temps de connexion
   - Utilisation mémoire
   - Stabilité sur longue durée

#### **3.3 Documentation et Formation**

**Créer :**
- Guide d'utilisation pour les développeurs
- Documentation des événements de session
- Guide de débogage des problèmes de session

## 🔧 **Configuration Recommandée**

### **Paramètres de Session Optimaux**
```javascript
const sessionConfig = {
  // Durée de session : 8 heures (journée de travail)
  sessionTimeout: 8 * 60 * 60 * 1000,

  // Vérification de santé : toutes les 5 minutes
  checkInterval: 5 * 60 * 1000,

  // Récupération automatique
  autoReconnect: true,
  maxRetryAttempts: 3,
  retryDelay: 2000,

  // Période de grâce avant expiration : 10 minutes
  gracePeriod: 10 * 60 * 1000
};
```

### **Sécurité Renforcée**
```javascript
// Partition dédiée avec chiffrement
const sessionPartition = 'persist:odoo-session-encrypted';

// Cookies sécurisés
const cookieOptions = {
  secure: true,      // HTTPS uniquement
  httpOnly: false,   // Compatibilité Odoo
  sameSite: 'strict' // Protection CSRF
};
```

## 📊 **Métriques de Succès**

### **Objectifs Quantifiables**
- ✅ **Réduction des déconnexions** : -90%
- ✅ **Temps de reconnexion** : <5 secondes
- ✅ **Complexité du code** : -60% de lignes
- ✅ **Temps de débogage** : -80%

### **Indicateurs de Performance**
- Taux de succès d'authentification : >99%
- Temps de récupération automatique : <10 secondes
- Stabilité de session : >24 heures sans intervention
- Utilisation mémoire : <50MB pour la gestion de session

## 🚀 **Déploiement**

### **Stratégie de Rollout**
1. **Tests internes** (Semaine 5)
2. **Déploiement beta** (Semaine 6)
3. **Déploiement production** (Semaine 7)
4. **Monitoring et ajustements** (Semaine 8)

### **Plan de Rollback**
- Conserver l'ancien code en commentaire pendant 2 semaines
- Tests de régression automatisés
- Procédure de rollback en <1 heure

Cette approche garantit une migration sûre et progressive vers un système de gestion de session robuste et maintenable.
