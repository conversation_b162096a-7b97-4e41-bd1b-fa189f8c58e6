# Guide de Test et Validation - Mode Local ERR_CONTENT_LENGTH_MISMATCH

## 🎯 **Objectif des Tests**

Valider que la solution élimine complètement les erreurs ERR_CONTENT_LENGTH_MISMATCH en mode local tout en maintenant la fonctionnalité complète d'Odoo dans Electron.

## 🧪 **Plan de Test Complet**

### **Phase 1 : Tests de Configuration (30 minutes)**

#### **Test 1.1 : Vérification des Intercepteurs**
```javascript
// Dans la console DevTools d'Electron
console.log('=== Test des Intercepteurs ===');

// Vérifier que les intercepteurs sont installés
window.EdaraLocalModeTest = {
  testInterceptors: async () => {
    const testUrl = 'http://localhost:8069/web/static/src/js/boot.js';
    
    try {
      const response = await fetch(testUrl, {
        headers: { 'Accept-Encoding': 'gzip' }
      });
      
      console.log('Headers de réponse:', response.headers);
      console.log('Content-Encoding:', response.headers.get('content-encoding'));
      console.log('Content-Length:', response.headers.get('content-length'));
      
      return {
        success: true,
        hasContentEncoding: !!response.headers.get('content-encoding'),
        hasContentLength: !!response.headers.get('content-length'),
        status: response.status
      };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
};

// Exécuter le test
window.EdaraLocalModeTest.testInterceptors().then(console.log);
```

**Résultat attendu :**
- ✅ `hasContentEncoding: false` (compression désactivée)
- ✅ `hasContentLength: false` ou valeur correcte
- ✅ `status: 200`

#### **Test 1.2 : Vérification du Cache et Session**
```javascript
// Test de nettoyage du cache
console.log('=== Test Cache et Session ===');

// Vérifier l'état du cache
window.EdaraCacheTest = {
  testCacheClearing: async () => {
    // Obtenir les statistiques avant nettoyage
    const beforeStats = await window.electronAPI?.sendAuthData({
      action: 'get-local-mode-stats'
    });
    
    // Forcer un nettoyage
    const clearResult = await window.electronAPI?.sendAuthData({
      action: 'force-local-mode-reset'
    });
    
    // Vérifier après nettoyage
    const afterStats = await window.electronAPI?.sendAuthData({
      action: 'get-local-mode-stats'
    });
    
    return {
      beforeStats,
      clearResult,
      afterStats,
      cacheCleared: clearResult?.success
    };
  }
};

window.EdaraCacheTest.testCacheClearing().then(console.log);
```

### **Phase 2 : Tests de Charge Assets (45 minutes)**

#### **Test 2.1 : Chargement Assets Critiques**
```bash
# Script de test en ligne de commande
#!/bin/bash
echo "🧪 Test de chargement des assets critiques"

# URLs des assets critiques à tester
ASSETS=(
  "http://localhost:8069/web/content/566-ba15075/web.assets_backend.js"
  "http://localhost:8069/web/content/567-ba15075/web.assets_common.js"
  "http://localhost:8069/web/content/568-ba15075/web.assets_backend.css"
  "http://localhost:8069/web/content/569-ba15075/web.assets_common.css"
)

for asset in "${ASSETS[@]}"; do
  echo "Testing: $asset"
  
  # Test avec compression
  echo "  - Test avec compression:"
  curl -H "Accept-Encoding: gzip" -I "$asset" 2>/dev/null | grep -E "(HTTP|Content-Length|Content-Encoding)"
  
  # Test sans compression
  echo "  - Test sans compression:"
  curl -H "Accept-Encoding: identity" -I "$asset" 2>/dev/null | grep -E "(HTTP|Content-Length|Content-Encoding)"
  
  echo "---"
done
```

#### **Test 2.2 : Test de Performance de Chargement**
```javascript
// Dans la console DevTools
console.log('=== Test Performance Assets ===');

window.EdaraPerformanceTest = {
  testAssetLoadingSpeed: async () => {
    const assets = [
      '/web/static/src/js/boot.js',
      '/web/static/src/css/bootstrap.css',
      '/web/static/lib/jquery/jquery.js'
    ];
    
    const results = [];
    
    for (const asset of assets) {
      const startTime = performance.now();
      
      try {
        const response = await fetch(asset, {
          headers: { 'Accept-Encoding': 'identity' }
        });
        
        const endTime = performance.now();
        const loadTime = endTime - startTime;
        
        results.push({
          asset,
          loadTime,
          status: response.status,
          size: response.headers.get('content-length'),
          success: response.ok
        });
        
      } catch (error) {
        results.push({
          asset,
          error: error.message,
          success: false
        });
      }
    }
    
    return {
      results,
      averageLoadTime: results.reduce((sum, r) => sum + (r.loadTime || 0), 0) / results.length,
      successRate: results.filter(r => r.success).length / results.length
    };
  }
};

window.EdaraPerformanceTest.testAssetLoadingSpeed().then(console.log);
```

**Critères de succès :**
- ✅ Temps de chargement < 3 secondes par asset
- ✅ Taux de succès > 95%
- ✅ Aucune erreur ERR_CONTENT_LENGTH_MISMATCH

### **Phase 3 : Tests de Récupération (30 minutes)**

#### **Test 3.1 : Simulation d'Erreurs Content-Length**
```javascript
// Simuler une erreur Content-Length
console.log('=== Test Récupération d\'Erreurs ===');

window.EdaraRecoveryTest = {
  simulateContentLengthError: async () => {
    // Créer un élément script avec une URL qui va échouer
    const script = document.createElement('script');
    script.src = '/web/static/src/js/nonexistent.js?simulate-error=1';
    
    return new Promise((resolve) => {
      let errorDetected = false;
      let recoveryAttempted = false;
      
      script.onerror = () => {
        errorDetected = true;
        console.log('Erreur détectée, vérification de la récupération...');
        
        // Vérifier si le système de récupération intervient
        setTimeout(() => {
          resolve({
            errorDetected,
            recoveryAttempted: window.EdaraIntelligentRecovery ? true : false,
            recoverySystemActive: !!window.EdaraIntelligentRecovery
          });
        }, 2000);
      };
      
      document.head.appendChild(script);
      
      // Timeout de sécurité
      setTimeout(() => {
        resolve({
          errorDetected: false,
          timeout: true
        });
      }, 5000);
    });
  }
};

window.EdaraRecoveryTest.simulateContentLengthError().then(console.log);
```

#### **Test 3.2 : Test de Récupération Automatique**
```javascript
// Test du système de récupération automatique
window.EdaraAutoRecoveryTest = {
  testAutoRecovery: async () => {
    if (!window.EdaraIntelligentRecovery) {
      return { error: 'Système de récupération non disponible' };
    }
    
    // Obtenir les stats avant
    const statsBefore = window.EdaraIntelligentRecovery.getStats();
    
    // Forcer une récupération
    const recoveryResult = await window.EdaraIntelligentRecovery.forceRecovery(
      'http://localhost:8069/web/static/src/js/test.js'
    );
    
    // Obtenir les stats après
    const statsAfter = window.EdaraIntelligentRecovery.getStats();
    
    return {
      statsBefore,
      recoveryResult,
      statsAfter,
      recoveryWorking: !!recoveryResult
    };
  }
};

window.EdaraAutoRecoveryTest.testAutoRecovery().then(console.log);
```

### **Phase 4 : Tests d'Intégration Odoo (60 minutes)**

#### **Test 4.1 : Test de Connexion et Navigation**
```javascript
// Test complet de navigation Odoo
console.log('=== Test Intégration Odoo ===');

window.EdaraOdooIntegrationTest = {
  testOdooNavigation: async () => {
    const tests = [];
    
    // Test 1: Vérifier que Odoo est chargé
    tests.push({
      name: 'Odoo Core Loaded',
      result: !!(window.odoo && window.odoo.define),
      details: {
        hasOdoo: !!window.odoo,
        hasDefine: !!(window.odoo && window.odoo.define),
        hasWebClient: !!document.querySelector('.o_web_client')
      }
    });
    
    // Test 2: Vérifier les menus
    tests.push({
      name: 'Menus Loaded',
      result: !!document.querySelector('.o_main_navbar'),
      details: {
        hasNavbar: !!document.querySelector('.o_main_navbar'),
        hasAppsMenu: !!document.querySelector('.o_apps_menu'),
        menuCount: document.querySelectorAll('.o_menu_item').length
      }
    });
    
    // Test 3: Vérifier les assets backend
    tests.push({
      name: 'Backend Assets',
      result: !!document.querySelector('script[src*="web.assets_backend"]'),
      details: {
        backendJS: !!document.querySelector('script[src*="web.assets_backend.js"]'),
        backendCSS: !!document.querySelector('link[href*="web.assets_backend.css"]'),
        commonJS: !!document.querySelector('script[src*="web.assets_common.js"]'),
        commonCSS: !!document.querySelector('link[href*="web.assets_common.css"]')
      }
    });
    
    return {
      tests,
      overallSuccess: tests.every(t => t.result),
      successRate: tests.filter(t => t.result).length / tests.length
    };
  }
};

window.EdaraOdooIntegrationTest.testOdooNavigation().then(console.log);
```

#### **Test 4.2 : Test de Fonctionnalités Odoo**
```javascript
// Test des fonctionnalités principales d'Odoo
window.EdaraOdooFunctionalityTest = {
  testOdooFunctionality: async () => {
    const results = {};
    
    // Test de l'ouverture d'une application
    try {
      // Simuler un clic sur une application (si disponible)
      const appIcon = document.querySelector('.o_app[data-menu-xmlid]');
      if (appIcon) {
        results.appClickable = true;
        results.appName = appIcon.getAttribute('data-menu-xmlid');
      } else {
        results.appClickable = false;
      }
    } catch (error) {
      results.appError = error.message;
    }
    
    // Test de la recherche
    try {
      const searchInput = document.querySelector('.o_searchview_input');
      results.searchAvailable = !!searchInput;
    } catch (error) {
      results.searchError = error.message;
    }
    
    // Test des notifications
    try {
      results.notificationSystem = !!window.odoo.__DEBUG__.services['notification'];
    } catch (error) {
      results.notificationError = error.message;
    }
    
    return results;
  }
};

window.EdaraOdooFunctionalityTest.testOdooFunctionality().then(console.log);
```

## 📊 **Critères de Validation**

### **Critères de Succès Obligatoires**
- ✅ **Zéro erreur ERR_CONTENT_LENGTH_MISMATCH** dans la console
- ✅ **Interface Odoo complètement chargée** (navbar, menus, assets)
- ✅ **Temps de chargement < 10 secondes** pour la page complète
- ✅ **Fonctionnalités Odoo opérationnelles** (navigation, recherche)

### **Critères de Performance**
- ✅ **Assets chargés en < 3 secondes** chacun
- ✅ **Taux de succès > 95%** pour tous les assets
- ✅ **Utilisation mémoire < 300MB** après chargement complet
- ✅ **Récupération automatique < 5 secondes** en cas d'erreur

### **Critères de Stabilité**
- ✅ **Aucun crash** pendant 30 minutes d'utilisation
- ✅ **Rechargements automatiques < 2** par session
- ✅ **Navigation fluide** entre les modules Odoo
- ✅ **Pas de fuite mémoire** détectable

## 🔧 **Scripts de Test Automatisés**

### **Script de Test Complet**
```bash
#!/bin/bash
# test-local-mode-complete.sh

echo "🧪 Test Complet Mode Local - Edara ERP"
echo "======================================"

# Variables
ODOO_URL="http://localhost:8069"
TEST_RESULTS_FILE="test-results-$(date +%Y%m%d-%H%M%S).json"

# Test 1: Connectivité de base
echo "📡 Test 1: Connectivité de base"
if curl -s --max-time 5 "$ODOO_URL/web/database/selector" > /dev/null; then
  echo "✅ Serveur Odoo accessible"
  CONNECTIVITY_OK=true
else
  echo "❌ Serveur Odoo inaccessible"
  CONNECTIVITY_OK=false
fi

# Test 2: Assets sans compression
echo "🎯 Test 2: Assets sans compression"
ASSETS_TEST_RESULTS=()

for asset in "web.assets_backend.js" "web.assets_common.js" "web.assets_backend.css"; do
  echo "  Testing: $asset"
  
  RESPONSE=$(curl -s -H "Accept-Encoding: identity" -I "$ODOO_URL/web/static/src/js/boot.js")
  
  if echo "$RESPONSE" | grep -q "200 OK"; then
    if echo "$RESPONSE" | grep -q "Content-Encoding:"; then
      echo "  ❌ Compression détectée (problème)"
      ASSETS_TEST_RESULTS+=("$asset:FAIL:compression")
    else
      echo "  ✅ Pas de compression (correct)"
      ASSETS_TEST_RESULTS+=("$asset:PASS:no-compression")
    fi
  else
    echo "  ❌ Asset non accessible"
    ASSETS_TEST_RESULTS+=("$asset:FAIL:not-accessible")
  fi
done

# Test 3: Performance de chargement
echo "⚡ Test 3: Performance de chargement"
LOAD_TIME=$(curl -s -w "%{time_total}" -o /dev/null "$ODOO_URL/web/login")
echo "  Temps de chargement page login: ${LOAD_TIME}s"

if (( $(echo "$LOAD_TIME < 5.0" | bc -l) )); then
  echo "  ✅ Performance acceptable"
  PERFORMANCE_OK=true
else
  echo "  ❌ Performance dégradée"
  PERFORMANCE_OK=false
fi

# Générer le rapport
echo "📊 Génération du rapport..."
cat > "$TEST_RESULTS_FILE" << EOF
{
  "timestamp": "$(date -Iseconds)",
  "connectivity": $CONNECTIVITY_OK,
  "performance": $PERFORMANCE_OK,
  "loadTime": $LOAD_TIME,
  "assets": [
$(printf '    "%s",' "${ASSETS_TEST_RESULTS[@]}" | sed 's/,$//')
  ],
  "summary": {
    "overall": $([ "$CONNECTIVITY_OK" = true ] && [ "$PERFORMANCE_OK" = true ] && echo "true" || echo "false")
  }
}
EOF

echo "✅ Rapport sauvegardé: $TEST_RESULTS_FILE"

# Résumé final
echo ""
echo "📋 RÉSUMÉ DES TESTS"
echo "=================="
echo "Connectivité: $([ "$CONNECTIVITY_OK" = true ] && echo "✅ OK" || echo "❌ ÉCHEC")"
echo "Performance: $([ "$PERFORMANCE_OK" = true ] && echo "✅ OK" || echo "❌ ÉCHEC")"
echo "Assets: $(echo "${ASSETS_TEST_RESULTS[@]}" | grep -c "PASS")/$(echo "${ASSETS_TEST_RESULTS[@]}" | wc -w) OK"

if [ "$CONNECTIVITY_OK" = true ] && [ "$PERFORMANCE_OK" = true ]; then
  echo ""
  echo "🎉 TOUS LES TESTS SONT PASSÉS!"
  echo "Le mode local fonctionne correctement."
  exit 0
else
  echo ""
  echo "❌ CERTAINS TESTS ONT ÉCHOUÉ"
  echo "Vérifier la configuration et les logs."
  exit 1
fi
```

### **Exécution des Tests**
```bash
# Rendre le script exécutable
chmod +x test-local-mode-complete.sh

# Exécuter les tests
./test-local-mode-complete.sh

# Vérifier les résultats
cat test-results-*.json | jq '.summary.overall'
```

## 🎯 **Validation Finale**

### **Checklist de Validation**
- [ ] Aucune erreur ERR_CONTENT_LENGTH_MISMATCH dans les DevTools
- [ ] Interface Odoo complètement fonctionnelle
- [ ] Navigation fluide entre les modules
- [ ] Temps de chargement acceptable (< 10s)
- [ ] Système de récupération automatique opérationnel
- [ ] Métriques de performance dans les seuils acceptables
- [ ] Tests automatisés tous passés

### **Critères d'Acceptation**
✅ **SUCCÈS** : Tous les critères obligatoires respectés + 80% des critères de performance
❌ **ÉCHEC** : Un ou plusieurs critères obligatoires non respectés

Cette solution garantit l'élimination complète des erreurs ERR_CONTENT_LENGTH_MISMATCH en mode local tout en maintenant une expérience utilisateur optimale.
