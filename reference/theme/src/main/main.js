/**
 * Processus principal de l'application Edara ERP
 * Ce fichier est le point d'entrée de l'application Electron
 */

const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const log = require('../../logger');

// Désactiver l'accélération matérielle pour éviter les erreurs graphiques
app.disableHardwareAcceleration();

// Garde une référence globale des objets window pour éviter qu'ils ne soient fermés par le garbage collector
let splashWindow = null;
let mainWindow = null;
let loginWindow = null;

/**
 * Crée la fenêtre de démarrage (splash screen)
 */
function createSplashWindow() {
  log.info('Création de la fenêtre de démarrage');

  splashWindow = new BrowserWindow({
    width: 950,
    height: 500,
    transparent: false, // Changer à false pour éviter les erreurs graphiques
    backgroundColor: '#f5f5f5', // Ajouter une couleur de fond
    frame: false,
    alwaysOnTop: true,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      contextIsolation: true,
      nodeIntegration: false,
      offscreen: false // Désactiver le rendu hors écran
    }
  });

  splashWindow.loadFile(path.join(__dirname, '../renderer/splash.html'));

  // Ferme la fenêtre de démarrage quand elle est fermée
  splashWindow.on('closed', () => {
    splashWindow = null;
  });
}

/**
 * Crée la fenêtre de connexion
 */
function createLoginWindow() {
  log.info('Création de la fenêtre de connexion');

  loginWindow = new BrowserWindow({
    width: 1400,
    height: 750,
    show: false,
    backgroundColor: '#f0f0f0', // Ajouter une couleur de fond
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      contextIsolation: true,
      nodeIntegration: false,
      offscreen: false // Désactiver le rendu hors écran
    }
  });

  loginWindow.loadFile(path.join(__dirname, '../renderer/custom-login.html'));

  // Affiche la fenêtre quand elle est prête
  loginWindow.once('ready-to-show', () => {
    loginWindow.show();
  });

  // Ferme la fenêtre de connexion quand elle est fermée
  loginWindow.on('closed', () => {
    loginWindow = null;
  });
}

/**
 * Crée la fenêtre principale de l'application avec une WebView pour Odoo
 * @param {Object} authData - Les données d'authentification
 */
function createMainWindow(authData) {
  log.info('Création de la fenêtre principale avec WebView');

  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    show: false,
    backgroundColor: '#f0f0f0', // Ajouter une couleur de fond
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      contextIsolation: true,
      nodeIntegration: false,
      webviewTag: true, // Activer la balise webview
      offscreen: false // Désactiver le rendu hors écran
    }
  });

  // Charger la page HTML qui contient la WebView
  mainWindow.loadFile(path.join(__dirname, '../renderer/main.html'));

  // Affiche la fenêtre quand elle est prête
  mainWindow.once('ready-to-show', () => {
    // Envoyer les données d'authentification à la page
    mainWindow.webContents.executeJavaScript(`
      localStorage.setItem('sessionId', '${authData.session_id}');
      localStorage.setItem('odooWebUrl', '${authData.odoo_url}');
    `).then(() => {
      mainWindow.show();
    }).catch(err => {
      log.error('Erreur lors de l\'initialisation de la WebView:', err);
    });
  });

  // Ferme l'application quand la fenêtre principale est fermée
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// Quand l'application est prête, crée la fenêtre de démarrage
app.whenReady().then(() => {
  log.info('Application démarrée');
  createSplashWindow();

  // Après 5 secondes, ferme la fenêtre de démarrage et ouvre la fenêtre de connexion
  setTimeout(() => {
    if (splashWindow) {
      splashWindow.close();
      createLoginWindow();
    }
  }, 5000);

  app.on('activate', () => {
    // Sur macOS, il est courant de recréer une fenêtre dans l'application quand
    // l'icône du dock est cliquée et qu'il n'y a pas d'autres fenêtres ouvertes.
    if (BrowserWindow.getAllWindows().length === 0) {
      createLoginWindow();
    }
  });
});

// Quitte l'application quand toutes les fenêtres sont fermées, sauf sur macOS
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Gestion des événements IPC
ipcMain.on('auth-data', (_, data) => { // Utiliser _ pour indiquer un paramètre non utilisé
  log.info('Données d\'authentification reçues:', data.action);

  // Gérer différentes actions
  switch (data.action) {
    case 'splash-finished':
      // L'écran de démarrage a terminé son animation
      log.info('Écran de démarrage terminé');
      if (splashWindow) {
        splashWindow.close();
        createLoginWindow();
      }
      break;

    case 'login':
      // Traiter les données d'authentification
      log.info('Tentative de connexion pour l\'utilisateur:', data.name);

      // Vérifier si l'URL Odoo est présente
      if (!data.odoo_url) {
        log.error('URL Odoo manquante dans les données d\'authentification:', data);
        return;
      }

      // Si le session_id est manquant, utiliser une valeur par défaut
      if (!data.session_id) {
        log.warn('Session ID manquant, utilisation d\'une valeur générée');
        // Générer un identifiant aléatoire de 32 caractères hexadécimaux
        data.session_id = Array.from(Array(32), () => Math.floor(Math.random() * 16).toString(16)).join('');
      }

      log.info('Connexion réussie, redirection vers l\'interface Odoo:', data.odoo_url);

      // Si l'authentification réussit, fermez la fenêtre de connexion et ouvrez la fenêtre principale
      if (loginWindow) {
        loginWindow.close();
        createMainWindow(data); // Passer les données d'authentification à la fonction
      }
      break;

    case 'logout':
      // Gérer la déconnexion
      log.info('Déconnexion de l\'utilisateur');
      if (mainWindow) {
        mainWindow.close();
        createLoginWindow();
      }
      break;

    default:
      log.warn('Action non reconnue:', data.action);
  }
});
