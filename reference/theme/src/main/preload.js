/**
 * Script de préchargement pour l'application Edara ERP
 * Ce fichier est chargé avant le processus de rendu et permet une communication
 * sécurisée entre le processus principal et le processus de rendu
 */

const { contextBridge, ipcRenderer } = require('electron');

// Expose des API protégées aux pages de rendu
contextBridge.exposeInMainWorld('electronAPI', {
  // API pour envoyer des données d'authentification au processus principal
  sendAuthData: (data) => {
    ipcRenderer.send('auth-data', data);
  },
  
  // Vous pouvez ajouter d'autres méthodes ici pour exposer plus de fonctionnalités
  // Par exemple, pour récupérer des données depuis le processus principal
  // getConfig: () => ipcRenderer.invoke('get-config'),
  
  // Pour écouter les événements du processus principal
  // onUpdateStatus: (callback) => ipcRenderer.on('update-status', callback),
});

// Vous pouvez également ajouter des écouteurs d'événements ici
// Par exemple, pour écouter les événements du processus principal
// ipcRenderer.on('some-event', (event, ...args) => {
//   // Faire quelque chose avec les arguments
// });
