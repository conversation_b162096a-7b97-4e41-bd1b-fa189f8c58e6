<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Edara - Splash Screen</title>
  <style>
    /* Variables CSS pour le thème clair */
    :root {
      --background-color: #f5f5f5;
      --text-color: #000000;
      --secondary-text-color: #333333;
      --progress-bar-background: #e6e6e6;
      --progress-bar-fill: #585765;
    }

    /* Variables CSS pour le thème sombre */
    .dark-theme {
      --background-color: #2d3436;
      --text-color: #f5f6fa;
      --secondary-text-color: #dfe6e9;
      --progress-bar-background: #3d4548;
      --progress-bar-fill: #585765;
    }

    /* Appliquer le thème sombre si le système est en mode sombre */
    @media (prefers-color-scheme: dark) {
      body {
        --background-color: #2d3436;
        --text-color: #f5f6fa;
        --secondary-text-color: #dfe6e9;
        --progress-bar-background: #3d4548;
        --progress-bar-fill: #585765;
      }
    }

    html, body {
      margin: 0;
      padding: 0;
      height: 100%;
      width: 100%;
      overflow: hidden;
      font-family: 'Arial', sans-serif;
      background-color: var(--background-color);
      display: flex;
    }

    /* Conteneur principal avec deux sections */
    .container {
      display: flex;
      width: 100%;
      height: 100%;
    }

    /* Section gauche (contenu) */
    .left-section {
      width: 50%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-left: 50px;
    }

    /* Conteneur pour le logo et le titre */
    .logo-container {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }

    .logo-container img {
      width: 70px;
      height: 70px;
      margin-right: 15px;
    }

    .logo-container h1 {
      font-size: 36px;
      font-weight: 600;
      color: var(--text-color);
      margin: 0;
    }

    /* Texte de chargement avec animation de points */
    .loading-text {
      font-size: 18px;
      font-weight: 400;
      color: var(--secondary-text-color);
      margin-bottom: 20px;
    }

    .loading-text::after {
      content: '...';
      animation: dots 1.5s infinite;
    }

    @keyframes dots {
      0% { content: '.'; }
      33% { content: '..'; }
      66% { content: '...'; }
    }

    /* Barre de progression */
    .progress-bar {
      width: 100%;
      max-width: 400px;
      height: 10px;
      background-color: var(--progress-bar-background);
      border-radius: 5px;
      overflow: hidden;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .progress-bar-fill {
      width: 0;
      height: 100%;
      background-color: var(--progress-bar-fill);
      transition: width 0.3s ease;
    }

    /* Section droite (illustration) */
    .right-section {
      width: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .right-section img {
      width: 80%;
      height: 80%;
      object-fit: contain;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Section gauche : contenu -->
    <div class="left-section">
      <div class="logo-container">
        <img src="../assets/images/icon.png" alt="Edara Logo">
        <h1>Edara</h1>
      </div>
      <p class="loading-text">Initialisation de l'application</p>
      <div class="progress-bar">
        <div class="progress-bar-fill" id="progressBar"></div>
      </div>
    </div>
    <!-- Section droite : illustration -->
    <div class="right-section">
      <img src="../assets/images/illustration.svg" alt="Illustration">
    </div>
  </div>

  <script>
    // Simulation de la progression (peut être remplacé par la logique réelle)
    const progressBar = document.getElementById('progressBar');
    let progress = 0;

    const updateProgress = () => {
      if (progress < 100) {
        progress += 1;
        progressBar.style.width = `${progress}%`;
        setTimeout(updateProgress, 50);
      } else {
        // Une fois la progression terminée, envoyer un message au processus principal
        console.log('Écran de démarrage terminé');
        // Envoyer un message au processus principal via l'API Electron
        if (window.electronAPI) {
          window.electronAPI.sendAuthData({ action: 'splash-finished' });
        }
      }
    };

    // Démarrer la progression
    console.log('Écran de démarrage initialisé');
    updateProgress();

    // Écoute des changements de thème via IPC (si applicable)
    try {
      const { ipcRenderer } = require('electron');
      ipcRenderer.on('theme-changed', (event, theme) => {
        if (theme === 'dark') {
          document.body.classList.add('dark-theme');
        } else {
          document.body.classList.remove('dark-theme');
        }
      });
    } catch (error) {
      console.error('Erreur lors de l\'initialisation de l\'IPC:', error);
    }
  </script>
</body>
</html>
