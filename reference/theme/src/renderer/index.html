<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Edara ERP - Tableau de bord</title>
  <link rel="stylesheet" href="../assets/css/theme.css">
</head>
<body>
  <div class="app-container">
    <header class="app-header">
      <div class="header-logo">
        <img src="../assets/images/logo.png" alt="Edara ERP Logo" class="small-logo">
        <span class="app-title">Edara ERP</span>
      </div>
      <div class="header-actions">
        <div class="user-info">
          <span id="user-name">Utilisateur</span>
          <button id="logout-btn" class="btn-logout">Déconnexion</button>
        </div>
      </div>
    </header>
    
    <div class="app-content">
      <nav class="sidebar">
        <ul class="menu">
          <li class="menu-item active">
            <a href="#dashboard">Tableau de bord</a>
          </li>
          <li class="menu-item">
            <a href="#sales">Ventes</a>
          </li>
          <li class="menu-item">
            <a href="#purchases">Achats</a>
          </li>
          <li class="menu-item">
            <a href="#inventory">Inventaire</a>
          </li>
          <li class="menu-item">
            <a href="#accounting">Comptabilité</a>
          </li>
          <li class="menu-item">
            <a href="#reports">Rapports</a>
          </li>
          <li class="menu-item">
            <a href="#settings">Paramètres</a>
          </li>
        </ul>
      </nav>
      
      <main class="main-content">
        <div class="dashboard-container">
          <h1>Tableau de bord</h1>
          <p>Bienvenue sur Edara ERP. Cette application est en cours de développement.</p>
          
          <div class="dashboard-widgets">
            <div class="widget">
              <h3>Ventes du jour</h3>
              <div class="widget-content">
                <p>Aucune donnée disponible</p>
              </div>
            </div>
            
            <div class="widget">
              <h3>Achats récents</h3>
              <div class="widget-content">
                <p>Aucune donnée disponible</p>
              </div>
            </div>
            
            <div class="widget">
              <h3>Stock</h3>
              <div class="widget-content">
                <p>Aucune donnée disponible</p>
              </div>
            </div>
            
            <div class="widget">
              <h3>Trésorerie</h3>
              <div class="widget-content">
                <p>Aucune donnée disponible</p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
  
  <script>
    // Ce script sera remplacé par des scripts plus complexes à mesure que l'application évolue
    document.addEventListener('DOMContentLoaded', () => {
      // Récupérer les informations de l'utilisateur depuis le stockage local
      const userName = localStorage.getItem('userName') || 'Utilisateur';
      document.getElementById('user-name').textContent = userName;
      
      // Gérer la déconnexion
      document.getElementById('logout-btn').addEventListener('click', () => {
        // Effacer les informations d'authentification
        localStorage.clear();
        // Rediriger vers la page de connexion (via le processus principal)
        window.electronAPI.sendAuthData({ action: 'logout' });
      });
    });
  </script>
</body>
</html>
