<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Edara ERP - Interface Odoo</title>
  <style>
    /* Styles pour que la WebView occupe tout l'espace disponible */
    html, body {
      margin: 0;
      padding: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }

    .container {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 100%;
    }

    .header {
      background-color: #2c3e50;
      color: white;
      padding: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 40px;
      flex-shrink: 0;
    }

    .header-logo {
      display: flex;
      align-items: center;
    }

    .small-logo {
      height: 30px;
      margin-right: 10px;
    }

    .app-title {
      font-size: 18px;
      font-weight: bold;
    }

    .header-actions {
      display: flex;
      align-items: center;
    }

    .btn-logout {
      background-color: #e74c3c;
      color: white;
      border: none;
      padding: 5px 10px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }

    .btn-logout:hover {
      background-color: #c0392b;
    }

    .webview-container {
      flex-grow: 1;
      width: 100%;
      height: calc(100% - 60px);
      position: relative;
    }

    webview {
      width: 100%;
      height: 100%;
      border: none;
      display: flex;
    }

    .loading-indicator {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
    }

    .spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      border-radius: 50%;
      border-top: 4px solid #3498db;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 0 auto 10px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <header class="header">
      <div class="header-logo">
        <img src="../assets/images/logo.png" alt="Edara ERP Logo" class="small-logo">
        <span class="app-title">Edara ERP - Interface Odoo</span>
      </div>
      <div class="header-actions">
        <button id="logout-btn" class="btn-logout">Déconnexion</button>
      </div>
    </header>

    <div class="webview-container">
      <!-- L'élément webview sera inséré ici par le script -->
      <div id="loading-indicator" class="loading-indicator">
        <div class="spinner"></div>
        <p>Chargement de l'interface Odoo...</p>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // Récupérer les informations d'authentification depuis le localStorage
      const sessionId = localStorage.getItem('sessionId');
      const odooWebUrl = localStorage.getItem('odooWebUrl');
      const odooUsername = localStorage.getItem('odooUsername') || 'admin';
      const odooPassword = localStorage.getItem('odooPassword') || 'admin';

      if (!sessionId || !odooWebUrl) {
        console.error('Session ID ou URL Odoo manquant');
        return;
      }

      // Créer l'élément webview
      const webview = document.createElement('webview');
      webview.src = odooWebUrl;
      webview.setAttribute('partition', 'persist:odoo');
      webview.setAttribute('allowpopups', 'true');

      // Ajouter l'élément webview au conteneur
      const container = document.querySelector('.webview-container');
      container.appendChild(webview);

      // Gérer les événements de la webview
      webview.addEventListener('dom-ready', () => {
        // Injecter le cookie de session et ajouter un gestionnaire pour la page de connexion
        const script = `
          // Injecter le cookie de session
          document.cookie = "session_id=${sessionId}; path=/; domain=${new URL(odooWebUrl).hostname}";
          console.log("Cookie de session injecté:", document.cookie);

          // Ajouter un gestionnaire pour détecter si nous sommes redirigés vers la page de connexion
          // et remplir automatiquement les identifiants
          setTimeout(() => {
            // Vérifier si nous sommes sur la page de connexion
            const loginForm = document.querySelector('form.oe_login_form');
            if (loginForm) {
              console.log("Page de connexion détectée, tentative de connexion automatique");

              // Remplir les champs de formulaire
              const loginField = document.querySelector('input[name="login"]');
              const passwordField = document.querySelector('input[name="password"]');
              const dbField = document.querySelector('input[name="db"]') || document.querySelector('select[name="db"]');

              if (loginField) loginField.value = '${odooUsername}';
              if (passwordField) passwordField.value = '${odooPassword}';
              if (dbField && dbField.tagName === 'SELECT') {
                // Sélectionner 'ligne-digitale' dans la liste déroulante
                for (let i = 0; i < dbField.options.length; i++) {
                  if (dbField.options[i].value === 'ligne-digitale') {
                    dbField.selectedIndex = i;
                    break;
                  }
                }
              } else if (dbField && dbField.tagName === 'INPUT') {
                dbField.value = 'ligne-digitale';
              }

              // Soumettre le formulaire automatiquement après un court délai
              setTimeout(() => {
                loginForm.submit();
              }, 500);
            }
          }, 1000);
        `;
        webview.executeJavaScript(script);

        // Masquer l'indicateur de chargement
        document.getElementById('loading-indicator').style.display = 'none';
      });

      webview.addEventListener('did-fail-load', (event) => {
        console.error('Échec du chargement de la webview:', event);
        document.getElementById('loading-indicator').innerHTML = `
          <p>Erreur de chargement: ${event.errorDescription}</p>
          <button onclick="location.reload()">Réessayer</button>
        `;
      });

      // Détecter les changements de navigation
      webview.addEventListener('did-navigate', (event) => {
        console.log('Navigation vers:', event.url);
      });

      // Détecter les erreurs de console dans la webview
      webview.addEventListener('console-message', (event) => {
        console.log('Message de la webview:', event.message);
      });

      // Gérer la déconnexion
      document.getElementById('logout-btn').addEventListener('click', () => {
        // Effacer les informations d'authentification
        localStorage.clear();
        // Rediriger vers la page de connexion (via le processus principal)
        window.electronAPI.sendAuthData({ action: 'logout' });
      });
    });
  </script>
</body>
</html>
