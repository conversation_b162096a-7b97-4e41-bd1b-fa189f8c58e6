<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Edara ERP - Connexion</title>
  <style>
    /* Variables CSS pour le thème clair */
    :root {
      --background-color: #f0f0f0;
      --text-color: #333333;
      --secondary-text-color: #666666;
      --button-background: #585765;
      --button-hover: #6c6b7b;
      --form-background: #ffffff;
      --input-border: #dddddd;
      --input-focus: #585765;
      --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
      --separator-color: #dddddd;
      --logo-background: #585765;
    }

    /* Variables CSS pour le thème sombre */
    .dark-theme {
      --background-color: #2d3436;
      --text-color: #f5f6fa;
      --secondary-text-color: #dfe6e9;
      --button-background: #585765;
      --button-hover: #6c6b7b;
      --form-background: #3d4548;
      --input-border: #485154;
      --input-focus: #585765;
      --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      --separator-color: #485154;
      --logo-background: #585765;
    }

    /* Appliquer le thème sombre si le système est en mode sombre */
    @media (prefers-color-scheme: dark) {
      body {
        --background-color: #2d3436;
        --text-color: #f5f6fa;
        --secondary-text-color: #dfe6e9;
        --button-background: #585765;
        --button-hover: #6c6b7b;
        --form-background: #3d4548;
        --input-border: #485154;
        --input-focus: #585765;
        --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        --separator-color: #485154;
        --logo-background: #585765;
      }
    }

    html, body {
      margin: 0;
      padding: 0;
      height: 100%;
      width: 100%;
      overflow: hidden;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: var(--background-color);
      display: flex;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    /* Conteneur principal avec deux sections */
    .container {
      display: flex;
      width: 100%;
      height: 100%;
    }

    /* Section gauche (formulaire) */
    .left-section {
      width: 50%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    /* Formulaire de connexion */
    .login-form-container {
      background-color: var(--form-background);
      width: 100%;
      max-width: 390px;
      padding: 30px;
      border-radius: 8px;
      box-shadow: var(--box-shadow);
    }

    /* Conteneur du logo */
    .logo-container {
      display: flex;
      justify-content: center;
      margin-bottom: 20px;
      margin-top: 5px;
    }

    .logo-container img {
      width: 100px;
      height: 100px;
      /* Suppression du fond et du padding pour éviter la redondance avec l'image */
      filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.15));
    }

    /* Ligne de séparation sous le logo */
    .separator {
      width: 100%;
      border-top: 1px solid var(--separator-color);
      margin-bottom: 25px;
    }

    /* Titre du formulaire */
    .login-title {
      font-size: 20px;
      font-weight: 600;
      color: var(--text-color);
      margin: 0 0 25px 0;
      text-align: center;
    }

    /* Groupes de formulaire */
    .form-group {
      width: 100%;
      margin-bottom: 20px;
    }

    .form-group label {
      display: block;
      font-size: 14px;
      font-weight: 500;
      color: var(--text-color);
      margin-bottom: 6px;
    }

    .form-group select,
    .form-group input {
      width: 100%;
      padding: 10px 12px;
      font-size: 14px;
      border: 1px solid var(--input-border);
      border-radius: 4px;
      background-color: transparent;
      color: var(--text-color);
      box-sizing: border-box;
      transition: border-color 0.2s ease;
    }

    .form-group select:focus,
    .form-group input:focus {
      outline: none;
      border-color: var(--input-focus);
    }

    /* Conteneur pour le champ mot de passe avec bouton Afficher/Masquer */
    .password-container {
      position: relative;
      width: 100%;
    }

    .password-container input {
      padding-right: 80px;
    }

    .password-container button {
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
      background: none;
      border: none;
      color: var(--secondary-text-color);
      font-size: 13px;
      cursor: pointer;
      padding: 4px 8px;
    }

    /* Bouton Se connecter */
    .login-button {
      width: 100%;
      padding: 12px;
      margin-top: 5px;
      background-color: var(--button-background);
      color: #ffffff;
      font-size: 15px;
      font-weight: 500;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s ease;
    }

    .login-button:hover {
      background-color: var(--button-hover);
    }

    /* Message d'erreur */
    #login-error {
      color: #e74c3c;
      margin-top: 15px;
      text-align: center;
      font-size: 13px;
      width: 100%;
    }

    /* Section droite (illustration) */
    .right-section {
      width: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    @keyframes float {
      0% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
      100% { transform: translateY(0px); }
    }

    .right-section img {
      width: 85%;
      max-width: 600px;
      object-fit: contain;
      filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.1));
      animation: float 6s ease-in-out infinite;
    }

    /* Responsive design */
    @media (max-width: 768px) {
      .container {
        flex-direction: column;
      }

      .right-section {
        display: none;
      }

      .left-section {
        width: 100%;
        padding: 20px;
      }

      .login-form-container {
        max-width: 100%;
      }
    }

    /* Icône œil pour le mot de passe */
    .eye-icon {
      font-size: 16px;
      display: inline-block;
      width: 16px;
      height: 16px;
      vertical-align: middle;
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Section gauche : formulaire -->
    <div class="left-section">
      <div class="login-form-container">
        <div class="logo-container">
          <img src="../assets/images/icon.png" alt="Edara Logo">
        </div>
        <div class="separator"></div>
        <h1 class="login-title">Connexion</h1>
        <form id="login-form">
          <div class="form-group">
            <label for="instance">Instance</label>
            <select id="instance" name="instance">
              <option value="local">Locale (localhost:8069)</option>
              <option value="remote">Distance (edara.ligne-digitale.com)</option>
            </select>
          </div>
          <div class="form-group">
            <label for="email">Identifiant</label>
            <input type="text" id="email" name="email" placeholder="Nom d'utilisateur ou email" required>
          </div>
          <div class="form-group">
            <label for="password">Mot de passe</label>
            <div class="password-container">
              <input type="password" id="password" name="password" placeholder="Votre mot de passe" required>
              <button type="button" id="togglePassword">
                <span class="eye-icon">👁️</span>
              </button>
            </div>
          </div>
          <button type="submit" class="login-button">Se connecter</button>
        </form>
        <div id="login-error"></div>
      </div>
    </div>
    <!-- Section droite : illustration -->
    <div class="right-section">
      <img src="../assets/images/illustration.svg" alt="Illustration">
    </div>
  </div>

  <script>
    // Afficher/Masquer le mot de passe
    const passwordInput = document.getElementById('password');
    const togglePassword = document.getElementById('togglePassword');

    togglePassword.addEventListener('click', () => {
      const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
      passwordInput.setAttribute('type', type);
    });
  </script>
  <script src="../assets/js/custom-login.js"></script>
  <script src="../assets/js/odoo-auth.js"></script>
</body>
</html>
