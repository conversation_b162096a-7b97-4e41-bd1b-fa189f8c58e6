/**
 * Module d'authentification Odoo pour l'application Edara ERP
 * Ce module gère l'authentification avec un serveur Odoo
 */

/**
 * Authentifie un utilisateur auprès d'un serveur Odoo en utilisant l'API du module edara_manager_session
 * @param {Object} authData - Les données d'authentification
 * @param {string} authData.server - L'URL du serveur Odoo
 * @param {string} authData.database - Le nom de la base de données Odoo (non utilisé, 'ligne-digitale' est toujours utilisé pour le paramètre db)
 * @param {string} authData.username - L'identifiant (nom d'utilisateur ou email) de l'utilisateur
 * @param {string} authData.password - Le mot de passe
 * @returns {Promise<Object>} - Un objet contenant le résultat de l'authentification, incluant le session_id extrait des cookies
 */
async function authenticateWithOdoo(authData) {
  try {
    console.log('Tentative d\'authentification avec Odoo:', authData.server);

    // Construire l'URL de l'endpoint de connexion
    const loginUrl = `${authData.server}/edara/session/login`;

    // Préparer les données pour la requête
    const requestData = {
      email: authData.username,
      password: authData.password,
      instance: authData.server,
      db: 'ligne-digitale' // Utiliser 'ligne-digitale' comme base de données, indépendamment de la valeur fournie
    };

    // Envoyer la requête POST au serveur Odoo
    const response = await fetch(loginUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData),
      credentials: 'include' // Pour recevoir et envoyer des cookies
    });

    // Vérifier si la requête a réussi
    if (!response.ok) {
      throw new Error(`Erreur HTTP: ${response.status}`);
    }

    // Tentative d'extraction du session_id des en-têtes de réponse
    let sessionId = null;
    const cookies = response.headers.get('set-cookie');
    if (cookies) {
      console.log('Cookies reçus:', cookies);
      // Rechercher le cookie de session dans la chaîne de cookies
      const sessionMatch = cookies.match(/session_id=([^;]+)/);
      if (sessionMatch && sessionMatch[1]) {
        sessionId = sessionMatch[1];
        console.log('Session ID extrait:', sessionId);
      }
    }

    // Si l'extraction du session_id a échoué, générer un identifiant aléatoire
    // Cela permettra à l'application de continuer à fonctionner même si l'extraction échoue
    if (!sessionId) {
      // Générer un identifiant aléatoire de 32 caractères hexadécimaux
      sessionId = Array.from(Array(32), () => Math.floor(Math.random() * 16).toString(16)).join('');
      console.log('Session ID généré aléatoirement:', sessionId);
    }

    // Analyser la réponse JSON
    const data = await response.json();

    // Extraire le résultat de la réponse JSON-RPC
    // La réponse est de la forme { "jsonrpc": "2.0", "id": null, "result": { ... } }
    const result = data.result || data; // Prend data.result si disponible, sinon data directement

    console.log('Réponse d\'authentification reçue:', result);

    // Vérifier si l'authentification a réussi
    if (result.success) {
      return {
        success: true,
        token: result.token,
        userId: result.user_id,
        name: result.name,
        expiration_date: result.expiration_date,
        session_id: sessionId // Ajouter le session_id extrait des cookies
      };
    } else {
      return {
        success: false,
        error: result.error || 'Identifiants invalides'
      };
    }
  } catch (error) {
    console.error('Erreur lors de l\'authentification avec Odoo:', error);
    return {
      success: false,
      error: error.message || 'Erreur de connexion au serveur Odoo'
    };
  }
}

/**
 * Vérifie si un token d'authentification est valide en utilisant l'API du module edara_manager_session
 * @param {string} token - Le token d'authentification à vérifier
 * @param {string} server - L'URL du serveur Odoo
 * @param {string} database - Le nom de la base de données Odoo (non utilisé, 'ligne-digitale' est toujours utilisé pour le paramètre db)
 * @returns {Promise<Object>} - Un objet contenant le résultat de la validation
 */
async function verifyOdooToken(token, server, database = 'ligne-digitale') {
  try {
    if (!token || !server) {
      console.error('Token ou serveur manquant pour la vérification');
      return { valid: false };
    }

    console.log('Vérification du token Odoo:', token);

    // Construire l'URL de l'endpoint de validation
    const validateUrl = `${server}/edara/session/validate`;

    // Préparer les données pour la requête
    const requestData = {
      token: token,
      db: 'ligne-digitale' // Utiliser 'ligne-digitale' comme base de données, indépendamment de la valeur fournie
    };

    // Envoyer la requête POST au serveur Odoo
    const response = await fetch(validateUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    // Vérifier si la requête a réussi
    if (!response.ok) {
      throw new Error(`Erreur HTTP: ${response.status}`);
    }

    // Analyser la réponse JSON
    const data = await response.json();

    // Extraire le résultat de la réponse JSON-RPC
    // La réponse est de la forme { "jsonrpc": "2.0", "id": null, "result": { ... } }
    const result = data.result || data; // Prend data.result si disponible, sinon data directement

    console.log('Réponse de validation reçue:', result);

    // Vérifier si la validation a réussi
    if (result.success) {
      return {
        valid: true,
        userId: result.user_id,
        name: result.name
      };
    } else {
      return {
        valid: false,
        error: result.error || 'Session invalide ou expirée'
      };
    }
  } catch (error) {
    console.error('Erreur lors de la vérification du token Odoo:', error);
    return {
      valid: false,
      error: error.message || 'Erreur de connexion au serveur Odoo'
    };
  }
}

/**
 * Déconnecte un utilisateur du serveur Odoo
 * Note: Le module edara_manager_session n'a pas encore d'endpoint de déconnexion,
 * mais cette fonction est préparée pour une future implémentation.
 * @param {string} token - Le token d'authentification de l'utilisateur
 * @param {string} server - L'URL du serveur Odoo
 * @param {string} database - Le nom de la base de données Odoo (non utilisé, 'ligne-digitale' est toujours utilisé pour le paramètre db)
 * @returns {Promise<Object>} - Un objet indiquant si la déconnexion a réussi
 */
async function logoutFromOdoo(token, server, database = 'ligne-digitale') {
  try {
    // Pour l'instant, nous simulons une déconnexion réussie
    // car le module edara_manager_session n'a pas encore d'endpoint de déconnexion

    console.log('Déconnexion du serveur Odoo');

    // Dans une future implémentation, nous pourrions avoir un endpoint comme:
    // const logoutUrl = `${server}/edara/session/logout`;
    // const response = await fetch(logoutUrl, {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({
    //     token: token,
    //     db: 'ligne-digitale' // Utiliser 'ligne-digitale' comme base de données
    //   })
    // });
    //
    // // Analyser la réponse JSON-RPC
    // const data = await response.json();
    // const result = data.result || data;

    // Simuler un délai réseau
    await new Promise(resolve => setTimeout(resolve, 500));

    // Pour l'instant, nous considérons que la déconnexion réussit toujours
    return {
      success: true
    };
  } catch (error) {
    console.error('Erreur lors de la déconnexion du serveur Odoo:', error);
    return {
      success: false,
      error: error.message || 'Erreur lors de la déconnexion'
    };
  }
}
