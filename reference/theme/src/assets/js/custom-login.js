/**
 * Script pour la page de connexion personnalisée de l'application Edara ERP
 * Ce script gère le formulaire de connexion et l'envoi des données d'authentification
 */

document.addEventListener('DOMContentLoaded', () => {
  const loginForm = document.getElementById('login-form');
  const loginError = document.getElementById('login-error');

  // Gérer la soumission du formulaire de connexion
  loginForm.addEventListener('submit', async (event) => {
    event.preventDefault();

    // Récupérer les valeurs du formulaire
    const instanceSelect = document.getElementById('instance');
    const instanceOption = instanceSelect.options[instanceSelect.selectedIndex];
    const instanceText = instanceOption.text;
    const instanceValue = instanceOption.value;

    // Déterminer le serveur en fonction de l'instance sélectionnée
    let server;
    if (instanceValue === 'local') {
      server = 'http://localhost:8069';
    } else if (instanceValue === 'remote') {
      server = 'https://edara.ligne-digitale.com';
    }

    const email = document.getElementById('email').value.trim();
    const password = document.getElementById('password').value;

    // Vérifier que tous les champs sont remplis
    if (!email || !password) {
      loginError.textContent = 'Veuillez remplir tous les champs';
      return;
    }

    // Préparer les données d'authentification
    const authData = {
      server,
      username: email, // Utiliser la valeur saisie comme identifiant (nom d'utilisateur ou email)
      password
    };

    try {
      // Afficher un message de connexion en cours
      loginError.textContent = 'Connexion en cours...';
      loginError.style.color = '#3498db'; // Bleu pour indiquer le chargement

      // Tenter de s'authentifier avec Odoo
      const result = await authenticateWithOdoo(authData);

      if (result.success) {
        // Stocker les informations d'authentification dans le stockage local
        localStorage.setItem('authToken', result.token);
        localStorage.setItem('userName', result.name);
        localStorage.setItem('userId', result.userId);
        localStorage.setItem('serverUrl', server);
        localStorage.setItem('tokenExpiration', result.expiration_date);

        // Stocker les identifiants pour la connexion automatique à l'interface Odoo
        // Note: Dans un environnement de production, il faudrait sécuriser ces données
        localStorage.setItem('odooUsername', email);
        localStorage.setItem('odooPassword', password);

        // Construire l'URL de l'interface Odoo
        const odooWebUrl = `${server}/web`;

        // Envoyer les données d'authentification au processus principal
        window.electronAPI.sendAuthData({
          action: 'login',
          token: result.token,
          userId: result.userId,
          name: result.name,
          server: server,
          expiration_date: result.expiration_date,
          session_id: result.session_id, // Ajouter le session_id
          odoo_url: odooWebUrl // Ajouter l'URL de l'interface Odoo avec la clé odoo_url
        });

        loginError.textContent = '';
      } else {
        // Afficher l'erreur d'authentification
        loginError.textContent = result.error || 'Échec de l\'authentification';
        loginError.style.color = '#e74c3c'; // Rouge pour indiquer l'erreur
      }
    } catch (error) {
      console.error('Erreur d\'authentification:', error);
      loginError.textContent = 'Une erreur s\'est produite lors de l\'authentification';
      loginError.style.color = '#e74c3c'; // Rouge pour indiquer l'erreur
    }
  });

  // Fonction pour simuler une authentification réussie (à des fins de démonstration)
  // Dans une application réelle, cette fonction serait remplacée par une véritable authentification
  window.simulateSuccessfulLogin = () => {
    const email = document.getElementById('email').value.trim() || '<EMAIL>';
    const instanceSelect = document.getElementById('instance');
    const instanceValue = instanceSelect.options[instanceSelect.selectedIndex].value;

    // Déterminer le serveur en fonction de l'instance sélectionnée
    let server;
    if (instanceValue === 'local') {
      server = 'http://localhost:8069';
    } else if (instanceValue === 'remote') {
      server = 'https://edara.ligne-digitale.com';
    }

    // Créer une date d'expiration (24 heures à partir de maintenant)
    const expiration = new Date();
    expiration.setHours(expiration.getHours() + 24);
    const expiration_date = expiration.toISOString();

    // Simuler une authentification réussie
    localStorage.setItem('authToken', 'fake-token-123');
    // Déterminer le nom d'utilisateur à afficher
    const userName = email.includes('@') ? email.split('@')[0] : email;
    localStorage.setItem('userName', userName);
    localStorage.setItem('userId', '1');
    localStorage.setItem('serverUrl', server);
    localStorage.setItem('tokenExpiration', expiration_date);

    // Construire l'URL de l'interface Odoo
    const odooWebUrl = `${server}/web`;

    // Envoyer les données d'authentification au processus principal
    window.electronAPI.sendAuthData({
      action: 'login',
      token: 'fake-token-123',
      userId: '1',
      name: userName,
      server: server,
      expiration_date: expiration_date,
      session_id: 'fake-session-id-123', // Ajouter un session_id factice
      odoo_url: odooWebUrl // Ajouter l'URL de l'interface Odoo avec la clé odoo_url
    });
  };
});
