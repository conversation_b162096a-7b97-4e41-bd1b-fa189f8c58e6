/**
 * Styles pour la page de connexion personnalisée de l'application Edara ERP
 */

body {
  margin: 0;
  padding: 0;
  height: 100vh;
  overflow: hidden;
  background-color: #121212;
}

.login-container {
  display: flex;
  height: 100vh;
}

.login-left {
  flex: 1;
  background-color: #3498db;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: 40px;
  color: white;
}

.login-logo {
  margin-top: 20px;
}

.login-logo img {
  width: 100px;
  height: 100px;
  object-fit: contain;
}

.login-illustration {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.login-illustration img {
  max-width: 80%;
  max-height: 60%;
  object-fit: contain;
}

.login-right {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #1e1e1e;
}

.login-form-container {
  width: 80%;
  max-width: 400px;
}

h1 {
  font-size: 24px;
  margin-bottom: 10px;
  color: #ffffff;
}

.login-subtitle {
  color: #cccccc;
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #ffffff;
}

input {
  width: 100%;
  padding: 12px;
  border: 1px solid #333333;
  border-radius: 4px;
  font-size: 14px;
  background-color: #2a2a2a;
  color: #ffffff;
}

input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-actions {
  margin-top: 30px;
}

.btn-login {
  width: 100%;
  padding: 12px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-login:hover {
  background-color: #2980b9;
}

.login-error {
  color: #e74c3c;
  margin-top: 20px;
  font-size: 14px;
  text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
  }

  .login-left {
    display: none;
  }

  .login-right {
    flex: 1;
  }
}
