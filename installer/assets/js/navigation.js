/**
 * Navigation script for Edara Workspace installation
 * Adapted for Electron
 */

// Exposer les fonctions de navigation globalement
window.navigation = {
  goToNextStep: function() {
    // Obtenir la page actuelle
    const currentPath = window.location.pathname;
    const currentPageName = currentPath.split('/').pop();
    const pageSequence = [
      'welcome.html',
      'terms.html',
      'product-key.html',
      'backup.html',
      'complete.html'
    ];
    const currentPageIndex = pageSequence.indexOf(currentPageName);

    // Naviguer vers la page suivante
    const nextPageIndex = currentPageIndex + 1;
    if (nextPageIndex < pageSequence.length) {
      window.location.href = pageSequence[nextPageIndex];
    }
  }
};

document.addEventListener('DOMContentLoaded', function() {
  // Define the page sequence
  const pageSequence = [
    'welcome.html',
    'terms.html',
    'product-key.html',
    'backup.html',
    'complete.html'
  ];

  // Get the current page from the URL
  const currentPath = window.location.pathname;
  const currentPageName = currentPath.split('/').pop();
  const currentPageIndex = pageSequence.indexOf(currentPageName);

  // Set up navigation buttons
  const nextButton = document.querySelector('.btn-next');
  const backButton = document.querySelector('.btn-back');
  const cancelButton = document.querySelector('.btn-cancel');

  // Handle next button click
  if (nextButton) {
    nextButton.addEventListener('click', function() {
      // Check if we need to validate anything on the current page
      let canProceed = true;

      // Example: Check terms acceptance on terms page
      if (currentPageName === 'terms.html') {
        const termsCheckbox = document.getElementById('accept-terms');
        if (termsCheckbox && !termsCheckbox.checked) {
          alert('Veuillez accepter les conditions d\'utilisation pour continuer.');
          canProceed = false;
        } else if (termsCheckbox && termsCheckbox.checked) {
          // Notify Electron that terms were accepted
          if (window.electronAPI) {
            window.electronAPI.acceptTerms(true);
          }
        }
      }

      // Check product key on product-key page
      if (currentPageName === 'product-key.html') {
        // The product key validation is handled by product-key.js
        // which enables/disables the next button based on key validity
        const keyInputs = document.querySelectorAll('.key-input');
        const workspaceNameInput = document.getElementById('workspace-name');
        let enteredKey = '';

        // Collect values from all input fields
        keyInputs.forEach(input => {
            enteredKey += input.value;
        });

        // Remove spaces for comparison
        enteredKey = enteredKey.replace(/\s/g, '');

        // Check if the workspace name is provided
        if (!workspaceNameInput.value.trim()) {
          const errorMessage = document.querySelector('.error-message');
          if (errorMessage) {
            errorMessage.textContent = "Veuillez entrer un nom pour votre espace.";
            errorMessage.classList.add('visible');
          }
          canProceed = false;
        }
        // Check if the key is valid (1111 1111 1111 1111)
        else if (enteredKey !== '1111111111111111') {
          // Mettre à jour le style du champ pour montrer que la valeur est valide
          if (workspaceNameInput) {
            workspaceNameInput.style.color = "";
            workspaceNameInput.style.fontStyle = "";
          }

          const errorMessage = document.querySelector('.error-message');
          if (errorMessage) {
            errorMessage.textContent = "Clé de produit invalide. Veuillez entrer '1111 1111 1111 1111'.";
            errorMessage.classList.add('visible');
          }
          canProceed = false;
        }
        // If everything is valid, store the workspace name
        else if (window.electronAPI) {
          // Mettre à jour le style du champ pour montrer que la valeur est valide
          if (workspaceNameInput) {
            workspaceNameInput.style.color = "";
            workspaceNameInput.style.fontStyle = "";
          }

          // Store the workspace name in user preferences
          try {
            window.electronAPI.setWorkspaceName(workspaceNameInput.value.trim());
          } catch (error) {
            console.error('Error storing workspace name:', error);
          }
        }
      }

      // Handle backup configuration page
      // Note: La gestion du chemin de sauvegarde est maintenant gérée dans backup.js
      // pour permettre la création du dossier avant de passer à l'étape suivante

      // Handle installation complete page
      if (currentPageName === 'complete.html') {
        const createShortcutCheckbox = document.getElementById('create-shortcut');
        if (createShortcutCheckbox && window.electronAPI) {
          // Tell Electron whether to create a desktop shortcut
          window.electronAPI.createDesktopShortcut(createShortcutCheckbox.checked);
        }

        // Finish installation and launch the app
        if (window.electronAPI) {
          window.electronAPI.finishInstallAndLaunchApp();
        }
        return; // Don't proceed with normal navigation
      }

      // Navigate to next page if validation passes
      if (canProceed) {
        // Tell Electron to go to the next step
        if (window.electronAPI) {
          window.electronAPI.goToNextStep();
        }

        // Also handle navigation in the browser
        const nextPageIndex = currentPageIndex + 1;
        if (nextPageIndex < pageSequence.length) {
          window.location.href = pageSequence[nextPageIndex];
        }
      }
    });
  }

  // Handle back button click
  if (backButton) {
    backButton.addEventListener('click', function() {
      const prevPageIndex = currentPageIndex - 1;
      if (prevPageIndex >= 0) {
        window.location.href = pageSequence[prevPageIndex];
      }
    });
  }

  // Handle cancel button click
  if (cancelButton) {
    cancelButton.addEventListener('click', function() {
      if (confirm('Êtes-vous sûr de vouloir annuler l\'installation ?')) {
        // Close the application using Electron API
        if (window.electronAPI) {
          window.electronAPI.cancelInstallation();
        } else {
          // Fallback for browser testing
          window.location.href = 'welcome.html';
        }
      }
    });
  }

  // Handle backup folder selection
  const selectFolderButton = document.getElementById('select-folder-button');
  if (selectFolderButton) {
    selectFolderButton.addEventListener('click', async function() {
      if (window.electronAPI) {
        try {
          const result = await window.electronAPI.selectBackupFolder();
          if (!result.canceled && result.filePaths.length > 0) {
            const backupPath = document.getElementById('backup-path');
            if (backupPath) {
              backupPath.value = result.filePaths[0];
            }
          }
        } catch (error) {
          console.error('Error selecting folder:', error);
        }
      }
    });
  }
});
