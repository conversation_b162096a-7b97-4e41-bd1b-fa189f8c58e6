/**
 * Product key validation script for Edara Workspace
 */
document.addEventListener('DOMContentLoaded', function() {
    // Get all key input fields
    const keyInputs = document.querySelectorAll('.key-input');
    const workspaceNameInput = document.getElementById('workspace-name');
    const nextButton = document.querySelector('.btn-next');
    const pasteButton = document.querySelector('.btn-paste');
    const errorMessage = document.querySelector('.error-message');

    // Configurer le champ de nom d'espace comme "edara" par défaut
    if (workspaceNameInput) {
        workspaceNameInput.value = "edara";
        // Permettre la saisie dans le champ
        workspaceNameInput.readOnly = false;

        // Appliquer le style initial (gris clair et italique)
        workspaceNameInput.style.color = "#999";
        workspaceNameInput.style.fontStyle = "italic";

        // Ajouter un gestionnaire d'événements pour le focus
        workspaceNameInput.addEventListener('focus', function() {
            // Sélectionner tout le texte lors du focus
            this.select();
            // Changer le style lorsque l'utilisateur commence à saisir
            this.style.color = "";
            this.style.fontStyle = "";
        });

        // Ajouter un gestionnaire d'événements pour la perte de focus
        workspaceNameInput.addEventListener('blur', function() {
            // Si le champ est vide ou contient la valeur par défaut, remettre le style initial
            if (!this.value.trim() || this.value === "edara") {
                this.value = "edara";
                this.style.color = "#999";
                this.style.fontStyle = "italic";
            }
        });
    }

    // Valid product key (as specified)
    const validKey = "1111 1111 1111 1111";

    // Format the key for display (with spaces)
    function formatKey(key) {
        // Remove all spaces
        key = key.replace(/\s/g, '');

        // Add a space every 4 characters
        return key.replace(/(.{4})/g, '$1 ').trim();
    }

    // Validate the complete key
    function validateKey() {
        let enteredKey = '';

        // Collect values from all input fields
        keyInputs.forEach(input => {
            enteredKey += input.value;
        });

        // Remove spaces for comparison
        enteredKey = enteredKey.replace(/\s/g, '');
        const validKeyNoSpaces = validKey.replace(/\s/g, '');

        if (enteredKey === validKeyNoSpaces) {
            // Valid key - enable next button and add valid styling
            nextButton.disabled = false;
            keyInputs.forEach(input => {
                input.classList.add('valid');
                input.classList.remove('error');
            });

            if (errorMessage) {
                errorMessage.classList.remove('visible');
            }

            return true;
        } else if (enteredKey.length === validKeyNoSpaces.length) {
            // Invalid key but correct length - show error
            nextButton.disabled = true;
            keyInputs.forEach(input => {
                input.classList.add('error');
                input.classList.remove('valid');
            });

            if (errorMessage) {
                errorMessage.textContent = "Clé de produit invalide. Veuillez vérifier et réessayer.";
                errorMessage.classList.add('visible');
            }

            return false;
        } else {
            // Incomplete key - neutral styling
            nextButton.disabled = true;
            keyInputs.forEach(input => {
                input.classList.remove('error');
                input.classList.remove('valid');
            });

            if (errorMessage) {
                errorMessage.classList.remove('visible');
            }

            return false;
        }
    }

    // Auto-tab between input fields
    keyInputs.forEach((input, index) => {
        input.addEventListener('input', function(e) {
            // Format the input (remove non-digits and spaces)
            let value = this.value.replace(/[^\d\s]/g, '');

            // Update the input value
            this.value = value;

            // If we've reached the max length, move to the next input
            if (value.length >= this.maxLength && index < keyInputs.length - 1) {
                keyInputs[index + 1].focus();
            }

            // Validate the key
            validateKey();
        });

        // Handle backspace to go to previous input
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Backspace' && this.value.length === 0 && index > 0) {
                keyInputs[index - 1].focus();
            }
        });
    });

    // Handle paste button
    if (pasteButton) {
        pasteButton.addEventListener('click', async function() {
            try {
                // Try to read from clipboard
                const text = await navigator.clipboard.readText();

                // Format the pasted text
                const formattedKey = formatKey(text);

                // Split the key into chunks for each input
                const keyChunks = formattedKey.split(' ');

                // Fill the inputs
                keyInputs.forEach((input, index) => {
                    if (index < keyChunks.length) {
                        input.value = keyChunks[index];
                    }
                });

                // Validate the key
                validateKey();
            } catch (err) {
                console.error('Failed to read clipboard contents: ', err);
                if (errorMessage) {
                    errorMessage.textContent = "Impossible d'accéder au presse-papiers. Veuillez saisir la clé manuellement.";
                    errorMessage.classList.add('visible');
                }
            }
        });
    }

    // Initially disable the next button
    if (nextButton) {
        nextButton.disabled = true;
    }
});
