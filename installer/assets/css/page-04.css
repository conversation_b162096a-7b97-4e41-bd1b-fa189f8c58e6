/* Specific styles for page 04 - Backup Configuration */

/* Description text */
.right-panel .description {
    font-family: 'Proxima Nova', sans-serif;
    font-weight: 400;
    font-size: clamp(14px, 1.5vw, 16px);
    line-height: 1.6;
    opacity: 0.9;
    margin-bottom: 30px;
    max-width: 100%;
}

/* Backup location container */
.backup-location-container {
    margin-top: 40px;
    display: flex;
    align-items: center;
    gap: 15px;
    width: 82%;
    max-width: 600px;
    margin-bottom: 100px; /* Space for the faded logo */
}

.location-input-group {
    flex-grow: 1;
    width: 100%;
}

.location-input {
    width: 90%;
    height: 40px;
    background-color: var(--color-input-bg);
    border: 1px solid var(--color-input-border);
    border-radius: 6px;
    color: var(--color-input-text);
    font-family: 'Proxima Nova', sans-serif;
    font-size: 14px;
    padding: 0 10px;
    transition: all 0.3s ease;
}

.location-input:focus {
    outline: none;
    border-color: var(--color-button-primary);
    background-color: var(--color-input-focus);
}

.btn-browse {
    height: 40px;
    background-color: var(--color-button-secondary);
    border: none;
    border-radius: 4px;
    color: #ffffff;
    font-family: 'Proxima Nova', sans-serif;
    font-size: 14px;
    padding: 0 15px;
    cursor: pointer;
    white-space: nowrap;
    transition: background-color 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.btn-browse:hover {
    opacity: 0.9;
}

/* Left panel illustration */
.left-illustration {
    max-width: 85% !important;
    position: relative;
    left: 10%;
    top: 0;
}
