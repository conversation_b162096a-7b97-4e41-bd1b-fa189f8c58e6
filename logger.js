/**
 * Module de journalisation pour l'application Edara ERP
 * Ce module utilise electron-log pour enregistrer les logs de l'application
 */

const log = require('electron-log');
const path = require('path');

// Configuration de electron-log
log.transports.file.level = 'info';
log.transports.console.level = 'debug';

// Format personnalisé pour les logs
log.transports.file.format = '{y}-{m}-{d} {h}:{i}:{s} [{level}] {text}';
log.transports.console.format = '[{level}] {text}';

// Définir le chemin du fichier de log
log.transports.file.resolvePathFn = () => {
  // Logs dans le dossier logs à la racine de l'application
  return path.join(process.cwd(), 'logs', 'edara-erp.log');
};

// Fonction pour enregistrer les erreurs non gérées
process.on('uncaughtException', (error) => {
  log.error('Erreur non gérée:', error);
});

// Fonction pour enregistrer les rejets de promesses non gérés
process.on('unhandledRejection', (reason, promise) => {
  log.error('Rejet de promesse non géré:', reason);
});

module.exports = log;
